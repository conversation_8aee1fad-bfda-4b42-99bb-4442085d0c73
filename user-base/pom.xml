<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.dxhy</groupId>
        <artifactId>user-service</artifactId>
        <version>1.0.0</version>
    </parent>
    <groupId>com.dxhy</groupId>
    <artifactId>user-base</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>
    <dependencies>
        <dependency>
            <groupId>com.dxhy</groupId>
            <artifactId>user-core</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>
    
    <profiles>
        <!-- 包含API同步模块的Profile -->
        <profile>
            <id>with-api-sync</id>
            <dependencies>
                <dependency>
                    <groupId>com.dxhy</groupId>
                    <artifactId>user-api-sync</artifactId>
                    <version>1.0.0</version>
                </dependency>
            </dependencies>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        
        <!-- 不包含API同步模块的Profile -->
        <profile>
            <id>without-api-sync</id>
            <!-- 此Profile不包含user-api-sync依赖 -->
        </profile>
    </profiles>
    
    <build>
        <finalName>${artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
