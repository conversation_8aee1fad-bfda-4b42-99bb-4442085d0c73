/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.base.controller;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysProduct;
import com.dxhy.core.pojo.entity.SysTenant;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import com.dxhy.core.service.ISysProductService;
import com.dxhy.core.service.ITenantService;
import com.dxhy.core.utils.KeyGenerator;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 10:39
 */
@RestController
@RequestMapping("/tenant")
@Api(tags = "租户管理")
@Slf4j
public class TenantController extends BaseController{

	@Autowired
	private ITenantService tenantService;

	@Autowired
	private ISysProductService productService;
	/**
	 * 详情
	 */
	@GetMapping("/detail/{id}")
	public Result detail(@PathVariable Long id) {
		SysTenant detail = tenantService.selectById(id);
		return Result.ok().put("data", detail);
	}

	/**
	 * 分页
	 */
	@PostMapping("/list")
	public Result list(@RequestBody Map<String, Object> params) {
		if (!params.containsKey("tenantId") || ObjectUtil.isEmpty(params.get("tenantId"))) {
			return Result.error("tenantId不能为空！");
		}
		int pageNo = Integer.parseInt(params.get("pageNo").toString());
		int pageSize = Integer.parseInt(params.get("pageSize").toString());
		Page<SysTenant> page = new Page(pageNo, pageSize);
		return tenantService.listByPage(page, params);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	public Result submit(@RequestBody SysTenant tenant) {
		return tenantService.saveTenant(this.getUserId()+"", tenant);
	}

	/**
	 * 删除
	 */
	@PostMapping("/remove")
	public Result remove(@RequestBody Map<String, Object> params) {
		if (params.containsKey("ids")) {
			String ids = (String) params.get("ids");
			return tenantService.remove(ids);
		}
		return Result.error("ids不能为空！");
	}

	/**
	 * 查询产品列表
	 *
	 */
	@GetMapping("/productList")
	public Result productList(@RequestParam(value = "tenantId", required = false) String tenantId) {
		//机构只能查看顶级租户已授权的产品
		if(StringUtils.isNotBlank(tenantId)){
			SysTenant tenantInfo = tenantService.getTenantInfo(tenantId);
			if(tenantInfo != null){
				List<SysTenantProduct> tenantProducts = tenantInfo.getTenantProducts();
				if(CollectionUtils.isNotEmpty(tenantProducts)){
					//获取全部产品id
					List<Long> productIdList = tenantProducts.stream().map(SysTenantProduct::getProductId).collect(Collectors.toList());
					List<SysProduct> products = productService.selectBatchIds(productIdList);
					return Result.ok().put("data", products);
				}
			}
			return Result.ok().put("data", null);
		}
		List<SysProduct> products = productService.selectList(new EntityWrapper<SysProduct>()
				.eq("is_deleted", "0")
		);
		return Result.ok().put("data", products);
	}


	/**
	 * 生成随机秘钥
	 *
	 */
	@GetMapping("/getGenerateKey")
	public Result getGenerateKey() {
		Map<String, String> key = KeyGenerator.getGenerateKey();
		return Result.ok().put("data", key);
	}

	/**
	 * 获取租户的secretKey
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2025-04-22
	 */
	@ApiOperation("获取租户的secretKey")
	@RequestMapping(value = "/getTenantAuthKey",method = RequestMethod.POST)
	public Result getTenantAuthKey(@RequestBody Map<String, String> params){
		log.info("[获取租户的secretKey /getTenantAuthKey]接收参数：{}",params);
		//密钥id
		String secretId = params.get("secretId");
		//租户编码
		String tenantCode = params.get("tenantCode");
		//默认根据密钥id查询
		SysTenant entity = tenantService.getSecret(secretId);
		if(StringUtils.isBlank(secretId)){
			//如果密钥id为空则根据租户编码查询
			entity = tenantService.getTenantInfo(tenantCode);
		}
		Map map = new HashMap();
		if(entity!=null){
			map.put("secretKey",entity.getSecretKey());
		}
		return Result.ok().put("data",map);
	}

	/**
	 * 根据密钥id和产品id校验该租户是否已授权该产品
	 * @param params
	 * @return
	 * <AUTHOR>
	 * @date 2025-04-22
	 */
	@ApiOperation("根据密钥id和产品id校验该租户是否已授权该产品")
	@RequestMapping(value = "/validTenantProduct",method = RequestMethod.POST)
	public Result validTenantProduct(@RequestBody Map<String, String> params){
		//密钥id
		String secretId = params.get("secretId");
		//租户编码
		String productId = params.get("productId");
		log.info("[校验租户是否有已授权产品]密钥id为：{}，产品id为：{}",secretId,productId);
		//根据密钥id查询租户信息
		boolean flag = tenantService.validTenantProduct(secretId,productId);
		return Result.ok().put("data",flag);
	}

}
