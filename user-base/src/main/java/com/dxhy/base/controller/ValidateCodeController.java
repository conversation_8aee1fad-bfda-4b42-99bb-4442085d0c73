/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.base.controller;

import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import com.dxhy.core.common.response.R;
import com.dxhy.core.constants.SecurityConstants;
import com.dxhy.core.pojo.DTO.CodeDTO;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.utils.Assert;
import com.google.code.kaptcha.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2017/12/18
 * 验证码提供
 */
@Controller
public class ValidateCodeController {
    @Autowired
    private Producer producer;
    @Autowired
    private SysUserService userService;

    /**
     * 创建验证码
     *
     * @param request request
     * @throws Exception
     */
    @GetMapping(SecurityConstants.DEFAULT_VALIDATE_CODE_URL_PREFIX + "/{randomStr}")
    @ResponseBody
    public R createCode(@PathVariable String randomStr, HttpServletRequest request) throws Exception {
        Assert.isBlank(randomStr, "机器码不能为空");
        //生成文字验证码
//        String text = producer.createText();
//        //生成图片验证码
//        BufferedImage image = producer.createImage(text);

        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(160,40,4,5);
        //设置背景色
        lineCaptcha.setBackground(new Color(246, 250, 255));
        //将验证码保存到redis
        String text = lineCaptcha.getCode();
        userService.saveImageCode(randomStr, text);

        //设置图形圆角
        BufferedImage image = lineCaptcha.getImage();
        int w = image.getWidth();
        int h = image.getHeight();
        BufferedImage output = new BufferedImage(w, h, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2 = output.createGraphics();
        output = g2.getDeviceConfiguration().createCompatibleImage(w, h, Transparency.TRANSLUCENT);
        g2.dispose();
        g2 = output.createGraphics();
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2.fillRoundRect(0, 0,w, h, 20, 20);
        g2.setComposite(AlphaComposite.SrcIn);
        g2.drawImage(image, 0, 0, w, h, null);

        //设置字边距 没生效
//        Map<TextAttribute, Object> attributes = new HashMap<TextAttribute, Object>();
//        attributes.put(TextAttribute.TRACKING, 0.5);
//        Font font = new Font("serif", Font.PLAIN, 16);
//        Font font2 = font.deriveFont(attributes);
//        g2.setFont(font2);

//        int x = (int) (w / 2 - 0.90 * g2.getFontMetrics().stringWidth(text) / 2);
//        int y = h / 2 + g2.getFontMetrics().getHeight() / 3;
//        String tempstr = new String();
//        int orgstringwight = g2.getFontMetrics().stringWidth(text);
//        int orgstringLength = text.length();
//        int tempx = x;
//        int tempy = y;
//        while (text.length() > 0) {
//            tempstr = text.substring(0, 1);
//            text = text.substring(1, text.length());
//            g2.drawString(tempstr, tempx, tempy);
//            tempx = (int) (tempx + (double) orgstringwight / (double) orgstringLength * 0.90);
//        }

        g2.dispose();

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(output, "png", baos);

//        //imgage 转成base64
//        ByteArrayOutputStream baos = new ByteArrayOutputStream();//io流
//        ImageIO.write(image, "png", baos);//写入流中
//        byte[] bytes = baos.toByteArray();//转换成字节

        BASE64Encoder encoder = new BASE64Encoder();
//        byte[] bytes = lineCaptcha.getImageBytes();
        byte[] bytes = baos.toByteArray();
        String png_base64 = encoder.encodeBuffer(bytes).trim();//转换成base64串
        png_base64 = png_base64.replaceAll("\n", "").replaceAll("\r", "");//删除 \r\n

//        ImageIO.write(bufferedImage, "png", new File("D:/qrcode1.png"));
//        System.out.println("值为："+"data:image/jpg;base64,"+png_base64);
        HashMap<String, Object> map = new HashMap<>();
        map.put("image", png_base64);
        return new R(map);
    }

//    @GetMapping(SecurityConstants.DEFAULT_VALIDATE_CODE_URL_PREFIX + "/{randomStr}")
//    public void createCode(@PathVariable String randomStr, HttpServletRequest request, HttpServletResponse response)
//            throws Exception {
//        Assert.isBlank(randomStr, "机器码不能为空");
//        response.setHeader("Cache-Control", "no-store, no-cache");
//        response.setContentType("image/jpeg");
//        //生成文字验证码
//        String text = producer.createText();
//        //生成图片验证码
//        BufferedImage image = producer.createImage(text);
//        userService.saveImageCode(randomStr, text);
//        ServletOutputStream out = response.getOutputStream();
//        ImageIO.write(image, "JPEG", out);
//        IOUtils.closeQuietly(out);
//    }


    /**
     * 生成图像验证码
     * @param response response请求对象
     * @throws IOException
     */
    @GetMapping(value = "/code/register/generateValidateCode")
    public void generateValidateCode(HttpServletResponse response) throws IOException {
        //设置response响应
        response.setCharacterEncoding("UTF-8");
        response.setHeader("Pragma", "No-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        response.setContentType("image/jpeg");
        //定义图形验证码的长和宽
        LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(160,40,4,5);
        String code = lineCaptcha.getCode();
        //输出浏览器
        OutputStream out=response.getOutputStream();
        lineCaptcha.write(out);
        out.flush();
        out.close();
    }

    /**
     * 发送手机验证码
     * 后期要加接口限制
     *
     * @param codeDTO
     * @return R
     */
    @ResponseBody
    @PostMapping(SecurityConstants.DEFAULT_VALIDATE_CODE_URL_PREFIX+"/sendCode")
    public R<Boolean> sendCode(@RequestBody CodeDTO codeDTO) {
        return userService.sendCode(codeDTO);
    }

    /**
     * 发送手机验证码 微信公众号
     * 后期要加接口限制
     *
     * @param codeDTO
     * @return R
     */
    @ResponseBody
    @PostMapping(SecurityConstants.DEFAULT_VALIDATE_CODE_URL_PREFIX+"/sendWeChatCode")
    public R<Boolean> sendWeChatCode(@RequestBody CodeDTO codeDTO) {
        return userService.sendWeChatCode(codeDTO);
    }
}
