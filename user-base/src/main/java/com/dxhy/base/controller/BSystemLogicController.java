package com.dxhy.base.controller;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.SysDeptMapper;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysTaxbureauInfo;
import com.dxhy.core.service.BSystemLogicService;
import com.dxhy.core.service.SigninService;
import com.dxhy.core.service.SysTaxbureauInfoService;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.utils.AESUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 外链系统业务逻辑处理
 * <AUTHOR>
 * @date 2022-08-08
 */
@Slf4j
@RestController
@RequestMapping("/bsyslogic")
public class BSystemLogicController extends BaseController {
    @Value("${security.encode.key}")
    private String key;//用于解密的Key

    @Autowired
    private NacosParam nacosParam;

    @Autowired
    private SysUserService sysUserService;

    @Resource
    private SigninService signinService;

    @Resource
    private BSystemLogicService bSystemLogicService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysTaxbureauInfoService sysTaxbureauInfoService;

    @Resource
    private SysDeptMapper sysDeptMapper;

//    @Value("${ele-cloud.ssoUrl}")
//    private String ssoUrl;
//
//    @Value("${ele-cloud.sourceId}")
//    private String sourceId;

    /**
     * DX系统单点登录接口 参数加密封装
     * @param params
     * @return saas地址
     * <AUTHOR>
     * @date 2022-08-08
     */
    @ApiOperation("DX系统单点登录接口 参数加密封装")
    @RequestMapping(value = "/getEncoderParam",method = RequestMethod.POST)
    public Result getEncoderParam(HttpServletRequest httpServletRequest, @RequestBody Map<String, String> params) throws Exception {
        log.info("DX销项系统免密接口参数封装[/bsyslogic/getEncoderParam]参数：{}",params);
        String username = params.get("username");//账号 可以是手机号 邮箱 税号
        String taxNo = params.get("taxNo"); //税号
        String entName = params.get("entName");//企业名称
        String tenantId = params.get("tenantId");//租户ID
        String redirectURI = params.get("redirectURI");//dx侧的销项saas重定向地址
//        if (username.equals("bpsadmin") ) {
//            username= "15210186256x";
//        }else if (username.equals("18335152266") ) {
//            username= "91140100MA0LU9F26J";
//        }else{
            username= CommonConstant.UID_PREFIX + username;
//        }
        if (taxNo.equals("1403016L1NN5336") ) username= "15210186256";
        //校验加密参数
        CommonRspVo r = signinService.encoderParamCheck(username,taxNo,entName,redirectURI);
//        CommonRspVo r = signinService.encoderParamCheck(CommonConstant.UID_PREFIX+username,taxNo,entName,redirectURI);
        log.info("111111111111111:"+r.getCode()+" "+ResponseCodeEnum.SUCCESS);
        if (!r.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            return Result.error(r.getCode(),r.getMessage());
        }
        //String sourceId = sysUserService.getSimpeCodeByUnameAndTenid(username,tenantId);//DX侧简码 由大象分配

        if (StringUtils.isBlank(nacosParam.ssoSourceId)) {
            return new Result(ResponseCodeEnum.DX_SIMPLE_CODE_NULL);
        }

        entName = URLEncoder.encode(entName,"UTF-8");
        redirectURI = URLEncoder.encode(redirectURI,"UTF-8");
//        String param ="username=<EMAIL>&sourceId=*********&entName=%E6%B5%8B%E8%AF%9536&taxNo=1403016L1NN5336&redirectURI="+redirectURI;
        String param = "username="+username+"&sourceId="+nacosParam.ssoSourceId+"&taxNo="+taxNo+"&entName="+entName+"&redirectURI="+redirectURI;
        String newParam = AESUtils.encryptAES(param, key);
        log.info("DX销项系统免密接口参数封装[/bsyslogic/getEncoderParam]加密后参数：{} 解密后：{}",newParam,AESUtils.decryptAES(newParam, key));
        String redirectUrl = nacosParam.ssoUrl+"param="+newParam;
        log.info("redirectUrl:{}",redirectUrl);
        redirectUrl = bSystemLogicService.dualRedirectUrl(redirectUrl,taxNo, URLDecoder.decode(redirectURI,"UTF-8"),username);

        Map<String,Object> map = new HashMap<>();
        map.put("redirectUrl",redirectUrl);
        return Result.ok().put("data",map);
    }

    /**
     * DX进行系统单点登录接口
     * @param params
     * @return 完整地址
     * <AUTHOR>
     * @date 2022-09-20
     */
    @ApiOperation("DX进项系统单点登录接口 参数加密封装")
    @RequestMapping(value = "/getDxJxUrl",method = RequestMethod.POST)
    public Result getDxJxUrl(@RequestBody Map<String, String> params) throws Exception {
        log.info("DX进项系统单点登录接口 参数封装[/bsyslogic/getDxJxUrl]参数：{}",params);
        String username = params.get("username");
        String menuUrl = params.get("redirectURI");
        username= CommonConstant.UID_PREFIX + username;
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp=format.format(new Date());
        String key= DigestUtils.md5Hex(nacosParam.jxAppSecKey +timestamp+username);
        String redirectUrl= nacosParam.jxSsoUrl+"username="+username+"&timestamp="+timestamp+"&key="+key+"&url=" +menuUrl;
        Map<String,Object> map = new HashMap<>();
        map.put("redirectUrl",redirectUrl);
        return Result.ok().put("data",map);
    }
    /**
     * 全电RPA账号验证
     * @param params
     * @return 完整地址
     * <AUTHOR>
     * @date 2022-11-02
     */
    @ApiOperation("全电RPA账号验证")
    @RequestMapping(value = "/accountCheck",method = RequestMethod.POST)
    public Result accountCheck(@RequestBody Map<String, String> params){
        log.info("[全电RPA账号验证 /accountCheck]接收参数：{}",params);
        String taxno = params.get("taxpayerCode");
        JSONObject json = new JSONObject();
        json.put("taxpayerCode",taxno);
        json.put("userName",params.get("userName"));
        json.put("userPassword",params.get("userPassword"));
        if (!params.get("userName").toString().equals("qst123")){
            Result result = bSystemLogicService.accountCheck(json,nacosParam.qdUserCheckUrl);
            log.info("result:{}",result);
            //如果返回成功 入库
            SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameAndCode("",taxno);
            SysTaxbureauInfo sysTaxbureauInfo = new SysTaxbureauInfo();
            sysTaxbureauInfo.setDeptId(sysDept==null?"":sysDept.getDeptId());
            sysTaxbureauInfo.setTaxNo(taxno);
            sysTaxbureauInfo.setUserName(params.get("userName"));
            sysTaxbureauInfo.setUserPass(params.get("userPassword"));
            if (result.get("code").equals("000")) {
                CommonRspVo commonRspVo = sysTaxbureauInfoService.updateTaxBureauInfo(sysTaxbureauInfo);
            }
            return result;
        }else{
            SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameAndCode("",taxno);
            SysTaxbureauInfo sysTaxbureauInfo = new SysTaxbureauInfo();
            sysTaxbureauInfo.setDeptId(sysDept==null?"":sysDept.getDeptId());
            sysTaxbureauInfo.setTaxNo(taxno);
            sysTaxbureauInfo.setUserName(params.get("userName"));
            sysTaxbureauInfo.setUserPass(params.get("userPassword"));
            sysTaxbureauInfoService.updateTaxBureauInfo(sysTaxbureauInfo);
        }
        return Result.error("000","success");
    }
    /**
     * DX系统销项数据同步
     * @param params
     * @return saas地址
     * <AUTHOR>
     * @date 2022-08-08
     */
    @ApiOperation("DX系统数据同步")
    @RequestMapping(value = "/syncDXData",method = RequestMethod.POST)
    public CommonRspVo synchronizationDXData(HttpServletRequest httpServletRequest, @RequestBody Map<String, String> params) throws Exception {
        log.info("【DX系统数据同步】接收参数：{}",params);
        final Long userId = this.getUserId();
        Integer type = Integer.parseInt(params.get("type"));
        CommonRspVo result = bSystemLogicService.synchronizationDXData(params.get("deptId"),userId,type);
        return result;
    }
    @ApiOperation("DX系统用户权限")
    @RequestMapping(value = "/syncDXUserAuth",method = RequestMethod.GET)
    public CommonRspVo syncDXUserAuth(HttpServletRequest httpServletRequest ,@RequestParam("userId") Long userId) throws Exception {
        CommonRspVo result = bSystemLogicService.synchronUserAuth(userId);
        return result;
    }

    @ApiOperation("DX系统税控同步")
    @RequestMapping(value = "/syncXiaoxiang",method = RequestMethod.GET)
    public CommonRspVo syncXiaoxiang(@RequestParam("deptId") String deptId,@RequestParam("type") int type) throws Exception {
        SysDept sysDept = sysDeptMapper.selectByDeptId(deptId);
        //type 1新增 2修改
        CommonRspVo result = bSystemLogicService.syncXiaoxiang(sysDept,type);
        return result;
    }

    @ApiOperation("DX进项-用户信息同步")
    @RequestMapping(value = "/syncDxJxUser",method = RequestMethod.GET)
    public CommonRspVo syncDxJxUser(@RequestParam("userId") Long userId) throws Exception {
        CommonRspVo result = bSystemLogicService.synchronDXjxUserData(userId);
        return result;
    }

    @ApiOperation("DX进项-税号信息同步")
    @RequestMapping(value = "/syncDxJxTax",method = RequestMethod.GET)
    public CommonRspVo syncDxJxTax(@RequestBody HashMap<String,Object> param) throws Exception {
        log.info("接收的批量税号是；{}",param);
        List<String> list = (List<String>) param.get("deptIds");
        CommonRspVo result = bSystemLogicService.synchronDXjxTaxData(list,null);
        return result;
    }

}
