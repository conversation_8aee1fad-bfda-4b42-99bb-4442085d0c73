/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.base.controller;

import com.dxhy.core.common.response.R;
import com.dxhy.core.constants.SecurityConstants;
import com.dxhy.core.handler.TokenHandleUtil;
import com.dxhy.core.utils.AuthUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.URLEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.provider.ClientDetailsService;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.net.URLDecoder;
import java.util.Collection;

/**
 * <AUTHOR>
 * @date 2019年03月11日
 */
@Slf4j
@RestController
@RequestMapping("/uauthentication")
public class AuthenticationController {
    @Autowired
    @Qualifier("consumerTokenServices")
    private ConsumerTokenServices consumerTokenServices;

    @Autowired
    private ClientDetailsService clientDetailsService;

    @Autowired
    @Qualifier("defaultAuthorizationServerTokenServices")
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    public static final String BASIC_ = "Basic ";

    protected AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new WebAuthenticationDetailsSource();

    @Autowired
    private UserDetailsService userDetailsService;

    @Autowired
    private TokenStore tokenStore;

    URLEncoder urlEncoder = new URLEncoder();

    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

    @Value("${security.encode.key}")
    private String key;//用于解密的Key

    @Value("${login.isOnce:1}")
    private String loginIsOnce;

    @Value("${demo.users:}")
    private String demoUsers;

    /**
     * 用户信息校验
     *
     * @param authentication 信息
     * @return 用户信息
     */
    @RequestMapping("/user")
    public Object user(Authentication authentication) {
        return authentication.getPrincipal();
    }

    /**
     * 清除Redis中 accesstoken refreshtoken
     *
     * @param accesstoken accesstoken
     * @return true/false
     */
    @PostMapping("/removeToken")
    @CacheEvict(value = SecurityConstants.TOKEN_USER_DETAIL, key = "#accesstoken")
    public R<Boolean> removeToken(String accesstoken) {
        return new R<>(consumerTokenServices.revokeToken(accesstoken));
    }


    //根据登录名称 client_id  进行删除用户token
    @PostMapping("/delToken")
    public Boolean delTokenByUserName(@RequestParam("userName") String userName,@RequestParam("clientId") String clientId){
        log.info("清除用户token,用户名:{}"+userName);
        try{
            userName = URLDecoder.decode(userName, "UTF-8");
            Collection<OAuth2AccessToken> tokensByClientIdAndUserName = tokenStore.findTokensByClientIdAndUserName(clientId, userName);
            tokensByClientIdAndUserName.forEach(oAuth2AccessToken -> {
                consumerTokenServices.revokeToken(oAuth2AccessToken.getValue());
            });
        }catch(Exception e){
            log.error("根据用户名清除用户登录记录出现异常,错误日志:{}"+e);
            return false;
        }
        return true;
    }

    /**
     * 强制踢出登录用户
     * @param clientId
     * @param userName
     * @return
     */
    private R<Boolean> forceOnceUser(String clientId, String userName){
        log.info("强制用户退出，clientId：{}，userName:{}", clientId, userName);
        boolean contains = demoUsers.contains(userName);
        log.info("强制用户退出,用户名:{},是否过滤:{}", userName,contains);
        if(contains){
            return new R<>(true);
        }

        Collection<OAuth2AccessToken> tokens= tokenStore.findTokensByClientIdAndUserName(clientId,userName);
        if(tokens==null || tokens.size() == 0){
            return new R<>(true);
        }
        log.info("强制用户退出，找到tokens信息条数：{}", tokens.size());
        for(OAuth2AccessToken token : tokens) {
            boolean bool = consumerTokenServices.revokeToken(token.getValue());
            log.info("清除结果：{}", bool);
        }
        //OAuth2AccessToken token=   (OAuth2AccessToken)tokens.toArray()[tokens.size()-1];
        return new R<>(true);

    }


    /**
     *  从请求头获取clientId,secret信息
     * @param request
     * @return
     * @throws IOException
     */
    private String[] getClientIdSecretFromHeader(HttpServletRequest request) throws IOException {
        String header = request.getHeader("Authorization");
        if (header == null || !header.startsWith(BASIC_)) {
            throw new UnapprovedClientAuthenticationException("请求头中client信息为空");
        }
        String[] tokens = AuthUtils.extractAndDecodeHeader(header);
        assert tokens.length == 2;
        return tokens;
    }

    /**
     *  延长token内置失效时间
     * @param request
     * @return
     * @throws IOException
     */
    @ApiOperation("延长token内置失效时间")
    @RequestMapping(value = "/keepValidToken",method = RequestMethod.GET)
    private R<Boolean> keepValidToken(HttpServletRequest request,@RequestParam("extendTime") Long extendTime) throws IOException {
        String token = request.getHeader("Authorization");
        log.info("接收参数："+token);
        String[] accessTokens = token.split("\\.");
        if(accessTokens.length <2){
            log.error("token异常：{}", token);
            return new R(Boolean.FALSE);
        }
        if (token == null) {
            throw new UnapprovedClientAuthenticationException("请求头中client信息为空");
        }
        TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
        Boolean flag = tokenHandleUtil.extendTokenValidity(token,extendTime);
        return new R(flag);
    }

}