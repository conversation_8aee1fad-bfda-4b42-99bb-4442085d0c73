package com.dxhy.base.controller;


import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.AssociatedMarketDto;
import com.dxhy.core.service.HwBuyProductInfoService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;


/**
 * 商品新购  过期  资源释放 资源状态变更 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 10:58:16
 */
@RestController
@RequestMapping("itax/hwbuyproductinfo")
@Slf4j
public class HwProductInfoController {
    @Autowired
    private HwBuyProductInfoService hwBuyProductInfoService;

    @RequestMapping(value = "/market",method = RequestMethod.GET)
    @ApiOperation("华为联运商品")
    public String market(AssociatedMarketDto associatedMarketDto, HttpServletResponse response){
        try {
            log.info("联运商品请求参数{}", JSONObject.toJSONString(associatedMarketDto));
            if("newInstance".equals(associatedMarketDto.getActivity())) {//新建商品

                return hwBuyProductInfoService.newInstance(associatedMarketDto,response);

            } else if("refreshInstance".equals(associatedMarketDto.getActivity())) {//商品续费

                return hwBuyProductInfoService.refreshInstance(associatedMarketDto,response);

            }
            else if("expireInstance".equals(associatedMarketDto.getActivity())) {//商品过期

                return hwBuyProductInfoService.expireInstance(associatedMarketDto,response);

            }
            else if("releaseInstance".equals(associatedMarketDto.getActivity())) {//资源释放

                return hwBuyProductInfoService.releaseInstance(associatedMarketDto,response);

            }
            else if("upgrade".equals(associatedMarketDto.getActivity())) {//商品升级

                return hwBuyProductInfoService.upgrade(associatedMarketDto,response);

            }
            else if("instanceStatus".equals(associatedMarketDto.getActivity())) {//资源状态变更

                return hwBuyProductInfoService.instanceStatus(associatedMarketDto,response);

            }
            else if("queryInstance".equals(associatedMarketDto.getActivity())) {//查询实例信息

                return hwBuyProductInfoService.queryInstance(associatedMarketDto,response);

            }
            return JSONObject.toJSONString(Result.ok());
        }catch (Exception e){
            log.error("联运商品操作失败：{}",e.getMessage());
            e.printStackTrace();
            return JSONObject.toJSONString(Result.error("操作失败！,类型："+associatedMarketDto.getActivity()+",实例ID："+associatedMarketDto.getInstanceId()));
        }

    }

}
