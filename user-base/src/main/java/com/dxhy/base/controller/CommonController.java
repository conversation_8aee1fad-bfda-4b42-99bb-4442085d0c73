package com.dxhy.base.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.SysTenantProductMapper;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.vo.SysVersionDataVo;
import com.dxhy.core.service.SystemVersionService;
import com.dxhy.core.utils.CommonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 版本日志
 * <AUTHOR>
 * @date 2022-09-08
 */
@Slf4j
@RestController
@RequestMapping("/common")
public class CommonController extends BaseController {
    @Autowired
    private SystemVersionService systemVersionService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysTenantProductMapper sysTenantProductMapper;

    @Autowired
    private NacosParam nacosParam;

    @ApiOperation("版本更新")
    @RequestMapping(value = "/queryVersion",method = RequestMethod.GET)
    public Result sendEmail() throws Exception {
        //根据token查询userId
//        final Long userId = this.getUserId();
//        log.info("查询用户信息接收参数为:userId = {}" + userId);
        //是否需要设计更新日志权限
        List<SysVersionDataVo> list =  systemVersionService.selectSysVersionList();
        return Result.ok().put("showData",list);
    }


    /**
     * 全电发票系统试用期倒计时提醒
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-11-02
     */
    @ApiOperation("全电发票系统试用期倒计时提醒")
    @RequestMapping(value = "/expireRemind",method = RequestMethod.GET)
    public Result expireRemind() throws Exception {
        //根据token查询userId
        final Long userId = this.getUserId();
        log.info("[到期提醒]接收参数为:userId = {}" + userId);
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser != null ) {
            if(sysUser.getUserSource().equals("1")){//官网注册 PLG
                String tenantId = sysUser.getTenantId();
                List<SysTenantProduct> list = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("tenant_id",tenantId));
                for (SysTenantProduct tenantProduct : list) {
                    SimpleDateFormat sdf=new SimpleDateFormat("yyyy-MM-dd");
                    String nowTime  = sdf.format(new Date());
                    String endTime = sdf.format(tenantProduct.getAuthEtime());
                    //处理时间
                    int remainDay  = CommonUtils.getRemainDay(nowTime,endTime);
                    if (remainDay <0) {
                        return Result.error().put("remainDay",remainDay);
                    }
                    return Result.ok().put("remainDay",remainDay);
                }
            }
        }
        return Result.error();
    }

    /**
     * 获取注册地址
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-11-16
     */
    @ApiOperation("获取注册地址")
    @RequestMapping(value = "/getRegisterUrl",method = RequestMethod.GET)
    public String getRegisUrl() throws Exception {
        return nacosParam.registerUrl;
    }
}
