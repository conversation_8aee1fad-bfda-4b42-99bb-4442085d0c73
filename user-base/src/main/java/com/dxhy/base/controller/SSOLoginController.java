package com.dxhy.base.controller;

import com.alibaba.fastjson.JSON;
import com.dxhy.core.config.SocialPropertiesConfig;
import com.dxhy.core.service.SigninService;
import com.dxhy.core.service.UserService;
import com.dxhy.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.URLEncoder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/uauthentication")
public class SSOLoginController {


    private static SocialPropertiesConfig socialPropertiesConfig;

    @Autowired
    public void init(SocialPropertiesConfig socialPropertiesConfig) {
        SSOLoginController.socialPropertiesConfig = socialPropertiesConfig;
    }


    @Autowired
    private UserService userService;


    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    @Qualifier("defaultAuthorizationServerTokenServices")
    private AuthorizationServerTokenServices authorizationServerTokenServices;



    @Autowired
    @Qualifier("consumerTokenServices")
    private ConsumerTokenServices consumerTokenServices;

    URLEncoder urlEncoder = new URLEncoder();


    @Resource
    private RedisConnectionFactory redisConnectionFactory;

    @Resource
    private SigninService signinService;

    /**
     * 认证页面
     *
     * @return ModelAndView
     */
    @GetMapping("/require")
    public ModelAndView require(HttpServletRequest request, HttpServletResponse response) {

        Map<String, String> map = CommonUtils.getRequestParamsFromCache(request, response);
//        System.out.println("》》》》》》》》》》》》》》》》》》》》》："+map);
        log.info("【单点登录】require,请求参数:{}",map == null ? "" : JSON.toJSONString(map));
        String page = "login";
        String activityId = "";
        if(map == null) {
            // 内部调用，不跳转
            log.info("内部调用，不跳转");
        } else {
            if(StringUtils.isNotBlank(map.get("page"))) {
                page = map.get("page");
            }
            if(StringUtils.isNotBlank(map.get("activityId"))) {
                activityId = map.get("activityId");
            }
        }
        ModelAndView mv = new ModelAndView("udist/index");
//        ModelAndView mv = new ModelAndView("ftl/login");
        mv.addObject("page", page);
        mv.addObject("activityId", activityId);
//        System.out.println("*********************************88"+mv);
        return mv;
    }

    /**
     *   单点登录前端页面退出登录
     * @param redirect_uri
     * @return
     */
    @GetMapping("/h5/logout")
    public ModelAndView h5Logout(@RequestParam(value = "redirect_uri") String redirect_uri,
                                 @RequestParam(value = "access_token", required = false) String access_token) {

        log.info("【单点登出】h5Logout,redirect_uri:{}", redirect_uri);
        log.info("【单点登出】h5Logout,access_token:{}", access_token);
        if(StringUtils.isNotBlank(access_token)) {
            boolean bool = consumerTokenServices.revokeToken(access_token);
            log.info("单点登出清除token：{}", bool);
        }
        ModelAndView mv = new ModelAndView("ftl/logout");
        mv.addObject("redirect_uri", redirect_uri);
        log.info("【单点登出】h5Logout,redirect_uri:{}", redirect_uri);
        return mv;
    }




}
