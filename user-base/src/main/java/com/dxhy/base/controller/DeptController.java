/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.base.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.excel.SysDeptImportListener;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.excel.SysDeptImportDto;
import com.dxhy.core.pojo.hw.TenantInfoEntity;
import com.dxhy.core.pojo.vo.SysDeptResqVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.ExcelUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;

/**
 * <p>
 * 部门管理 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-29
 */
@RestController
@RequestMapping("/dept")
@Slf4j
@Api(tags = "组织管理")
public class DeptController extends BaseController {
    @Resource
    private SysDeptService sysDeptService;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;


    @Resource
    private SysMenuMapper sysMenuMapper;

    private ThreadFactory threadFactory = Executors.defaultThreadFactory();
    @Autowired
    private ISysTaxControlService sysTaxControlService;

    @Autowired
    private NacosParam nacosParam;

    @Autowired
    private ICustomerProductService iCustomerProductService;
    @Autowired
    private IDictionaryService dictionaryService;
    @Resource
    private SysTaxbureauInfoMapper sysTaxbureauInfoMapper;
    @Autowired
    private ProductStatusMapper productStatusMapper;
    @Autowired
    private TenantInfoService tenantInfoService;

    /**
     * 查询自己及以下组织(查询左侧机构树)
     * @param deptId	所在 分支机构/企业/集团id
     * @param isContainBm	是否查询部门
     * @return
     */
    @ApiOperation("查询自己及以下组织")
    @PostMapping("/listMyselfAll")
    public Result listMyselfAll(String deptId, boolean isContainBm){
        log.info("/dept/listMyselfAll开始执行,请求参数：{}", deptId,isContainBm);
        return sysDeptService.listMyselfAll(deptId,isContainBm,getUserId());
    }

    /**
     * 查询当前级以下组织
     * @param deptId	所在 分支机构/企业/集团id
     * @param isContainBm	是否查询部门
     * @return
     */
    @ApiOperation("查询当前级以下组织")
    @PostMapping("/listMyselfAllByTier")
    public Result listMyselfAllByTier( String deptId,  boolean isContainBm){
        log.info("/dept/listMyselfAllByTier开始执行,请求参数：{}", deptId,isContainBm);
        return sysDeptService.listMyselfAllByTier(deptId,isContainBm);
    }

    /**
     * 查询自己及以下组织及每个组织的总人数
     * @param deptId	所在 分支机构/企业/集团id
     * @param isContainBm	是否查询部门
     * @return
     */
    @ApiOperation("查询自己及以下组织及每个组织的总人数")
    @PostMapping("/listMyselfAllAndUserCount")
    public Result listMyselfAllAndUserCount(String deptId, boolean isContainBm){
        log.info("/dept/listMyselfAllAndUserCount开始执行,请求参数：{}", deptId,isContainBm);
        return sysDeptService.listMyselfAllAndUserCount(deptId,isContainBm);
    }


    /**
     * 查询自己下一级组织（查询机构列表）
     * @param deptId	所在 分支机构/企业/集团id
     * @param isContainBm	是否查询部门
     * @return
     */
    @ApiOperation("查询自己下一级组织")
    @GetMapping("/listMyselfOneLevel")
    public Result listMyselfOneLevel(Integer pageSize, Integer currPage, String deptId, boolean isContainBm,String entName,String nsrsbh) {
        log.info("/dept/listMyselfOneLevel开始执行,请求参数：{}", pageSize,  currPage,  deptId,  isContainBm,entName,nsrsbh);
        return sysDeptService.listMyselfOneLevel( pageSize,  currPage,  deptId,  isContainBm,entName,nsrsbh);
    }

    /**
     * 通过ID查询
     *
     * @param id ID
     * @return SysDept
     */
    @GetMapping("/{id}")
    @ApiOperation("通过ID查询组织信息")
    public Result get(@PathVariable("id") String  id) {
        log.info("根据deptId查询组织信息开始执行,请求参数：{}", id);
        return sysDeptService.selectById(id);
    }

    /**
     * 添加组织
     *
     * @param sysDept 实体
     * @return success/false
     */
    @ApiOperation("添加组织")
    @PostMapping("/addDept")
    public Result addDept(HttpServletRequest httpServletRequest, @RequestBody SysDeptResqVo sysDept) {
        final Long userId = this.getUserId();
        SysDept apiDeptEntity = sysDept.getApiDeptEntity();
        log.info("/dept/addDept开始执行,请求参数：{},数据同步开关：{}", JSON.toJSONString(sysDept),nacosParam.dxSwitch);
        apiDeptEntity.setCreateTime(new Date());
        apiDeptEntity.setUpdateTime(new Date());
        if(StringUtils.isBlank(apiDeptEntity.getDataSource())){
            apiDeptEntity.setDataSource("4");
        }
        // 查询租户有多少个组织信息了，校验设置的最大数量
        int count = sysDeptService.selectCount(new EntityWrapper<SysDept>().eq("tenant_id", apiDeptEntity.getTenantId()).eq("del_flag",0));
        log.info("/dept/addDept,租户下机构数量{}",count);
        if (count > nacosParam.maxOrgCount) {
            return new Result(ResponseCodeEnum.MAXORG_LIMT);
        }
        Result result = null;
        try {
            result = sysDeptService.addDept(sysDept,userId+"");
        } catch (Exception e) {
            return Result.error("添加组织异常："+e.getMessage());
        }
        return result;
    }



    /**
     * 编辑
     *
     * @param sysDept 实体
     * @return success/false
     */
    @ApiOperation("修改组织")
    @PostMapping("/editDept")
    public Result edit(@RequestBody SysDeptResqVo sysDept) {
        log.info("/dept/editDept开始执行,请求参数：{},数据同步开关：{}", JSON.toJSONString(sysDept),nacosParam.dxSwitch);
        final Long userId = this.getUserId();
        SysDept apiDeptEntity = sysDept.getApiDeptEntity();
        apiDeptEntity.setUpdateTime(new Date());
        if(apiDeptEntity.getCreateUser() == null){
            apiDeptEntity.setCreateUser(userId);
        }
        //税控类型字典查询
        log.info(sysDept.getSksbbm()+" : "+sysDept.getSksbmc());
        if (StringUtils.isNotBlank(sysDept.getSksbbm())) {
            Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("name",sysDept.getSksbbm()).eq("code",Integer.parseInt(sysDept.getSksbmc())));
            sysDept.setSksbbm(dictionary.getDesc());
            sysDept.setSksbmc(sysDept.getSksbmc().equals("1")?"本地-"+dictionary.getName():"托管-"+dictionary.getName());
        }
        //后期要支持编辑税控设备类型
        Result result = null;
        try {
            result = sysDeptService.updateDept(sysDept,true);
        } catch (Exception e) {
            return Result.error("编辑组织异常："+e.getMessage());
        }
        log.info("result的值：：：："+result);
        return result;
    }

    /**
     * 删除
     *
     * @param deptId
     * @return success/false
     */
    @ApiOperation("删除组织")
    @RequestMapping("/deleteDept/{deptId}")
    public Result delete(@PathVariable("deptId") String deptId) {
        return sysDeptService.deleteDeptByDeptId(deptId);
    }

    /**
     * 删除
     *
     * @param params
     * @return success/false
     */
    @ApiOperation("批量删除组织")
    @PostMapping("/deleteDeptBatch")
    public Result deleteDeptBatch(@RequestBody Map<String, String> params) {
        String deptIds = params.get("deptIds");
        return sysDeptService.deleteDeptBatch(deptIds);
    }

    /**
     * 通过ID查询父级企业ID
     * @param deptId
     * @return
     */
    @ApiOperation("通过ID查询顶级企业ID）")
    @RequestMapping(value = "/selectPdeptId",method = RequestMethod.GET)
    public Result selectDeptById(@RequestParam("deptId") String deptId){
        log.info("通过ID查询父级企业ID,请求参数：{}", deptId);
        if(StringUtils.isBlank(deptId)){
            return Result.error("请求参数为空");
        }
        if(deptId.equals("0")){
            return Result.ok().put("data","0");
        }
        SysDept sysDept = sysDeptMapper.selectTopLevelByDeptId(deptId);
        if (sysDept == null) {
            SysDept tenantdept = sysDeptMapper.selectTopOrgByTenantId(deptId);
            if (tenantdept == null) {
                return new Result(ResponseCodeEnum.DEPT_NOT_EXITES);
            }
            deptId=tenantdept.getDeptId();
            return Result.ok().put("data",deptId);
        }
        return Result.ok().put("data",StringUtils.isNotBlank(sysDept.getDeptId())?sysDept.getDeptId():deptId);
    }

    /**
     * 根据组织名称和税号查询组织信息
     */
    @ApiOperation("根据组织名称和税号查询组织信息")
    @RequestMapping(value = "/queryDeptByNameAndCode", method = { RequestMethod.POST})
    public Result queryDeptByNameAndCode(@RequestBody JSONObject parm){
        String name = parm.getString("name");
        String taxpayerCode = parm.getString("taxpayerCode");
        if (StringUtils.isEmpty(name)&&StringUtils.isEmpty(taxpayerCode)) {
            return Result.error("查询条件企业名称和税号不能都为空");
        }
        log.info("/dept/queryDeptByNameAndCode开始执行,请求参数：{}", name,taxpayerCode);
        return sysDeptService.queryDeptByTaxpayerNameAndCode(name, taxpayerCode);
    }
    /**
     * 查询税号所属集团的所有企业税号
     */
    @ApiOperation("查询税号所属集团的所有企业税号")
    @RequestMapping(value = "/queryAllTaxListByTax")
    public Result queryAllTaxListByTax(@RequestParam(value = "taxNo") String taxNo){
        log.info("/dept/queryAllTaxListByTax,请求参数：{}", taxNo);
        Long userId = this.getUserId();
        UserVO userVO = sysUserService.selectUserVoById(userId);
        log.info("租户ID为："+userVO.getTenantId());
        return sysDeptService.queryAllTaxListByTax(taxNo,userVO.getTenantId());
    }

    /**
     * 获取租户的secretKey
     * @param params
     * @return
     * <AUTHOR>
     * @date 2023-09-09
     */
    @ApiOperation("获取租户的secretKey")
    @RequestMapping(value = "/getTenantAuthKey",method = RequestMethod.POST)
    public Result accountCheck(@RequestBody Map<String, String> params){
        log.info("[获取租户的secretKey /getTenantAuthKey]接收参数：{}",params);
        String secretId = params.get("secretId");
        TenantInfoEntity entity = tenantInfoService.getSecretKey(secretId);
        Map map = new HashMap();
        if(entity!=null){
            map.put("secretKey",entity.getAppSecret());
        }
        return Result.ok().put("data",map);
    }

    /**
     * 批量导入机构
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @ApiOperation("批量导入机构")
    @PostMapping("/importDept")
    public Result importDept(@RequestParam("file") MultipartFile file) {
        log.info("/dept/importDept开始执行");
        if (file.isEmpty()) {
            return Result.error("上传文件不能为空");
        }
        
        try {
            // 获取当前用户信息
            final Long userId = this.getUserId();
            UserVO userVO = sysUserService.selectUserVoById(userId);
            String tenantId = userVO.getTenantId();
            
            // 查询租户有多少个组织信息了，校验设置的最大数量
            int count = sysDeptService.selectCount(new EntityWrapper<SysDept>().eq("tenant_id", tenantId).eq("del_flag", 0));
            log.info("/dept/importDept,租户下机构数量{}", count);
            
            // 判断是否超过最大组织数量限制
            if (count >= nacosParam.maxOrgCount) {
                return new Result(ResponseCodeEnum.MAXORG_LIMT);
            }
            
            // 创建Excel监听器
            SysDeptImportListener listener = new SysDeptImportListener(sysDeptService, userId.toString(), tenantId, nacosParam, count);
            
            // 解析Excel文件
            ExcelUtils.readExcel(file.getInputStream(), SysDeptImportDto.class, listener);
            
            // 获取导入结果
            List<SysDeptImportListener.ImportResult> importResults = listener.getImportResults();
            int successCount = listener.getSuccessCount();
            
            log.info("/dept/importDept执行完成，成功导入{}条记录", successCount);
            return Result.ok().put("data", importResults).put("successCount", successCount);
        } catch (IOException e) {
            log.error("导入机构异常", e);
            return Result.error("导入机构异常: " + e.getMessage());
        }
    }
    
    /**
     * 下载机构导入模板
     *
     * @param response HttpServletResponse
     */
    @ApiOperation("下载机构导入模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("/dept/downloadTemplate开始执行");
        ExcelUtils.generateTemplate(response, "机构导入模板", "机构信息", SysDeptImportDto.class);
    }
}
