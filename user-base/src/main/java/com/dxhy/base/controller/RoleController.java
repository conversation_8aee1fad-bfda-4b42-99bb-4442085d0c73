/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.base.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.common.util.Query;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.pojo.DTO.AdminRoleOperateDto;
import com.dxhy.core.pojo.DTO.RoleDTO;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysRoleMenu;
import com.dxhy.core.service.SysRoleMenuService;
import com.dxhy.core.service.SysRoleService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/11/5
 */
@RestController
@RequestMapping("/role")
@Slf4j
public class RoleController extends BaseController {
    @Autowired
    private SysRoleService sysRoleService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;

    /**
     * 通过ID查询角色信息
     *
     * @param id ID
     * @return 角色信息
     */
    @GetMapping("/{id}")
    public SysRole role(@PathVariable Integer id) {
        return sysRoleService.selectById(id);
    }

    /**
     * 添加角色
     *
     * @param roleDto 角色信息
     * @return success、false
     */
    @PostMapping
    public R<Boolean> role(@RequestBody RoleDTO roleDto) {
        return new R<>(sysRoleService.insertRole(roleDto));
    }

    /**
     * 修改角色
     *
     * @param roleDto 角色信息
     * @return success/false
     */
    @PutMapping
    public R<Boolean> roleUpdate(@RequestBody RoleDTO roleDto) {
        return new R<>(sysRoleService.updateRoleById(roleDto));
    }

    @DeleteMapping("/{id}")
    public R<Boolean> roleDel(@PathVariable Integer id) {
        SysRole sysRole = sysRoleService.selectById(id);
        sysRole.setDelFlag(CommonConstant.STATUS_DEL);
        return new R<>(sysRoleService.updateById(sysRole));
    }

    /**
     * 获取角色列表
     *
     * @param deptId 部门ID
     * @return 角色列表
     */
    @GetMapping("/roleList/{deptId}")
    public List<SysRole> roleList(@PathVariable Integer deptId) {
        return sysRoleService.selectListByDeptId(deptId);

    }

    /**
     * 分页查询角色信息
     *
     * @param params 分页对象
     * @return 分页对象
     */
    @RequestMapping("/rolePage")
    public Page rolePage(@RequestParam Map<String, Object> params) {
        params.put(CommonConstant.DEL_FLAG, CommonConstant.STATUS_NORMAL);
        return sysRoleService.selectwithDeptPage(new Query<>(params), new EntityWrapper<>());
    }

    /**
     * 更新角色菜单
     *
     * @param roleId  角色ID
     * @param menuIds 菜单结合
     * @return success、false
     */
    @PutMapping("/roleMenuUpd")
    public R<Boolean> roleMenuUpd(Long roleId, @RequestParam(value = "menuIds", required = false) String menuIds) {
        SysRole sysRole = sysRoleService.selectById(roleId);
        return new R<>(sysRoleMenuService.insertRoleMenus(sysRole.getRoleCode(), roleId, menuIds));
    }


    /**
     * 辅助运营通过角色id查询角色名称
     * @param roleId
     * @return
     */
    @RequestMapping(value = "/aosp/getRoleInfoById", method = {RequestMethod.GET, RequestMethod.POST})
    public R getRoleInfoById (@RequestParam(required = true) String roleId){
        SysRole sysRole = sysRoleService.selectById(roleId);
        if(sysRole != null){
            HashMap hashMap = new HashMap();
            hashMap.put("rolId",sysRole.getRoleId());
            hashMap.put("roleName",sysRole.getRoleName());
            return new R(hashMap);
        }else{
            return new R(1,"角色id："+roleId+"不存在");
        }
    }



    /**
     * 查询部门对应角色列表
     */
    @RequestMapping("/listRoles")
    @ApiOperation("查询部门下角色列表")
    public Result listRolesByDeptId(@RequestBody Map<String, Object> params){
        log.info("查询部门下角色列表接收参数为" + params);
        Long userId = this.getUserId();
        params.put("loginUserId",userId);
        try {
            return sysRoleService.listRolesByDeptId(params);
        } catch (Exception e) {
            log.error("查询部门下角色列表异常",e);
            return Result.error("查询部门下角色列表异常");
        }
    }


    /**
     * 查询部门下对应角色列表
     */
    @RequestMapping("/listRoleList")
    @ApiOperation("查询部门下角色列表")
    public Result listRoleListByDeptId(@RequestBody Map<String, Object> params){
        log.info("查询部门下角色列表接收参数为" + params);
        try {
            return sysRoleService.listRoleListByDeptId(params);
        } catch (Exception e) {
            log.error("查询部门下角色列表异常",e);
            return Result.error("查询部门下角色列表异常");
        }
    }

    /**
     * dxhy 20220817
     * 查询部门下对应角色列表
     * deptid 可以是渠道ID
     */
    @RequestMapping("/roleList")
    @ApiOperation("查询角色列表")
    public Result roleListByDeptId(@RequestParam("deptId") String deptId){
        log.info("查询部角色列表接收参数为" + deptId);
        try {
            return sysRoleService.roleListByDeptId(deptId);
        } catch (Exception e) {
            log.error("查询部门下角色列表异常",e);
            return Result.error("查询部门下角色列表异常");
        }
    }

    /**
     * 查询角色信息
     */
    @RequestMapping("/roleInfo")
    public Result roleInfo(@RequestParam Long roleId){
        log.info("查询角色信息接收参数为" + roleId);
        try {
            return sysRoleService.roleInfo(roleId,this.getUserId());
        } catch (Exception e) {
            log.error("查询角色信息异常",e);
            return Result.error("查询角色信息异常");
        }
    }

    /**
     * 新增时查询有权限菜单信息
     */
    @RequestMapping("/queryMenus")
    public Result queryMenus(){
        try {
            return sysRoleService.queryMenus(this.getUserId());
        } catch (Exception e) {
            log.error("查询角色信息异常",e);
            return Result.error("查询角色信息异常");
        }
    }



    /**
     * 查询角色信息(大B用)
     */
    @RequestMapping("/roleInfoById")
    @ApiOperation("查询角色信息(大B用)")
    public Result roleInfoById(@RequestParam Long roleId){
        log.info("查询角色信息接收参数为" + roleId);
        try {
            return sysRoleService.roleInfoById(roleId);
        } catch (Exception e) {
            log.error("查询角色信息异常",e);
            return Result.error("查询角色信息异常");
        }
    }

    /**
     * 更新角色
     */
    @RequestMapping("/updateRole")
    public Result updateRole(@RequestBody AdminRoleOperateDto adminRoleOperateDto){
        log.info("更新角色接收参数为" + adminRoleOperateDto);
        try {
            return sysRoleService.updateRole(adminRoleOperateDto);
        } catch (Exception e) {
            log.error("更新角色异常",e);
            return Result.error("更新角色异常");
        }
    }

    /**
     * 添加角色
     */
    @RequestMapping("/addRole")
    public Result addRole(@RequestBody AdminRoleOperateDto adminRoleOperateDto){
        log.info("添加角色接收参数为" + adminRoleOperateDto);

        try {
            return sysRoleService.addRole(adminRoleOperateDto);
        } catch (Exception e) {
            log.error("添加角色异常",e);
            return Result.error("添加角色异常");
        }
    }

    /**
     * 删除角色
     */
    @RequestMapping("/deleteRole")
    public Result deleteRole(Long roleId){
        log.info("删除角色接收参数为" + roleId);
        try {
            return sysRoleService.deleteRole(roleId);
        } catch (Exception e) {
            log.error("删除角色异常",e);
            return Result.error("删除角色异常");
        }
    }

    /**
     * 批量删除角色
     */
    @PostMapping("/deleteRoleBatch")
    public Result deleteRoleBatch(@RequestBody Map<String, String> params){
        log.info("删除角色接收参数为" + params);
        String roleIds = params.get("roleIds");
        try {
            Result result = null;
            String[] split = roleIds.split(",");
            for (int i = 0; i < split.length; i++) {
                result = sysRoleService.deleteRole(Long.valueOf(split[i]));
            }
            return result;
        } catch (Exception e) {
            log.error("删除角色异常",e);
            return Result.error("删除角色异常");
        }
    }

    /**
     * 查询列表中角色个数人员列表
     */
    @RequestMapping("/queryUsersByRoleId")
    public Result queryUsersByRoleId(@RequestBody Map<String, Object> params){
        log.info("查询角色绑定人员列表" + params);
        try {
            return sysRoleService.queryUsersByRoleId(params);
        } catch (Exception e) {
            log.error("查询角色绑定人员列表异常",e);
            return Result.error("查询角色绑定的人员列表异常");
        }
    }

    /**
     * 辅助运营查询菜单是否被角色关联
     * @param menuId
     * @return
     */
    @GetMapping("/aosp/getMenuId")
    public R getMenuId(@RequestParam(required = true) String menuId) {
        List<SysRoleMenu> roleMenuList = sysRoleMenuService.selectList(new EntityWrapper<SysRoleMenu>().eq("menu_id", menuId));
        if(roleMenuList != null && roleMenuList.size() > 0){
            return new R<>(true);
        }
        return new R<>(false);
    }
}
