package com.dxhy.base.controller;

import com.dxhy.core.common.LocalParam;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.service.TestService;
import com.dxhy.core.utils.RedisUtil;
import com.dxhy.core.utils.UserUtils;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:15
 * @Version 1.0
 */
@Api(tags = "测试controller类")
@ResponseBody
@RestController
@Slf4j
public class BaseController {
    @Autowired
    private HttpServletRequest request;

    @Autowired
    private RedisUtil redis;

    @Value("${sit.str}")
    public String sitStr;


    @Autowired
    private NacosParam nacosParam;

    @Autowired
    private LocalParam localParam;

    @Autowired
    private TestService testService;
//
//    @PostMapping("test")
//    @ApiOperation(value = "测试controller接口")
//    @RefreshScope
//    public String TestController(@RequestBody IdDTO idDTO){
//        System.out.println("myname的值是：：：："+nacosParam.myname);
//        System.out.println(idDTO.toString());
//        System.out.println(localParam.testStr);
//        System.out.println(sitStr);
//        System.out.println(nacosParam.dataAge);
//        System.out.println("redis : " + redis.get("testStr"));
//        redis.set("testKey", "testValue", 5);
//        TestUtil.TestMethod(idDTO.getId().toString());
//        System.out.println(testService.getById(idDTO.getId()).toString());
//        System.out.println(testService.selectById(idDTO.getId()).toString());
//        System.out.println(" ---------------- PAGE ----------------");
//        System.out.println(JSONObject.toJSONString(testService.testPage()));
//        log.info("test log info");
//        log.debug("test log debug");
//        log.error("test log error");
//        System.out.println(1/0);
//        return "OK " + nacosParam.myname;
//    }

    public String getTenantId() {
        return UserUtils.getTenantId(request);
    }

    public Long getUserId() {
        return UserUtils.getUserId(request);
    }
    public String getUser() {
        return UserUtils.getUser();
    }
    public Long getExp() {
        return UserUtils.getExp(request);
    }

//    @RequestMapping("/test/getSgin")
//    @RefreshScope
//    public String getSgin(@RequestBody HashMap<String,Object> param){
//        String content = param.get("content").toString();
//        String secretId = nacosParam.secretId;
//        String secretKey = nacosParam.secretKey;
//        log.info("获取签名-接收参数：secretId:{},secretKey:{}",secretId,secretKey);
////        String key= DigestUtils.md5Hex("4b530cd24ba411edbb4b52540079e9e2" +content+"5517745f4ba411edbb4b52540079e9e2");
//        String key= DigestUtils.md5Hex(secretId +content+secretKey);
//        System.out.println("*****************:"+key);
//        return key;
//    }


}
