package com.dxhy.base.controller;

import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.service.ISysIndustryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/industry")
@Slf4j
public class IndustryController {

    @Autowired
    private ISysIndustryService sysIndustryService;


    @ResponseBody
    @RequestMapping(value = "/list", method = {RequestMethod.POST,RequestMethod.GET})
    public CommonRspVo list() {

        return CommonRspVo.success(sysIndustryService.getIndustryList());
    }

    @ResponseBody
    @RequestMapping(value = "/getByName", method = {RequestMethod.POST,RequestMethod.GET})
    public CommonRspVo getByName(@RequestParam String industryName) {

        return CommonRspVo.success(sysIndustryService.selectIndustryByName(industryName));
    }

    @ResponseBody
    @RequestMapping(value = "/getByCode", method = {RequestMethod.POST,RequestMethod.GET})
    public CommonRspVo getByCode(@RequestParam String industryCode) {

        return CommonRspVo.success(sysIndustryService.selectIndustryByCode(industryCode));
    }
}
