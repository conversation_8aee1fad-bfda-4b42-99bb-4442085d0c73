/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.base.controller;

import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysLog;
import com.dxhy.core.pojo.vo.SysLogVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.ILogService;
import com.dxhy.core.service.SysUserService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/30 17:02
 */
@Api(tags = "日志管理")
@RestController
@AllArgsConstructor
@RequestMapping("/log")
public class LogController extends BaseController {

	@Autowired
	private ILogService logService;

	@Autowired
	private SysUserService sysUserService;

	@PostMapping("/save")
	public Result save(@RequestBody SysLog log) {
		logService.insert(log);
		return Result.ok();
	}

	/**
	 * 查询单条
	 */
	@GetMapping("/detail/{Id}")
	public Result detail(@PathVariable Long Id) {
		SysLog sysLog = logService.selectById(Id);
		return Result.ok().put("data", sysLog);
	}

	/**
	 * 查询多条(分页)
	 */
	@PostMapping("/list")
	public Result list(@RequestBody SysLogVo log) {
		Long userId = this.getUserId();
		UserVO userVO = sysUserService.selectUserVoById(userId);
		log.setTenantId(userVO.getTenantId());
		return logService.listByPage(log);
	}

}