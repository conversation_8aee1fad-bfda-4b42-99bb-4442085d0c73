/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.base.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.vo.StatisticsRzVo;
import com.dxhy.core.pojo.vo.StatisticsVo;
import com.dxhy.core.service.IStatisticsService;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/2 09:51
 */
@Api(tags = "统计查询")
@RestController
@AllArgsConstructor
@RequestMapping("/statistics")
@Slf4j
public class StatisticsController extends BaseController {


	@Autowired
	private IStatisticsService statisticsService;

	/**
	 * 查验统计查询(分页)
	 */
	@PostMapping("/cylist")
	public Result cylist(@RequestBody StatisticsVo vo) {
		log.info("请求参数：vo={}", JSON.toJSONString(vo));
		return statisticsService.cylist(vo);
	}

	/**
	 * 认证统计查询(分页)
	 */
	@PostMapping("/rzlist")
	public Result rzlist(@RequestBody StatisticsRzVo vo) {
		vo.setStartTime(DateUtil.beginOfMonth(vo.getStartTime()));
		vo.setEndTime(DateUtil.endOfMonth(vo.getEndTime()));
		return statisticsService.rzlist(vo);
	}


	/**
	 * 开票统计查询(分页)
	 */
	@PostMapping("/kplist")
	public Result kplist(@RequestBody StatisticsVo vo) {
		return statisticsService.kplist(vo);
	}
}