package com.dxhy.base.controller;/**
 * @Auther: 李永强
 * @Date: 2020/4/1 09:09
 * @Description:
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.R;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.handler.TokenHandleUtil;
import com.dxhy.core.pojo.DTO.EntUserDTO;
import com.dxhy.core.pojo.hw.GeneralParam;
import com.dxhy.core.pojo.hw.GetAccessTokenResp;
import com.dxhy.core.pojo.hw.GetHwUserResp;
import com.dxhy.core.pojo.hw.TenantTokenVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.SigninService;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.service.TenantInfoService;
import com.dxhy.core.service.UserService;
import com.dxhy.core.utils.MycstUtil;
import com.xiaoleilu.hutool.codec.Base64;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 *@program: user-center
 *@description:
 *@author: LiYongQiang
 *@create: 2020-04-01 09:09
 */
@Slf4j
@Api(tags = "用户登录")
@RequestMapping("/uauthentication")
@RestController
public class LoginController {

    @Resource
    private SigninService signinService;

    @Autowired
    @Qualifier("consumerTokenServices")
    private ConsumerTokenServices consumerTokenServices;

    @Value("${demo.users:}")
    private String demoUsers;

    @Autowired
    private UserService userService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${security.encode.key}")
    private String key;//用于解密的Key

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private NacosParam nacosParam;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private TenantInfoService tenantInfoService;
    @Autowired
    private MycstUtil mycstUtil;


    /**
     * 账号密码登录-pc端登录 微信登录
     * 1 校验入参的准确性
     *
     * @param entUserDTO
     * @return
     */
    @RequestMapping("/portal/login")
    public R login(HttpServletRequest request, @RequestBody EntUserDTO entUserDTO) throws Exception {

        log.info("账号密码登录:/login入参:{}", JSON.toJSONString(entUserDTO));
        //测试用
        //this.redisTemplate.opsForValue().set(SystemConstants.DEFAULT_SMS_CODE_KEY + "13730072040","1234",300, TimeUnit.SECONDS);

        R<UserVO> r = signinService.parameterCheck(entUserDTO);
        if (r.getCode() != R.SUCCESS) {
            return r;
        }
        UserVO data = r.getData();
        //获取token
        String token = signinService.getToken(request, entUserDTO);
        log.info("token:::::"+token);
        //token重新存入redis 方便续期
        String access_token = (String) this.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
        if (access_token == null) {
            log.info("登录：redis中不存在USER_TOKEN_CODE_KEY，重新存入");
            TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
            DecodedJWT djwt = tokenHandleUtil.verify(token, CommonConstant.SIGN_KEY);
            if (djwt != null) {
                Long exp =Long.valueOf(djwt.getClaim("exp").asInt());
                Long time = exp-System.currentTimeMillis()/1000;
                log.info("剩余时间："+time);
                this.redisTemplate.opsForValue().set(SystemConstants.USER_TOKEN_CODE_KEY + token,token,time*2, TimeUnit.SECONDS);
            }
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("token", token);
        map.put("userId", data.getUserId());
        //税航票帮手获取token
        String mycstToken = "";
        try {
            mycstToken = mycstUtil.getToken();
        } catch (Exception e) {
            log.error("获取税航票帮手token异常:"+e.getMessage());
        }
        map.put("mycstToken", mycstToken);
        signinService.updateUserLastLoginTime(data.getUserId());
        return new R(map);

    }


    /**
     * 退出
     *
     * @param map
     * @return
     */
    @PostMapping("/logout")
    public R<Boolean> logout(@RequestBody Map<String, String> map,HttpServletRequest request) {
        log.info("【用户退出】logout,请求参数：{}",JSON.toJSONString(map));
        String[] accessTokens = map.get("accessToken").split("\\.");
        if(accessTokens.length <2){
            log.error("【用户退出】logout,token异常：{}",JSON.toJSONString(map));
            return new R<>(false);
        }
        JSONObject jsonObject =JSONObject.parseObject(Base64.decodeStr(accessTokens[1]));
        String username = jsonObject.getString("user_name");
        log.info("【用户退出】logout,解析token后的用户名：{}",username);
//        if(StringUtils.isNotBlank(request.getHeader("isHw")) && request.getHeader("isHw").equals("1")){
//            //华为云退出登录
//            String hwLogoutUrl = nacosParam.hwDomainName + nacosParam.hwLogoutUrl;
//            String userId = jsonObject.getString("userId");
//            restTemplate.getForEntity(hwLogoutUrl,String.class);
//            return new R<>(true);
//        }else{
            boolean contains = demoUsers.contains(username);
            log.info("【用户退出】logout,用户名是否在需要过滤：{}",contains);
            if(contains){
                return new R<>(true);
            }else{
                //删除自定义存储的token 同一用户多地登录会同时退出
                //this.redisTemplate.delete(SystemConstants.USER_TOKEN_CODE_KEY +map.get("accessToken"));
                return new R<>(consumerTokenServices.revokeToken(map.get("accessToken")));
            }
//        }


    }


    /**
     * 单点登录(目前没用，统一使用ApiSyncController中的单点登录接口)
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023-04-19
     */

    @PostMapping("/portal/xxLogin")
    public String xxLogin(String username, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // String username = (String) request.getAttribute("username");
        // 校验登录账号
        UserVO localUserInfo = sysUserService.findUserByUsername(username);
        if(localUserInfo == null || localUserInfo.getUserId() == null){
            log.error("单点登录 getLocalUser error, userName: {}, localUserInfo: {}", username, localUserInfo);
            return "跳转失败";
        }
        Long userId = localUserInfo.getUserId();
        EntUserDTO entUserDTO = new EntUserDTO();
        entUserDTO.setClientId("fatc");
        entUserDTO.setClientSecret("fatc");
        entUserDTO.setUsername(username);
        String token = signinService.getToken(request, entUserDTO);
        log.info("==> token:"+token);
        //token重新存入redis 方便续期
        String access_token = (String) this.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
        if (access_token == null) {
            log.info("==>单点登录：redis中不存在USER_TOKEN_CODE_KEY，重新存入");
            TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
            DecodedJWT djwt = tokenHandleUtil.verify(token, CommonConstant.SIGN_KEY);
            if (djwt != null) {
                Long exp =Long.valueOf(djwt.getClaim("exp").asInt());
                Long time = exp-System.currentTimeMillis()/1000;
                log.info("剩余时间："+time);
                this.redisTemplate.opsForValue().set(SystemConstants.USER_TOKEN_CODE_KEY + token,token,time*2, TimeUnit.SECONDS);
            }
        }
        Cookie cookie = new Cookie("Portal-Token",token);
        response.addHeader("Authorization",CommonConstant.TOKEN_SPLIT+token);
        response.addCookie(cookie);
        signinService.updateUserLastLoginTime(userId);
        log.debug("==>单点登录 success, userName: {}, token: {}", username, token);
        response.sendRedirect("http://10.1.5.203:3200/#/index");
        return "redirect:" + nacosParam.dxEtaxUrl;
    }

    /**
     * 华为云企业工作台登录
     * @param model
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023-04-19
     */
    @GetMapping("/portal/hwLogin")
    public String login(Model model, HttpServletRequest request, HttpServletResponse response) throws Exception {
        // 1 调华为云接口校验用户身份
        // 1.1 获取url中的请求参数
        String hwCode = request.getParameter("code");
        String hwTenantId = request.getParameter("tenant");
        log.info("华为云登录:/login入参: code: {} tenant:{}",hwCode,hwTenantId);
//        if(StringUtils.isEmpty(hwCode) || StringUtils.isEmpty(hwTenantId)){
//            log.debug("hwSso login error, hwCode or hwTenantId null");
//            model.addAttribute("errorMsg", "状态异常，请重新通过华为云登录");
//            return "error";
//        }

        // 1.2 调华为云接口获取令牌，根据http状态码判断成功或者失败
        TenantTokenVo tenantTokenVo = tenantInfoService.getAccessTokenParam(hwTenantId);
        String hwClientId = "";
        String hwClientSecret = "";
        String hwDomainName = "";
        if (tenantTokenVo != null) {
            hwClientId = tenantTokenVo.getClientId();
            hwClientSecret = tenantTokenVo.getClientSecret();
            hwDomainName = tenantTokenVo.getDomainName();
        }else{
            hwClientId = nacosParam.hwClientId;
            hwClientSecret = nacosParam.hwClientSecret;
            hwDomainName = nacosParam.hwDomainName;
        }
        String hwLogoutUrl = hwDomainName + nacosParam.hwLogoutUrl;
        String hwGetAccessTokenUrl = hwDomainName + nacosParam.hwAccTokenUrl;
        Map<String, String> getAccessTokenReqMap = new HashMap<>();
        getAccessTokenReqMap.put("client_id", hwClientId);
        getAccessTokenReqMap.put("client_secret", hwClientSecret);
        getAccessTokenReqMap.put("code", hwCode);
        getAccessTokenReqMap.put("grant_type", "authorization_code");
        log.debug("hwSso getToken, hwCode: {}, getAccessTokenReq: {}", hwCode, getAccessTokenReqMap);
        String getAccessTokenContent = getContent(hwGetAccessTokenUrl, getAccessTokenReqMap);
        GetAccessTokenResp getAccessTokenResp = JSONObject.parseObject(getAccessTokenContent, GetAccessTokenResp.class);
        log.debug("hwSso getToken, hwCode: {}, getAccessTokenResp: {}", hwCode, getAccessTokenResp);
        if(getAccessTokenResp == null || org.apache.commons.lang.StringUtils.isEmpty(getAccessTokenResp.getAccess_token())){
            log.debug("hwSso getToken error, hwCode: {}, getAccessTokenResp: {}", hwCode, getAccessTokenResp);
            return "redirect:" + hwLogoutUrl;
        }

        // 1.3 获取令牌成功后，调华为云接口查询当前用户信息
        HttpHeaders hwGetUserHeaders = new HttpHeaders();
        hwGetUserHeaders.set("authorization", "Bearer " + getAccessTokenResp.getAccess_token());
        String hwGetUserInfoUrl = hwDomainName + nacosParam.hwUserInfoUrl;
        log.debug("hwSso getUser, hwCode: {}", hwCode);
        ResponseEntity<GetHwUserResp> getHwUserInfoUrlResp = restTemplate.exchange(hwGetUserInfoUrl, HttpMethod.GET, new org.springframework.http.HttpEntity<>(null, hwGetUserHeaders), GetHwUserResp.class);
        String getHwUserInfoUrlRespHttpState = getHwUserInfoUrlResp.getStatusCode().toString();
        if(!getHwUserInfoUrlRespHttpState.contains(GeneralParam.HW_HTTP_STATE_CODE_200)){
            log.debug("hwSso getUser error, hwCode: {}", hwCode);
            return "redirect:" + hwLogoutUrl;
        }
        GetHwUserResp getHwUserResp = getHwUserInfoUrlResp.getBody();
        if(getHwUserResp == null || org.apache.commons.lang.StringUtils.isEmpty(getHwUserResp.getUserName())){
            log.debug("hwSso getUser error, hwCode: {}, getHwUserResp: {}", hwCode, getHwUserResp);
            return "redirect:" + hwLogoutUrl;
        }

        // 1.4 华为云用户匹配本地用户成功后，获取到用户信息用于本地做登录处理
        String userName = getHwUserResp.getUserName();
        UserVO localUserInfo = sysUserService.findUserByUsername(userName);
        if(localUserInfo == null || localUserInfo.getUserId() == null){
            log.debug("hwSso getLocalUser error, hwCode: {}, userName: {}, localUserInfo: {}", hwCode, userName, localUserInfo);
            return "redirect:" + hwLogoutUrl;
        }
        Long userId = localUserInfo.getUserId();

        // 1.5 用户id + 令牌存到redis中，用于后续调华为云接口（例如刷新token）
        //String redisKey = GeneralParam.HW_LOCAL_TOKEN_REDIS_KEY_PREFIX + userId;
        //log.debug("hwSso redis set, key: {}, token: {}", redisKey, getAccessTokenResp.getAccess_token());

//        String token = getAccessTokenResp.getAccess_token();
//        //token重新存入redis 方便续期
//        String access_token = (String) this.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
//        if (access_token == null) {
//            log.info("登录：redis中不存在USER_TOKEN_CODE_KEY，重新存入");
//            TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
//            DecodedJWT djwt = tokenHandleUtil.verify(token, CommonConstant.SIGN_KEY);
//            if (djwt != null) {
//                Long exp =Long.valueOf(djwt.getClaim("exp").asInt());
//                Long time = exp-System.currentTimeMillis()/1000;
//                log.info("剩余时间："+time);
//                this.redisTemplate.opsForValue().set(SystemConstants.USER_TOKEN_CODE_KEY + token,token,time, TimeUnit.SECONDS);
//            }
//        }else{
//            //判断过期时间 换新token
//        }        // 2. 更新上次登录时间
        //获取token
        EntUserDTO entUserDTO = new EntUserDTO();
        entUserDTO.setClientId("fatc");
        entUserDTO.setClientSecret("fatc");
        entUserDTO.setUsername(userName);
        String token = signinService.getToken(request, entUserDTO);
        log.info("token:::::"+token);
        //token重新存入redis 方便续期
        String access_token = (String) this.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
        if (access_token == null) {
            log.info("登录：redis中不存在USER_TOKEN_CODE_KEY，重新存入");
            TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
            DecodedJWT djwt = tokenHandleUtil.verify(token, CommonConstant.SIGN_KEY);
            if (djwt != null) {
                Long exp =Long.valueOf(djwt.getClaim("exp").asInt());
                Long time = exp-System.currentTimeMillis()/1000;
                log.info("剩余时间："+time);
                this.redisTemplate.opsForValue().set(SystemConstants.USER_TOKEN_CODE_KEY + token,token,time*2, TimeUnit.SECONDS);
            }
        }
        Cookie cookie = new Cookie("Portal-Token",token);
        response.addHeader("Authorization",CommonConstant.TOKEN_SPLIT+token);
        response.addCookie(cookie);
        signinService.updateUserLastLoginTime(userId);
        log.debug("hwSso success, hwCode: {}, userName: {}, token: {}", hwCode, userName, token);
        response.sendRedirect(nacosParam.dxEtaxUrl);
        return "redirect:" + nacosParam.dxEtaxUrl;
    }

    /**
     * 调用华为云接口时用
     * @param
     * @return java.lang.String
     * <AUTHOR>
     **/
    public static String getContent(String url, Map<String, String> mapdata) {
        CloseableHttpResponse response = null;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        // 创建httppost
        HttpPost httpPost = new HttpPost(url);
        try {
            // 设置提交方式
            httpPost.addHeader("Content-type", "application/x-www-form-urlencoded; charset=utf-8");
            // 添加参数
            List<NameValuePair> nameValuePairs = new ArrayList<>();
            if (mapdata.size() != 0) {
                // 将mapdata中的key存在set集合中，通过迭代器取出所有的key，再获取每一个键对应的值
                Set keySet = mapdata.keySet();
                Iterator it = keySet.iterator();
                while (it.hasNext()) {
                    String k =  it.next().toString();// key
                    String v = mapdata.get(k);// value
                    nameValuePairs.add(new BasicNameValuePair(k, v));
                }
            }
            httpPost.setEntity( new UrlEncodedFormEntity(nameValuePairs,"UTF-8"));
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 获得http响应体
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                // 响应的结果
                String content = EntityUtils.toString(entity, "UTF-8");
                log.info("hwSso getContent success, content: {}", content);
                return content;
            }
            log.info("hwSso getContent : content is empty");
            return "";
        } catch (Exception e) {
            log.error("hwSso getContent error", e);
            return "";
        }
    }

}