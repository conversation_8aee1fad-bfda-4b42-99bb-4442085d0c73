package com.dxhy.base.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.excel.SysUserImportListener;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.SysMenuMapper;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.pojo.DTO.*;
import com.dxhy.core.pojo.entity.SysTenant;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.entity.SystemVersion;
import com.dxhy.core.pojo.excel.SysUserImportDto;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.AESUtils;
import com.dxhy.core.utils.ExcelUtils;
import com.dxhy.core.utils.ToolUtils;
import com.dxhy.core.utils.UserUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xiaoleilu.hutool.util.RandomUtil;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.provider.token.ConsumerTokenServices;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.time.Duration;
import java.time.Instant;
import java.util.Base64;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2017/10/28
 */
@RestController
@RequestMapping("/user")
@Api(tags = "用户管理")
@Slf4j
public class UserController extends BaseController {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private HttpServletRequest request;

    @Value("${security.encode.key}")
    private String key;//用于解密的Key

    @Autowired
    @Qualifier("consumerTokenServices")
    private ConsumerTokenServices consumerTokenServices;

    @Autowired
    private NacosParam nacosParam;

    @Resource
    private BSystemLogicService bSystemLogicService;

    @Resource
    private SysUserRoleService sysUserRoleService;

    @Resource
    private SysUserMapper sysUserMapper;

    @Resource
    private SysMenuMapper sysMenuMapper;

    @Autowired
    private ICustomerProductService iCustomerProductService;

    @Autowired
    private ITenantService tenantService;
    /**
     * 查询用户信息
     */
    @ApiOperation("查询用户所有信息")
    @RequestMapping(value = "/queryUserInfo",method = RequestMethod.GET)
    public Result queryUserInfo(){
        final Instant now = Instant.now();
        //根据token查询userId
        final Long userId = this.getUserId();
        log.info("查询用户信息接收参数为:userId = {}", userId);
        try {
            Result result = (Result)redisTemplate.opsForValue().get(SystemConstants.USER_INFO_CODE_KEY + userId);
            if(result == null){
                result = sysUserService.queryUserInfo(userId);
                redisTemplate.opsForValue().set(SystemConstants.USER_INFO_CODE_KEY + userId, result, 6, TimeUnit.HOURS);
            }
            log.info("查询用户信息返回结果为:" + result);
            log.info("[queryUserInfo接口]查询用户信息耗时:"+ Duration.between(now,Instant.now()).toMillis());
            return result;
        } catch (Exception e) {
            log.error("查询用户信息异常",e);
            return   Result.error("查询用户信息异常");
        }
    }

    /**
     * 查询当前登录用户的权限信息
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-06-22
     */
    @ApiOperation("查询当前登录用户的权限信息")
    @RequestMapping(value = "/getDeptInfo",method = RequestMethod.GET)
    public Result getDeptInfo(){
        final Instant now = Instant.now();
        //根据token查询userId
        final Long userId = this.getUserId();
        log.info("查询权限信息接收参数为:userId = " + userId);
        try {
            Result result = (Result)redisTemplate.opsForValue().get(SystemConstants.DEPT_INFO_CODE_KEY + userId);
            if(result == null){
                result = sysUserService.getDeptInfo(userId);
                redisTemplate.opsForValue().set(SystemConstants.DEPT_INFO_CODE_KEY + userId, result, 6, TimeUnit.HOURS);
            }
            log.info("查询权限信息返回结果为:" + result);
            log.info("[getDeptInfo接口]查询权限信息耗时:"+ Duration.between(now,Instant.now()).toMillis());
            return result;
        } catch (Exception e) {
            log.error("查询权限信息异常",e);
            return   Result.error("查询权限信息异常");
        }
    }
    /**
     * 校验身份 (目前只校验token有效无效)
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-06-21
     */
    @ApiOperation("校验身份")
    @RequestMapping(value = "/verifyIdentity")
    public R verifyIdentity(HttpServletRequest request){
        String token = request.getHeader("Authorization").split(" ")[1];
        String access_token = (String) this.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
        String[] accessTokens =access_token.split("\\.");
        JSONObject jsonObject =JSONObject.parseObject(com.xiaoleilu.hutool.codec.Base64.decodeStr(accessTokens[1]));
        Long exp = jsonObject.getLong("exp");
        String date = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new java.util.Date( exp * 1000));
        log.info("校验身份-解析token有效期: exp = {},时间：{}" ,exp,date);

        HashMap<String, Object> map = new HashMap<>();
        try {
            //解析token 有效期
            if (exp == null) {
                Result.error("校验身份解析token异常，解析出exp为空");
            }else{
                //获取当前系统时间戳
                long nowTime = System.currentTimeMillis()/1000;
                if (nowTime >= exp) {
                    log.info("【verifyIdentity】校验身份：token已过期,nowTime:{},exp:{}",nowTime,exp);
                    map.put("token_invalid", false);
                    map.put("token_exp",exp);
                    return new R(map);
                }else{
                    log.info("【verifyIdentity】校验身份：token未过期，nowTime:{},exp:{}",nowTime,exp);
                    map.put("token_invalid", true);
                    map.put("token_exp",exp);
                    return new R(map);
                }
            }
        } catch (Exception e) {
            log.error("校验身份异常",e);
            return new R(10001,"verifyIdentity,校验身份异常");
        }
        return null;
    }

    /**
     * 前端修改密码
     * @param httpServletRequest
     * @param map 参数是加密过的
     * @return
     * <AUTHOR>
     * @date 2022-06-23
     */
    @ApiOperation("修改密码")
    @RequestMapping(value = "/updateSecret")
    public R<Boolean> upassword(HttpServletRequest httpServletRequest,@RequestBody Map<String, String> map) throws Exception {
        Long userId = this.getUserId();
        String token = UserUtils.getToken(httpServletRequest);
        String heavenKey = Base64.getEncoder().encodeToString("HEAVEN".getBytes());
        Claims claims = (Claims) Jwts.parser().setSigningKey(heavenKey).parseClaimsJws(token).getBody();


        String newPassword = AESUtils.decryptAES(map.get("newPassword"), key);
        map.put("newPassword", newPassword);

        //设置密码
        if(StringUtils.isBlank(map.get("password"))){
            return sysUserService.settingPasswrod(map, userId);
        }

        String password = AESUtils.decryptAES(map.get("password"), key);
        map.put("password", password);

        //修改密码
        R<Boolean> r = sysUserService.updatePassword(map, userId);
        log.info("修改密码返回结果{},token{}",r,token);
        if (r.getCode()==0 && r.getMsg().equals("success") && r.getData()) {
            log.info("清除redis中token信息");
            this.redisTemplate.delete(SystemConstants.USER_TOKEN_CODE_KEY +token);
            consumerTokenServices.revokeToken(token);
        }

        return r;
    }

    /**
     * 原方法：忘记密码
     * 修改后：校验部分剥离
     * @param map
     * @return
     * <AUTHOR>
     * @date 2022-11-01
     */
    @PostMapping("/password/forget")
    public R<Boolean> forgetPassword(@RequestBody Map<String, String> map) throws Exception {

        String username = null;
        if(StringUtils.isNotBlank(map.get("mobile"))) {
            username = map.get("mobile");
        } else if(StringUtils.isNotBlank(map.get("email"))){
            username = map.get("email");
        }
        if(StringUtils.isBlank(username)) {
            return new R(1, "用户名为空");
        };

        //校验签名signId
        if (StringUtils.isBlank(map.get("signId"))) {
            return new R(4, "签名认证ID为空");
        }else{
            String signCode = (String) redisTemplate.opsForValue().get(SystemConstants.USER_SIGN_CODE_KEY + username);
            if (StringUtils.isBlank(signCode)) {
                return new R(4, "签名认证ID已失效，请重新获取");
            }
            if (!signCode.equals(map.get("signId"))) {
                return new R(4, "签名认证ID已失效，请重新获取");
            }else{
                log.info("[忘记密码]签名认证ID校验通过。"+signCode);
            }
        }

        return sysUserService.forgetPassword(map, username);
    }

    /**
     * 校验手机号或者邮箱验证码
     * @param map
     * @return
     * <AUTHOR>
     * @date 2022-11-01
     */
    @PostMapping("/vaildCode")
    public R<String> vaildCode(@RequestBody Map<String, String> map) {

        String username = null;
        if(StringUtils.isNotBlank(map.get("mobile"))) {
            username = map.get("mobile");
        } else if(StringUtils.isNotBlank(map.get("email"))){
            username = map.get("email");
        }
        if(StringUtils.isBlank(username)) {
            return new R(3, "用户名为空");
        };
        int count = sysUserMapper.getAccountTotalByName(username);
        if (count>1) {
            return new R(3, "手机号或邮箱绑定了多个账号，请联系管理员");
        }else if (count==0){
            return new R(3, "未获取到用户信息");
        }

        R validateRet = sysUserService.validateCode(map.get("mobile"), map.get("email"), map.get("code"));
        if (validateRet.getCode()==0) {
            //封装验证信息
            String code = RandomUtil.randomNumbers(4);
            redisTemplate.opsForValue().set(SystemConstants.USER_SIGN_CODE_KEY + username, code, 5, TimeUnit.MINUTES);
            return new R<>(code);
        }
       return validateRet;
    }


    /**
     * 临时工具类
     * @return
     * @throws Exception
     */
    @ApiOperation("加密参数")
    @RequestMapping(value = "/encryptParam",method = RequestMethod.GET)
    public R<Boolean> encryptParam(@RequestParam("param") String param) throws Exception {
        log.info("加密参数原值为:{}",param);
        String newParam = AESUtils.encryptAES(param, key);
        String ep = AESUtils.encryptParam(param);
        log.info("加密参数结果为:{}\n{}",newParam,ep);

        Map<String,Object> map = new HashMap<>();
        map.put("AESEnCode",newParam);
        map.put("BCryptPasswordEncoder",ep);
        return new R(map);
    }
    /**
     * 1.校验身份
     * 2.续期
     * 3.获取当前用户租户ID
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/getTenantId")
    public R getTenantId(HttpServletRequest request) {
        Long userId = this.getUserId();
        UserVO userVO = sysUserService.selectUserVoById(userId);
        log.info("租户ID为："+userVO.getTenantId());
        return new R(userVO.getTenantId());
    }

    /**
     * 获取当前用户租户信息
     * @param request
     * @return
     */
    @RequestMapping(value = "/getTenantInfo")
    public R getTenantInfo(HttpServletRequest request) {
        Long userId = this.getUserId();
        UserVO userVO = sysUserService.selectUserVoById(userId);
        String tenantId = userVO.getTenantId();
        SysTenant tenantInfo = tenantService.getTenantInfo(tenantId);
        return new R(tenantInfo);
    }

    /**
     * 用户列表查询
     */
    @RequestMapping(value = "/listUsers",method = RequestMethod.POST)
    public Result listUsers(@RequestBody Map<String, Object> params){
        log.info("查询部门下人员列表接收参数为" + params);
        try {
            return sysUserService.listUsersByDeptId(params);
        } catch (Exception e) {
            log.error("查询部门下人员列表异常",e);
            return   Result.error("查询部门下人员列表异常");
        }
    }

    /**
     * 用户详情
     */
    @ApiOperation("用户详情")
    @RequestMapping(value = "/userInfo",method = RequestMethod.GET)
    public Result userInfo(@RequestParam(required = false) Long userId){
        if(userId == null){
            String token = UserUtils.getToken(request);
            String key = Base64.getEncoder().encodeToString("HEAVEN".getBytes());
            Claims claims = Jwts.parser().setSigningKey(key).parseClaimsJws(token).getBody();
            userId =Long.valueOf(claims.get("userId").toString());
        }
        try {
            return sysUserService.userInfo(userId);
        } catch (Exception e) {
            log.error("查询用户信息异常",e);
            return   Result.error("查询用户信息异常");
        }
    }
    /**
     * 用户新增
     */
    @RequestMapping(value = "/addUser",method = RequestMethod.POST)
    public Result addUser(@RequestBody AdminUserOperateDto adminUserOperateDto){
        log.info("用户新增接收参数为:{}",adminUserOperateDto.toString());
        try {
            if (StringUtils.isBlank(adminUserOperateDto.getUserSource())) adminUserOperateDto.setUserSource("4");

            UserVO user = sysUserMapper.selectUserVoById(this.getUserId());
            if (user.getUserSource().equals("5")) {
                adminUserOperateDto.setUserSource("5");
            }
            Result result = sysUserService.addUser(adminUserOperateDto);
            sysUserService.syncUserInfo(result,adminUserOperateDto.getDeptId(),adminUserOperateDto.getType());
            return result;

        } catch (Exception e) {
            log.error("新增用户异常",e);
            return  Result.error("新增用户异常");
        }


    }

    /**
     * 用户修改
     */
    @RequestMapping(value = "/updateUser",method = RequestMethod.POST)
    public Result updateUser(@RequestBody AdminUserOperateDto adminUserOperateDto){
        log.info("用户修改接收参数为:{}",adminUserOperateDto.toString());
        try {
            if (StringUtils.isBlank(adminUserOperateDto.getUserSource())) adminUserOperateDto.setUserSource("4");
            UserVO user = sysUserMapper.selectUserVoById(adminUserOperateDto.getUserId());
            if (user.getUserSource().equals("5")) {
                adminUserOperateDto.setUserSource("5");
            }
            Result result = sysUserService.updateUserByUserId(adminUserOperateDto);
            sysUserService.syncUserInfo(result,adminUserOperateDto.getDeptId(),adminUserOperateDto.getType());
            return result;
        } catch (Exception e) {
            log.error("更新用户异常",e);
            return   Result.error("更新用户异常");
        }
    }

    /**
     * 删除用户信息
     *
     * @param id ID
     * @return R
     */
    @ApiOperation(value = "删除用户", notes = "根据ID删除用户")
    @ApiImplicitParam(name = "id", value = "用户ID", required = true, dataType = "int", paramType = "path")
    @RequestMapping("delUser/{id}")
    public Result userDel(@PathVariable Integer id) {
        SysUser sysUser = sysUserService.selectById(id);
        if(sysUser!=null&&sysUser.getUsername().equals("admin")){
            log.error("admin账户不允许删除");
            return Result.error(R.FAIL,"admin账户不允许删除");
        }

        if(sysUser!=null&&"5".equals(sysUser.getUserType())&&"MR".equals(sysUser.getRemark())){
            log.error("渠道侧默认创建账户不允许删除");
            return Result.error(R.FAIL,"渠道默认账户不允许删除");
        }

        if(sysUser!=null&&"1".equals(sysUser.getUserType())){
            log.error("主账户不允许删除");
            return Result.error(R.FAIL,"主账户不允许删除");
        }
        return Result.ok().put("data",sysUserService.deleteUserById(sysUser));
    }

    @PostMapping("/bind")
    public R<Boolean> bind(@RequestBody EntUserDTO userDTO) {
        if (StringUtils.isNotBlank(userDTO.getMobile())) {
            R validateRet = sysUserService.validateCode(userDTO.getMobile(),userDTO.getEmail(), userDTO.getCode());
            if (validateRet.getCode() != 0) {
                return validateRet;
            }
        }

        //获取用户id
        Long userId = getUserId();
        UserVO user = sysUserService.selectUserVoById(userId);
        return sysUserService.bind(userDTO, user.getUsername());
    }

    /**
     * 重置密码
     *
     * @param userDTO
     * @return
     * @throws Exception
     */
    @PostMapping("/password/reset")
    public R resetPassword(@RequestBody UserDTO userDTO) throws Exception {
        String passWord = ToolUtils.generatePassWord();

        log.info("[添加用户]对应生成的密码为:{}"+passWord);
        userDTO.setNewPassword(passWord);
        UserVO userVO = sysUserService.findUserByUsername(userDTO.getUsername());
        if (userVO == null) {
            return new R(1, "未获取到用户信息");
        }
        return sysUserService.resetPassword(userVO.getUserId(), userVO.getUsername(), userDTO.getNewPassword(),userVO.getPhone());
    }

    /**
     * 查询用户对应销项税控菜单
     * @param
     * @return
     * <AUTHOR>
     * @date 2022-08-19
     */
    @ApiOperation("查询用户对应销项税控菜单")
    @RequestMapping(value = "/queryUserSKMenu",method = RequestMethod.GET)
    public Result queryUserSKMenu(){
        final Instant now = Instant.now();
        //根据token查询userId
        final Long userId = this.getUserId();
        log.info("查询用户对应销项税控菜单 接收参数为:userId = {}", userId);
        try {
            Result result = (Result)redisTemplate.opsForValue().get(SystemConstants.USER_SKMENU_CODE_KEY + userId);
            if(result == null){
                result = sysUserService.getUserSKMenuList(userId);
                redisTemplate.opsForValue().set(SystemConstants.USER_SKMENU_CODE_KEY + userId, result, 6, TimeUnit.HOURS);
            }
            log.info("查询用户对应销项税控菜单 返回结果为:" + result);
            log.info("[queryUserSKMenu接口]查询用户对应销项税控菜单 耗时:"+ Duration.between(now,Instant.now()).toMillis());
            return result;
        } catch (Exception e) {
            log.error("查询用户对应销项税控菜单 异常",e);
            return   Result.error("查询用户对应销项税控菜单 异常");
        }
    }

    /**
     * 查询当前用户 版本状态 relea_version
     * =0：暂无新版本发布
     * =1：有需要提醒yoghurt  =
     * =2: 用户已查看
     * @param
     * @return 当前版本信息
     * <AUTHOR>
     * @date 2022-08-22
     */
    @ApiOperation("用户当前版本状态")
    @RequestMapping(value = "/versionStatus",method = RequestMethod.GET)
    public Result versionStatus(){
        final Long userId = this.getUserId();
        log.info("用户当前版本状态 接收参数为:userId = " + userId);
        List<SystemVersion> list = sysUserService.versionStatus(userId);
        log.info("用户当前版本状态 返回结果为:" + list);
        if (list!=null&&list.size()>0) {
            //更新为已读
            int num = sysUserService.updateVersionStatus(userId,2);
            if (num == 0) {
                log.info("用户版本已读状态修改失败");
            }
        }
        return Result.ok().put("data",list);

    }


    /**
     * 批量删除用户
     * @return
     */
    @RequestMapping("/deleteBatch")
    public Result deleteBatch(@RequestBody HashMap<String,Object> param){
        log.info("批量删除的用户Id是；{}",param);
        List<Long> list = (List<Long>) param.get("ids");

        for (int i = 0; i < list.size(); i++) {
            SysUser sysUser = sysUserService.selectById(list.get(i));
            if (sysUser != null && sysUser.getUsername().equals("admin")) {
                log.error("admin账户不允许删除");
                return Result.error(R.FAIL, "admin账户不允许删除");
            }

            if (sysUser != null && "5".equals(sysUser.getUserType()) && "MR".equals(sysUser.getRemark())) {
                log.error("渠道侧默认创建账户不允许删除");
                return Result.error(R.FAIL, "渠道默认账户不允许删除");
            }

            if (sysUser != null && "1".equals(sysUser.getUserType())) {
                log.error("主账户不允许删除");
                return Result.error(R.FAIL, "主账户不允许删除");
            }
        }
        //删除用户角色关联
        ObjectMapper mapper = new ObjectMapper();
        List<Long> list2 = mapper.convertValue(list, new TypeReference<List<Long>>() { });
        list2.stream().forEach(userId -> {
            sysUserRoleService.deleteByUserId(userId);
            sysUserMapper.deleteUserDeptRelation(userId);
        });
        //删除用户数据权限关联
        return Result.ok().put("data",sysUserService.deleteBatchIds(list));
    }

    /**
     * 通过税号查询 赋予该税号数据权限的用户
     * @param taxNo
     * @return result
     */
    @ApiOperation("通过税号查询赋予该税号数据权限的用户")
    @RequestMapping(value = "/getAuthNameByTaxno",method = RequestMethod.GET)
    public Result getAuthNameByTaxno(@RequestParam("taxNo") String  taxNo,@RequestParam("flag") Boolean flag) {
        log.info("通过税号查询赋予该税号数据权限的用户,请求参数：{}", taxNo);
        List<String> list = sysUserService.getAuthNameByTaxno(taxNo,flag);
        return Result.ok().put("data",list);
    }

    /**
     * Saas系统同步租户信息到票税系统
     * @param saasTenantInfoDto
     * @return result
     */
    @ApiOperation("Saas系统同步租户信息到票税系统")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    @RequestMapping(value = "/syncSaasData",method = RequestMethod.POST)
    public Result syncSaasData(@RequestBody SaasTenantInfoDto saasTenantInfoDto) throws Exception {
        String dataId = saasTenantInfoDto.getDataId();
        log.info("Saas系统同步租户信息到票税系统,请求参数：{},dataId:{}", saasTenantInfoDto,dataId);
        //签名鉴权
        String key= DigestUtils.md5Hex(nacosParam.secretId +saasTenantInfoDto.getContent()+nacosParam.secretKey);
        if (!key.equals(saasTenantInfoDto.getSign())){
            return new Result(ResponseCodeEnum.SIGN_ERROR);
        }
        //解析信息
        SaasTenantData saasTenantData = JSON.parseObject(cn.hutool.core.codec.Base64.decodeStr(saasTenantInfoDto.getContent()), SaasTenantData.class);
        log.info("Saas系统同步租户信息到票税系统 内层报文：{},dataId:{}",saasTenantData,dataId);
        //增加校验
        CommonRspVo commonRspVo = sysUserService.checkTenantInfo(saasTenantData);
        if (!commonRspVo.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("Saas系统同步租户信息到票税系统,参数检验不通过：响应结果 {},dataId:{}",commonRspVo,dataId);
            return Result.error(commonRspVo.getCode(),commonRspVo.getMessage());
        }

        Result result = sysUserService.syncSaasTenant(saasTenantData,dataId);
        log.info("Saas系统同步租户信息到票税系统,数据同步结果：{},dataId:{}",result,dataId);
        if (result.get("code").equals("0000")) {
            return Result.ok().put("dataId",saasTenantInfoDto.getDataId());
        }else{
            log.info("Saas系统同步租户信息到票税系统,同步失败，手动进行回滚,dataID:{}",dataId);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }

        return Result.error(result.get("code").toString(),result.get("msg").toString()).put("dataId",saasTenantInfoDto.getDataId());

    }

    /**
     * 批量导入用户
     *
     * @param file Excel文件
     * @return 导入结果
     */
    @ApiOperation("批量导入用户")
    @PostMapping("/importUser")
    public Result importUser(@RequestParam("file") MultipartFile file) {
        log.info("/user/importUser开始执行");
        if (file.isEmpty()) {
            return Result.error("上传文件不能为空");
        }
        
        try {
            // 获取当前用户信息
            final Long userId = this.getUserId();
            UserVO userVO = sysUserService.selectUserVoById(userId);
            String tenantId = userVO.getTenantId();
            
            // 创建Excel监听器
            SysUserImportListener listener = new SysUserImportListener(sysUserService, userId.toString(), tenantId);
            
            // 解析Excel文件
            ExcelUtils.readExcel(file.getInputStream(), SysUserImportDto.class, listener);
            
            // 获取导入结果
            List<SysUserImportListener.ImportResult> importResults = listener.getImportResults();
            int successCount = listener.getSuccessCount();
            
            log.info("/user/importUser执行完成，成功导入{}条记录", successCount);
            return Result.ok().put("data", importResults).put("successCount", successCount);
        } catch (IOException e) {
            log.error("导入用户异常", e);
            return Result.error("导入用户异常: " + e.getMessage());
        }
    }
    
    /**
     * 下载用户导入模板
     *
     * @param response HttpServletResponse
     */
    @ApiOperation("下载用户导入模板")
    @GetMapping("/downloadTemplate")
    public void downloadTemplate(HttpServletResponse response) {
        log.info("/user/downloadTemplate开始执行");
        ExcelUtils.generateTemplate(response, "用户导入模板", "用户信息", SysUserImportDto.class);
    }
}
