package com.dxhy.base.controller;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.toolkit.StringUtils;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.PageQueryEntity;
import com.dxhy.core.pojo.vo.MyPage;
import com.dxhy.core.pojo.vo.RegionParent;
import com.dxhy.core.properties.DictionaryProperties;
import com.dxhy.core.service.IDictionaryService;
import com.dxhy.core.service.SysRegionService;
import com.dxhy.core.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@Slf4j
@RestController
@RequestMapping("/dictionary")
public class DictionaryController {
    @Autowired
    private IDictionaryService dictionaryService;
    @Autowired
    private DictionaryProperties dictionaryProperties;
    private static final String LOGGER_MSG = "(字典Controller)";

    /**
     * 地区信息
     */
    @Autowired
    private SysRegionService sysRegionService;
    /**
    * 通过ID查询
    *
    * @param id ID
    * @return Dictionary
    */
    @GetMapping("/getById")
    public Result get(@RequestParam("id") String id) {
        return Result.ok().put("data",dictionaryService.selectById(id));
    }

    /**
     * 通过code查询,没用
     *
     * @param code code
     * @return Dictionary
     */
    @GetMapping("/getByCode")
    public Result getByCode(@RequestParam("code") String code) {
        Dictionary result = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code", code));
        return Result.ok().put("data",result);
    }
    /**
     * 通过父code查询树
     *
     * @param parent code
     * @return Dictionary
     */
    @GetMapping("/getTree")
    public Result getTree(@RequestParam("parent") String parent) {
        List<Dictionary> result = dictionaryService.getTree(parent);
        return Result.ok().put("data",result);
    }

    /**
     * 通过多个父code查询字典树
     *
     * @param parentIds 父code数组
     * @return Map<String, List<Dictionary>> 各个父id对应的字典项
     */
    @GetMapping("/getTreesByParentIds")
    public Result getTreesByParentIds(@RequestParam("parentIds") String[] parentIds) {
        log.info("{}通过多个父code查询字典树, parentIds:{}", LOGGER_MSG, Arrays.toString(parentIds));
        
        Map<String, List<Dictionary>> resultMap = new HashMap<>();
        
        if (parentIds != null && parentIds.length > 0) {
            for (String parentId : parentIds) {
                if (StringUtils.isNotEmpty(parentId)) {
                    List<Dictionary> dictionaryList = dictionaryService.getTree(parentId);
                    resultMap.put(parentId, dictionaryList);
                }
            }
        }
        
        return Result.ok().put("data",(resultMap));
    }

    /**
    * 分页查询信息
    *
    * @param
    * @return 分页对象
    */
    @GetMapping("/page")
    public Result page(@RequestParam Map<String,Object> params) {
        log.info("{}分页模糊查询信息, params:{}", LOGGER_MSG, params);
        PageQueryEntity pageQueryEntity = new PageQueryEntity();
        pageQueryEntity.setPage(Integer.valueOf(String.valueOf(params.get("currPage"))));
        pageQueryEntity.setLimit(Integer.valueOf(String.valueOf(params.get("pageSize"))));
        Wrapper wrapper = new EntityWrapper();
        Iterator iterator = params.entrySet().iterator();
        for (; iterator.hasNext();) {
            Map.Entry entry = (Map.Entry) iterator.next();
            if (StringUtils.isEmpty((String) entry.getValue())) {
                continue;
            }
            if ("name".equals(entry.getKey())) {
                wrapper.like((String) entry.getKey(), (String) entry.getValue());
            }
            if("desc".equals(entry.getKey())){
                wrapper.orderDesc(Collections.singletonList(entry.getValue()));
            }
        }
        wrapper.eq("parent",-1);
        Page page = dictionaryService.selectPage(new MyPage(pageQueryEntity), wrapper);
        PageUtils pageUtils = new PageUtils(page);
        return Result.ok().put("data", (pageUtils));
    }

    /**
     * 添加
     * @param  dictionary  实体
     * @return success/false
     */
    @PostMapping("/addDictionary")
    public Result add(@RequestBody Dictionary dictionary) {
        //查询表中id最大值加1作为新的id
        Long id = dictionaryService.selectMaxId()+1;
        dictionary.setId(id);
        dictionaryService.insert(dictionary);
        return Result.ok().put("data",id);
    }

    /**
     * 删除
     * @param id ID
     * @return success/false
     */
    @DeleteMapping("/{id}")
    public Result delete(@PathVariable Long id) {
        Dictionary dictionary = dictionaryService.selectById(id);
        if (dictionary!=null){
            if (dictionary.getParent() != null && dictionary.getParent().equals("-1")){
                dictionaryService.delete(new EntityWrapper<Dictionary>().eq("parent",dictionary.getCode()));
            }
        }
        boolean b = dictionaryService.deleteById(id);
        return Result.ok().put("data",b);
    }

    /**
     * 编辑
     * @param  dictionary  实体
     * @return success/false
     */
    @PostMapping("/editDictionary")
    public Result edit(@RequestBody Dictionary dictionary) {
        boolean b = dictionaryService.updateById(dictionary);
        return Result.ok().put("data",b);
    }

    /**
     * 通过ID查询
     *
     * @param
     * @return Dictionary
     */
    @GetMapping("/getTreeByCode")
    public Result getTree() {
        Map<String,Object> map = new HashMap<>();
        for (String par : dictionaryProperties.getParent()){
            List<Dictionary> list = dictionaryService.getTree(par);
            if (("50").equals(par)){
                map.put("administrateType",list);
            }else if (("34").equals(par)){
                map.put("taxpayerType",list);
            }else if (("37").equals(par)){
                map.put("accountStandard",list);
            }else if (("53").equals(par)){
                map.put("companyType",list);
            }else if (("46").equals(par)){
                map.put("sksbbm",list);
            }
        }
        return Result.ok().put("data",(map));
    }

    /**
     * 获取详情
     *
     * @param code code
     * @return Dictionary
     */
    @GetMapping("/detail")
    public Result detail(@RequestParam("code") String code,@RequestParam("parent") Integer parent) {
        Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code", code).eq("parent", parent));
        return Result.ok().put("data",dictionary);
    }

    /**
     * 根据条件获取字典列表
     * @param id
     * @param code
     * @param name
     * @param desc
     * @param parent
     * @param flag
     * @return
     */
    @GetMapping("/selectDictionary")
    public Result selectDictionary(@RequestParam("id") String id,@RequestParam("code") String code,
                                       @RequestParam("name") String name,@RequestParam("desc") String desc,
                                       @RequestParam("parent") String parent,@RequestParam("flag") String flag){
        log.info("根据条件获取字典列表：id:{},code:{},name:{},desc:{},parent:{},flag:{}",id,code,name,desc,parent,flag);
        List<Dictionary> list = dictionaryService.selectDictionary(id,code,name,desc,parent,flag);
        return Result.ok().put("data",list);
    }

    @GetMapping("/locations")
    public Result locations() {
        List list = formatRegion(sysRegionService.selectAllArea());
        if (list == null) {
            log.error("地区返回结果为空：{}", list);
            return Result.error();
        } else {
            return Result.ok().put("data",(list));
        }
    }

    private List<RegionParent> formatRegion(List<RegionParent> regions) {
        if (regions == null || regions.isEmpty()) {
            return regions;
        }
        return getRegionParents(regions);
    }

    private List<RegionParent> getRegionParents(List<RegionParent> regions) {
        Map<Integer, List<RegionParent>> regionMap = new HashMap<>();
        changeLocation(regions, regionMap);
        return regions;
    }

    private void changeLocation(List<RegionParent> regions, Map<Integer, List<RegionParent>> regionMap) {
        for (int i = 0; i < regions.size(); i++) {
            Integer regionCode = regions.get(i).getRegionCode();
            if (regionCode % 10000 == 0) {
                upgradeRegion(regions, regionMap, i, regionCode);
            } else if (regionCode % 100 == 0) {
                //市级地区
                //放入省级子集合中
                i = upgradeCity(regions, regionMap, i, regionCode);
            } else {
                //县级地区
                //放入市级子集合中
                upgradeCountry(regions, regionMap, i, regionCode);
                i--;
            }
        }
    }

    /**
     * 升级城镇
     * @param regions
     * @param regionMap
     * @param i
     * @param regionCode
     */
    private void upgradeCountry(List<RegionParent> regions, Map<Integer, List<RegionParent>> regionMap, int i, Integer regionCode) {
        List<RegionParent> partners = regionMap.get(regionCode / 100 * 100);
        if (partners == null) {
            partners = new ArrayList<>();
            regionMap.put(regionCode / 100 * 100, partners);
        }
        partners.add(regions.get(i));
        regions.remove(i);
    }

    /**
     * 升级市区
     * @param regions
     * @param regionMap
     * @param i
     * @param regionCode
     * @return
     */
    private int upgradeCity(List<RegionParent> regions, Map<Integer, List<RegionParent>> regionMap, int i, Integer regionCode) {
        List<RegionParent> partners = regionMap.computeIfAbsent(regionCode / 10000 * 10000,
                k -> new ArrayList<>());
        partners.add(regions.get(i));
        //设置子集合
        List<RegionParent> children = regionMap.computeIfAbsent(regionCode, k -> new ArrayList<>());
        regions.get(i).setChildren(children);
        regions.remove(i);
        i--;
        return i;
    }

    /**
     * 升级地区
     * @param regions
     * @param regionMap
     * @param i
     * @param regionCode
     */
    private void upgradeRegion(List<RegionParent> regions, Map<Integer, List<RegionParent>> regionMap, int i, Integer regionCode) {
        //升级地区
        List<RegionParent> children = regionMap.get(regionCode);
        if (children == null) {
            if (regions.get(i).getChildren() != null) {
                children = regions.get(i).getChildren();
            } else {
                children = new ArrayList<>();
            }
            regionMap.put(regionCode, children);
        }
        regions.get(i).setChildren(children);
    }

    @GetMapping("/getByFlag")
    public Result getByFlag(@RequestParam("flag") String flag) {
        return Result.ok().put("data",dictionaryService.getByFlag(flag));
    }
}
