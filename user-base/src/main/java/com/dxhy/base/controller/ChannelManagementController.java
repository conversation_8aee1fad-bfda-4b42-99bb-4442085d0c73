package com.dxhy.base.controller;

import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.ChannelManagementListDTO;
import com.dxhy.core.pojo.DTO.ChannelManagementSaveDTO;
import com.dxhy.core.pojo.DTO.IdDTO;
import com.dxhy.core.service.ChannelManagementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 通道管理模块 controller
 * <AUTHOR>
 * @Date 2025/01/10
 * @Version 1.0
 **/
@RestController
@Api(tags = "通道管理模块")
@Slf4j
@RequestMapping("/channelManagement")
public class ChannelManagementController extends BaseController{
    private final String modelName = "通道管理模块";

    @Autowired
    private ChannelManagementService channelManagementService;

    /**
     * 分页查询
     */
    @PostMapping("/page")
    @ApiOperation(value = "通道管理列表")
    public Result page(@RequestBody @Valid ChannelManagementListDTO channelManagementListDTO){
        log.info("{} - list - {}", this.modelName, channelManagementListDTO);
        return channelManagementService.queryPage(channelManagementListDTO);
    }

    /**
     * 对外提供，查询满足条件的通道名称
     */
    @PostMapping("/getChannelName")
    @ApiOperation(value = "获取匹配到的通道名称-对外接口")
    public Result getChannelName(@RequestBody @Valid ChannelManagementListDTO channelManagementListDTO){
        log.info("{} - list - {}", this.modelName, channelManagementListDTO);
        return channelManagementService.getChannelName(channelManagementListDTO);
    }

    /**
     * 查询满足条件的所有业务类型
     */
    @PostMapping("/getBusinessTypes")
    @ApiOperation(value = "获取匹配到的通道名称-对外接口")
    public Result getBusinessTypes(@RequestBody ChannelManagementListDTO channelManagementListDTO){
        log.info("{} - list - {}", this.modelName, channelManagementListDTO);
        return channelManagementService.getBusinessTypes(channelManagementListDTO);
    }

    /**
     * 查询匹配到的业务类型
     */
    @PostMapping("/getBusinessTypeList")
    @ApiOperation(value = "获取匹配到的业务类型")
    public Result getBusinessTypeList(@RequestBody @Valid ChannelManagementListDTO channelManagementListDTO){
        log.info("{} - list - {}", this.modelName, channelManagementListDTO);
        return channelManagementService.getBusinessTypeList(channelManagementListDTO);
    }

    /**
     * 详情
     */
    @PostMapping("/info")
    @ApiOperation(value = "通道管理详情")
    public Result info(@RequestBody @Valid IdDTO idDTO){
        log.info("{} - info - {}", this.modelName, idDTO);
        return channelManagementService.getById(idDTO.getId());
    }

    /**
     * 新增
     */
    @PostMapping("/save")
    @ApiOperation(value = "新增通道管理")
    public Result save(@RequestBody @Valid ChannelManagementSaveDTO channelManagementSaveDTO){
        log.info("{} - save - {}", this.modelName, channelManagementSaveDTO);
        String userName = this.getUser();
        return channelManagementService.saveData(channelManagementSaveDTO,userName);
    }

    /**
     * 删除
     */
    @PostMapping("/delete")
    @ApiOperation(value = "删除通道管理")
    public Result delete(@RequestBody @Valid IdDTO idDTO){
        log.info("{} - delete - {}", this.modelName, idDTO);
        String userName = this.getUser();
        return channelManagementService.deleteData(idDTO.getId(),userName);
    }
} 