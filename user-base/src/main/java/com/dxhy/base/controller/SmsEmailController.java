package com.dxhy.base.controller;

import com.alibaba.fastjson.JSON;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.EmailDto;
import com.dxhy.core.service.SmsEmailService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Map;

/**
 * 短信邮件服务
 * <AUTHOR>
 * @date 2022-08-22
 */
@Slf4j
@RestController
@RequestMapping("/smsEmail")
public class SmsEmailController extends BaseController {
    @Autowired
    private SmsEmailService smsEmailService;

    @ApiOperation("邮件发送")
    @RequestMapping(value = "/sendEmail",method = RequestMethod.POST)
    public Result sendEmail(HttpServletRequest httpServletRequest, @RequestBody Map<String, Object> params) throws Exception {
        log.info("【邮件发送】接收参数：{}",params);
        EmailDto emailDto = JSON.parseObject(JSON.toJSONString(params),EmailDto.class);
        smsEmailService.sendEmail(emailDto);
        return Result.ok();
    }
}
