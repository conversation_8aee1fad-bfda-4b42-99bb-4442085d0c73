package com.dxhy.base.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.DictParentConstants;
import com.dxhy.core.constants.ProductConstant;
import com.dxhy.core.mapper.SysDeptMapper;
import com.dxhy.core.mapper.SysRoleMapper;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.ProductMenu;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.vo.CommonRspVo;
import com.dxhy.core.pojo.vo.ProductMenuTree;
import com.dxhy.core.service.IDictionaryService;
import com.dxhy.core.service.IProductInfoService;
import com.dxhy.core.service.IProductMenuService;
import com.dxhy.core.utils.UserUtils;
import com.elephant.dbcache.annotation.collection.CacheParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * sys_permission 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Slf4j
@RestController
@RequestMapping("/menu")
public class ProductMenuController {
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private IProductMenuService productMenuService;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private IProductInfoService productService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IDictionaryService dictionaryService;

    @Value("${system.sign}")
    private String systemSign;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    /**
    * 通过ID查询
    *
    * @param id ID
    * @return ProductMenu
    */
    @GetMapping("/getProductMenuById")
    public ProductMenu getProductMenuById(@RequestParam("id") String id) {
        ProductMenu productMenu = productMenuService.selectById(id);
        if(StringUtils.isNotBlank(productMenu.getCreateBy())){
            SysUser createUser = sysUserMapper.selectById(productMenu.getCreateBy());
            if(createUser != null){
                productMenu.setCreateBy(createUser.getUsername());
            }
        }else {
            productMenu.setCreateBy("admin");
        }
        return productMenu;
    }

    /**
    * 查询所有产品菜单(过滤未授权的菜单)
    *
    */
    @PostMapping("/getProductMenu")
    public Result getProductMenu(@RequestBody String str) {
        JSONObject jsonObject = JSON.parseObject(str);
        Wrapper<ProductMenu> wrapper = new EntityWrapper();
        wrapper.eq("status","0");
        if (jsonObject.containsKey("parentId") && StringUtils.isNotEmpty(jsonObject.getString("parentId"))) {
            wrapper.eq("parent_id", jsonObject.getString("parentId"));
        }
        Long userId = getUserId();
        if(userId != 1L){
            SysUser sysUser = sysUserMapper.selectById(userId);
            //查询该用户的角色
            List<SysRole> sysRoles = sysRoleMapper.selectRolesByUserId(userId);
            //取角色id的集合
            List<Long> roleIdList = new ArrayList<>();
            sysRoles.forEach(sysRole -> {
                roleIdList.add(sysRole.getRoleId());
            });
            //查询全部角色拥有的菜单
//            List<Long> productIdList = sysDeptMapper.selectProductIdByDeptId(sysUser.getDeptId());
            List<String> menuIdList = sysUserMapper.queryMenuIdListByRoles(roleIdList);
            if(!CollectionUtils.isEmpty(menuIdList)){
                wrapper.andNew().in("id", menuIdList).or().isNull("product_id").or().eq("product_id", "");
            }else {
                wrapper.andNew("product_id is null or product_id = ''");
            }
        }
        List<ProductMenu> list = productMenuService.selectList(wrapper);
        list.forEach(productMenu -> {
            if(StringUtils.isNotBlank(productMenu.getCreateBy())){
                SysUser createUser = sysUserMapper.selectById(productMenu.getCreateBy());
                if(createUser != null){
                    productMenu.setCreateBy(createUser.getUsername());
                }
            }else {
                productMenu.setCreateBy("admin");
            }
            if(StringUtils.isNotBlank(productMenu.getProductId())){
                //字典转换
                Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code",productMenu.getProductId()).eq("parent", DictParentConstants.TD_CPLX));
                if(dictionary != null){
                    productMenu.setProductName(dictionary.getName());
                }
            }
        });
        return Result.ok().put("data", list);
    }

    /**
     * 添加
     * @param  productMenu  实体
     * @return success/false
     */
    @PostMapping("/addProductMenu")
    public Result add(@RequestBody ProductMenu productMenu) {
        productMenu.setModifyTime(new Date());
        productMenu.setCreateTime(new Date());
        Long userId = getUserId();
        productMenu.setCreateBy(String.valueOf(userId));
        productMenu.setUpdateBy(String.valueOf(userId));
        if(StringUtils.isBlank(productMenu.getProductId())){
            productMenu.setProductId(null);
        }
        productMenuService.insert(productMenu);
        return Result.ok();
    }

    /**
     * 删除
     * @param id ID
     * @return success/false
     */
    @PostMapping("/delProductMenu/{id}")
    public Result delete(@PathVariable String id) {
        int selectCount = productMenuService.selectCount(new EntityWrapper().eq("parent_id", id).eq("status","0"));
        if (selectCount > 0) {
            return Result.error("请先删除子节点!");
        }
        Long userId = getUserId();
        ProductMenu productMenu = new ProductMenu();
        productMenu.setModifyTime(new Date());
        productMenu.setStatus("1");
        productMenu.setId(id);
        productMenu.setUpdateBy(String.valueOf(userId));
        productMenuService.updateById(productMenu);
        return Result.ok();
    }

    /**
     * 编辑
     * @param  productMenu  实体
     * @return success/false
     */
    @PostMapping("/editProductMenu")
    public Result edit(@RequestBody ProductMenu productMenu) {
        productMenu.setModifyTime(new Date());
        productMenu.setUpdateBy(String.valueOf(getUserId()));
        productMenuService.updateById(productMenu);
        return Result.ok();
    }

    /**
     * 批量新增产品菜单
     * <AUTHOR>
     * @return
     */
    @PostMapping("/addProductMenuList")
    public Boolean addProductMenuList(@RequestBody List<ProductMenu> productMenus){
        Long userId = getUserId();
        productMenus.parallelStream().forEach(p -> {
            productMenuService.deleteCache(p.getId());
            p.setCreateBy(String.valueOf(userId));
            p.setUpdateBy(String.valueOf(userId));
        });
        return productMenuService.insertBatch(productMenus);
    }

    /**
     * 根据产品id删除
     * @param productId
     * @return success/false
     */
    @GetMapping("/deleteByProductId")
    public Boolean deleteByProductId(@CacheParam @RequestParam("productId") String productId) {
        return productMenuService.delete(new EntityWrapper().eq("product_id",productId));
    }

    /**
     * 查询所有产品菜单
     *
     */
    @GetMapping("/getProductMenuByProductId")
    public List<ProductMenu> getProductMenuByProductId(@CacheParam @RequestParam(value = "productId") String productId) {
        Wrapper wrapper = new EntityWrapper();
        wrapper.eq("product_id", productId);
        wrapper.eq("status","0");
        List<ProductMenu> list = productMenuService.selectList(wrapper);
        list.forEach(productMenu->{
            if(StringUtils.isNotBlank(productMenu.getCreateBy())){
                SysUser createUser = sysUserMapper.selectById(productMenu.getCreateBy());
                if(createUser != null){
                    productMenu.setCreateBy(createUser.getUsername());
                }
            }else {
                productMenu.setCreateBy("admin");
            }
        });
        return list;
    }

    /**
     * 根据产品ID和售卖类型ID查询产品菜单
     * @param productId 产品ID
     * @param sellLabelId 售卖类型ID
     * @return
     */
    @GetMapping("/getProductMenuByProductIdAndSellLabelId")
    public List<ProductMenu> getProductMenuByProductIdAndsellLabelId(@RequestParam(value = "productId") String productId,
                                                                @RequestParam(value = "sellLabelId", required = false) String sellLabelId) {
        log.info("根据产品ID和售卖类型ID查询产品菜单,产品ID:{},售卖类型ID:{}",
                productId, sellLabelId);
        List<ProductMenu> productMenuList = productMenuService.
                getProductMenuByProductIdAndSellLabelId(productId, sellLabelId);
        log.info("根据产品ID和售卖类型ID查询产品菜单,产品ID:{},售卖类型ID:{},菜单数量：{}",
                productId, sellLabelId, productMenuList.size());
        productMenuList.forEach(productMenu -> {
            if(StringUtils.isNotBlank(productMenu.getCreateBy())){
                SysUser createUser = sysUserMapper.selectById(productMenu.getCreateBy());
                if(createUser != null){
                    productMenu.setCreateBy(createUser.getUsername());
                }
            }else {
                productMenu.setCreateBy("admin");
            }
        });
        return productMenuList;
    }

    /**
     * 根据企业ID查询超级管理员菜单列表 目前只针对主企业
     * @param deptId
     * @return
     */
    @GetMapping("/getProductMenuByDeptId")
    public CommonRspVo getProductMenuByDeptId(@RequestParam("deptId") String deptId,@RequestParam("roleType") Integer roleType) {
        log.info("通过组织ID查询产品菜单，参数：{}", deptId);
        List<ProductMenu> productMenus = productMenuService.getProMenuListByDeptId(deptId,roleType);
        productMenus.forEach(productMenu -> {
            if(StringUtils.isNotBlank(productMenu.getCreateBy())){
                SysUser createUser = sysUserMapper.selectById(productMenu.getCreateBy());
                if(createUser != null){
                    productMenu.setCreateBy(createUser.getUsername());
                }
            }else {
                productMenu.setCreateBy("admin");
            }
        });
        return new CommonRspVo(productMenus);
    }

    public Long getUserId() {
        return UserUtils.getUserId(request);
    }

    /**
     * 只获取销项开票税控管理的子菜单接口
     * @param deptId
     * @return
     */
    @GetMapping("/getTaxMenusByDeptId")
    public CommonRspVo getTaxMenusByDeptId(@RequestParam("deptId") String deptId) {
        //1.查询DX税控管理菜单
        ProductMenu pm = productMenuService.selectMenuId("税控管理",systemSign);
        //2.最外层封装DX税控设备父级菜单
        ProductMenuTree productMenuTree = new ProductMenuTree(pm);
        List<ProductMenu> productMenulist = productMenuService.getProductMenu(pm.getId());
        String sksbbm = productService.selectTaxControlByDeptId(deptId);
        if(StringUtils.isBlank(sksbbm)) {
            log.info("企业未获取到税控设备，不返回税控管理菜单，deptId:{}", deptId);
        }
        Set<String> menuSet = stringRedisTemplate.opsForSet().members(ProductConstant.OMP_SKGL_PERFIX + sksbbm);
        if(menuSet == null || menuSet.size() == 0) {
            log.info("从缓存获取税控管理菜单配置为空，开始从数据库中获取，sksbbm:{}", sksbbm);
            Dictionary dictionary = dictionaryService.getByFlag("sksbbm" + sksbbm);
            if(Optional.ofNullable(dictionary).isPresent()) {
                String desc = dictionary.getDesc();
                log.info("从数据库中获取税控管理菜单配置为:{},sksbbm:{}", desc, sksbbm);
                Long l = stringRedisTemplate.opsForSet().add(
                        ProductConstant.OMP_SKGL_PERFIX + sksbbm,desc.split("\\|"));
                log.info("从数据库中获取税控管理菜单成功并插入缓存:{},sksbbm:{}", l, sksbbm);
                menuSet = stringRedisTemplate.opsForSet().members(ProductConstant.OMP_SKGL_PERFIX + sksbbm);
            } else {
                log.info("从数据库中获取税控管理菜单配置为空，请检查是否已进行初始化配置，sksbbm:{}", sksbbm);
                menuSet = new HashSet<>();
            }

        }
        // 如果不存在集合里，删除该菜单
        Set<String> lamdaMenuSet = menuSet;
        productMenulist.removeIf(productMenu -> (
                        !lamdaMenuSet.contains(productMenu.getName())  // 删除不在范围内的菜单 ↓
                )
        );
        // productMenulist 转 pmtList
        List<ProductMenuTree> pmtList  = new ArrayList<>();
        productMenulist.stream().forEach(productMenu -> pmtList.add(new ProductMenuTree(productMenu)));
        //3.内层封装税控设备二级以下菜单
        productMenuTree.setChildren(pmtList);

        return new CommonRspVo(productMenuTree);
    }


}
