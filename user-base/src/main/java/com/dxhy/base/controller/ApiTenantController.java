package com.dxhy.base.controller;

import cn.hutool.core.date.DateTime;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.HwResult;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.SysDeptMapper;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.hw.*;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.HwUtil;
import com.dxhy.core.utils.RSAUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019/6/6 16:51
 */
@RestController
@RequestMapping("produceAPI")
@Slf4j
public class ApiTenantController {

    @Autowired
    private TenantInfoService tenantInfoService;
    @Autowired
    private TenantAppInfoService tenantAppInfoService;
    @Autowired
    private TenantAuthInfoService tenantAuthInfoService;
    @Autowired
    private IDistributorService distributorService;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysUserRoleService sysUserRoleService;
    @Autowired
    private SysDeptService sysDeptService;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysUserDeptService sysUserDeptService;
    @Autowired
    private HwOrgInfoService hwOrgInfoService;
    @Resource
    private HwUtil hwUtil;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private NacosParam nacosParam;

    /**
     * 同步租户信息
     */
    @RequestMapping(value = "/tenantSync", method = RequestMethod.POST)
    @ApiOperation("同步租户信息")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    public String tenantSync(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        jsonObject = hwUtil.dualUrlCode(jsonObject);
        TenantRequestDto tenantRequestDto = JSON.parseObject(jsonObject.toString(),TenantRequestDto.class);
        log.info("同步租户信息参数为：" + jsonObject);
        //authToken校验
        if (nacosParam.hwAuthTokenCheck.equals("1")) {
            Boolean check = hwUtil.checkAuthTokenForPost2(jsonObject,request);
            if (!check) {
                return JSONObject.toJSONString(HwResult.error("000001","鉴权失败"));
            }
        }
        //租户信息同步
        log.info("接收到的租户参数为" + JSONObject.toJSONString(tenantRequestDto));
        TenantInfoEntity tenantInfoEntity = new TenantInfoEntity();
        tenantInfoEntity.setInstanceId(tenantRequestDto.getInstanceId());
        tenantInfoEntity.setOrderId(tenantRequestDto.getOrderId());
        tenantInfoEntity.setTenantId(tenantRequestDto.getTenantId());
        tenantInfoEntity.setTenantCode(tenantRequestDto.getTenantCode());
        tenantInfoEntity.setName(tenantRequestDto.getName());
        tenantInfoEntity.setDomainName(tenantRequestDto.getDomainName());
        tenantInfoEntity.setTimeStamp(tenantRequestDto.getTimeStamp());
        tenantInfoEntity.setFlag(tenantRequestDto.getFlag());
        tenantInfoEntity.setTestFlag(tenantRequestDto.getTestFlag());
        DateTime dateTime = new DateTime();
        tenantInfoEntity.setCreateTime(dateTime);
        tenantInfoEntity.setUpdateTime(dateTime);
        int flag = tenantRequestDto.getFlag();
        boolean result = false;
        if (flag == 0) {//逻辑删除 or 物理删除
            result  = tenantInfoService.delete(new EntityWrapper<TenantInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()));
            //删除 渠道表
            result = distributorService.deleteById(tenantRequestDto.getTenantId());
        }else if(flag==1){//新增或修改
            //查询该条信息是否已存在数据库
            List<TenantInfoEntity> tenantInfoList = tenantInfoService.selectList(new EntityWrapper<TenantInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()));

            if(tenantInfoList.size()>0){
                result  = tenantInfoService.update(tenantInfoEntity,new EntityWrapper<TenantInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()));
            }else{
                result  = tenantInfoService.insert(tenantInfoEntity);
                //初始化角色
                Result s = tenantAuthInfoService.InitRole(tenantInfoEntity);
                if (!s.get("code").equals("0000")) {
                    log.info("租户信息:初始化角色 同步失败，手动进行回滚");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
                }
                //同步到渠道表
                s = tenantInfoService.transfromDisData(tenantInfoEntity);
                if (!s.get("code").equals("0000")) {
                    log.info("租户信息:同步到渠道表 同步失败，手动进行回滚");
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
                }
            }
        }
        log.info("result={}", result);
        if (result) {
            log.info("同步租户信息信息结果为 {},flag {}" , JSONObject.toJSONString(tenantInfoEntity),flag);
            Map<String, Object> map = new HashMap<>();
            map.put("resultCode", "000000");
            map.put("resultMsg", "success");
            String json = JSONObject.toJSONString(map);
            hwUtil.signForResp(response, json);
            return json;
        }
        return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
    }

    /**
     * 租户应用信息同步接口
     */
    @RequestMapping(value = "/applicationSync", method = RequestMethod.POST)
    @ApiOperation("同步租户应用信息接口")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    public String applicationSync(@RequestBody JSONObject jsonObject, HttpServletResponse response) throws Exception {
        jsonObject = hwUtil.dualUrlCode(jsonObject);
        TenantRequestDto tenantRequestDto = JSON.parseObject(jsonObject.toString(),TenantRequestDto.class);
        log.info("同步租户应用信息参数为：" + jsonObject);
        //authToken校验
        if (nacosParam.hwAuthTokenCheck.equals("1")) {
            Boolean check = hwUtil.checkAuthTokenForPost2(jsonObject, request);
            if (!check) {
                return JSONObject.toJSONString(HwResult.error("000001","鉴权失败"));
            }
        }
        //租户应用信息同步
        log.info("接收到的租户应用信息参数为" + JSONObject.toJSONString(tenantRequestDto));
        TenantAppInfoEntity tenantAppInfoEntity = new TenantAppInfoEntity();
        tenantAppInfoEntity.setTenantId(tenantRequestDto.getTenantId());
        tenantAppInfoEntity.setAppId(tenantRequestDto.getAppId());
        tenantAppInfoEntity.setClientId(tenantRequestDto.getClientId());
        tenantAppInfoEntity.setClientSecret(tenantRequestDto.getClientSecret());
        tenantAppInfoEntity.setFlag(tenantRequestDto.getFlag());
        tenantAppInfoEntity.setTestFlag(tenantRequestDto.getTestFlag());
        tenantAppInfoEntity.setTimeStamp(tenantRequestDto.getTimeStamp());
        tenantAppInfoEntity.setCreateTime(new DateTime());
        //RSA解密clientSecret
        String clientSecret = RSAUtils.rsaDecode(nacosParam.privateKey,tenantAppInfoEntity.getClientSecret());
        tenantAppInfoEntity.setClientSecret(clientSecret);
        int flag = tenantRequestDto.getFlag();
        boolean result = false;
        if (flag == 0) {//逻辑删除 or 物理删除
            result  = tenantAppInfoService.delete(new EntityWrapper<TenantAppInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()).eq("app_id",tenantRequestDto.getAppId()));
        }else if(flag==1) {//新增或修改
            //查询该条信息是否已存在数据库
            List<TenantAppInfoEntity> appInfoList = tenantAppInfoService.selectList(new EntityWrapper<TenantAppInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()).eq("app_id",tenantRequestDto.getAppId()));
            if(appInfoList.size()>0){
                result  = tenantAppInfoService.update(tenantAppInfoEntity, new EntityWrapper<TenantAppInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()).eq("app_id",tenantRequestDto.getAppId()));
            }else{
                result  = tenantAppInfoService.insert(tenantAppInfoEntity);
            }
        }
        if (result) {
            log.info("同步租户应用信息结果为 {},flag {}",JSONObject.toJSONString(tenantAppInfoEntity),flag);
            Map<String, Object> map = new HashMap<>();
            map.put("resultCode", "000000");
            map.put("resultMsg", "success");
            String json = JSONObject.toJSONString(map);
            hwUtil.signForResp(response, json);
            return json;

        }
        return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
    }

    /**
     * 租户信用授权信息同步
     * 包含用户信息
     */
    @RequestMapping(value = "/authSync", method = RequestMethod.POST)
    @ApiOperation("租户信用授权信息同步接口")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    public String authSync(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        jsonObject = hwUtil.dualUrlCode(jsonObject);
        TenantRequestDto tenantRequestDto = JSON.parseObject(jsonObject.toString(),TenantRequestDto.class);
        log.info("同步租户应用授权信息参数为：" + jsonObject);
        //authToken校验
        if (nacosParam.hwAuthTokenCheck.equals("1")) {
            Boolean check = hwUtil.checkAuthTokenForPost2(jsonObject, request);
            if (!check) {
                return JSONObject.toJSONString(HwResult.error("000001", "鉴权失败"));
            }
        }

        //入库保存
        TenantAuthInfoEntity tenantAuthInfoEntity = new TenantAuthInfoEntity();
        tenantAuthInfoEntity.setTenantId(tenantRequestDto.getTenantId());
        tenantAuthInfoEntity.setAppId(tenantRequestDto.getAppId());
        tenantAuthInfoEntity.setUserList(tenantRequestDto.getUserList());
        tenantAuthInfoEntity.setCurrentSyncTime(tenantRequestDto.getCurrentSyncTime());
        tenantAuthInfoEntity.setFlag(tenantRequestDto.getFlag());
        tenantAuthInfoEntity.setTestFlag(tenantRequestDto.getTestFlag());
        tenantAuthInfoEntity.setTimeStamp(tenantRequestDto.getTimeStamp());
        tenantAuthInfoEntity.setCreateTime(new DateTime());
        int flag = tenantRequestDto.getFlag();
        boolean result = false;
        if(flag==2) {//修改
            //查询该条信息是否已存在数据库
            result  = tenantAuthInfoService.update(tenantAuthInfoEntity, new EntityWrapper<TenantAuthInfoEntity>().eq("tenant_id",tenantRequestDto.getTenantId()).eq("app_id",tenantRequestDto.getAppId())
            );
        }else if(flag==1) {//新增
            result  = tenantAuthInfoService.insert(tenantAuthInfoEntity);
        }
        if(flag==0){
            tenantAuthInfoService.delUser(tenantAuthInfoEntity);
        }else{
            //1.解析数据同步到sys_user 用户表中
            Result res = tenantAuthInfoService.hwAuthSync(tenantRequestDto,flag);
            log.info("华为同步租户信息到票税系统,数据同步结果：{},dataId:{}",res);
            if (!res.get("code").equals("0000")) {
                log.info("华为同步租户信息到票税系统,同步失败，手动进行回滚");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                JSONObject.toJSONString(res);
                return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
            }
        }
        if (result) {
            log.info("同步租户应用授权信息结果为" + JSONObject.toJSONString(tenantAuthInfoEntity));
            Map<String, Object> map = new HashMap<>();
            map.put("resultCode", "000000");
            map.put("resultMsg", "success");

            String json = JSONObject.toJSONString(map);
            hwUtil.signForResp(response, json);
            return json;
        }
        return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
    }

    /**
     * 同步单个租户信息
     */
    @RequestMapping(value = "/singleOrgSync", method = RequestMethod.POST)
    @ApiOperation("组织部门信息同步（增量）接口")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    public String singleOrgSync(@RequestBody JSONObject jsonObject, HttpServletResponse response) {
        jsonObject = hwUtil.dualUrlCode(jsonObject);
        OrgRequestDto orgRequestDto = JSON.parseObject(jsonObject.toString(),OrgRequestDto.class);
        //租户信息同步
        log.info("接收到的组织（增量）参数为" + JSONObject.toJSONString(orgRequestDto));
        //authToken校验
        if (nacosParam.hwAuthTokenCheck.equals("1")) {
            Boolean check = hwUtil.checkAuthTokenForPost2(jsonObject,request);
            if (!check) {
                return JSONObject.toJSONString(HwResult.error("000001","鉴权失败"));
            }
        }
        if (StringUtils.isBlank(orgRequestDto.getOrgCode())) {
            return JSONObject.toJSONString(HwResult.error("000005","组织ID不能为空"));
        }
        int flag = orgRequestDto.getFlag();
        Boolean result = false;
        if (flag == 0) {//删除  逻辑删除  规则：不能删除上级
            //Result res = sysDeptService.deleteDeptById(orgRequestDto.getOrgCode());
            Result res = hwOrgInfoService.delDept(orgRequestDto);
            if (!res.get("code").equals("0000")) {
                return JSONObject.toJSONString(res);
            }
            result = true;
        }else {
            SysDept dept = sysDeptMapper.selectByDeptIdAndTenantId(orgRequestDto.getOrgCode(),orgRequestDto.getTenantId(),null);
            if (dept != null && orgRequestDto.getFlag()==1) {
                orgRequestDto.setFlag(2);
            }
            result = hwOrgInfoService.addOrgBySingle(orgRequestDto);
            hwOrgInfoService.synInvoiceData(orgRequestDto);
        }

        if (result) {
            //调用接口同步增量组织信息
            Map<String, Object> map = new HashMap<>();
            map.put("resultCode", "000000");
            map.put("resultMsg", "success");

            String json = JSONObject.toJSONString(map);
            hwUtil.signForResp(response, json);
            return json;
        }
        return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
    }

    //allOrgSync
    /**
     * 组织部门信息同步（全量）
     */
    @RequestMapping(value = "/allOrgSync", method = RequestMethod.POST)
    @ApiOperation("组织部门信息同步（全量）接口")
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    public String allOrgSync(@RequestBody JSONObject jsonObject,HttpServletResponse response) {
        jsonObject = hwUtil.dualUrlCode(jsonObject);
        OrgRequestDto orgRequestDto = JSON.parseObject(jsonObject.toString(),OrgRequestDto.class);
        //租户信息同步
        log.info("接收到的组织（全量）参数为" + JSONObject.toJSONString(orgRequestDto));
        //authToken校验
        if (nacosParam.hwAuthTokenCheck.equals("1")) {
            Boolean check = hwUtil.checkAuthTokenForPost2(jsonObject, request);
            if (!check) {
                return JSONObject.toJSONString(HwResult.error("000001", "鉴权失败"));
            }
        }
        Boolean result = hwOrgInfoService.addOrgByBatch(orgRequestDto);
        if (result) {
            //调用接口同步增量组织信息
            Map<String, Object> map = new HashMap<>();
            map.put("resultCode", "000000");
            map.put("resultMsg", "success");

            String json = JSONObject.toJSONString(map);
            hwUtil.signForResp(response, json);
            return json;
        }
        return JSONObject.toJSONString(Result.error("00005","其他服务内部错误"));
    }

}
