package com.dxhy.base.config;

import com.dxhy.core.common.response.Result2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/6 12:21
 * @Version 1.0
 */
@Slf4j
@RestControllerAdvice
public class DefaultExceptionHandler {

    /**
     * 缺少必要的参数
     **/
    @ExceptionHandler(value = MissingServletRequestParameterException.class)
    public Result2 missingParameterHandler(HttpServletRequest request, MissingServletRequestParameterException e) {
        this.logError(request, e);
        return Result2.error("PARAM_MISSING");
    }

    /**
     * 参数类型不匹配
     **/
    @ExceptionHandler(value = MethodArgumentTypeMismatchException.class)
    public Result2 methodArgumentTypeMismatchException(HttpServletRequest request, MethodArgumentTypeMismatchException e) {
        this.logError(request, e);
        return Result2.error("PARAM_TYPE_MISMATCH");
    }


    /**
     * 不支持的请求方法
     **/
    @ExceptionHandler(value = HttpRequestMethodNotSupportedException.class)
    public Result2 httpRequestMethodNotSupportedException(HttpServletRequest request, HttpRequestMethodNotSupportedException e) {
        this.logError(request, e);
        return Result2.error("HTTP_REQUEST_METHOD_NOT_SUPPORTED_ERROR");
    }

    /**
     * 参数错误
     **/
    @ExceptionHandler(value = IllegalArgumentException.class)
    public Result2 illegalArgumentException(HttpServletRequest request, IllegalArgumentException e) {
        this.logError(request, e);
        return Result2.error("SERVER_ERROR_PRARM");
    }

    /**
     * 其他异常统一处理
     **/
    @ExceptionHandler(value = Exception.class)
    public Result2 exception(HttpServletRequest request, Exception e) {
        this.logError(request, e);
        return Result2.error("SERVER_ERROR");
    }

    /**
     * 记录错误日志（兜底）
     **/
    private void logError(HttpServletRequest request, Exception e){
        log.error("path:{}, queryParam:{}, errorMessage:{}", request.getRequestURI(), request.getQueryString(), e.getMessage(), e);
    }
}
