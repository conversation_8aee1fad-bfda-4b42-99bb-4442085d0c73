package com.dxhy.base;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.ComponentScan;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * user-serviceDistributorVo
 * 用户中心
 * <AUTHOR>
 **/
@SpringBootApplication
@ComponentScan(basePackages={"com.dxhy.core","com.dxhy.base"})
@EnableSwagger2
@MapperScan(basePackages = {"com.dxhy.core.mapper"})
@ServletComponentScan
//@EnableScheduling
public class UserBaseApplication {

    public static void main(String[] args) {
        SpringApplication.run(UserBaseApplication.class, args);
    }

}