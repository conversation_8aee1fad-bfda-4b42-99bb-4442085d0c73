server:
  port: 8501
  tomcat:
    uri-encoding: utf-8
  servlet:
    context-path: /user-base
nacos:
  config:
    server-addr: http://116.63.9.39:8848
    namespace: 98ca7d84-cf07-47ee-aeb1-10d585c0c4c4
    user-center:
      group: user-center
      dataId: user-service.yml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************************************************************************************************************************************************************************
    username: dxhy
    password: 3SjWqF2fo5Z8+Pe4
    initial-size: 8
    min-idle: 8
    max-active: 16
    max-wait: 60000
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 300000
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
  redis:
    host: redis-8a3e3ee8-f4ad-465b-908b-f78d878b2697.cn-north-4.dcs.myhuaweicloud.com
    port: 6379
    password: '!PBcBzEfdmH93O1h'
    database: 0
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 500
        min-idle: 0
    lettuce:
      shutdown-timeout: 0

redis:
  host: redis-8a3e3ee8-f4ad-465b-908b-f78d878b2697.cn-north-4.dcs.myhuaweicloud.com
  port: 6379
  password: '!PBcBzEfdmH93O1h'
  pool.max-active: 8
  pool.max-wait: -1
  pool.max-idle: 8
  pool.min-idle: 0
  timeout: 1000

redisson:
  address: redis-8a3e3ee8-f4ad-465b-908b-f78d878b2697.cn-north-4.dcs.myhuaweicloud.com:6379
  password: '!PBcBzEfdmH93O1h'
  database: 81

test:
  str: testYml
sit:
  str: 中文测试

sourceId:
  guangxi: 450101001
  haokuaiji: 110108189         #好会计测试环境渠道配置
  yidaizhang: 110108191        #易代账测试环境渠道配置
defalut:
  role: 24121414
  parent: 3000
sensors:
  project: default

security:
  encode:
    key: '1234567887654321' #必须16位
  validate:
    code: true
    preview: false
  sessions: stateless
  oauth2:
    client:
      client-id: app
      client-secret: app
    resource:
      jwt:
        #        key-uri: http://*************:3001/oauth/token_key #解析jwt令牌所需要密钥的地址
        key-uri: http://localhost:8501/oauth/token_key #解析jwt令牌所需要密钥的地址
        key-value: app
    authorization:
      check-token-access: http://localhost:8501/oauth/check_token


#每隔1分钟执行1次
refresh:
  token:
    #time: 0 */1 * * * ?
    time: 0/1 * * * * ?


bigb: 100001-16
#demouids: 100001,975,100007
demoauth: z3OYGEqt9InWw15wsBv0Pw==,PjCJb9AUVq7I6Dy1kViWig==
exptime: 2

#短信验证码有效期
sms:
  expire: 10
  #短信接口与地址
  smsUrl: http://127.0.0.1:8080/dps/message/send?
  #短信平台编号
  platformNo: PSY
  #短信平台秘钥
  platformKey: 4eece46160eac3ab
  smsContentLogin: 验证码：{0}，有效期10分钟。如非本人操作，请忽略。
  smsContentRegister: （注册）验证码：{0}，有效期10分钟。如非本人操作，请忽略。
  smsContentUpass: （修改密码）验证码：{0}，有效期10分钟。如非本人操作，请忽略。
  smsContentBind: （绑定）验证码：{0}，有效期10分钟。如非本人操作，请忽略。
email:
  emailUrl: http://127.0.0.1:8080/dps/email/send
  emailContent: 尊敬的用户您好，现已为您开通全数通系统账号，用户名：{0} 密码：{1} 祝您生活愉快~
  emailContentReset: 尊敬的用户您好，现已为您重置全数通系统账号，用户名：{0} 密码：{1} 祝您生活愉快~
webhook:
#  wxurl: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ff269032-482f-48ba-920d-74d649a3e979
  wxurl: 123
#DX免密接口地址
ele-cloud:
  ssoUrl: https://omp-pre.dxyun.com/uauthentication/gotoapp?
  tokenUrl: https://sandbox.ele-cloud.com/api/authen/token?
  registerEntUrl: https://sandbox.ele-cloud.com/api/aosp-portal-center/combo/registerEnterpriseAll?
  thridSynUrl: https://omp-pre.dxyun.com/fatsapi/uadmin/dept/aosp/thirdSynDept?
  thirdAddUserUrl: https://omp-pre.dxyun.com/fatsapi/uadmin/user/aosp/thirdAddUser?
  SKSBUrl: https://sandbox.ele-cloud.com/api/order-api/order-api/v5/SyncTaxEquipmentInfo?
  appKey: F1hupjg0APTgm8HEfZLKMJSJ
  appSecret: WNyjzzLaTKtlVvRds7ScOHuR
  entCode: 91440300MA5EF4Q15E
  sourceId: *********
  tcId: b2e5b283-d1bd-4714-8d7c-4fde290b87f7

#企业信息中的 企业类型 会计准则等字典表
aosp:
  dictionary:
    parent: 12,13,14,15,34

#销项 使用的系统类型
system:
  sign: XX01