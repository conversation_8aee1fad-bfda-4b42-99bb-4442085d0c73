spring:
  profiles:
    active: @profileActive@
  application:
    name: user-service

# API同步模块配置
apisync:
  # 控制是否启用API同步功能
  enabled: true

# Spring整合Mybatis
mybatis:
  # 定义别名包
  type-aliases-package: com.dxhy.core.pojo
  # 导入映射文件
  mapper-locations: classpath:/mapper/*.xml
  # 开启驼峰映射
  configuration:
    map-underscore-to-camel-case: true
# 关闭MybatisPlus的banner
mybatis-plus:
  global-config:
    banner: false
  mapper-locations: classpath:/mapper/*.xml
logging:
  level:
    # sql日志级别
    com.dxhy.core.mapper: debug
  file:
    path: log

ignore:
  clients:
    - app
  urls:
    - /uauthentication/**
    - /udist/**
    - /social
    - /signin
    - /signup
    - /info
    - /health
    - /metrics/**
    - /loggers/**
    - /mobile/
    - /riskmgt/**
    - /fats-expert/**
    - /tax/**
    - /lawsinfo-website/**
    - /invoice/**
    - /fatscourse/**
    - /mobile/**
    - /user/**
    - /auth/**
    - /admin/ent/getSourceInfo
    - /admin/code/*
    - /admin/sendCode/**
    - /admin/user/info
    - /admin/user/register
    - /admin/user/password/forget
    - /admin/menu/userMenu
    - /swagger-resources/**
    - /swagger-ui.html
    - /*/v2/api-docs
    - /swagger/api-docs
    - /webjars/**
    - /consult-ht/**
    - /aosp-consult/**
    - /aosp-portal-web/**
    - /aosp-extra-service/**
    - /tax-manage-member/**
    - /zuul/**
    - /admin/user/aosp/sensors
    - /tax-manage-central/**
    - /platform-admin-server/**
    - /invoice-wxapp-service/**
    - /uauth/**
    - /uadmin/**
    - /aosp-portal-web-test/**
    - /ntax/**
    - /ntaxyyzt/**
    - /log/**