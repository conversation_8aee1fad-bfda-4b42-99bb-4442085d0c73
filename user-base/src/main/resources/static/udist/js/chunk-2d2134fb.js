(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2134fb"],{abae:function(e,t,n){"use strict";n.r(t);var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div")},a=[],c=(n("9e76"),n("9dd9"),n("9a33"),{created:function(){localStorage.setItem("activityId",this.getUrlParam("activityId"))},mounted:function(){window.location.href=this.getBackUrlParam("activityUrl")},methods:{getBackUrlParam:function(e){var t=window.location.href;console.log(decodeURI(t));var n=t.split(e+"=")[1].split("activityId=")[0];return console.log(decodeURIComponent(n)),decodeURIComponent(n)},getUrlParam:function(e){var t=new RegExp("(^|&)".concat(e,"=([^&]*)(&|$)")),n=decodeURIComponent(window.location.href).split("?")[1]||"",o=n.match(t);return null!=o?decodeURI(o[2]):null}}}),i=c,l=n("e90a"),r=Object(l["a"])(i,o,a,!1,null,null,null);t["default"]=r.exports}}]);