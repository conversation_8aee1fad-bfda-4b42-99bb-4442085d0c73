(function(e){function t(t){for(var o,r,a=t[0],c=t[1],u=t[2],l=0,d=[];l<a.length;l++)r=a[l],Object.prototype.hasOwnProperty.call(s,r)&&s[r]&&d.push(s[r][0]),s[r]=0;for(o in c)Object.prototype.hasOwnProperty.call(c,o)&&(e[o]=c[o]);h&&h(t);while(d.length)d.shift()();return i.push.apply(i,u||[]),n()}function n(){for(var e,t=0;t<i.length;t++){for(var n=i[t],o=!0,r=1;r<n.length;r++){var a=n[r];0!==s[a]&&(o=!1)}o&&(i.splice(t--,1),e=c(c.s=n[0]))}return e}var o={},r={app:0},s={app:0},i=[];function a(e){return c.p+"js/"+({"h5-login":"h5-login","h5-setting":"h5-setting","pc-login":"pc-login","pc-setting":"pc-setting"}[e]||e)+".js"}function c(t){if(o[t])return o[t].exports;var n=o[t]={i:t,l:!1,exports:{}};return e[t].call(n.exports,n,n.exports,c),n.l=!0,n.exports}c.e=function(e){var t=[],n={"h5-login":1,"h5-setting":1,"pc-setting":1};r[e]?t.push(r[e]):0!==r[e]&&n[e]&&t.push(r[e]=new Promise((function(t,n){for(var o="css/"+({"h5-login":"h5-login","h5-setting":"h5-setting","pc-login":"pc-login","pc-setting":"pc-setting"}[e]||e)+".css",s=c.p+o,i=document.getElementsByTagName("link"),a=0;a<i.length;a++){var u=i[a],l=u.getAttribute("data-href")||u.getAttribute("href");if("stylesheet"===u.rel&&(l===o||l===s))return t()}var d=document.getElementsByTagName("style");for(a=0;a<d.length;a++){u=d[a],l=u.getAttribute("data-href");if(l===o||l===s)return t()}var h=document.createElement("link");h.rel="stylesheet",h.type="text/css",h.onload=t,h.onerror=function(t){var o=t&&t.target&&t.target.src||s,i=new Error("Loading CSS chunk "+e+" failed.\n("+o+")");i.code="CSS_CHUNK_LOAD_FAILED",i.request=o,delete r[e],h.parentNode.removeChild(h),n(i)},h.href=s;var m=document.getElementsByTagName("head")[0];m.appendChild(h)})).then((function(){r[e]=0})));var o=s[e];if(0!==o)if(o)t.push(o[2]);else{var i=new Promise((function(t,n){o=s[e]=[t,n]}));t.push(o[2]=i);var u,l=document.createElement("script");l.charset="utf-8",l.timeout=120,c.nc&&l.setAttribute("nonce",c.nc),l.src=a(e);var d=new Error;u=function(t){l.onerror=l.onload=null,clearTimeout(h);var n=s[e];if(0!==n){if(n){var o=t&&("load"===t.type?"missing":t.type),r=t&&t.target&&t.target.src;d.message="Loading chunk "+e+" failed.\n("+o+": "+r+")",d.name="ChunkLoadError",d.type=o,d.request=r,n[1](d)}s[e]=void 0}};var h=setTimeout((function(){u({type:"timeout",target:l})}),12e4);l.onerror=l.onload=u,document.head.appendChild(l)}return Promise.all(t)},c.m=e,c.c=o,c.d=function(e,t,n){c.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:n})},c.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},c.t=function(e,t){if(1&t&&(e=c(e)),8&t)return e;if(4&t&&"object"===typeof e&&e&&e.__esModule)return e;var n=Object.create(null);if(c.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var o in e)c.d(n,o,function(t){return e[t]}.bind(null,o));return n},c.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return c.d(t,"a",t),t},c.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},c.p="/udist/",c.oe=function(e){throw console.error(e),e};var u=window["webpackJsonp"]=window["webpackJsonp"]||[],l=u.push.bind(u);u.push=t,u=u.slice();for(var d=0;d<u.length;d++)t(u[d]);var h=l;i.push([0,"chunk-vendors"]),n()})({0:function(e,t,n){e.exports=n("56d7")},"21af":function(e,t,n){},"2f14":function(e,t,n){"use strict";n("9dd9"),n("9a33"),n("2b45"),n("9e76");var o=n("e82a"),r=n.n(o);t["a"]={browserRedirect:function(){var e=navigator.userAgent.toLowerCase(),t="ipad"==e.match(/ipad/i),n="iphone os"==e.match(/iphone os/i),o="midp"==e.match(/midp/i),r="rv:*******"==e.match(/rv:*******/i),s="ucweb"==e.match(/ucweb/i),i="android"==e.match(/android/i),a="windows ce"==e.match(/windows ce/i),c="windows mobile"==e.match(/windows mobile/i);return t||n||o||r||s||i||a||c?"phone":"pc"},Encrypt:function(e,t,n){var o="",s="";t&&(o=r.a.enc.Latin1.parse(t),s=r.a.enc.Latin1.parse(n));var i=r.a.enc.Utf8.parse(e),a=r.a.AES.encrypt(i,o,{iv:s,mode:r.a.mode.CBC,padding:r.a.pad.ZeroPadding}),c=(a.toString(),a.toString());return c},Decrypt:function(e,t,n){var o="",s="";t&&(o=r.a.enc.Latin1.parse(t),s=r.a.enc.Latin1.parse(n));var i=r.a.AES.decrypt(e,o,{iv:s,mode:r.a.mode.CBC,padding:r.a.pad.ZeroPadding}),a=i.toString(r.a.enc.Utf8);return a.toString()},getUrlArg:function(e){var t=window.location.hash.split("?");if(1==t.length)return"";for(var n=t[1].split("&"),o=0;o<n.length;o++)if(n[o].split("=")[0]==e)return n[o].split("=")[1]},getUrlArgumentValue:function(e){var t=new RegExp("(^|&)".concat(e,"=([^&]*)(&|$)")),n=window.location.href.split("?")[1]||"",o=n.match(t);return null!=o?decodeURI(o[2]):null}}},3571:function(e,t,n){"use strict";var o=n("cfe9"),r=n.n(o);r.a},"404a":function(e,t,n){e.exports=n.p+"img/door-lb.png"},4122:function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return i}));var o=function(e,t,n){""===t||void 0==t?n(new Error("请输入账户名/手机号/邮箱")):n()},r=function(e,t,n){""===t||void 0==t?n(new Error("请输入密码")):n()},s=function(e,t,n){""===t?n(new Error("请输入密码")):/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/.test(t)?n():n(new Error("请输入6-16位字母和数字组成的密码"))},i=function(e,t,n){""===t||void 0==t?n(new Error("请输入验证码")):4!=t.length?n(new Error("请输入4位验证码")):n()}},"48c0":function(e,t,n){},"4ec3":function(e,t,n){"use strict";n.d(t,"k",(function(){return f})),n.d(t,"j",(function(){return g})),n.d(t,"p",(function(){return v})),n.d(t,"s",(function(){return b})),n.d(t,"u",(function(){return w})),n.d(t,"r",(function(){return y})),n.d(t,"f",(function(){return I})),n.d(t,"l",(function(){return C})),n.d(t,"q",(function(){return x})),n.d(t,"a",(function(){return _})),n.d(t,"b",(function(){return S})),n.d(t,"t",(function(){return P})),n.d(t,"o",(function(){return F})),n.d(t,"n",(function(){return k})),n.d(t,"d",(function(){return $})),n.d(t,"e",(function(){return O})),n.d(t,"g",(function(){return R})),n.d(t,"c",(function(){return T})),n.d(t,"i",(function(){return E})),n.d(t,"m",(function(){return N})),n.d(t,"h",(function(){return A}));n("9a33");var o={getCode:"/api/toGetUserInfo",getUserInfo:"/wxservice/api/getUserInfo",sendUserInfo:"/ufatsupms/weChat/login/H5",wxFirstBindPhone:"/ufatsupms/weChat/bind/phone",userBindPhone:"/ufatsupms/user/login/bind",getProductInfo:"/uauthentication/user/getProductInfo",h5Login:"/uauthentication/h5Login",untiedWechat:"/ufatsupms/weChat/bind/untiedWechat",bindWechat:"/ufatsupms/weChat/bind/bindWechat",createQrcode:"/ufatsupms/weChat/login/createQrcode",verificationCode:"/ufatsupms/weChat/login/verificationCode",sendCode:"/uauthentication/sendCode",sendCode_P:"/uauthentication/personal/sendCode",register:"/uauthentication/user/register",register_P:"/uauthentication/personal/user/register",forget:"/uauthentication/user/password/forget",forget_P:"/uauthentication/personal/user/password/forget",getProductAuthority:"/uauthentication/user/getProductAuthority",getSourceInfo:"/uauthentication/ent/getSourceInfo",editPassword:"/uauthentication/user/password/edit",getUserByToken:"/uauthentication/user/getUserByToken",phoneEmailBind:"/uauthentication/user/bind",getUserTypeByToken:"/uauthentication/user/getUserTypeByToken",userInfo:"/uauthentication/user/userInfo",getUserAgreement:"/uauthentication/getUserAgreement"},r=n("f753"),s=n.n(r),i=["/weChat"],a=["/ufatsupms/weChat"],c="production",u=function(e,t,n){return new Promise((function(o,r){s.a.post(d(e),t,n).then((function(e){o(e)}),(function(e){r("网络连接失败，请稍后重试")})).catch((function(e){r("网络连接失败，请稍后重试")}))}))},l=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{params:{},data:{}},t=arguments.length>1?arguments[1]:void 0;return new Promise((function(n,o){s.a.get(d(e.url),{params:e.params},t).then((function(e){n(e)})).catch((function(e){o(e)}))}))};function d(e){return void 0===e||"undefined"===e||null===e||""===e||"production"===c&&i.forEach((function(t,n){var o=e.indexOf(t);if(-1!=o)return e=e.substring(o+t.length,e.length),e=a[n]+e,e})),e}var h=n("51be"),m=n("2f14"),p="wx08abdb5f5e4361d5";function f(e,t,n){sessionStorage.removeItem("codePass");var o=window.location.href;o=-1==o.indexOf("?")?o:o.split("?")[0];var r=m["a"].getUrlArg("productId")?m["a"].getUrlArg("productId"):"",s=t?encodeURIComponent(o+"?type="+e+"&backUrl="+t+"&userStatus="+n+"&productId="+r):encodeURIComponent(o+"?type="+e+"&userStatus="+n+"&productId="+r),i="/api/toGetUserInfo?appid=".concat(p,"&type=").concat(e,"&path=").concat(s);window.parent.location.href=h["b"]+i}function g(e){return u(o.getUserInfo,e)}function v(e){return u(o.sendUserInfo,e)}function b(e){return u(o.userInfo,e)}function w(e){return u(o.wxFirstBindPhone,e)}function y(e){return u(o.userBindPhone,e)}function I(e){return u(o.getProductInfo,e)}function C(e){return u(o.h5Login,e)}function x(e){return l({url:o.untiedWechat,params:e})}function _(e){return u(o.bindWechat,e)}function S(e){return u(o.createQrcode,e)}function P(e){return u(o.verificationCode,e)}function F(e){return u(o.sendCode,e)}function k(e){return u(o.register,e)}function $(e){return u(o.forget,e)}function O(e){return u(o.getProductAuthority,e)}function R(e){return u(o.getSourceInfo,e)}function T(e){return u(o.editPassword,e)}function E(e){return u(o.getUserByToken,e)}function N(e){return u(o.phoneEmailBind,e)}function A(e){return u(o.getUserAgreement,e)}},"50d1":function(e,t,n){e.exports=n.p+"img/door-bg.png"},"51be":function(e,t,n){"use strict";n.d(t,"a",(function(){return o})),n.d(t,"b",(function(){return r}));var o="",r="",s=Object({NODE_ENV:"production",BASE_URL:"/udist/"});("development"===s.NODE_ENV||"production"===s.NODE_ENV||"test"===s.NODE_ENV||"pre"===s.VUE_APP_ENV)&&(o="",r="https://wdfp.5ifapiao.com/wxservice")},"55d4":function(e,t,n){"use strict";n.d(t,"b",(function(){return u})),n.d(t,"a",(function(){return l}));n("a464"),n("375c");var o,r=n("e93b"),s=n.n(r),i=0,a=function(){o=s.a.service({lock:!0,text:"加载中…",fullscreen:!0,background:"rgba(0, 0, 0, 0.7)"})},c=function(){o.close()},u=function(){0===i&&a(),i+=1},l=function(){i<=0||(i-=1,0===i&&c())}},"56d7":function(e,t,n){"use strict";n.r(t);n("e44b"),n("6648"),n("5f54"),n("f0e6");var o=n("0261"),r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"app"}},[n("transition",[n("keep-alive",[e.$route.meta.keepAlive?n("router-view"):e._e()],1)],1),n("transition",[e.$route.meta.keepAlive?e._e():n("router-view")],1)],1)},s=[],i={name:"App",data:function(){return{}}},a=i,c=n("e90a"),u=Object(c["a"])(a,r,s,!1,null,null,null),l=u.exports,d=(n("9e76"),n("163d"),n("9a33"),n("cc57"),n("b449"),n("17d6")),h=n("1bee"),m=n("38bc"),p=n.n(m),f=(n("70e7"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"common-web-login",class:[e.$store.state.product_door?"web-door":"web-common",1==e.$store.state.product_theme?"course-web-login":2==e.$store.state.product_theme?"laws-web-login":""]},[n("div",{directives:[{name:"wechat-title",rawName:"v-wechat-title",value:e.$route.meta.title,expression:"$route.meta.title"}]}),n("Carousel"),n("div",{key:e.viewId,staticClass:"web-box"},[n("div",{staticClass:"loginBox"},[n("div",{staticClass:"content"},[n("div",{staticClass:"nav"},[n("span",{class:{active:1===e.tabIndex},on:{click:function(t){return e.toggleIndex("uName")}}},[e._v("账户登录")]),n("span",{class:{active:2===e.tabIndex},on:{click:function(t){return e.toggleIndex("phone")}}},[e._v("手机号快速登录")]),e.$store.state.source_id?n("span",{class:{active:3===e.tabIndex},on:{click:function(t){return e.toggleIndex("scan")}}},[e._v("微信扫码登录")]):e._e()]),n("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,"status-icon":"",rules:e.rules,action:"",method:"post",enctype:"application/x-www-form-urlencoded"}},[1===e.tabIndex?n("div",[n("div",{staticClass:"elLine"},[n("i",{staticClass:"iconfont iconren-copy"}),n("el-form-item",{attrs:{prop:"userName",error:e.errorMsg.phone}},[n("el-input",{staticStyle:{display:"none"},attrs:{name:"username",autocomplete:"off"},model:{value:e.ruleForm.userName,callback:function(t){e.$set(e.ruleForm,"userName",t)},expression:"ruleForm.userName"}}),n("el-input",{attrs:{type:"text",placeholder:"请输入账户名/手机号/邮箱",clearable:""},on:{input:function(t){return e.filterChinese(1,t)}},model:{value:e.ruleForm.userName,callback:function(t){e.$set(e.ruleForm,"userName",t)},expression:"ruleForm.userName"}})],1)],1),n("div",{staticClass:"elLine"},[n("i",{staticClass:"iconfont iconicon-test"}),n("el-form-item",{attrs:{prop:"password",error:e.errorMsg.phonePass}},[n("el-input",{staticStyle:{display:"none"},attrs:{id:"password",type:"password",name:"password",autocomplete:"off"}}),n("el-input",{attrs:{type:e.elInputType,onfocus:"this.type='password'",autocomplete:"off",placeholder:"请输入密码",clearable:"",readonly:e.readonlyStatus},on:{input:function(t){return e.filterChinese(2,t)}},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}},[null!=e.ruleForm.password&&""!=e.ruleForm.password?n("i",{staticClass:"el-icon-view el-input__icon",attrs:{slot:"suffix"},on:{click:e.showPassword},slot:"suffix"}):e._e()])],1)],1)]):e._e(),2===e.tabIndex?n("div",[n("div",{staticClass:"elLine phone"},[n("i",{staticClass:"iconfont iconshouji1"}),n("span",{staticClass:"icon-span"},[e._v("+86")]),n("el-form-item",{attrs:{prop:"phone",error:e.errorMsg.yzmPhone}},[n("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入11位手机号",autocomplete:"new-password",clearable:""},on:{input:function(t){return e.lengthControl(1,t)}},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1)],1),n("div",{staticClass:"elLine code hideStatus"},[n("i",{staticClass:"iconfont iconanquan"}),n("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[n("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入验证码",autocomplete:"new-password"},on:{input:function(t){return e.lengthControl(2,t)}},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1),n("div",{staticClass:"yzm"},[0!=e.timer||1!=e.tabIndex&&!e.phonePass?n("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))]):n("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))])])],1)]):e._e(),3===e.tabIndex?n("div",{staticClass:"canvasBox"},[n("div",{staticClass:"canvas"},[e.refresh?n("div",{staticClass:"shade"},[n("p",[e._v("二维码已过期")]),n("span",{staticClass:"button solid_button",on:{click:function(t){return e.getQrcode("QRCodeLogin",1)}}},[e._v("点击刷新")])]):e._e(),e.canvas?n("canvas",{staticClass:"qrcode",attrs:{id:"QRCodeLogin"}}):e._e()]),""!=e.errorMsg.weChat?n("p",{staticClass:"error"},[e._v(e._s(e.errorMsg.weChat))]):e._e(),n("p",{staticClass:"tip"},[e._v("微信扫一扫登录快捷又安全")])]):e._e()]),n("div",{staticClass:"tools"},[1===e.tabIndex?n("div",{staticClass:"right"},[n("span",{staticClass:"aliRight",on:{click:e.goFindPwd}},[e._v("忘记密码")])]):e._e(),2===e.tabIndex?n("div",[n("div",{staticClass:"left"},[n("el-checkbox",{model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),n("p",{staticClass:"userAgree"},[e._v("\n                未注册的手机号将自动注册，且代表您同意\n                "),n("span",{on:{click:e.showAgreement}},[e._v("《用户协议》")])])],1)]):e._e(),3===e.tabIndex?n("div",{staticClass:"center"},[e._v("\n            未注册的微信将自动注册，且代表您同意\n            "),n("span",{on:{click:e.showAgreement}},[e._v("《用户协议》")])]):e._e()]),1==e.tabIndex||2==e.tabIndex?n("div",{staticClass:"login"},[e.unbind?n("button",[e._v("登 录")]):n("button",{on:{click:function(t){return e.Login("ruleForm")}}},[e._v("登 录")])]):e._e(),1==e.tabIndex||2==e.tabIndex?n("div",{staticClass:"register"},[n("span",{on:{click:e.goRegister}},[e._v("注册账户")])]):e._e()],1)])]),e.agreementStatus?n("Agreement",{on:{close:e.closeUserAgreement}}):e._e(),e.noVerifyPhone?n("Phone",{attrs:{userId:e.userId,userStatus:e.userStatus,userName:e.ruleForm.userName,tabIndex:e.tabIndex,from:e.from},on:{noPhoneClick:function(t){e.noVerifyPhone=!1}}}):e._e()],1)}),g=[],v=(n("ecf7"),n("986e")),b=(n("f548"),n("2b45"),n("55d4")),w=n("2837"),y=n.n(w),I=n("debc"),C=n.n(I),x=n("4122"),_=n("4ec3"),S={name:"Login",data:function(){var e=this,t=function(t,n,o){""==n||void 0==n?(e.phonePass=!1,o(new Error("请输入手机号"))):/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/.test(n)?(e.phonePass=!0,o()):(e.phonePass=!1,o(new Error("请输入正确的手机号")))};return{viewId:0,tabIndex:1,ruleForm:{userName:"",password:"",phone:"",code:""},rules:{password:[{validator:x["b"],trigger:"blur"}],userName:[{validator:x["d"],trigger:"blur"}],phone:[{validator:t,trigger:"blur"}],code:[{validator:x["a"],trigger:"blur"}]},errorMsg:{phone:"",yzmPhone:"",code:"",weChat:""},timer:0,codeTimer:void 0,elInputType:"text",unbind:!1,phonePass:!1,keyboardStatus:"text",readonlyStatus:!1,checked:!1}},computed:{toastText:function(){return 0===this.timer?"获取验证码":this.timer+"s"}},created:function(){var e=this;document.onkeypress=function(t){(1==e.tabIndex||2==e.tabIndex)&&window.event.keyCode}},mounted:function(){"phone"==this.$aes.browserRedirect()?this.keyboardStatus="number":this.keyboardStatus="text"},methods:{guideIntros:function(){this.$alert("<p>以企业为单位创建的账号，后续发生的交易、购买权益等归企业所有</p>","说明",{dangerouslyUseHTMLString:!0,center:!0,showClose:!1})},Login:function(e){var t=this;this.$refs[e].validate((function(e){return console.log(e),!!e&&(t.elInputType="password",t.checked||2!=t.tabIndex?(Object(b["b"])(),t.unbind=!0,void t.goLogin()):(t.$notify.closeAll(),void t.$notify({message:" 请仔细阅读用户协议并同意",showClose:!1,duration:2e3,type:"warning"})))}))},goLogin:function(e){var t=this,n="",o="";1==this.tabIndex?(o=this.userStatus?this.ruleForm.userName+"PP":this.ruleForm.userName+"CP",n={username:o}):2==this.tabIndex?(o=this.userStatus?this.ruleForm.phone+"PC":this.ruleForm.phone+"CC",n={code:this.ruleForm.code,username:o}):(o=this.userStatus?e+"PS":e+"CS",n={username:o});var r=localStorage.getItem("activityId");r={activityId:r||(activityUrlId||"")},n=Object.assign(n,r),Object(_["e"])(n).then((function(n){if(0==n.code){t.resetError();var r="";console.log(t.ruleForm),r=1==t.tabIndex?y.a.stringify({username:o,password:t.ruleForm.password}):2==t.tabIndex?y.a.stringify({username:o,password:t.ruleForm.code}):y.a.stringify({username:o,password:e}),console.log(r);var s=t;C.a.ajax({type:"post",headers:{"Content-type":"application/x-www-form-urlencoded",SavedRequest:"device_id=123&page=register2&client_id=fatc&response_type=code&redirect_uri=http%3A%2F%2Fwww.sso.com%2Fsso%2Fsso_index.html%3Fid%3D123%26name%3DMr.li%26age%3D12"},url:"/uauthentication/form",data:r,dataType:"json",async:!1,success:function(e){s.unbind=!1,Object(b["a"])(),0==e.code?(localStorage.removeItem("activityId"),s.$refs["ruleForm"].resetFields(),1==s.tabIndex&&(localStorage.setItem("u-last-userName",s.ruleForm.userName),localStorage.setItem("u-last-pass",s.$aes.Encrypt(s.ruleForm.password,"1234567887654321","1234567887654321"))),window.location.href="/uoauth/authorize?client_id="+e.data.client_id+"&response_type=code&redirect_uri="+e.data.redirect_uri):1==e.code?1==s.tabIndex?(s.errorMsg.phonePass=Math.random().toString(),s.$nextTick((function(){s.errorMsg.phonePass=e.msg}))):2==s.tabIndex?(s.errorMsg.code=Math.random().toString(),s.$nextTick((function(){s.errorMsg.code=e.msg}))):(s.errorMsg.weChat=Math.random().toString(),s.$nextTick((function(){s.errorMsg.weChat=e.msg}))):10==e.code?s.noVerifyPhone=!0:s.$notify({message:e.msg,showClose:!1,duration:2e3,type:"warning"})},error:function(){}})}else Object(b["a"])(),t.unbind=!1,1==t.tabIndex?(t.errorMsg.phone=Math.random().toString(),t.$nextTick((function(){t.errorMsg.phone=n.msg})),16==n.code&&t.channel(1,n.msg)):2==t.tabIndex?1==n.code?(t.errorMsg.code=Math.random().toString(),t.$nextTick((function(){t.errorMsg.code=n.msg}))):16==n.code?t.channel(2,n.msg):(t.errorMsg.yzmPhone=Math.random().toString(),t.$nextTick((function(){t.errorMsg.yzmPhone=n.msg}))):16==n.code?t.channel(3,n.msg):t.errorMsg.weChat="扫码登录失败，"+n.msg})).catch((function(e){setTimeout((function(){t.unbind=!1}),1500)}))},channel:function(e,t){if(-1!=t.indexOf("|")){var n=t.split("|");"phone"==this.$aes.browserRedirect()?1==e?this.errorMsg.phone="请在正确地址登录:"+n[1]:2==e?this.errorMsg.yzmPhone="请在正确地址登录:"+n[1]:this.errorMsg.weChat="请在正确地址登录:"+n[1]:1==e?this.errorMsg.phone=n[0]:2==e?this.errorMsg.yzmPhone=n[0]:this.errorMsg.weChat=n[0]}else 1==e?this.errorMsg.phone=t:2==e?this.errorMsg.yzmPhone=t:this.errorMsg.weChat=t},getCode:function(){var e=this;this.viewId+=1,this.timer=60,this.codeTimer=setInterval((function(){e.timer--,0===e.timer&&(clearInterval(e.codeTimer),e.timer=0)}),1e3);var t={mobile:this.ruleForm.phone,type:5,productId:this.$store.state.product_h5.productId,sourceId:this.$store.state.source_id_data};Object(_["o"])(t).then((function(t){e.handlerResult(t)})).catch((function(e){console.log("获取验证码error")}))},handlerResult:function(e){"0"==e.code?(this.errorMsg={phone:"",yzmPhone:"",code:"",weChat:""},this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3})):(this.errorMsg.code=e.msg,clearInterval(this.codeTimer),this.timer=0)},lengthControl:function(e,t){1==e?(this.ruleForm.phone=t.replace(/[\u4E00-\u9FA5]/g,""),t.length>11&&(this.ruleForm.phone=t.slice(0,11))):2==e&&(this.ruleForm.code=t.replace(/[\u4E00-\u9FA5]/g,""),t.length>4&&(this.ruleForm.code=t.slice(0,4)))},showPassword:function(){""==this.elInputType&&""!=this.ruleForm.password?this.elInputType="password":this.elInputType=""},toggleIndex:function(e){var t=this;clearTimeout(this.canvasTimeout),"uName"===e?(this.tabIndex=1,this.ruleForm={userName:"",password:""},this.reset(),this.judgeSave()):"phone"===e?(this.tabIndex=2,this.ruleForm={userName:"",password:"",phone:"",code:""},this.reset()):"scan"===e&&(this.tabIndex=3,this.$nextTick((function(){t.getQrcode("QRCodeLogin",1)})))},goFindPwd:function(){"phone"==this.$aes.browserRedirect()?this.$router.push({name:"h5FindPass",query:{uStatus:this.userStatus}}):this.$router.push({name:"FindPass",query:{uStatus:this.userStatus}})},goRegister:function(){"phone"==this.$aes.browserRedirect()?this.$router.push({name:"h5Register",query:{uStatus:this.userStatus}}):this.$router.push({name:"Register",query:{uStatus:this.userStatus}})},readFocus:function(){this.readonlyStatus=!1},reset:function(){var e=this;this.ws&&this.ws.close(),this.phonePass=!1,clearInterval(this.codeTimer),this.timer=0,this.codeTimer4Phone=0,this.$nextTick((function(){setTimeout((function(){e.$refs["ruleForm"].resetFields(),e.$refs["ruleForm"].clearValidate(),e.resetError(),e.ruleForm={userName:"",password:"",phone:"",code:""}}),300)}))},resetError:function(){this.errorMsg={phone:"",phonePass:"",code:"",yzmPhone:"",weChat:""}},judgeSave:function(){this.elInputType="password",this.ruleForm.userName=localStorage.getItem("u-last-userName");var e=localStorage.getItem("u-last-pass");this.ruleForm.password=e?this.$aes.Decrypt(e,"1234567887654321","1234567887654321"):""}},beforeRouteLeave:function(e,t,n){"/h5Agreement"!=e.path&&(this.readonlyStatus=!0,this.ruleForm={userName:"",password:"",phone:"",code:""},this.reset(),this.tabIndex=1),n()},beforeRouteEnter:function(e,t,n){n((function(n){console.log(n),console.log("登录页",n.userStatus),"/h5Agreement"!=t.path&&(n.readonlyStatus=!1,n.ruleForm={phone:"",code:""},void 0!=e.query.uStatus&&(n.userStatus=e.query.uStatus),n.ruleForm={userName:"",password:"",phone:"",code:""},localStorage.getItem("u-last-userName")&&n.judgeSave(),void 0!==n.$refs["ruleForm"]&&n.$refs["ruleForm"].resetFields(),n.$refs["ruleForm"].resetFields())}))}},P=n("b58d"),F=n("d221"),k=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{staticClass:"agreement-web",attrs:{visible:e.showAgreement,title:"用户协议",center:"","show-close":!0,"close-on-click-modal":!1,top:"11vh"},on:{"update:visible":function(t){e.showAgreement=t},close:e.close}},[n("div",{staticClass:"wrap",domProps:{innerHTML:e._s(e.count)}})])},$=[],O={name:"agreement",data:function(){return{showAgreement:!0,company:"",count:""}},created:function(){this.company=this.$store.state.company_name?this.$store.state.company_name:"【本公司】",this.getUserAgreement()},methods:{close:function(){this.$emit("close")},getUserAgreement:function(){var e=this,t={type:1,sourceId:this.$store.state.product_h5.sourceId};Object(_["h"])(t).then((function(t){console.log(t),"0"==t.code&&(e.count=t.data.agreement)}))}}},R=O,T=(n("3571"),Object(c["a"])(R,k,$,!1,null,null,null)),E=T.exports,N=n("f6f8"),A={mixins:[S,P["a"]],data:function(){return{noVerifyPhone:!1,from:"first",userId:""}},components:{Carousel:F["a"],Agreement:E,Phone:N["a"]},mounted:function(){var e=this;v["a"].$on("QRCodeLogin",(function(t){e.Websocket_Login(t)})),v["a"].$on("verifyFirstSuccess",(function(t){e.goLogin(t.phone)}))},methods:{Websocket_Login:function(e){console.log("扫码登录返回结果了，开始处理！"),console.log(e);var t=e.ev.code,n=e.ev.data;"0"==t&&n.phone?this.goLogin(n.phone):"1"==t?(this.errorMsg.weChat="",this.refresh=!0):("4"==t||"0"==t&&!n.phone)&&(this.userId=n.userId,localStorage.setItem("unionid",n.unionid),this.noVerifyPhone=!0)}}},j=A,M=Object(c["a"])(j,f,g,!1,null,null,null),U=M.exports,L=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"common-web-login",class:[e.$store.state.product_door?"web-door":"web-common",1==e.$store.state.product_theme?"course-web-login":2==e.$store.state.product_theme?"laws-web-login":""]},[n("div",{directives:[{name:"wechat-title",rawName:"v-wechat-title",value:e.$route.meta.title,expression:"$route.meta.title"}]}),n("Carousel"),n("div",{key:e.viewId,staticClass:"web-box"},[n("div",{staticClass:"loginBox"},[n("div",{staticClass:"content"},[n("div",{staticClass:"nav"},[n("span",{class:{active:1===e.tabIndex},on:{click:function(t){return e.toggleIndex("phone")}}},[e._v("手机号注册")]),n("span",{class:{active:2===e.tabIndex},on:{click:function(t){return e.toggleIndex("email")}}},[e._v("邮箱注册")])]),n("el-form",{ref:"rRuleForm",attrs:{model:e.rRuleForm,rules:e.rules,"status-icon":"",action:"",method:"post",enctype:"application/x-www-form-urlencoded"}},[1===e.tabIndex?n("div",{staticClass:"elLine phone"},[n("i",{staticClass:"iconfont iconshouji1"}),n("span",{staticClass:"icon-span"},[e._v("+86")]),n("el-form-item",{attrs:{prop:"phoneName",error:e.errorMsg.phone}},[n("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入11位手机号",clearable:""},on:{input:function(t){return e.lengthControl(1,t)}},model:{value:e.rRuleForm.phoneName,callback:function(t){e.$set(e.rRuleForm,"phoneName",t)},expression:"rRuleForm.phoneName"}})],1)],1):e._e(),2===e.tabIndex?n("div",{staticClass:"elLine"},[n("i",{staticClass:"iconfont icontongzhi- iconsmall"}),n("el-form-item",{attrs:{prop:"email",error:e.errorMsg.email}},[n("el-input",{attrs:{type:"email",placeholder:"请输入邮箱",clearable:""},on:{input:function(t){return e.filterChinese(5,t)}},model:{value:e.rRuleForm.email,callback:function(t){e.$set(e.rRuleForm,"email",t)},expression:"rRuleForm.email"}})],1)],1):e._e(),n("div",{staticClass:"elLine code hideStatus"},[n("i",{staticClass:"iconfont iconanquan"}),n("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[n("el-input",{staticStyle:{display:"none"},attrs:{name:"yzm",type:"password",autocomplete:"new-password"},model:{value:e.rRuleForm.code1,callback:function(t){e.$set(e.rRuleForm,"code1",t)},expression:"rRuleForm.code1"}}),n("el-input",{attrs:{name:"yzm",placeholder:"请输入验证码"},on:{input:function(t){return e.lengthControl(2,t)}},model:{value:e.rRuleForm.code,callback:function(t){e.$set(e.rRuleForm,"code",t)},expression:"rRuleForm.code"}})],1),1==e.tabIndex?n("div",{staticClass:"yzm"},[0==e.timerPhone&&e.phonePass?n("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):n("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e(),2==e.tabIndex?n("div",{staticClass:"yzm"},[0==e.timerEmail&&e.emailPass?n("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):n("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e()],1),n("div",{staticClass:"elLine"},[n("i",{staticClass:"iconfont iconicon-test"}),n("el-form-item",{attrs:{prop:"pass"}},[n("el-input",{attrs:{placeholder:"6 - 16位密码，区分大小写",maxlength:"16",clearable:"",type:"text",onfocus:"this.type='password'",readonly:e.readonlyStatus1},on:{input:function(t){return e.filterChinese(3,t)}},model:{value:e.rRuleForm.pass,callback:function(t){e.$set(e.rRuleForm,"pass",t)},expression:"rRuleForm.pass"}})],1)],1),n("div",{staticClass:"elLine"},[n("i",{staticClass:"iconfont iconicon-test"}),n("el-form-item",{attrs:{prop:"confirmPass"}},[n("el-input",{attrs:{placeholder:"请确认密码",maxlength:"16",clearable:"",autocomplete:"off",type:"text",onfocus:"this.type='password'",readonly:e.readonlyStatus2},on:{input:function(t){return e.filterChinese(4,t)}},model:{value:e.rRuleForm.confirmPass,callback:function(t){e.$set(e.rRuleForm,"confirmPass",t)},expression:"rRuleForm.confirmPass"}})],1)],1)]),n("div",{staticClass:"tools"},[n("div",{staticClass:"left"},[n("el-checkbox",{model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),n("p",{staticClass:"userAgree"},[e._v("同意"),n("span",{on:{click:e.showAgreement}},[e._v("《用户协议》")])])],1)]),n("div",{staticClass:"login"},[e.unbind?n("button",{staticClass:"minBtn"},[e._v("注 册")]):n("button",{staticClass:"minBtn",on:{click:function(t){return e.goRegister("rRuleForm")}}},[e._v("注 册")]),n("span",{on:{click:e.goLogin}},[e._v("使用已有账号登录")])])],1)])]),e.agreementStatus?n("Agreement",{on:{close:e.closeUserAgreement}}):e._e()],1)},B=[],D=n("92ce"),z={mixins:[D["a"],P["a"]],components:{Carousel:F["a"],Agreement:E}},J=z,q=Object(c["a"])(J,L,B,!1,null,null,null),V=q.exports,Q=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"common-h5-login",class:1==e.$store.state.product_theme?"course-h5-login":2==e.$store.state.product_theme?"rule-h5-login":""},[o("p",{staticClass:"loginTitle"},[e._v("登录"),o("span",{domProps:{textContent:e._s("企业用户")}})]),o("div",{key:e.viewId,staticClass:"h5-box"},[o("div",{staticClass:"nav"},[o("span",{class:{active:1===e.tabIndex},on:{click:function(t){return e.toggleIndex("uName")}}},[e._v("账户登录")]),o("span",{class:{active:2===e.tabIndex},on:{click:function(t){return e.toggleIndex("phone")}}},[e._v("验证码登录")])]),o("el-form",{ref:"ruleForm",staticClass:"form-left-width",attrs:{model:e.ruleForm,"status-icon":"",rules:e.rules,action:"",method:"post",enctype:"application/x-www-form-urlencoded"}},[1===e.tabIndex?o("div",{staticClass:"phone"},[o("span",{staticClass:"l-t"},[e._v("账   号：")]),o("el-form-item",{attrs:{error:e.errorMsg.phone}},[o("el-input",{attrs:{type:"text",placeholder:"请输入账户名/手机号/邮箱",autocomplete:"new-password",clearable:""},on:{input:function(t){return e.filterChinese(1,t)}},model:{value:e.ruleForm.userName,callback:function(t){e.$set(e.ruleForm,"userName",t)},expression:"ruleForm.userName"}})],1)],1):e._e(),1===e.tabIndex?o("div",{staticClass:"password"},[o("span",{staticClass:"l-t"},[e._v("密   码：")]),o("el-form-item",{attrs:{prop:"password",error:e.errorMsg.phonePass}},[o("el-input",{staticStyle:{display:"none"},attrs:{type:e.elInputType,placeholder:"输入密码",autocomplete:"new-password",clearable:""},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}}),o("el-input",{attrs:{type:e.elInputType,placeholder:"输入密码",autocomplete:"new-password",clearable:"",readonly:e.readonlyStatus},on:{input:function(t){return e.filterChinese(2,t)}},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}},[null!=e.ruleForm.password&&""!=e.ruleForm.password?o("i",{staticClass:"el-icon-view el-input__icon",attrs:{slot:"suffix"},on:{click:e.showPassword},slot:"suffix"}):e._e()])],1)],1):e._e(),2===e.tabIndex?o("div",{staticClass:"phone"},[o("span",{staticClass:"l-t"},[e._v("手机号：")]),o("el-form-item",{attrs:{prop:"phone",error:e.errorMsg.yzmPhone}},[o("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入11位手机号",autocomplete:"new-password",clearable:""},on:{input:function(t){return e.lengthControl(1)}},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1)],1):e._e(),2===e.tabIndex?o("div",{staticClass:"code hideStatus"},[o("span",{staticClass:"l-t"},[e._v("验证码：")]),o("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[o("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入验证码",autocomplete:"new-password"},on:{input:function(t){return e.lengthControl(2)}},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1),o("div",{staticClass:"yzm h5"},[0!=e.timer||1!=e.tabIndex&&!e.phonePass?o("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))]):o("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))])])],1):e._e()]),o("div",{staticClass:"tools"},[o("div",{staticClass:"l-and-r"},[o("span",{on:{click:e.goFindPwd}},[e._v("忘记密码")]),o("span",{on:{click:e.goRegister}},[e._v("注册")])])]),o("div",{staticClass:"login forLogin"},[e.unbind?o("button",[e._v("确定")]):o("button",{on:{click:function(t){return e.Login("ruleForm")}}},[e._v("确定")])]),2===e.tabIndex?o("div",{staticClass:"treaty"},[o("el-checkbox",{model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),o("div",{staticClass:"userAgree"},[e._v("未注册的手机号将自动注册，且代表您同意"),o("span",{on:{click:function(t){return e.goAgreement(e.tabIndex)}}},[e._v("《用户协议》")])])],1):e._e(),e.$store.state.source_id?o("el-divider",{staticClass:"divider-login",attrs:{"content-position":"center"}},[e._v("其他账户登录")]):e._e(),e.$store.state.source_id?o("div",{staticClass:"footer"},[o("img",{staticClass:"wechatlogin",attrs:{src:n("b208")},on:{click:function(t){return e.wechatlogin(!1)}}})]):e._e()],1)])},H=[],W=(n("48c0"),{mixins:[S,P["a"]],created:function(){var e=this.$aes.getUrlArgumentValue("code"),t=this.$aes.getUrlArgumentValue("type"),n=this.$aes.getUrlArgumentValue("userStatus");n="true"===n||!0===n,this.userStatus=n,e&&(sessionStorage.setItem("hasLoginCode",!0),t=t||"1",this.takeCodeBy(e,t)),console.log(this.$store.state)},methods:{takeCodeBy:function(){var e=Object(d["a"])(regeneratorRuntime.mark((function e(t,n){var o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getUserInfo(t,n);case 2:o=e.sent,console.log(o),0==o.subscribe?(Object(b["a"])(),Object(_["k"])("2",!1,this.userStatus)):o&&this.sendUserInfo(o);case 5:case"end":return e.stop()}}),e,this)})));function t(t,n){return e.apply(this,arguments)}return t}(),sendUserInfo:function(e){var t=this;e.userType=1==this.userStatus?"P":"C",console.log(e),Object(_["p"])(e).then((function(n){console.log("将微信信息发送给后端结果",n),"0"==n.code&&n.data.phone?t.autoLogin(n.data.phone):(Object(b["a"])(),t.$router.replace({name:"h5Phone",query:{userId:n.data.userId,unionid:n.data.unionid,userType:e.userType}}))})).catch((function(e){Object(b["a"])()}))},autoLogin:function(){var e=Object(d["a"])(regeneratorRuntime.mark((function e(t){var n,o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getProductInfo(this.$aes.getUrlArgumentValue("backUrl"),!0);case 2:n=e.sent,n&&(o=1==this.userStatus?"P":"C",this.h5Login(n,t,o));case 4:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},beforeRouteEnter:function(e,t,n){n((function(e){t.name&&("h5Agreement"==t.name?e.tabIndex=e.$store.state.userTab||1:e.getProductInfo("",!0))}))},beforeRouteLeave:function(e,t,n){"h5Agreement"!=e.name&&this.$store.commit("USER_TAB",1),n()}}),Y=W,G=Object(c["a"])(Y,Q,H,!1,null,null,null),Z=G.exports,K=n("2f14"),X=n("8876");o["default"].use(X["a"]);var ee=new X["a"].Store({state:{bindStatus:JSON.parse(window.sessionStorage.getItem("u-bindStatus"))||"",product:JSON.parse(window.sessionStorage.getItem("u-product"))||"",product_h5:JSON.parse(window.sessionStorage.getItem("u-product-h5"))||"",userTab:JSON.parse(window.sessionStorage.getItem("u-userTab"))||1,product_door:JSON.parse(window.sessionStorage.getItem("u-product-door"))||!1,source_id:JSON.parse(window.sessionStorage.getItem("u-source-id"))||!1,source_id_data:JSON.parse(window.sessionStorage.getItem("u-sourceId-data"))||"",product_theme:JSON.parse(window.localStorage.getItem("u-product-theme"))||"",company_name:JSON.parse(window.sessionStorage.getItem("u-company-name"))||""},mutations:{BINDING_STATUS:function(e,t){window.sessionStorage.setItem("u-bindStatus",JSON.stringify(t)),e.bindStatus=t},PRODUCT_INFO:function(e,t){window.sessionStorage.setItem("u-product",JSON.stringify(t)),e.product=t},PRODUCT_H5:function(e,t){window.sessionStorage.setItem("u-product-h5",JSON.stringify(t)),e.product_h5=t},USER_TAB:function(e,t){window.sessionStorage.setItem("u-userTab",JSON.stringify(t)),e.userTab=t},PRODUCT_DOOR:function(e,t){window.sessionStorage.setItem("u-product-door",JSON.stringify(t)),e.product_door=t},SOURCE_ID:function(e,t){window.sessionStorage.setItem("u-source-id",JSON.stringify(t)),e.source_id=t},SOURCEID_DATA:function(e,t){window.sessionStorage.setItem("u-sourceId-data",JSON.stringify(t)),e.source_id_data=t},PRODUCT_THEME:function(e,t){window.localStorage.setItem("u-product-theme",JSON.stringify(t)),e.product_theme=t},COMPANY_NAME:function(e,t){window.sessionStorage.setItem("u-company-name",JSON.stringify(t)),e.company_name=t}},actions:{}});o["default"].use(h["a"]),p.a.configure({easing:"ease",speed:500,showSpinner:!1,trickleSpeed:200,minimum:.3});var te=new h["a"]({base:"/udist/",routes:[{path:"/login",name:"Login",component:U,meta:{title:"用户中心系统-用户登录",keepAlive:!1}},{path:"/register",name:"Register",component:V,meta:{title:"用户中心系统-用户注册",keepAlive:!0}},{path:"/findPass",name:"FindPass",component:function(){return n.e("pc-login").then(n.bind(null,"1928"))},meta:{title:"用户中心系统-找回密码",keepAlive:!0}},{path:"/setting",name:"setting",component:function(){return n.e("pc-setting").then(n.bind(null,"9db2"))}},{path:"/h5Login",name:"h5Login",meta:{title:"登录",isPhone:!0,keepAlive:!0},component:Z},{path:"/h5Register",name:"h5Register",meta:{title:"注册",isPhone:!0,keepAlive:!0},component:function(){return n.e("h5-login").then(n.bind(null,"4968"))}},{path:"/h5FindPass",name:"h5FindPass",meta:{title:"忘记密码",isPhone:!0},component:function(){return n.e("h5-login").then(n.bind(null,"d41a"))}},{path:"/h5Agreement",name:"h5Agreement",meta:{title:"《用户协议》",isPhone:!0},component:function(){return n.e("h5-login").then(n.bind(null,"4cbd"))}},{path:"/h5Setting",name:"h5Setting",meta:{title:"设置",isPhone:!0,keepAlive:!0},component:function(){return n.e("h5-setting").then(n.bind(null,"3c20"))}},{path:"/h5Phone",name:"h5Phone",meta:{title:"绑定手机号",isPhone:!0},component:function(){return n.e("h5-setting").then(n.bind(null,"fbeb"))}},{path:"/h5Email",name:"h5Email",meta:{title:"绑定邮箱",isPhone:!0},component:function(){return n.e("h5-setting").then(n.bind(null,"ec90"))}},{path:"/h5cPass",name:"h5cPass",meta:{title:"修改密码",isPhone:!0},component:function(){return n.e("h5-setting").then(n.bind(null,"0b88"))}},{path:"/transfer",name:"transfer",meta:{isPhone:!0},component:function(){return n.e("h5-login").then(n.bind(null,"6bde"))}},{path:"/activity",name:"activity",component:function(){return n.e("chunk-2d2134fb").then(n.bind(null,"abae"))}}]});function ne(){ee.commit("PRODUCT_THEME",""),ee.commit("PRODUCT_DOOR",!1),ee.commit("SOURCE_ID",!1),ee.commit("PRODUCT_H5","")}function oe(){var e=sessionStorage.getItem("u-window-history");return!(e&&!(e&&window.history.length-Number(e)>=2))}function re(e,t){if(e.name||-1!=window.location.href.indexOf("h5Setting"))t();else{var n=ee.state.product_h5,o={redirectURI:n&&n.redirectURI?n.redirectURI:""};Object(_["f"])(o).then((function(e){if("0"==e.code){var n=e.data,o=n.productId;ee.commit("PRODUCT_DOOR","27d8f87724924b3089cc6fbd7d3684c7"==o),ee.commit("SOURCE_ID","*********"==n.sourceId||"*********"==n.sourceId),ee.commit("PRODUCT_H5",n);var r="";switch(o){case"ac5463eec58f4fca80d1a00802581ddc":r=1;break;case"036a01086f1342d48f9af733a4d521d6":r=2;break;default:break}ee.commit("PRODUCT_THEME",r),t()}else t()})).catch((function(e){t()})),se()}}function se(){Object(_["g"])().then((function(e){ee.commit("COMPANY_NAME","0"==e.code?e.data.entName:"【本公司】")})).catch((function(e){}))}te.beforeEach(function(){var e=Object(d["a"])(regeneratorRuntime.mark((function e(t,n,o){var r,s,i,a;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(console.log("历史长度",window.history.length),r=!!oe(),sessionStorage.setItem("u-window-history",window.history.length),p.a.start(),s=window.location.href,"phone"!=K["a"].browserRedirect()){e.next=14;break}if("h5Login"==t.name||""!=s.split("/#/")[1]&&(-1!=s.indexOf("h5")||-1!=s.indexOf("transfer")||-1!=s.indexOf("activity"))){e.next=11;break}return o("/h5Login"),e.abrupt("return");case 11:re(n,(function(){o()}));case 12:e.next=35;break;case 14:if(n.name||"register"!=page){e.next=24;break}if("Register"==t.name){e.next=20;break}return o("/register"),e.abrupt("return");case 20:r&&ne(),re(n,(function(){o()}));case 22:e.next=35;break;case 24:if("Login"==t.name||"setting"==t.name||"activity"==t.name||""!=s.split("/#/")[1]&&(-1!=s.indexOf("login")||-1!=s.indexOf("register")||-1!=s.indexOf("findPass")||-1!=s.indexOf("setting")||-1!=s.indexOf("activity"))){e.next=34;break}if(i=window.parent.document.querySelector("iframe"),!i||-1==i.src.indexOf("setting")){e.next=31;break}return o("/setting"),e.abrupt("return");case 31:o("/login");case 32:e.next=35;break;case 34:-1==s.indexOf("setting")&&-1==s.indexOf("activity")?-1==s.indexOf("login")?(a=window.parent.document.querySelector("iframe"),a&&a.src.indexOf("setting"),o()):(r&&ne(),re(n,(function(){o()}))):o();case 35:case"end":return e.stop()}}),e)})));return function(t,n,o){return e.apply(this,arguments)}}()),te.afterEach(function(){var e=Object(d["a"])(regeneratorRuntime.mark((function e(t,n){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:p.a.done(),document.title!=t.meta.title&&(document.title=t.meta.title||"");case 2:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}());var ie=te,ae=(n("1ce0"),n("375c"),n("daa7")),ce=n.n(ae),ue=n("f753"),le=n.n(ue),de=n("51be"),he=n("e04f"),me=n.n(he),pe="x-access-token",fe=new Date((new Date).getTime()+72e5);function ge(){return me.a.get(pe)}function ve(e){return me.a.set(pe,e,{expires:fe})}le.a.defaults.timeout=3e4,le.a.defaults.headers.post["Content-Type"]="application/json",le.a.defaults.validateStatus=function(e){return/^(2|3)\d{2}$/.test(e)},le.a.defaults.withCredentials=!0,Object.assign(le.a.defaults,{baseURL:de["a"]}),le.a.interceptors.request.use((function(e){return ge()&&""!=ge()?e.headers["Authorization"]="Bearer "+ge():e.headers["Authorization"]="Basic bGItYW9zcDpsYi1hb3Nw",e}),(function(e){return Promise.reject(e)})),le.a.interceptors.response.use((function(e){if(e.headers&&"application/octet-stream"===e.headers["content-type"]||"application/x-msdownload"===e.headers["content-type"])return e;var t=e.data,n=t.code,o=t.message||"未知错误";return void 0!=t&&null!=t?t:"0002"===n?(ce.a.confirm(o,"确定退出",{confirmButtonText:"重新登录",cancelButtonText:"取消",type:"warning"}).then(function(){var e=Object(d["a"])(regeneratorRuntime.mark((function e(t){return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:store.dispatch("FedLogOut").then((function(){location.reload()}));case 1:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()).catch((function(){})),Promise.reject(new Error(o))):Promise.reject(new Error(o))}),(function(e){return void 0!=e.response?("Network Error"===e.message||404===e.response.status||e.response.status,401===e.response.status&&(e.message="token已失效"),Promise.reject(new Error(e))):Promise.reject(new Error("网络连接失败，请稍后重试"))}));var be=le.a,we=n("4d3a"),ye=n.n(we),Ie=(n("513d"),n("785d")),Ce=n.n(Ie),xe=(n("994f"),n("a05e")),_e=n.n(xe),Se=(n("a464"),n("e93b")),Pe=n.n(Se),Fe=(n("da43"),n("e3b4")),ke=n.n(Fe),$e=(n("b565"),n("8afa")),Oe=n.n($e),Re=(n("c619"),n("a2ea")),Te=n.n(Re),Ee=(n("a408"),n("5162")),Ne=n.n(Ee),Ae=(n("ea85"),n("5448")),je=n.n(Ae),Me=(n("3a64"),n("8ad7")),Ue=n.n(Me),Le=(n("9436"),n("a716")),Be=n.n(Le),De=(n("a48b"),n("7c6d")),ze=n.n(De),Je=(n("dde0"),n("a3cf")),qe=n.n(Je),Ve=(n("5d5c"),n("df62")),Qe=n.n(Ve),He=(n("ab77"),n("c77e")),We=n.n(He),Ye=(n("c405"),n("ec2d")),Ge=n.n(Ye),Ze=(n("7404"),n("78de")),Ke=n.n(Ze),Xe=(n("665b"),n("9c26")),et=n.n(Xe),tt=(n("ac7f"),n("8508")),nt=n.n(tt),ot=(n("9dde"),n("a742")),rt=n.n(ot),st=(n("d7a0"),n("9504")),it=n.n(st),at=(n("1b10"),n("f52a")),ct=n.n(at),ut=(n("e794"),n("a6bb")),lt=n.n(ut);n("46c6");o["default"].use(lt.a),o["default"].use(ct.a),o["default"].use(it.a),o["default"].use(rt.a),o["default"].use(nt.a),o["default"].use(et.a),o["default"].use(Ke.a),o["default"].use(Ge.a),o["default"].use(We.a),o["default"].use(Qe.a),o["default"].use(qe.a),o["default"].use(ze.a),o["default"].use(Be.a),o["default"].use(Ue.a),o["default"].use(je.a),o["default"].use(Ne.a),o["default"].use(Te.a),o["default"].use(Oe.a),o["default"].use(ke.a),o["default"].prototype.$loading=Pe.a.service,o["default"].prototype.$alert=ce.a.alert,o["default"].prototype.$confirm=ce.a.confirm,o["default"].prototype.$notify=_e.a,o["default"].prototype.$message=Ce.a;n("21af"),n("b37a");o["default"].prototype.axios=be,o["default"].prototype.$aes=K["a"],o["default"].use(ye.a),o["default"].config.productionTip=!1,localStorage.getItem("for-set-token")!=K["a"].getUrlArg("token")?sessionStorage.setItem("u-first-In",!0):sessionStorage.setItem("u-first-In",!1),K["a"].getUrlArg("token")&&(ve(K["a"].getUrlArg("token")),localStorage.setItem("for-set-token",K["a"].getUrlArg("token"))),new o["default"]({router:ie,store:ee,render:function(e){return e(l)}}).$mount("#app")},"92ce":function(e,t,n){"use strict";n("f548");var o=n("b27f"),r=(n("e04f"),n("2837")),s=n.n(r),i=n("debc"),a=n.n(i),c=n("55d4"),u=n("4122"),l=n("4ec3");t["a"]={name:"Register",data:function(){var e=this,t=function(t,n,o){""===n||void 0==n?(e.phonePass=!1,o(new Error("请输入手机号"))):/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/.test(n)?(e.phonePass=!0,o()):(e.phonePass=!1,o(new Error("请输入正确的手机号")))},n=function(t,n,o){""===n||void 0==n?(e.emailPass=!1,o(new Error("请输入邮箱"))):/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/.test(n)?(e.emailPass=!0,o()):(e.emailPass=!1,o(new Error("请输入正确的邮箱")))},o=function(t,n,o){""===n||void 0==n?o(new Error("请再次输入密码")):n!==e.rRuleForm.pass?o(new Error("两次输入密码不一致")):o()};return{viewId:0,userStatus:"",checked:!1,tabIndex:1,timerPhone:0,timerEmail:0,codeTimer4Phone:void 0,codeTimer4Email:void 0,rRuleForm:{phoneName:"",email:"",code:"",pass:"",confirmPass:""},rules:{phoneName:[{validator:t,trigger:"blur"}],email:[{validator:n,trigger:"blur"}],code:[{validator:u["a"],trigger:"blur"}],pass:[{validator:u["c"],trigger:"blur"}],confirmPass:[{validator:o,trigger:"blur"}]},phoneMid:!1,emailMid:!1,errorMsg:{code:"",phone:"",email:""},elInputType1:"text",elInputType2:"text",elStatus1:!1,elStatus2:!1,emailPass:!1,phonePass:!1,unbind:!1,keyboardStatus:"text",readonlyStatus1:!1,readonlyStatus2:!1}},computed:{toastText:function(){return 1===this.tabIndex?0===this.timerPhone?"获取验证码":this.timerPhone+"s":0===this.timerEmail?"获取验证码":this.timerEmail+"s"}},mounted:function(){"phone"==this.$aes.browserRedirect()?this.keyboardStatus="number":this.keyboardStatus="text";var e=this;Object(o["setTimeout"])((function(){e.rRuleForm.code=""}),300)},methods:{test:function(){console.log(1111)},goRegister:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;if(t.checked){Object(c["b"])(),t.unbind=!0;var n="";n=1==t.tabIndex?{mobile:t.rRuleForm.phoneName,code:t.rRuleForm.code,password:t.$aes.Encrypt(t.rRuleForm.pass,"1234567887654321","1234567887654321")}:{email:t.rRuleForm.email,code:t.rRuleForm.code,password:t.$aes.Encrypt(t.rRuleForm.pass,"1234567887654321","1234567887654321")};var r=localStorage.getItem("activityId");r={activityId:r||(activityUrlId||"")},n=Object.assign(n,r),Object(l["n"])(n).then((function(e){t.registerHandle(e)})).catch((function(e){Object(o["setTimeout"])((function(){Object(c["a"])(),t.unbind=!1,t.$notify.error({message:"注册失败！",showClose:!1,duration:2e3})}),1500)}))}else t.$notify.closeAll(),t.$notify({message:"请仔细阅读用户协议并同意",showClose:!1,duration:2e3,type:"warning"})}))},registerHandle:function(e){var t=this;0==e.code?(Object(c["a"])(),this.$notify({message:"注册成功！",showClose:!1,type:"success",duration:2e3}),Object(o["setTimeout"])((function(){localStorage.removeItem("activityId");var e="";e=1==t.tabIndex?t.userStatus?t.rRuleForm.phoneName+"PP":t.rRuleForm.phoneName+"CP":t.userStatus?t.rRuleForm.email+"PP":t.rRuleForm.email+"CP";var n=s.a.stringify({username:e,password:t.rRuleForm.pass}),o=t;a.a.ajax({type:"post",headers:{"Content-type":"application/x-www-form-urlencoded"},url:"/uauthentication/form",data:n,dataType:"json",async:!1,success:function(e){0==e.code?(localStorage.setItem("u-last-userName",1==o.tabIndex?o.rRuleForm.phoneName:o.rRuleForm.email),localStorage.setItem("u-last-pass",o.$aes.Encrypt(o.rRuleForm.pass,"1234567887654321","1234567887654321")),window.location.href="/uoauth/authorize?client_id="+e.data.client_id+"&response_type=code&redirect_uri="+e.data.redirect_uri):o.$notify({message:"自动登录失败，请进入登录页重试！",showClose:!1,duration:2e3,type:"warning"})},error:function(){}})}),1800),this.unbind=!1):(Object(c["a"])(),this.viewId+=1,this.unbind=!1,this.errorMsg.code=e.msg)},getCode:function(){var e=this;if(this.errorMsg.code="",1==this.tabIndex){if(this.phoneMid)return;this.timerPhone=60,this.phoneMid=!0,this.codeTimer4Phone=setInterval((function(){e.timerPhone--,0===e.timerPhone&&(e.phonePass=!0,e.resetPhone())}),1e3)}else{if(this.emailMid)return;this.timerEmail=60,this.emailMid=!0,this.codeTimer4Email=setInterval((function(){e.timerEmail--,0===e.timerEmail&&(e.emailPass=!0,e.resetEmail())}),1e3)}var t="";t=1==this.tabIndex?{mobile:this.rRuleForm.phoneName,type:2}:{email:this.rRuleForm.email,type:2};var n={productId:this.$store.state.product_h5.productId,sourceId:this.$store.state.source_id_data};t=Object.assign(t,n),Object(l["o"])(t).then((function(t){e.handlerResult(t)})).catch((function(e){console.log("获取验证码error")}))},handlerResult:function(e){this.viewId+=1,"0"==e.code?(this.errorMsg={code:"",phone:"",email:""},this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3})):1==this.tabIndex?("2"==e.code?this.errorMsg.phone=e.msg:this.errorMsg.code=e.msg,this.resetPhone()):("2"==e.code?this.errorMsg.email=e.msg:this.errorMsg.code=e.msg,this.resetEmail())},lengthControl:function(e,t){console.log(this.tabIndex),console.log(this.timerPhone),console.log(this.phonePass),console.log(this.rRuleForm.code),console.log(this.rRuleForm.pass),1==e?(this.rRuleForm.phoneName=t.replace(/[\u4E00-\u9FA5]/g,""),t.length>11&&(this.rRuleForm.phoneName=t.slice(0,11))):2==e&&(this.rRuleForm.code=t.replace(/[\u4E00-\u9FA5]/g,""),t.length>4&&(this.rRuleForm.code=t.slice(0,4)))},showPassword:function(e){1==e?(this.elStatus1=!this.elStatus1,""!=this.rRuleForm.pass&&("text"==this.elInputType1?this.elInputType1="password":this.elInputType1="text")):(this.elStatus2=!this.elStatus2,""!=this.rRuleForm.confirmPass&&("text"==this.elInputType2?this.elInputType2="password":this.elInputType2="text"))},toggleIndex:function(e){this.elInputType1="password",this.elInputType2="password",this.errorMsg.code="",this.rRuleForm={phoneName:"",email:"",code:"",pass:"",confirmPass:""},"phone"===e?(this.reset1(),this.tabIndex=1):(this.reset2(),this.tabIndex=2),this.$refs["rRuleForm"].resetFields()},goLogin:function(){var e="Login";"phone"==this.$aes.browserRedirect()&&(e="h5Login"),this.$router.push({name:e,query:{uStatus:this.userStatus}})},readFocus:function(e){1==e?this.readonlyStatus1=!1:this.readonlyStatus2=!1},resetPhone:function(){clearInterval(this.codeTimer4Phone),this.timerPhone=0,this.codeTimer4Phone=0,this.phoneMid=!1},resetEmail:function(){clearInterval(this.codeTimer4Email),this.timerEmail=0,this.codeTimer4Email=0,this.emailMid=!1},reset1:function(){this.emailPass=!1,this.resetPhone(),this.elInputType1="text",this.$refs["rRuleForm"].resetFields()},reset2:function(){this.phonePass=!1,this.resetEmail(),this.elInputType2="text",this.$refs["rRuleForm"].resetFields()}},beforeRouteLeave:function(e,t,n){"/h5Agreement"!=e.path&&(this.elInputType1="text",this.elInputType2="text",this.readonlyStatus1=!0,this.readonlyStatus2=!0,this.reset1(),this.reset2(),this.tabIndex=1),n()},beforeRouteEnter:function(e,t,n){n((function(n){console.log("注册页",n.userStatus),"/h5Agreement"!=t.path&&(n.readonlyStatus1=!1,n.readonlyStatus2=!1,n.errorMsg.code="",n.rRuleForm={phoneName:"",email:"",code:"",pass:"",confirmPass:""},n.userStatus=e.query.uStatus)}))}}},"986e":function(e,t,n){"use strict";var o=n("0261"),r=new o["default"];t["a"]=r},b208:function(e,t,n){e.exports=n.p+"img/wechat.png"},b37a:function(e,t,n){},b58d:function(e,t,n){"use strict";n("f548"),n("cc57");var o=n("4ec3"),r=n("55d4"),s=n("5618"),i=n.n(s),a=n("986e");t["a"]={data:function(){return{show:!1,sceneId:"",refresh:!1,canvas:!0,canvasTimeout:"",agreementStatus:!1,loading:"",userStatus:!1,ws:""}},created:function(){},mounted:function(){},methods:{wechatlogin:function(e){console.log(e),void 0!=this.userStatus?(localStorage.setItem("userStatus",this.userStatus),Object(o["k"])("1",e,this.userStatus)):Object(o["k"])("1",e)},getUserInfo:function(e,t){var n=this;return new Promise((function(s,i){try{Object(r["b"])();var a={code:e,type:t,gzh_appid:"wx08abdb5f5e4361d5"};Object(o["j"])(a).then((function(e){console.log("拿code取用户信息",e),s(e)})).catch((function(e){setTimeout((function(){n.unbind=!1}),1500)}))}catch(c){i(c)}}))},h5Login:function(e,t,n,s){var i=this,a=this.$route.query.userType;a=void 0==a?n:a;var c={username:t+a+"C",clientId:"fatc",clientSecret:"fatc",productId:e.productId,sourceId:e.sourceId};Object(o["l"])(c).then((function(t){if(console.log("自动登录返回",t),Object(r["a"])(),"0"==t.code){localStorage.removeItem("activityId");var n=t.data.token,o=e.redirectURI;o=s||o,o=decodeURIComponent(o),o=-1==o.indexOf("?")?o+"?token="+n:o+"&token="+n,console.log(o),window.location.href=o}else i.$notify({message:t.msg,showClose:!1,duration:2e3,type:"warning"})})).catch((function(e){Object(r["a"])(),i.$notify.error({message:"自动登录失败",showClose:!1,duration:2e3})}))},getProductInfo:function(e,t){var n=this,r=this.$store.state.product_h5;console.log(r);try{return new Promise((function(s,i){if(e||t){var a={redirectURI:e||(r&&""!=r?r.redirectURI:"")};try{Object(o["f"])(a).then((function(e){if(console.log("获取产品信息",e),"0"==e.code){var t=e.data;n.$store.commit("SOURCEID_DATA",t.sourceId),n.$store.commit("PRODUCT_THEME","ac5463eec58f4fca80d1a00802581ddc"==t.productId?1:"036a01086f1342d48f9af733a4d521d6"==t.productId?2:""),n.$store.commit("PRODUCT_DOOR","27d8f87724924b3089cc6fbd7d3684c7"==t.productId),n.$store.commit("SOURCE_ID","*********"==t.sourceId||"*********"==t.sourceId),n.$store.commit("PRODUCT_H5",t)}s(e.data)})).catch((function(e){}))}catch(c){console.log(c)}}else s(r)}))}catch(s){reject(s)}},getBindingStatus:function(){var e=this,t=this.$store.state.bindStatus;return new Promise((function(n,s){if(t&&!sessionStorage.getItem("u-first-In"))e.show=!0,n(t);else try{Object(r["b"])(),Object(o["i"])({token:localStorage.getItem("for-set-token")}).then((function(t){e.show=!0,"0"==t.code?(sessionStorage.setItem("u-first-In",!1),e.$store.commit("SOURCEID_DATA",t.data.sourceId),e.$store.commit("BINDING_STATUS",t.data),e.$store.commit("SOURCE_ID","*********"==t.data.sourceId||"*********"==t.data.sourceId),n(t.data)):(e.$notify.closeAll(),e.$notify({message:t.msg,showClose:!1,duration:2e3,type:"warning"})),Object(r["a"])()})).catch((function(t){e.show=!0,Object(r["a"])()}))}catch(i){e.show=!0,s(i)}}))},createQrcode:function(e,t){return new Promise((function(n,r){try{var s={QRCodeType:e,userType:t};console.log(s),Object(o["b"])(s).then((function(e){n(e)})).catch((function(e){}))}catch(i){r(i)}}))},getQrcode:function(e,t){var n=this;clearTimeout(this.canvasTimeout),this.$nextTick((function(){n.loading=n.$loading({fullscreen:!1,spinner:"el-icon-loading",background:"rgba(0, 0, 0, 0)",target:document.querySelector(".canvas")}),n.canvas=!1,n.refresh=!1,console.log(n.userStatus);var o=1!=t?n.userType:n.userStatus?"P":"C";n.createQrcode(t,o).then((function(t){if(console.log(t),0==t.code){var o=t.data;n.sceneId=o.sceneId,n.canvas=!0,n.$nextTick((function(){if(e){var t=document.getElementById(e),r=n;i.a.toCanvas(t,o.url,(function(e){r.loading.close(),r.canvasTimeout=setTimeout((function(){r.errorMsg.weChat="",r.refresh=!0}),18e4),console.log(e)})),n.Websocket(e)}}))}else n.$notify({message:t.msg,showClose:!1,duration:2e3,type:"warning"})}),(function(e){}))}))},Websocket:function(e){var t="http:"==window.location.protocol?"ws://":"wss://",n=window.location.hostname,o=window.location.port?":"+window.location.port:"";console.log(t+n+o+"/ufatsupms/webSocket/"+this.sceneId),this.ws=new WebSocket(t+n+o+"/ufatsupms/webSocket/"+this.sceneId),this.ws.onopen=function(){},this.ws.onmessage=function(t){console.log("from",JSON.parse(t.data)),a["a"].$emit(e,{ev:JSON.parse(t.data)})},this.ws.onclose=function(e){console.log("websocket-close")},this.ws.onerror=function(e){console.log("websocket-error")}},verifyCode:function(e){return 1==e?"二维码已过期":2==e?"用户信息unionid为空":4==e?"需绑定手机号才可登录":5==e?"绑定新微信号":6==e?"该微信号已绑定其他账号，请使用新的微信号进行绑定":7==e?"解绑微信号验证通过":8==e?"请使用已绑定的微信号扫码验证！":9==e?"该手机号已在其他渠道注册，请选择其他手机号绑定":10==e?"手机号用户已绑定微信号":void 0},userChange:function(){var e=this;"Login"==this.$router.history.current.name&&3==this.tabIndex&&this.$nextTick((function(){e.getQrcode("QRCodeLogin",1)})),this.userStatus=!this.userStatus},showAgreement:function(){this.agreementStatus=!0},closeUserAgreement:function(){this.agreementStatus=!1},goAgreement:function(e){this.$store.commit("USER_TAB",e),this.$router.push({name:"h5Agreement"})},filterChinese:function(e,t){var n=t.replace(/[\u4E00-\u9FA5]/g,"");1===e?this.ruleForm.userName=n:2===e?this.ruleForm.password=n:3===e?this.rRuleForm.pass=n:4===e?this.rRuleForm.confirmPass=n:5===e?this.rRuleForm.email=n:6===e?this.ruleForm.pass=n:7===e?this.ruleForm.confirmPass=n:8===e?this.ruleForm.email=n:9===e?this.phoneForm.phone=n:10===e?this.phoneForm.code=n:11===e?this.emailForm.email=n:12===e&&(this.emailForm.code=n)}}}},cfe9:function(e,t,n){},d221:function(e,t,n){"use strict";var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("div",{staticClass:"loginIcon"},[""!=e.logo?n("img",{attrs:{src:e.logo}}):e._e(),n("span",[e._v(e._s(e.productName))])]),e.$store.state.product_h5.banner.length>0?n("div",{staticClass:"loginItem"},[n("el-carousel",{attrs:{"indicator-position":"outside",arrow:"never"}},e._l(e.banner,(function(e){return n("el-carousel-item",{key:e},[n("img",{attrs:{src:e}})])})),1)],1):n("div",{staticClass:"loginDoor"},[n("img",{staticClass:"doorBG",attrs:{src:e.door.bg,alt:""}}),n("img",{staticClass:"doorLB",attrs:{src:e.door.lb,alt:""}}),n("img",{staticClass:"doorRT",attrs:{src:e.door.rt,alt:""}})])])},r=[],s=n("b58d"),i={mixins:[s["a"]],name:"carousel",data:function(){return{logo:"",productName:"",banner:[],door:{bg:n("50d1"),lb:n("404a"),rt:n("f5e0")}}},created:function(){var e=this.$store.state.product_h5;this.logo=e.logo,this.productName=e.productName,this.banner=e.banner}},a=i,c=n("e90a"),u=Object(c["a"])(a,o,r,!1,null,null,null);t["a"]=u.exports},ecf7:function(e,t,n){},ef7a:function(e,t,n){},f5e0:function(e,t,n){e.exports=n.p+"img/door-rt.png"},f6f8:function(e,t,n){"use strict";var o=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"account-set",class:e.classAccount},[n("el-dialog",{staticClass:"modifyPhoneDialog",attrs:{width:"450px",center:"","close-on-click-modal":!1,title:e.phoneTitle,top:"32vh",visible:e.modifyPhoneDialog},on:{"update:visible":function(t){e.modifyPhoneDialog=t},open:function(t){return e.resetPhone()},close:function(t){return e.noBindClick("phoneForm")}}},[n("el-form",{key:e.viewId,ref:"phoneForm",staticClass:"phoneForm",attrs:{model:e.phoneForm,rules:e.phoneRules}},["first"==e.from?n("el-form-item",{attrs:{label:"手机号：",prop:"phone","label-width":"56px",error:e.errorPhoneMsg.phone}},[n("el-input",{attrs:{maxlength:"11",placeholder:"请填写要绑定的手机号",clearable:""},on:{input:function(t){return e.filterChinese(9,t)}},model:{value:e.phoneForm.phone,callback:function(t){e.$set(e.phoneForm,"phone",t)},expression:"phoneForm.phone"}})],1):e._e(),"wechat"==e.from?n("el-form-item",{attrs:{label:"手机号：","label-width":"56px"}},[n("span",{staticStyle:{"text-align":"left"}},[e._v(e._s(e.phoneForm.phone))])]):e._e(),n("el-form-item",{attrs:{label:"验证码：",prop:"code","label-width":"56px",error:e.errorPhoneMsg.code}},[n("el-input",{staticClass:"code",attrs:{maxlength:"4",placeholder:"输入验证码"},on:{input:function(t){return e.filterChinese(10,t)}},model:{value:e.phoneForm.code,callback:function(t){e.$set(e.phoneForm,"code",t)},expression:"phoneForm.code"}}),"first"==e.from?n("div",{staticStyle:{display:"inline-block"}},[0==e.timerPhone&&e.phonePass?n("span",{staticClass:"codeBtn",on:{click:function(t){return e.getYzmCode(1)}}},[e._v(e._s(e.toastText4Phone))]):n("span",{staticClass:"codeBtn disable"},[e._v(e._s(e.toastText4Phone))])]):e._e(),"wechat"==e.from?n("div",{staticStyle:{display:"inline-block"}},[0!=e.timerPhone?n("span",{staticClass:"codeBtn"},[e._v(e._s(e.toastText4Phone))]):n("span",{staticClass:"codeBtn",on:{click:function(t){return e.getYzmCode(1)}}},[e._v(e._s(e.toastText4Phone))])]):e._e()],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("span",{staticClass:"button line_button",on:{click:function(t){return e.noBindClick("phoneForm")}}},[e._v("取消")]),n("span",{staticClass:"button solid_button",staticStyle:{"margin-left":"16px"},on:{click:function(t){return e.okBindClick("phoneForm",1)}}},[e._v("确定")])])],1)],1)},r=[],s=(n("b449"),n("17d6")),i=(n("ef7a"),n("55d4")),a=n("4122"),c=n("b58d"),u=n("986e"),l=n("4ec3"),d={mixins:[c["a"]],props:["from","userStatus","userId","tabIndex","userName"],data:function(){var e=this,t=function(t,n,o){""===n?(e.phonePass=!1,o(new Error("请输入手机号"))):/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/.test(n)?(e.phonePass=!0,o()):(e.phonePass=!1,o(new Error("请输入正确的手机号")))};return{modifyPhoneDialog:!0,timerPhone:0,codeTimer4Phone:void 0,phoneForm:{phone:"",code:""},phoneRules:{phone:[{validator:t,trigger:"blur"}],code:[{validator:a["a"],trigger:"blur"}]},isBindPhone:!1,phoneTitle:this.isBindPhone?"更换手机号":"绑定手机号",phoneDefault:"",phoneMid:!1,refPhone:{},errorPhoneMsg:{phone:"",code:""},phonePass:!1,unbind:!1,viewId:0,productId:"",classAccount:""}},computed:{toastText4Phone:function(){return 0===this.timerPhone?"获取验证码":this.timerPhone+"s"}},created:function(){var e=this.$store.state.product_theme;switch(e){case 1:this.classAccount="account-course-set";break;case 2:this.classAccount="account-laws-set";break;default:break}console.log(this.$store.state.bindStatus),"wechat"==this.from&&(this.phoneForm.phone=this.$store.state.bindStatus.phone)},methods:{modifyPhone:function(){this.modifyPhoneDialog=!0,this.timerPhone=0,clearInterval(this.codeTimer4Phone)},noBindClick:function(e){this.phoneForm={phone:"",code:""},this.phonePass=!1,this.$refs[e].resetFields(),this.$emit("noPhoneClick")},okBindClick:function(e,t){var n=this;this.$refs[e].validate((function(e){if(!e)return!1;n.unbind=!0,Object(i["b"])();try{1==n.tabIndex||""==n.tabIndex?n.userBindPhone():"first"==n.from?n.changeBind():n.verifyBind()}catch(t){Object(i["a"])()}}))},userBindPhone:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,n=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t={mobile:this.phoneForm.phone,code:this.phoneForm.code,username:this.userName},Object(l["r"])(t).then((function(e){Object(i["a"])(),"0"==e.code?u["a"].$emit("verifyFirstSuccess",{phone:n.phoneForm.phone}):n.errorPhoneMsg.code=e.msg})).catch((function(e){Object(i["a"])()}));case 2:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),changeBind:function(){var e=Object(s["a"])(regeneratorRuntime.mark((function e(){var t,n,o,r,s=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log("当前手机弹框的用户状态是",this.userStatus),t="first"==this.from?this.userStatus?"P":"C":this.$store.state.bindStatus.userType,e.next=4,this.getProductInfo();case 4:n=e.sent,n&&(o={productName:n.productName,version:n.version,productId:n.productId,moblie:this.phoneForm.phone,code:this.phoneForm.code,userType:t,unionid:localStorage.getItem("unionid"),sourceId:n.sourceId,userId:this.userId},r=localStorage.getItem("activityId"),r={activityId:r||(activityUrlId||"")},o=Object.assign(o,r),Object(l["u"])(o).then((function(e){Object(i["a"])(),"0"==e.code?u["a"].$emit("verifyFirstSuccess",{phone:s.phoneForm.phone}):s.errorPhoneMsg.code=e.msg})).catch((function(e){Object(i["a"])()})));case 6:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),verifyBind:function(){var e=this,t={mobile:this.phoneForm.phone,code:this.phoneForm.code};Object(l["t"])(t).then((function(t){"0"==t.code?u["a"].$emit("verifySuccess"):e.errorPhoneMsg.code=t.msg,Object(i["a"])()})).catch((function(e){Object(i["a"])()}))},getYzmCode:function(e){var t=this;"first"==this.from?this.userStatus:this.$store.state.bindStatus.userType;if(this.viewId+=1,!this.phoneMid){this.timerPhone=60,this.phoneMid=!0,this.codeTimer4Phone=setInterval((function(){t.timerPhone--,0===t.timerPhone&&(clearInterval(t.codeTimer4Phone),t.timerPhone=0,t.phoneMid=!1)}),1e3);try{this.errorPhoneMsg={phone:"",code:""};var n={mobile:this.phoneForm.phone,type:"first"==this.from?6:7,productId:this.productId,sourceId:this.$store.state.source_id_data};Object(l["o"])(n).then((function(n){t.handlerResult(n,e)})).catch((function(e){console.log("获取验证码error")}))}catch(o){}}},handlerResult:function(e,t){0==e.code?this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3}):(this.viewId+=1,1==t&&(clearInterval(this.codeTimer4Phone),this.timerPhone=0,this.phoneMid=!1,4==e.code?this.errorPhoneMsg.code=e.msg:this.errorPhoneMsg.phone=e.msg))},resetPhone:function(){this.phoneMid=!1,this.refPhone=this.$refs,void 0!=this.refPhone.phoneForm&&this.refPhone.phoneForm.resetFields()}}},h=d,m=n("e90a"),p=Object(m["a"])(h,o,r,!1,null,null,null);t["a"]=p.exports}});