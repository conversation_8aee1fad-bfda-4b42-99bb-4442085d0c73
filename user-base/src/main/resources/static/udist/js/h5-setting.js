(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["h5-setting"],{"0b88":function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"common-h5-login h5-set",class:1==e.$store.state.product_theme?"course-h5-login":2==e.$store.state.product_theme?"rule-h5-login":""},[s("div",{directives:[{name:"wechat-title",rawName:"v-wechat-title",value:e.hasPassStatus?e.$route.meta.title:"设置密码",expression:"hasPassStatus?$route.meta.title:'设置密码'"}]}),e.hasPassStatus?e._e():s("p",{staticClass:"setTips"},[e._v("为保证安全，请及时设置密码")]),s("div",{key:e.viewId,staticClass:"cPass-box h5-box"},[s("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules}},[e.hasPassStatus?s("div",{staticClass:"formInput l-tt"},[s("span",{staticClass:"l-t"},[e._v("原密码：")]),s("el-form-item",{attrs:{prop:"password",error:e.errorMsg1}},[s("el-input",{attrs:{type:"password",autocomplete:"new-password","show-password":"",placeholder:"请输入原密码",clearable:""},on:{input:function(t){return e.filterChinese(1,t)}},model:{value:e.ruleForm.password,callback:function(t){e.$set(e.ruleForm,"password",t)},expression:"ruleForm.password"}})],1)],1):e._e(),s("div",{staticClass:"formInput l-tt"},[s("span",{staticClass:"l-t",domProps:{textContent:e._s(e.hasPassStatus?"新密码：":"密码：")}}),s("el-form-item",{attrs:{prop:"newPassword",error:e.errorMsg2}},[s("el-input",{attrs:{type:"password",autocomplete:"new-password","show-password":"",placeholder:e.hasPassStatus?"请输入新密码":"请输入密码",clearable:""},model:{value:e.ruleForm.newPassword,callback:function(t){e.$set(e.ruleForm,"newPassword",t)},expression:"ruleForm.newPassword"}})],1)],1),s("div",{staticClass:"formInput l-tt"},[s("span",{staticClass:"l-t",domProps:{textContent:e._s(e.hasPassStatus?"确认新密码：":"确认密码：")}}),s("el-form-item",{attrs:{prop:"surepassword"}},[s("el-input",{attrs:{type:"password",autocomplete:"new-password","show-password":"",placeholder:e.hasPassStatus?"请再次输入新密码":"请再次输入密码",clearable:""},on:{input:function(t){return e.filterChinese(2,t)}},model:{value:e.ruleForm.surepassword,callback:function(t){e.$set(e.ruleForm,"surepassword",t)},expression:"ruleForm.surepassword"}})],1)],1)]),s("p",{staticClass:"passTips"},[e._v("密码长度为6-16位，须包含字母和数字，字母区分大小写")])],1),s("div",{staticClass:"h5-box"},[s("div",{staticClass:"login",on:{click:function(t){return e.changePassword("ruleForm")}}},[s("button",[e._v("确定")])])])])},r=[],i=(s("f548"),s("4a5b"),s("55d4")),n=s("4ec3"),a={name:"ChangePassword",data:function(){var e=this,t=function(e,t,s){""==t?s(new Error("请输入原密码")):s()},s=function(t,s,o){""==s?o(new Error(e.hasPassStatus?"请输入新密码":"请输入密码")):/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{6,16}$/.test(s)?o():o(new Error(e.hasPassStatus?"请输入6-16位字母和数字组成的新密码":"请输入6-16位字母和数字组成的密码"))},o=function(t,s,o){""==s?o(new Error(e.hasPassStatus?"请输入确认新密码":"请输入确认密码")):s!=e.ruleForm.newPassword?o(new Error("再次输入密码不正确")):o()};return{hasPassStatus:!1,viewId:0,ruleForm:{password:"",newPassword:"",surepassword:""},rules:{password:[{validator:t,trigger:"blur"}],newPassword:[{validator:s,trigger:"blur"}],surepassword:[{validator:o,trigger:"blur"}]},token:"",errorMsg1:"",errorMsg2:""}},computed:{},created:function(){},methods:{changePassword:function(e){var t=this;this.errorMsg1="",this.errorMsg2="",this.$refs[e].validate((function(e){e&&(Object(i["b"])(),Object(n["c"])({password:t.$aes.Encrypt(t.ruleForm.password,"1234567887654321","1234567887654321"),newPassword:t.$aes.Encrypt(t.ruleForm.newPassword,"1234567887654321","1234567887654321"),token:localStorage.getItem("for-set-token")}).then((function(e){Object(i["a"])(),"0"==e.code?(t.$store.commit("BINDING_STATUS",""),t.$notify({message:"修改成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){t.$router.go(-1)}),2800)):(t.viewId+=1,t.hasPassStatus?t.errorMsg1=e.msg:t.errorMsg2=e.msg)})).catch((function(e){})))}))},reset:function(){this.ruleForm={password:"",newPassword:"",surepassword:""},this.errorMsg1="",this.errorMsg2="",this.$refs["ruleForm"].resetFields()},filterChinese:function(e,t){var s=t.replace(/[\u4E00-\u9FA5]/g,"");1===e?this.ruleForm.password=s:2===e&&(this.ruleForm.surepassword=s)}},beforeRouteEnter:function(e,t,s){s((function(t){t.hasPassStatus=e.query.hasBind}))},beforeRouteLeave:function(e,t,s){this.reset(),s()}},c=a,l=s("e90a"),u=Object(l["a"])(c,o,r,!1,null,null,null);t["default"]=u.exports},1642:function(e,t,s){},"3c20":function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"common-h5-login box-set",class:[1==e.$store.state.product_theme?"course-set":2==e.$store.state.product_theme?"rule-set":""]},[o("div",{directives:[{name:"wechat-title",rawName:"v-wechat-title",value:e.$route.meta.title,expression:"$route.meta.title"}]}),e.$store.state.source_id&&e.setshow?o("ul",e._l(e.list,(function(t,r){return o("li",{key:r,on:{click:function(s){return e.forSetting(t,r)}}},[o("span",[o("i",{staticClass:"iconfont",class:t.icon})]),o("span",[e._v(e._s(t.name))]),o("div",[o("span",[e._v(e._s(0==r||1==r?""!=t.bind?t.bind:"未绑定":2==r?t.bind?"已绑定":"未绑定":""))]),o("img",{attrs:{src:s("fa4b")}})])])})),0):e._e(),!e.$store.state.source_id&&e.setshow?o("ul",e._l(e.list,(function(t,r){return 2!=r?o("li",{key:r,on:{click:function(s){return e.forSetting(t,r)}}},[o("span",[o("i",{staticClass:"iconfont",class:t.icon})]),o("span",[e._v(e._s(t.name))]),o("div",[o("span",[e._v(e._s(0==r||1==r?""!=t.bind?t.bind:"未绑定":""))]),o("img",{attrs:{src:s("fa4b")}})])]):e._e()})),0):e._e()])},r=[],i=(s("cc57"),s("b449"),s("17d6")),n=(s("4a5b"),s("55d4")),a=s("b58d"),c=s("4ec3"),l={mixins:[a["a"]],name:"Setting",data:function(){return{userType:"",userId:"",list:[{icon:"iconshouji",name:"手机",bind:""},{icon:"iconyouxiang",name:"邮箱",bind:""},{icon:"iconweixin",name:"微信",bind:""},{icon:"iconxiugaimima",name:"修改密码",bind:""}],productId:"",setshow:!1}},computed:{},created:function(){var e=this.$aes.getUrlArg("productId");this.$store.commit("PRODUCT_THEME","ac5463eec58f4fca80d1a00802581ddc"==e?1:"036a01086f1342d48f9af733a4d521d6"==e?2:""),console.log(this.$store.state);var t=this.$aes.getUrlArgumentValue("code"),s=this.$aes.getUrlArgumentValue("type");t&&!sessionStorage.getItem("codePass")&&(s=s||"1",this.takeCodeBy(t,s))},methods:{takeCodeBy:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(t,s){var o;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getUserInfo(t,s);case 2:o=e.sent,0==o.subscribe?(Object(n["a"])(),Object(c["k"])("2")):this.bindWechat(o);case 4:case"end":return e.stop()}}),e,this)})));function t(t,s){return e.apply(this,arguments)}return t}(),init:function(){var e=this;this.getBindingStatus().then((function(t){t&&(e.setshow=!0,e.userType=t.userType,e.userId=t.userId,e.$set(e.list[0],"bind",t.phone?t.phone:""),e.$set(e.list[1],"bind",t.email?t.email:""),e.$set(e.list[2],"bind",t.wecaht.bind),e.$set(e.list[3],"bind",t.password?t.password:""))})).catch((function(){e.setshow=!0}))},forSetting:function(e,t){var s=this;console.log(e),console.log(t);var o=["/h5Phone","h5Email","","h5cPass"];2!=t?(console.log(e.bind),this.$router.push({path:o[t],query:{hasBind:e.bind,userType:this.userType}})):e.bind?this.$confirm("确定要解除账号与微信的关联吗？","解除绑定",{dangerouslyUseHTMLString:!0,center:!0,showClose:!1,cancelButtonText:"取消",confirmButtonText:"解除绑定"}).then((function(){Object(n["b"])(),s.unbindWechat()})).catch((function(e){})):this.wechatlogin()},bindWechat:function(e){var t=this;e.token=localStorage.getItem("for-set-token"),Object(c["a"])(e).then((function(e){sessionStorage.setItem("codePass",!0),Object(n["a"])(),"6"==e.code?t.$alert("<p>此微信已绑定其他账号，无法与当前账号绑定</p>","温馨提示",{dangerouslyUseHTMLString:!0,center:!0,showClose:!1}):(t.$store.commit("BINDING_STATUS",""),t.$notify({message:"绑定成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){t.init()}),2e3))})).catch((function(e){Object(n["a"])(),t.$notify.error({message:"绑定失败",showClose:!1,duration:2e3})}))},unbindWechat:function(){var e=this;Object(c["q"])({userId:this.userId}).then((function(t){Object(n["a"])(),"0"==t.code?(e.$store.commit("BINDING_STATUS",""),e.$notify({message:"解绑成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){e.init()}),2e3)):e.$notify({message:"解绑失败",showClose:!1,duration:2e3,type:"warning"})})).catch((function(t){Object(n["a"])(),e.$notify.error({message:"解绑失败",showClose:!1,duration:2e3})}))}},beforeRouteEnter:function(e,t,s){s((function(e){var s=e.$aes.getUrlArg("productId");e.$store.commit("PRODUCT_THEME","ac5463eec58f4fca80d1a00802581ddc"==s?1:"036a01086f1342d48f9af733a4d521d6"==s?2:""),t.name&&-1!=t.name.indexOf("h5")||e.$store.commit("BINDING_STATUS",""),e.init()}))}},u=l,d=(s("c8e4"),s("e90a")),h=Object(d["a"])(u,o,r,!1,null,"08639e84",null);t["default"]=h.exports},"4a5b":function(e,t,s){},c8e4:function(e,t,s){"use strict";var o=s("1642"),r=s.n(o);r.a},ec90:function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"common-h5-login h5-set",class:1==e.$store.state.product_theme?"course-h5-login":2==e.$store.state.product_theme?"rule-h5-login":""},[""==e.hasBindEmail?s("p",{staticClass:"setTips"},[e._v("绑定邮箱后，可使用新邮箱进行登录及获取信息")]):s("div",{staticClass:"setTips"},[s("p",[e._v("当前绑定邮箱："+e._s(e.hasBindEmail))]),s("p",[e._v("更换邮箱后，可使用新的邮箱进行登录及获取信息")])]),s("div",{key:e.viewId,staticClass:"cPass-box h5-box"},[s("el-form",{ref:"ruleForm",staticClass:"form-left-width",attrs:{model:e.ruleForm,rules:e.rules,"status-icon":"",action:"",method:"post",enctype:"application/x-www-form-urlencoded"}},[s("div",{staticClass:"email"},[s("span",{staticClass:"l-t"},[e._v("邮   箱：")]),s("el-form-item",{attrs:{prop:"email",error:e.errorMsg.email}},[s("el-input",{attrs:{type:"email",placeholder:""==e.hasBindEmail?"请填写要绑定的邮箱":"请填写要绑定的新邮箱",clearable:""},on:{input:function(t){return e.filterChinese(t)}},model:{value:e.ruleForm.email,callback:function(t){e.$set(e.ruleForm,"email",t)},expression:"ruleForm.email"}})],1)],1),s("div",{staticClass:"code hideStatus"},[s("span",{staticClass:"l-t"},[e._v("验证码：")]),s("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[s("el-input",{attrs:{type:"number",placeholder:"请输入验证码"},on:{input:e.lengthControl},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1),s("div",{staticClass:"yzm"},[0==e.timerEmail&&e.emailPass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])])],1)])],1),s("div",{staticClass:"h5-box"},[s("div",{staticClass:"login"},[s("button",{on:{click:function(t){return e.save("ruleForm")}}},[e._v("确定")])])])])},r=[],i=(s("f548"),s("4a5b"),s("55d4")),n=s("4ec3"),a={data:function(){var e=this,t=function(t,s,o){""===s||void 0==s?(e.emailPass=!1,o(new Error("请输入邮箱"))):/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/.test(s)?(e.emailPass=!0,o()):(e.emailPass=!1,o(new Error("请输入正确的邮箱")))},s=function(e,t,s){""===t||void 0==t?s(new Error("请输入验证码")):4!=t.length?s(new Error("请输入4位验证码")):s()};return{hasBindEmail:"",viewId:0,ruleForm:{email:"",code:""},rules:{email:[{validator:t,trigger:"blur"}],code:[{validator:s,trigger:"blur"}]},errorMsg:{email:"",code:""},codeTimer4Email:void 0,timerEmail:0,emailPass:!1,emailMid:!1}},computed:{toastText:function(){return 0===this.timerEmail?"获取验证码":this.timerEmail+"s"}},methods:{save:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(i["b"])();try{var s={email:t.ruleForm.email,code:t.ruleForm.code,token:localStorage.getItem("for-set-token")};Object(n["m"])(s).then((function(e){Object(i["a"])(),"0"==e.code?(t.$store.commit("BINDING_STATUS",""),t.$notify({message:"绑定成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){t.$router.go(-1)}),2800)):(t.viewId+=1,t.errorMsg.code=e.msg)})).catch((function(e){Object(i["a"])()}))}catch(o){Object(i["a"])()}}))},getCode:function(){var e=this;if(this.errorMsg.code="",!this.emailMid){this.timerEmail=60,this.emailMid=!0,this.codeTimer4Email=setInterval((function(){e.timerEmail--,0===e.timerEmail&&(e.emailMid=!1,clearInterval(e.codeTimer4Email),e.emailPass=!0,e.timerEmail=0)}),1e3);var t={email:this.ruleForm.email,type:4,sourceId:this.$store.state.source_id_data};Object(n["o"])(t).then((function(t){e.handlerResult(t)})).catch((function(e){console.log("获取验证码error")}))}},handlerResult:function(e){0==e.code?this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3}):(this.viewId+=1,clearInterval(this.codeTimer4Email),this.timerEmail=0,this.emailMid=!1,4==e.code?this.errorMsg.code=e.msg:this.errorMsg.email=e.msg)},lengthControl:function(e){this.ruleForm.code.length>4&&(this.ruleForm.code=this.ruleForm.code.slice(0,4))},reset:function(){this.ruleForm={email:"",code:""},this.$refs["ruleForm"].resetFields(),this.emailMid=!1,clearInterval(this.codeTimer4Email),this.hasBindEmail="",this.timerEmail=0,this.emailPass=!1},filterChinese:function(e){this.ruleForm.email=e.replace(/[\u4E00-\u9FA5]/g,"")}},beforeRouteEnter:function(e,t,s){s((function(t){t.hasBindEmail=e.query.hasBind}))},beforeRouteLeave:function(e,t,s){this.reset(),s()}},c=a,l=s("e90a"),u=Object(l["a"])(c,o,r,!1,null,null,null);t["default"]=u.exports},fa4b:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAAgCAYAAADwvkPPAAAAAXNSR0IArs4c6QAAAb9JREFUSA2tlg1SwjAQhdtewmtQzuUPOjCgggoiUFAEvRf0Gl6i9b1Mt5Om6T+ZYTa73Xx9yS5tHScZ5/N5hF8gfhvrchFBcRxvOXddd+v7/pjzpsMFaAjQzlgY9Pv9iRGrdD1kXFmyxqfTaWOJl4Y8bGnked6XJWschuHaEi8MUZnT6/WGMHszK4qiCY5hZcaLfFUAuYitUeGD+GJRlDV28CR+kc3AmAQlexTk3lwA4ArAZzOu+zkYLwL4DeBAT+QcwA8Ap2ZcfCuMF9sAC2EJ8ACFd3JnzS7RhzPNV1NVTTMoPrY0wNaO4mt2irZZaL6aliqTZCw8ok1uxReL/nxHW72kvkzKLBZwq79mDm4ww43mEq+lTJLRhz+Y34gvFkcxx5G8NYJxMYBUeC0gsQSWFkAS69pGsGSbVlWNtll0XqjoAgV6pfpaytgayM0dfNIaCkRYZQHq9hhhpcrw/zzYmhXrlnqzEsRRqOxif/Q2IKsygC7zcOwCyihDH3V6/hOmqonyf2Kee5Ggjzbo7MoXCUEcfKPv8DTlqy4zCEL5HzPBCofK/iw5QVMQGarPoC793sCjpPWHSyoKwM6fVP8GRMrGm7EY+wAAAABJRU5ErkJggg=="},fbeb:function(e,t,s){"use strict";s.r(t);var o=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"common-h5-login h5-set",class:1==e.$store.state.product_theme?"course-h5-login":2==e.$store.state.product_theme?"rule-h5-login":""},[e.hasBindPhone?e._e():s("p",{staticClass:"setTips"},[e._v("为了您的账号安全，请绑定手机号")]),1==e.hasBindPhone?s("p",{staticClass:"setTips"},[e._v("首次登录，为了您的账号安全，请绑定手机号")]):e._e(),e.hasBindPhone&&1!=e.hasBindPhone?s("div",{staticClass:"setTips"},[s("p",[e._v("当前绑定手机号："+e._s(e.hasBindPhone))]),s("p",[e._v("更换手机号后，可使用新的手机号进行登录及获取信息")])]):e._e(),s("div",{key:e.viewId,staticClass:"cPass-box h5-box"},[s("el-form",{ref:"ruleForm",staticClass:"form-left-width",attrs:{model:e.ruleForm,rules:e.rules,"status-icon":"",action:"",method:"post",enctype:"application/x-www-form-urlencoded"}},[s("div",{staticClass:"phone"},[s("span",{staticClass:"l-t"},[e._v("手机号：")]),s("span",{staticClass:"icon-span"},[e._v("+86")]),s("el-form-item",{attrs:{prop:"phoneName",error:e.errorMsg.phone}},[s("el-input",{staticClass:"inner-left",attrs:{type:"number",placeholder:e.hasBindPhone&&1!=e.hasBindPhone?"请填写要绑定的新手机号":"请填写要绑定的手机号",clearable:""},on:{input:function(t){return e.lengthControl(1)}},model:{value:e.ruleForm.phoneName,callback:function(t){e.$set(e.ruleForm,"phoneName",t)},expression:"ruleForm.phoneName"}})],1)],1),s("div",{staticClass:"code hideStatus"},[s("span",{staticClass:"l-t"},[e._v("验证码：")]),s("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[s("el-input",{attrs:{type:"number",placeholder:"请输入验证码"},on:{input:function(t){return e.lengthControl(2)}},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1),s("div",{staticClass:"yzm"},[0==e.timerPhone&&e.phonePass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])])],1)])],1),s("div",{staticClass:"h5-box"},[s("div",{staticClass:"login"},[s("button",{on:{click:function(t){return e.save("ruleForm")}}},[e._v("确定")])])])])},r=[],i=(s("b449"),s("17d6")),n=(s("4a5b"),s("55d4")),a=s("4122"),c=s("b58d"),l=s("4ec3"),u={mixins:[c["a"]],data:function(){var e=this,t=function(t,s,o){""===s||void 0==s?(e.phonePass=!1,o(new Error("请输入手机号"))):/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/.test(s)?(e.phonePass=!0,o()):(e.phonePass=!1,o(new Error("请输入正确的手机号")))};return{tabIndex:"",viewId:0,ruleForm:{phoneName:"",code:""},errorMsg:{phone:"",code:""},rules:{phoneName:[{validator:t,trigger:"blur"}],code:[{validator:a["a"],trigger:"blur"}]},codeTimer4Phone:void 0,hasBindPhone:"",timerPhone:0,phonePass:!1,phoneMid:!1}},computed:{toastText:function(){return 0===this.timerPhone?"获取验证码":this.timerPhone+"s"}},methods:{save:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(n["b"])();try{t.$route.query.unionid?t.wxFirstBindPhone():t.bindPhone()}catch(s){Object(n["a"])()}}))},wxFirstBindPhone:function(){var e=Object(i["a"])(regeneratorRuntime.mark((function e(){var t,s,o,r,i=this;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t=this.$route.query,e.next=3,this.getProductInfo(t.backUrl);case 3:s=e.sent,s&&(o={productName:s.productName,version:s.version,productId:s.productId,moblie:this.ruleForm.phoneName,code:this.ruleForm.code,userType:t.userType,unionid:t.unionid,userId:t.userId,sourceId:s.sourceId},r=localStorage.getItem("activityId"),r={activityId:r||(activityUrlId||"")},o=Object.assign(o,r),Object(l["u"])(o).then((function(e){Object(n["a"])(),"0"==e.code?(i.$store.commit("BINDING_STATUS",""),i.$notify({message:"绑定成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){i.h5Login(s,i.ruleForm.phoneName,t.userType,t.backUrl)}),2800)):(i.viewId+=1,i.errorMsg.code=e.msg)})).catch((function(e){Object(n["a"])()})));case 5:case"end":return e.stop()}}),e,this)})));function t(){return e.apply(this,arguments)}return t}(),bindPhone:function(){var e=this,t={mobile:this.ruleForm.phoneName,code:this.ruleForm.code,token:localStorage.getItem("for-set-token")};Object(l["m"])(t).then((function(t){Object(n["a"])(),"0"==t.code?(e.$store.commit("BINDING_STATUS",""),e.$notify({message:"绑定成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){e.$router.go(-1)}),2800)):(e.viewId+=1,e.errorMsg.code=t.msg)})).catch((function(e){Object(n["a"])()}))},getCode:function(){var e=this;if(this.errorMsg.code="",!this.phoneMid){this.timerPhone=60,this.phoneMid=!0,this.codeTimer4Phone=setInterval((function(){e.timerPhone--,0===e.timerPhone&&(e.phoneMid=!1,clearInterval(e.codeTimer4Phone),e.phonePass=!0,e.timerPhone=0)}),1e3);var t={mobile:this.ruleForm.phoneName,type:this.$route.query.unionid?6:4,sourceId:this.$store.state.source_id_data};Object(l["o"])(t).then((function(t){e.handlerResult(t)})).catch((function(e){console.log("获取验证码error")}))}},handlerResult:function(e){0==e.code?this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3}):(this.viewId+=1,clearInterval(this.codeTimer4Phone),this.timerPhone=0,this.phoneMid=!1,4==e.code?this.errorMsg.code=e.msg:this.errorMsg.phone=e.msg)},lengthControl:function(e){1==e?this.ruleForm.phoneName.length>11&&(this.ruleForm.phoneName=this.ruleForm.phoneName.slice(0,11)):2==e&&this.ruleForm.code.length>4&&(this.ruleForm.code=this.ruleForm.code.slice(0,4))},reset:function(){this.ruleForm={phoneName:"",code:""},this.$refs["ruleForm"].resetFields(),this.phoneMid=!1,clearInterval(this.codeTimer4Phone),this.hasBindPhone="",this.timerPhone=0,this.phonePass=!1}},beforeRouteEnter:function(e,t,s){s((function(t){t.hasBindPhone=e.query.unionid?1:e.query.hasBind}))},beforeRouteLeave:function(e,t,s){this.reset(),s()}},d=u,h=s("e90a"),m=Object(h["a"])(d,o,r,!1,null,null,null);t["default"]=m.exports}}]);