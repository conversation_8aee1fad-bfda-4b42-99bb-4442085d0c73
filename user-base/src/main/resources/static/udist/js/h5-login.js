(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["h5-login"],{4968:function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"common-h5-login",class:1==e.$store.state.product_theme?"course-h5-login":2==e.$store.state.product_theme?"rule-h5-login":""},[s("p",{staticClass:"loginTitle"},[e._v("注册为"),s("span",{domProps:{textContent:e._s(e.userStatus?"个人用户":"企业用户")}})]),s("div",{key:e.viewId,staticClass:"h5-box"},[s("div",{staticClass:"nav"},[s("span",{class:{active:1===e.tabIndex},on:{click:function(t){return e.toggleIndex("phone")}}},[e._v("手机号注册")]),s("span",{class:{active:2===e.tabIndex},on:{click:function(t){return e.toggleIndex("email")}}},[e._v("邮箱注册")])]),s("el-form",{ref:"rRuleForm",attrs:{model:e.rRuleForm,rules:e.rules,"status-icon":"",action:"",method:"post",enctype:"application/x-www-form-urlencoded"}},[1===e.tabIndex?s("div",{staticClass:"phone"},[s("span",{staticClass:"l-t"},[e._v("手机号：")]),s("span",{staticClass:"icon-span"},[e._v("+86")]),s("el-form-item",{attrs:{prop:"phoneName",error:e.errorMsg.phone}},[s("el-input",{staticClass:"inner-left",attrs:{type:e.keyboardStatus,placeholder:"请输入11位手机号",clearable:""},on:{input:function(t){return e.lengthControl(1)}},model:{value:e.rRuleForm.phoneName,callback:function(t){e.$set(e.rRuleForm,"phoneName",t)},expression:"rRuleForm.phoneName"}})],1)],1):e._e(),2===e.tabIndex?s("div",{staticClass:"email"},[s("span",{staticClass:"l-t"},[e._v("邮   箱：")]),s("el-form-item",{attrs:{prop:"email",error:e.errorMsg.email}},[s("el-input",{attrs:{type:"email",placeholder:"请输入邮箱",clearable:""},on:{input:function(t){return e.filterChinese(5,t)}},model:{value:e.rRuleForm.email,callback:function(t){e.$set(e.rRuleForm,"email",t)},expression:"rRuleForm.email"}})],1)],1):e._e(),s("div",{staticClass:"code hideStatus"},[s("span",{staticClass:"l-t"},[e._v("验证码：")]),s("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[s("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入验证码"},on:{input:function(t){return e.lengthControl(2)}},model:{value:e.rRuleForm.code,callback:function(t){e.$set(e.rRuleForm,"code",t)},expression:"rRuleForm.code"}})],1),1===e.tabIndex?s("div",{staticClass:"yzm"},[0==e.timerPhone&&e.phonePass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e(),2===e.tabIndex?s("div",{staticClass:"yzm"},[0==e.timerEmail&&e.emailPass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e()],1),s("div",{staticClass:"rePassword"},[s("span",{staticClass:"l-t"},[e._v("新密码：")]),s("el-form-item",{attrs:{prop:"pass"}},[s("el-input",{staticStyle:{display:"none"},attrs:{placeholder:"6 - 16位密码，区分大小写",type:e.elInputType1,clearable:""},model:{value:e.rRuleForm.pass,callback:function(t){e.$set(e.rRuleForm,"pass",t)},expression:"rRuleForm.pass"}}),s("el-input",{attrs:{placeholder:"6 - 16位密码，区分大小写",type:e.elInputType1,clearable:"",readonly:e.readonlyStatus1},on:{input:function(t){return e.filterChinese(3,t)}},model:{value:e.rRuleForm.pass,callback:function(t){e.$set(e.rRuleForm,"pass",t)},expression:"rRuleForm.pass"}},[null!=e.rRuleForm.pass&&""!=e.rRuleForm.pass?s("i",{staticClass:"el-icon-view el-input__icon",attrs:{slot:"suffix"},on:{click:function(t){return e.showPassword(1)}},slot:"suffix"}):e._e()])],1)],1),s("div",{staticClass:"rePassword"},[s("span",{staticClass:"l-t"},[e._v("确认密码：")]),s("el-form-item",{attrs:{prop:"confirmPass"}},[s("el-input",{staticStyle:{display:"none"},attrs:{placeholder:"确认密码",type:e.elInputType2,clearable:""},model:{value:e.rRuleForm.confirmPass,callback:function(t){e.$set(e.rRuleForm,"confirmPass",t)},expression:"rRuleForm.confirmPass"}}),s("el-input",{attrs:{placeholder:"确认密码",type:e.elInputType2,clearable:"",readonly:e.readonlyStatus2},on:{input:function(t){return e.filterChinese(4,t)}},model:{value:e.rRuleForm.confirmPass,callback:function(t){e.$set(e.rRuleForm,"confirmPass",t)},expression:"rRuleForm.confirmPass"}},[null!=e.rRuleForm.confirmPass&&""!=e.rRuleForm.confirmPass?s("i",{staticClass:"el-icon-view el-input__icon",attrs:{slot:"suffix"},on:{click:function(t){return e.showPassword(2)}},slot:"suffix"}):e._e()])],1)],1)]),s("div",{staticClass:"treaty reg-treaty"},[s("el-checkbox",{model:{value:e.checked,callback:function(t){e.checked=t},expression:"checked"}}),s("p",{staticClass:"userAgree"},[e._v("同意"),s("span",{on:{click:function(t){return e.goAgreement(e.tabIndex)}}},[e._v("《用户协议》")])])],1),s("div",{staticClass:"login"},[e.unbind?s("button",[e._v("注册")]):s("button",{on:{click:function(t){return e.goRegister("rRuleForm")}}},[e._v("注册")])]),s("p",{staticClass:"goLogin",on:{click:e.goLogin}},[e._v("使用已有账号登录")])],1)])},n=[],a=(s("cc57"),s("48c0"),s("92ce")),o=s("b58d"),i={mixins:[a["a"],o["a"]],beforeRouteEnter:function(e,t,s){s((function(e){e.tabIndex=e.$store.state.userTab&&t.name?e.$store.state.userTab:1}))},beforeRouteLeave:function(e,t,s){"h5Agreement"!=e.name&&this.$store.commit("USER_TAB",1),s()}},l=i,c=s("e90a"),u=Object(c["a"])(l,r,n,!1,null,null,null);t["default"]=u.exports},"4cbd":function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"agreement-h5"},[s("div",{directives:[{name:"wechat-title",rawName:"v-wechat-title",value:e.$route.meta.title,expression:"$route.meta.title"}]}),s("div",{staticClass:"wrap",domProps:{innerHTML:e._s(e.count)}})])},n=[],a=s("4ec3"),o={data:function(){return{company:"",count:""}},created:function(){this.company=this.$store.state.company_name?this.$store.state.company_name:"【大象慧云信息技术有限公司】",this.getUserAgreement()},methods:{getUserAgreement:function(){var e=this,t={type:1,sourceId:this.$store.state.product_h5.sourceId};Object(a["h"])(t).then((function(t){console.log(t),"0"==t.code&&(e.count=t.data.agreement)}))}}},i=o,l=(s("af2d"),s("e90a")),c=Object(l["a"])(i,r,n,!1,null,"f011a3ba",null);t["default"]=c.exports},"5d58":function(e,t,s){},"6bde":function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div")},n=[],a=(s("f548"),s("b449"),s("17d6")),o=s("4ec3"),i=s("b58d"),l=s("55d4"),c={mixins:[i["a"]],methods:{takeCodeBy:function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t,s){var r;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,this.getUserInfo(t,s);case 2:r=e.sent,0==r.subscribe?(Object(l["a"])(),Object(o["k"])("2")):r&&this.sendUserInfo(r);case 4:case"end":return e.stop()}}),e,this)})));function t(t,s){return e.apply(this,arguments)}return t}(),sendUserInfo:function(e){var t=this;e.userType="C",console.log(e),Object(o["p"])(e).then((function(s){console.log(e),"0"==s.code&&s.data.phone?t.autoLogin(s.data.phone):(Object(l["a"])(),t.$router.replace({name:"h5Phone",query:{userId:s.data.userId,unionid:s.data.unionid,userType:"C",backUrl:t.$aes.getUrlArgumentValue("backUrl")}}))})).catch((function(e){Object(l["a"])()}))},autoLogin:function(){var e=Object(a["a"])(regeneratorRuntime.mark((function e(t){var s,r,n;return regeneratorRuntime.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return console.log(this.$aes.getUrlArgumentValue("backUrl")),e.next=3,this.getProductInfo(this.$aes.getUrlArgumentValue("backUrl"),!0);case 3:s=e.sent,s&&(Object(l["a"])(),r=1==this.userStatus?"P":"C",n=this.$aes.getUrlArgumentValue("backUrl"),this.h5Login(s,t,r,n));case 5:case"end":return e.stop()}}),e,this)})));function t(t){return e.apply(this,arguments)}return t}()},beforeRouteEnter:function(e,t,s){s((function(e){var t=e.$aes.getUrlArgumentValue("code"),s=e.$aes.getUrlArgumentValue("type");t?(s=s||"1",e.takeCodeBy(t,s)):e.wechatlogin(e.$aes.getUrlArgumentValue("backUrl"))}))}},u=c,m=s("e90a"),d=Object(m["a"])(u,r,n,!1,null,null,null);t["default"]=d.exports},af2d:function(e,t,s){"use strict";var r=s("5d58"),n=s.n(r);n.a},d41a:function(e,t,s){"use strict";s.r(t);var r=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"common-h5-login",class:1==e.$store.state.product_theme?"course-h5-login":2==e.$store.state.product_theme?"rule-h5-login":""},[s("p",{staticClass:"loginTitle"},[s("span",{domProps:{textContent:e._s(e.userStatus?"个人用户":"企业用户")}}),e._v("找回密码")]),s("div",{key:e.viewId,staticClass:"h5-box"},[s("div",{staticClass:"nav"},[s("span",{class:{active:1===e.tabIndex},on:{click:function(t){return e.toggleIndex("phone")}}},[e._v("手机号找回密码")]),s("span",{class:{active:2===e.tabIndex},on:{click:function(t){return e.toggleIndex("email")}}},[e._v("邮箱找回密码")])]),s("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"status-icon":""}},[1===e.tabIndex?s("div",{staticClass:"phone"},[s("span",{staticClass:"l-t"},[e._v("手机号：")]),s("span",{staticClass:"icon-span"},[e._v("+86")]),s("el-form-item",{attrs:{prop:"phone",error:e.errorMsg.phone}},[s("el-input",{staticClass:"inner-left",attrs:{type:e.keyboardStatus,placeholder:"请输入手机号",clearable:""},on:{input:function(t){return e.lengthControl(1)}},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1)],1):e._e(),2===e.tabIndex?s("div",{staticClass:"email"},[s("span",{staticClass:"l-t"},[e._v("邮   箱：")]),s("el-form-item",{attrs:{prop:"email",error:e.errorMsg.email}},[s("el-input",{attrs:{placeholder:"请输入邮箱",clearable:""},on:{input:function(t){return e.filterChinese(8,t)}},model:{value:e.ruleForm.email,callback:function(t){e.$set(e.ruleForm,"email",t)},expression:"ruleForm.email"}})],1)],1):e._e(),s("div",{staticClass:"code hideStatus"},[s("span",{staticClass:"l-t"},[e._v("验证码：")]),s("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[s("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入验证码"},on:{input:function(t){return e.lengthControl(2)}},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1),1===e.tabIndex?s("div",{staticClass:"yzm"},[0==e.timerPhone&&e.phonePass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e(),2===e.tabIndex?s("div",{staticClass:"yzm"},[0==e.timerEmail&&e.emailPass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e()],1),s("div",{staticClass:"rePassword"},[s("span",{staticClass:"l-t"},[e._v("新密码：")]),s("el-form-item",{attrs:{prop:"pass"}},[s("el-input",{staticStyle:{display:"none"},attrs:{placeholder:"6 - 16位密码，区分大小写",type:e.elInputType1,clearable:""},model:{value:e.ruleForm.pass,callback:function(t){e.$set(e.ruleForm,"pass",t)},expression:"ruleForm.pass"}}),s("el-input",{attrs:{placeholder:"6 - 16位密码，区分大小写",type:e.elInputType1,clearable:"",readonly:e.readonlyStatus1},on:{input:function(t){return e.filterChinese(6,t)}},model:{value:e.ruleForm.pass,callback:function(t){e.$set(e.ruleForm,"pass",t)},expression:"ruleForm.pass"}},[null!=e.ruleForm.pass&&""!=e.ruleForm.pass?s("i",{staticClass:"el-icon-view el-input__icon",attrs:{slot:"suffix"},on:{click:function(t){return e.showPassword(1)}},slot:"suffix"}):e._e()])],1)],1),s("div",{staticClass:"rePassword"},[s("span",{staticClass:"l-t"},[e._v("确认密码：")]),s("el-form-item",{attrs:{prop:"confirmPass"}},[s("el-input",{staticStyle:{display:"none"},attrs:{placeholder:"确认密码",type:e.elInputType2,clearable:""},model:{value:e.ruleForm.confirmPass,callback:function(t){e.$set(e.ruleForm,"confirmPass",t)},expression:"ruleForm.confirmPass"}}),s("el-input",{attrs:{placeholder:"确认密码",type:e.elInputType2,clearable:"",readonly:e.readonlyStatus2},on:{input:function(t){return e.filterChinese(7,t)}},model:{value:e.ruleForm.confirmPass,callback:function(t){e.$set(e.ruleForm,"confirmPass",t)},expression:"ruleForm.confirmPass"}},[null!=e.ruleForm.confirmPass&&""!=e.ruleForm.confirmPass?s("i",{staticClass:"el-icon-view el-input__icon",attrs:{slot:"suffix"},on:{click:function(t){return e.showPassword(2)}},slot:"suffix"}):e._e()])],1)],1)]),s("div",{staticClass:"login"},[e.unbind?s("button",{staticClass:"minBtn"},[e._v("确定")]):s("button",{staticClass:"minBtn",on:{click:function(t){return e.findPassword("ruleForm")}}},[e._v("确定")])]),s("p",{staticClass:"goLogin",on:{click:e.goLogin}},[e._v("使用已有账号登录")])],1)])},n=[],a=s("e624"),o=s("b58d"),i={mixins:[a["a"],o["a"]]},l=i,c=s("e90a"),u=Object(c["a"])(l,r,n,!1,null,null,null);t["default"]=u.exports},e624:function(e,t,s){"use strict";s("f548");var r=s("55d4"),n=s("4122"),a=s("4ec3");t["a"]={name:"FindPassword",data:function(){var e=this,t=function(t,s,r){""===s||void 0==s?(e.phonePass=!1,r(new Error("请输入手机号"))):/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/.test(s)?(e.phonePass=!0,r()):(e.phonePass=!1,r(new Error("请输入正确的手机号")))},s=function(t,s,r){""===s||void 0==s?(e.emailPass=!1,r(new Error("请输入邮箱"))):/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/.test(s)?(e.emailPass=!0,r()):(e.emailPass=!1,r(new Error("请输入正确的邮箱")))},r=function(t,s,r){""===s||void 0==s?r(new Error("请再次输入密码")):s!==e.ruleForm.pass?r(new Error("两次输入密码不一致")):r()};return{viewId:0,tabIndex:1,timerPhone:0,timerEmail:0,codeTimer4Phone:void 0,codeTimer4Email:void 0,ruleForm:{phone:"",email:"",code:"",pass:"",confirmPass:""},rules:{phone:[{validator:t,trigger:"blur"}],email:[{validator:s,trigger:"blur"}],code:[{validator:n["a"],trigger:"blur"}],pass:[{validator:n["c"],trigger:"blur"}],confirmPass:[{validator:r,trigger:"blur"}]},phoneMid:!1,emailMid:!1,errorMsg:{phone:"",code:"",email:""},elInputType1:"text",elInputType2:"text",elStatus1:!1,elStatus2:!1,emailPass:!1,phonePass:!1,unbind:!1,keyboardStatus:"text",readonlyStatus1:!1,readonlyStatus2:!1}},computed:{toastText:function(){return 1===this.tabIndex?0===this.timerPhone?"获取验证码":this.timerPhone+"s":0===this.timerEmail?"获取验证码":this.timerEmail+"s"}},created:function(){},mounted:function(){"phone"==this.$aes.browserRedirect()?this.keyboardStatus="number":this.keyboardStatus="text"},methods:{findPassword:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(r["b"])(),t.viewId+=1,t.unbind=!0;var s="";s=1==t.tabIndex?{mobile:t.ruleForm.phone,code:t.ruleForm.code,newPassword:t.$aes.Encrypt(t.ruleForm.confirmPass,"1234567887654321","1234567887654321")}:{email:t.ruleForm.email,code:t.ruleForm.code,newPassword:t.$aes.Encrypt(t.ruleForm.confirmPass,"1234567887654321","1234567887654321")},t.errorMsg.code="",Object(a["d"])(s).then((function(e){t.findpassHandle(e)})).catch((function(e){setTimeout((function(){t.unbind=!1}),1500)}))}))},findpassHandle:function(e){var t=this;"0"==e.code?(Object(r["a"])(),this.$notify({message:" 修改成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){t.goLogin()}),2800),this.unbind=!1):(this.unbind=!1,"1"==e.code||"2"==e.code?this.errorMsg.code=e.msg:1==this.tabIndex?this.errorMsg.phone=e.msg:this.errorMsg.email=e.msg,Object(r["a"])())},getCode:function(){var e=this;if(this.errorMsg.code="",this.viewId+=1,1==this.tabIndex){if(this.phoneMid)return;this.timerPhone=60,this.phoneMid=!0,this.codeTimer4Phone=setInterval((function(){e.timerPhone--,0===e.timerPhone&&(e.phonePass=!0,e.resetPhone())}),1e3)}else{if(this.emailMid)return;this.timerEmail=60,this.emailMid=!0,this.codeTimer4Email=setInterval((function(){e.timerEmail--,0===e.timerEmail&&(e.emailPass=!0,e.resetEmail())}),1e3)}var t="";t=1==this.tabIndex?{mobile:this.ruleForm.phone,type:3}:{email:this.ruleForm.email,type:3},Object(a["o"])(t).then((function(t){e.handlerResult(t)})).catch((function(e){console.log("获取验证码error")}))},handlerResult:function(e){"0"==e.code?(this.errorMsg={phone:"",code:"",email:""},this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3})):"3"==e.code?this.errorMsg.phone=e.msg:(this.errorMsg.code=e.msg,1==this.tabIndex?this.resetPhone():this.resetEmail())},lengthControl:function(e,t){1==e?(this.ruleForm.phone=t.replace(/[\u4E00-\u9FA5]/g,""),this.ruleForm.phone.length>11&&(this.ruleForm.phone=this.ruleForm.phone.slice(0,11))):2==e&&(this.ruleForm.code=t.replace(/[\u4E00-\u9FA5]/g,""),this.ruleForm.code.length>4&&(this.ruleForm.code=this.ruleForm.code.slice(0,4)))},toggleIndex:function(e){this.elInputType1="text",this.elInputType2="text",this.errorMsg={phone:"",code:"",email:""},this.ruleForm={phone:"",email:"",code:"",pass:"",confirmPass:""},"phone"===e?(this.reset1(),this.tabIndex=1):(this.reset2(),this.tabIndex=2),this.$refs["ruleForm"].resetFields()},goLogin:function(){var e="Login";"phone"==this.$aes.browserRedirect()&&(e="h5Login"),this.$router.push({name:e,query:{uStatus:this.userStatus}})},resetPhone:function(){clearInterval(this.codeTimer4Phone),this.timerPhone=0,this.codeTimer4Phone=0,this.phoneMid=!1},resetEmail:function(){clearInterval(this.codeTimer4Email),this.timerEmail=0,this.codeTimer4Email=0,this.emailMid=!1},reset1:function(){this.emailPass=!1,this.resetPhone(),this.elInputType1="text",this.$refs["ruleForm"].resetFields()},reset2:function(){this.phonePass=!1,this.resetEmail(),this.elInputType2="text",this.$refs["ruleForm"].resetFields()}},beforeRouteLeave:function(e,t,s){this.elInputType1="text",this.elInputType2="text",this.readonlyStatus1=!0,this.readonlyStatus2=!0,this.reset1(),this.reset2(),this.tabIndex=1,s()},beforeRouteEnter:function(e,t,s){s((function(t){t.readonlyStatus1=!1,t.readonlyStatus2=!1,t.ruleForm={phone:"",email:"",code:"",pass:"",confirmPass:""},t.userStatus=e.query.uStatus}))}}}}]);