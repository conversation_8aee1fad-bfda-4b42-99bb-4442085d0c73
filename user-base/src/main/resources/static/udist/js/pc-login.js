(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pc-login"],{1928:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"common-web-login",class:[e.$store.state.product_door?"web-door":"web-common",1==e.$store.state.product_theme?"course-web-login":2==e.$store.state.product_theme?"laws-web-login":""]},[s("div",{directives:[{name:"wechat-title",rawName:"v-wechat-title",value:e.$route.meta.title,expression:"$route.meta.title"}]}),s("input",{staticStyle:{visibility:"hidden"},attrs:{type:"text",id:"e"}}),s("input",{staticStyle:{visibility:"hidden"},attrs:{type:"password",id:"f"}}),s("Carousel"),s("div",{staticClass:"web-box"},[s("div",{key:e.viewId,staticClass:"loginBox"},[s("div",{staticClass:"content"},[s("div",{staticClass:"nav"},[s("span",{class:{active:1===e.tabIndex},on:{click:function(t){return e.toggleIndex("phone")}}},[e._v("手机号找回密码")]),s("span",{class:{active:2===e.tabIndex},on:{click:function(t){return e.toggleIndex("email")}}},[e._v("邮箱找回密码")])]),s("el-form",{ref:"ruleForm",attrs:{model:e.ruleForm,rules:e.rules,"status-icon":""}},[1===e.tabIndex?s("div",{staticClass:"elLine phone"},[s("i",{staticClass:"iconfont iconshouji1"}),s("span",{staticClass:"icon-span"},[e._v("+86")]),s("el-form-item",{attrs:{prop:"phone",error:e.errorMsg.phone}},[s("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入11位手机号",clearable:""},on:{input:function(t){return e.lengthControl(1,t)}},model:{value:e.ruleForm.phone,callback:function(t){e.$set(e.ruleForm,"phone",t)},expression:"ruleForm.phone"}})],1)],1):e._e(),2===e.tabIndex?s("div",{staticClass:"elLine"},[s("i",{staticClass:"iconfont icontongzhi- iconsmall"}),s("el-form-item",{attrs:{prop:"email",error:e.errorMsg.email}},[s("el-input",{attrs:{placeholder:"请输入邮箱",clearable:""},on:{input:function(t){return e.filterChinese(8,t)}},model:{value:e.ruleForm.email,callback:function(t){e.$set(e.ruleForm,"email",t)},expression:"ruleForm.email"}})],1)],1):e._e(),s("div",{staticClass:"elLine code hideStatus"},[s("i",{staticClass:"iconfont iconanquan"}),s("el-form-item",{attrs:{prop:"code",error:e.errorMsg.code}},[s("el-input",{attrs:{type:e.keyboardStatus,placeholder:"请输入验证码"},on:{input:function(t){return e.lengthControl(2,t)}},model:{value:e.ruleForm.code,callback:function(t){e.$set(e.ruleForm,"code",t)},expression:"ruleForm.code"}})],1),1===e.tabIndex?s("div",{staticClass:"yzm"},[0==e.timerPhone&&e.phonePass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e(),2===e.tabIndex?s("div",{staticClass:"yzm"},[0==e.timerEmail&&e.emailPass?s("span",{staticClass:"codeBtn",on:{click:e.getCode}},[e._v(e._s(e.toastText))]):s("span",{staticClass:"disabled"},[e._v(e._s(e.toastText))])]):e._e()],1),s("div",{staticClass:"elLine"},[s("i",{staticClass:"iconfont iconicon-test"}),s("el-form-item",{attrs:{prop:"pass"}},[s("el-input",{staticStyle:{display:"none"},attrs:{placeholder:"6 - 16位密码，区分大小写",type:e.elInputType1,clearable:""},model:{value:e.ruleForm.pass,callback:function(t){e.$set(e.ruleForm,"pass",t)},expression:"ruleForm.pass"}}),s("el-input",{attrs:{placeholder:"6 - 16位密码，区分大小写",type:"password",clearable:"",readonly:e.readonlyStatus1},on:{input:function(t){return e.filterChinese(6,t)}},model:{value:e.ruleForm.pass,callback:function(t){e.$set(e.ruleForm,"pass",t)},expression:"ruleForm.pass"}})],1)],1),s("div",{staticClass:"elLine"},[s("i",{staticClass:"iconfont iconicon-test"}),s("el-form-item",{attrs:{prop:"confirmPass"}},[s("el-input",{staticStyle:{display:"none"},attrs:{placeholder:"请确认密码",type:e.elInputType2,clearable:""},model:{value:e.ruleForm.confirmPass,callback:function(t){e.$set(e.ruleForm,"confirmPass",t)},expression:"ruleForm.confirmPass"}}),s("el-input",{attrs:{placeholder:"请确认密码",type:"password",clearable:"",readonly:e.readonlyStatus2},on:{input:function(t){return e.filterChinese(7,t)}},model:{value:e.ruleForm.confirmPass,callback:function(t){e.$set(e.ruleForm,"confirmPass",t)},expression:"ruleForm.confirmPass"}})],1)],1)]),s("div",{staticClass:"login"},[e.unbind?s("button",{staticClass:"minBtn"},[e._v("确 定")]):s("button",{staticClass:"minBtn",on:{click:function(t){return e.findPassword("ruleForm")}}},[e._v("确 定")]),s("span",{on:{click:e.goLogin}},[e._v("使用已有账号登录")])])],1)])])],1)},o=[],r=(s("ecf7"),s("e624")),n=s("d221"),a=s("b58d"),l={mixins:[r["a"],a["a"]],components:{Carousel:n["a"]}},c=l,u=s("e90a"),d=Object(u["a"])(c,i,o,!1,null,null,null);t["default"]=d.exports},e624:function(e,t,s){"use strict";s("f548");var i=s("55d4"),o=s("4122"),r=s("4ec3");t["a"]={name:"FindPassword",data:function(){var e=this,t=function(t,s,i){""===s||void 0==s?(e.phonePass=!1,i(new Error("请输入手机号"))):/^(13[0-9]|14[0-9]|15[0-9]|16[0-9]|17[0-9]|18[0-9]|19[0-9])\d{8}$/.test(s)?(e.phonePass=!0,i()):(e.phonePass=!1,i(new Error("请输入正确的手机号")))},s=function(t,s,i){""===s||void 0==s?(e.emailPass=!1,i(new Error("请输入邮箱"))):/[\w!#$%&'*+/=?^_`{|}~-]+(?:\.[\w!#$%&'*+/=?^_`{|}~-]+)*@(?:[\w](?:[\w-]*[\w])?\.)+[\w](?:[\w-]*[\w])?/.test(s)?(e.emailPass=!0,i()):(e.emailPass=!1,i(new Error("请输入正确的邮箱")))},i=function(t,s,i){""===s||void 0==s?i(new Error("请再次输入密码")):s!==e.ruleForm.pass?i(new Error("两次输入密码不一致")):i()};return{viewId:0,tabIndex:1,timerPhone:0,timerEmail:0,codeTimer4Phone:void 0,codeTimer4Email:void 0,ruleForm:{phone:"",email:"",code:"",pass:"",confirmPass:""},rules:{phone:[{validator:t,trigger:"blur"}],email:[{validator:s,trigger:"blur"}],code:[{validator:o["a"],trigger:"blur"}],pass:[{validator:o["c"],trigger:"blur"}],confirmPass:[{validator:i,trigger:"blur"}]},phoneMid:!1,emailMid:!1,errorMsg:{phone:"",code:"",email:""},elInputType1:"text",elInputType2:"text",elStatus1:!1,elStatus2:!1,emailPass:!1,phonePass:!1,unbind:!1,keyboardStatus:"text",readonlyStatus1:!1,readonlyStatus2:!1}},computed:{toastText:function(){return 1===this.tabIndex?0===this.timerPhone?"获取验证码":this.timerPhone+"s":0===this.timerEmail?"获取验证码":this.timerEmail+"s"}},created:function(){},mounted:function(){"phone"==this.$aes.browserRedirect()?this.keyboardStatus="number":this.keyboardStatus="text"},methods:{findPassword:function(e){var t=this;this.$refs[e].validate((function(e){if(!e)return!1;Object(i["b"])(),t.viewId+=1,t.unbind=!0;var s="";s=1==t.tabIndex?{mobile:t.ruleForm.phone,code:t.ruleForm.code,newPassword:t.$aes.Encrypt(t.ruleForm.confirmPass,"1234567887654321","1234567887654321")}:{email:t.ruleForm.email,code:t.ruleForm.code,newPassword:t.$aes.Encrypt(t.ruleForm.confirmPass,"1234567887654321","1234567887654321")},t.errorMsg.code="",Object(r["d"])(s).then((function(e){t.findpassHandle(e)})).catch((function(e){setTimeout((function(){t.unbind=!1}),1500)}))}))},findpassHandle:function(e){var t=this;"0"==e.code?(Object(i["a"])(),this.$notify({message:" 修改成功",showClose:!1,type:"success",duration:2e3}),setTimeout((function(){t.goLogin()}),2800),this.unbind=!1):(this.unbind=!1,"1"==e.code||"2"==e.code?this.errorMsg.code=e.msg:1==this.tabIndex?this.errorMsg.phone=e.msg:this.errorMsg.email=e.msg,Object(i["a"])())},getCode:function(){var e=this;if(this.errorMsg.code="",this.viewId+=1,1==this.tabIndex){if(this.phoneMid)return;this.timerPhone=60,this.phoneMid=!0,this.codeTimer4Phone=setInterval((function(){e.timerPhone--,0===e.timerPhone&&(e.phonePass=!0,e.resetPhone())}),1e3)}else{if(this.emailMid)return;this.timerEmail=60,this.emailMid=!0,this.codeTimer4Email=setInterval((function(){e.timerEmail--,0===e.timerEmail&&(e.emailPass=!0,e.resetEmail())}),1e3)}var t="";t=1==this.tabIndex?{mobile:this.ruleForm.phone,type:3}:{email:this.ruleForm.email,type:3},Object(r["o"])(t).then((function(t){e.handlerResult(t)})).catch((function(e){console.log("获取验证码error")}))},handlerResult:function(e){"0"==e.code?(this.errorMsg={phone:"",code:"",email:""},this.$notify({message:" 验证码已发送",showClose:!1,type:"success",duration:2e3})):"3"==e.code?this.errorMsg.phone=e.msg:(this.errorMsg.code=e.msg,1==this.tabIndex?this.resetPhone():this.resetEmail())},lengthControl:function(e,t){1==e?(this.ruleForm.phone=t.replace(/[\u4E00-\u9FA5]/g,""),this.ruleForm.phone.length>11&&(this.ruleForm.phone=this.ruleForm.phone.slice(0,11))):2==e&&(this.ruleForm.code=t.replace(/[\u4E00-\u9FA5]/g,""),this.ruleForm.code.length>4&&(this.ruleForm.code=this.ruleForm.code.slice(0,4)))},toggleIndex:function(e){this.elInputType1="text",this.elInputType2="text",this.errorMsg={phone:"",code:"",email:""},this.ruleForm={phone:"",email:"",code:"",pass:"",confirmPass:""},"phone"===e?(this.reset1(),this.tabIndex=1):(this.reset2(),this.tabIndex=2),this.$refs["ruleForm"].resetFields()},goLogin:function(){var e="Login";"phone"==this.$aes.browserRedirect()&&(e="h5Login"),this.$router.push({name:e,query:{uStatus:this.userStatus}})},resetPhone:function(){clearInterval(this.codeTimer4Phone),this.timerPhone=0,this.codeTimer4Phone=0,this.phoneMid=!1},resetEmail:function(){clearInterval(this.codeTimer4Email),this.timerEmail=0,this.codeTimer4Email=0,this.emailMid=!1},reset1:function(){this.emailPass=!1,this.resetPhone(),this.elInputType1="text",this.$refs["ruleForm"].resetFields()},reset2:function(){this.phonePass=!1,this.resetEmail(),this.elInputType2="text",this.$refs["ruleForm"].resetFields()}},beforeRouteLeave:function(e,t,s){this.elInputType1="text",this.elInputType2="text",this.readonlyStatus1=!0,this.readonlyStatus2=!0,this.reset1(),this.reset2(),this.tabIndex=1,s()},beforeRouteEnter:function(e,t,s){s((function(t){t.readonlyStatus1=!1,t.readonlyStatus2=!1,t.ruleForm={phone:"",email:"",code:"",pass:"",confirmPass:""},t.userStatus=e.query.uStatus}))}}}}]);