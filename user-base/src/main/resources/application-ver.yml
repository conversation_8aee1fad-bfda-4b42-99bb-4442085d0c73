server:
  port: 8501
  tomcat:
    uri-encoding: utf-8
  servlet:
    context-path: /user-base
nacos:
  config:
    server-addr: *************:8848
    namespace: 07f008c8-7837-4f19-8291-9c49a9374b7d
    user-center:
      group: user-center
      dataId: user-service.yml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://*************:3306/test?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true
    username: invoice
    password: <PERSON><PERSON><PERSON><PERSON><PERSON>@1234
    initial-size: 8
    min-idle: 8
    max-active: 16
    max-wait: 60000
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 300000
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
  redis:
    host: *************
    port: 6379
    password: <PERSON><PERSON><PERSON><PERSON><PERSON>@1234
    database: 0
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 500
        min-idle: 0
    lettuce:
      shutdown-timeout: 0
test:
  str: testYml
sit:
  str: 中文测试

