server:
  port: 8501
  tomcat:
    uri-encoding: utf-8
  servlet:
    context-path: /user-base


spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://**********:36000/iomp?serverTimezone=Asia/Shanghai&useUnicode=true&characterEncoding=utf-8&zeroDateTimeBehavior=convertToNull&useSSL=false&allowPublicKeyRetrieval=true
    username: invoice
    password: Invoice@.^.98
    initial-size: 8
    min-idle: 8
    max-active: 16
    max-wait: 60000
    pool-prepared-statements: true
    max-pool-prepared-statement-per-connection-size: 20
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 300000
    test-while-idle: true
    test-on-borrow: false
    test-on-return: false
  redis:
    host: **********
    port: 38000
    password: q92RBG89bbVdn^2k
    database: 71
    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 500
        min-idle: 0
    lettuce:
      shutdown-timeout: 0

redisson:
  address: redis://**********:38000
  password: q92RBG89bbVdn^2k
  database: 71

test:
  str: testYml
  age: 21

sit:
  str: 中文测试


myname: chen111

sourceId:
  guangxi: 450101001
  haokuaiji: 110108189         #好会计测试环境渠道配置
  yidaizhang: 110108191        #易代账测试环境渠道配置


#数据同步开关 0关
dx_switch: 0

#风控cookie过期时间
cookie:
  expire: 30

demouids: 100001,975,100022

productId:
  QDKP: 1580039484260196352
  QDYP: 1580039484260196353
  XXXT: 1580039484260196354
  JXXT: 1580039484260196355
  SBXT: 1580039484260196356
  BXXT: 1580039484260196357
  FKXT: 1580039484260196358

defalut:
  role: 24121414
  parent: 3000
sensors:
  project: default

security:
  encode:
    key: '1234567887654321' #必须16位
  validate:
    code: true
    preview: false
  sessions: stateless
  oauth2:
    client:
      client-id: app
      client-secret: app
    resource:
      jwt:
        #        key-uri: http://*************:3001/oauth/token_key #解析jwt令牌所需要密钥的地址
        key-uri: http://172.16.61.87:8501/oauth/token_key #解析jwt令牌所需要密钥的地址
        key-value: app

refresh:
  token:
    time: 10/10 * * * * ?


bigb: 975-20
#demouids: 100001,975,100022
demoauth: VUW64oCMWB2bGKYHkuMEPg==,rzYhPucdyNKWbD8aFTpypw==
exptime: 72

#短信验证码有效期
sms:
  expire: 10
  #短信接口与地址
  smsUrl: http://127.0.0.1:8080/dps/message/send?
  #短信平台编号
  platformNo: PSY
  #短信平台秘钥
  platformKey: 4eece46160eac3ab
  smsContentLogin: 验证码：{0}，有效期10分钟。如非本人操作，请忽略。
  smsContentRegister: （注册）验证码：{0}，有效期10分钟。如非本人操作，请忽略。
  smsContentUpass: （忘记/修改密码）验证码：{0}，有效期10分钟。如非本人操作，请忽略。
  smsContentBind: （绑定）验证码：{0}，有效期10分钟。如非本人操作，请忽略。
email:
  #emailUrl: http://47.106.48.231:8080/dps/email/send
  emailUrl: http://127.0.0.1:8080/dps/email/send
  emailContent: 尊敬的用户您好，现已为您开通全数通系统账号，用户名：{0} 密码：{1} 祝您生活愉快~
  emailContentReset: 尊敬的用户您好，现已为您重置全数通系统账号，用户名：{0} 密码：{1} 祝您生活愉快~
webhook:
  #wxurl: https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6ad278ea-aed8-4848-aa20-b852673da5cd
  wxurl: 123

#DX免密接口地址
ele-cloud:
  ssoUrl: https://omp-pre.dxyun.com/uauthentication/gotoapp?
  tokenUrl: https://sandbox.ele-cloud.com/api/authen/token?
  registerEntUrl: https://sandbox.ele-cloud.com/api/aosp-portal-center/combo/registerEnterpriseAll?
  thridSynUrl: https://omp-pre.dxyun.com/fatsapi/uadmin/dept/aosp/thirdSynDept?
  thirdAddUserUrl: https://omp-pre.dxyun.com/fatsapi/uadmin/user/aosp/thirdAddUser?
  SKSBUrl: https://sandbox.ele-cloud.com/api/order-api/order-api/v5/SyncTaxEquipmentInfo?
  appKey: F1hupjg0APTgm8HEfZLKMJSJ
  appSecret: WNyjzzLaTKtlVvRds7ScOHuR
  entCode: 91440300MA5EF4Q15E
  sourceId: *********
  tcId: b2e5b283-d1bd-4714-8d7c-4fde290b87f7
  ssoSourceId: *********
  jxsys:
    #公共参数
    core: BPY
    signAceKey: biaopuyun
    psw: BPYxqKPKS3HxNGdG^u8p$MhSJUWkpbK68kN
    #免密登录
    appSecKey: dxhy
    syncUserUrl: http://************:19003/api/erp/user/syncUser
    syncTaxUrl: http://************:19003/api/erp/user/syncTax
    #采集鉴权id
    aceId: 1122
    #采集鉴权key
    aceKey: 2322
    jxSsoUrl: http://************:19003/authRedirect?

#企业信息中的 企业类型 会计准则等字典表
aosp:
  dictionary:
    parent: 50,34,37,53,46

#销项 使用的系统类型
system:
  sign: XX01

#税航票帮手配置
mycst:
  getTokenUrl: http://mycst.cn/NEWKP/LOGIN/GetToken
  userCode: DXHY
  userPwd: Aa@12345
  terminalRegistUrl: http://mycst.cn/NEWKP/TERM/REGISTRY
  zclx: 0 # 终端注册类型 0试用 1正式



saas:
  secretId: 4b530cd24ba411edbb4b52540079e9e2
  secretKey: 5517745f4ba411edbb4b52540079e9e2
maxOrgCount: 100
#暂时不用
qdUserCheckUrl: http://**********:6060/invoice/api/accountCheck
#暂时不用
registerUrl: http://**********:8080/register
qdDataJhkUrl: http://10.1.5.203:18113/einvoice/user/addDept
jxDataJhkUrl: http://10.1.5.203:8003/initData/addDept


#华为云接入参数
hwcloud:
  domainName: http://127.0.0.1
  accessKey: 123
  #获取token
  clientId: Qk76GnKXo97X5IbDnCLyQsl2FkoC29SI
  clientSecret: 1fZQj32wrhEI1pw39cttY4fJjE6Ru4Be5FhLQ1jB7eFDLD61ZNQSZXZYZgoC4emS
  accessTokenUrl: ${hwcloud.domainName}/api/v1/oauth2/token
  redirectUri: https://example.com
  #根据token获取用户信息
  userInfoUrl: ${hwcloud.domainName}/api/v1/oauth2/userinfo
  #登出
  logoutUrl:  /api/v1/logout
  # 系统前端地址
  frontEndUrl: xxxxxxxxxxx
  # 系统管理端地址
  adminUrl: cccccccccc
  # authToken鉴权开关 0关 1 开
  authTokenCheck: 0
  publicKey: MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAvba1waNb84DqknbTRhU2un9cbj8Lh0EuIB0OGfDtSePZJBs/YYhSj4k630hLWVb5/6nEIV3WuHBQsJPgrnL01qD1YUS2UvbwWHonO31oR5PZfW1UFIC33CjVTOFskfDWhFroMHYlOomWUCkUHyvVC2e5YjFn1vEbwn/wRQ9YPxwMx4cWSSr7FccArqNGW1hbw3vNOA74+DVl7vgn8eTAHvAAa9wjtFVv/bxlrNH89WnDxkcaI/vnCH4iacKd5uJwUJDjm9/JRy/beFKBFH5CuguykIS7/EA0qLdJaBjTgCIbt7PRBMzPtLiZbtJc9hrvZbBSq2jsU71dQFZd9p4nbKuZMj8F0FLfY7DnbxlgyGxFnrbrJC4qQdUrR5o9I9BuoULTOufBLUIRc4o0ziyh//ED9dP/bBVvDpKbxcbTSC3xipLKaXPzfnpu4vfXOBqJduEa12Tn4bLASVgV1Qd5iVO0+fx7OVYUEXGwV5h3dcIyWYsHFNpdW1L4KxKGe58cyGBKE8Uu8IzEctsbrjdG9/dDg1OlmgRBkUZGKdObAgcgnD9bdlBeuKOt0XflEwVLLieBai7O5gOqA/x5I/O2C0Gi6yrkD0nrd9Ow1zPyX4WfzUUZuSDzaLxQc/y4710lN9ycQcnrv6/aN1ipbvwKZzSYxuYeHdm8Rr5jRJn35J0CAwEAAQ==
  privateKey: MIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggkoAgEAAoICAQC9trXBo1vzgOqSdtNGFTa6f1xuPwuHQS4gHQ4Z8O1J49kkGz9hiFKPiTrfSEtZVvn/qcQhXda4cFCwk+CucvTWoPVhRLZS9vBYeic7fWhHk9l9bVQUgLfcKNVM4WyR8NaEWugwdiU6iZZQKRQfK9ULZ7liMWfW8RvCf/BFD1g/HAzHhxZJKvsVxwCuo0ZbWFvDe804Dvj4NWXu+Cfx5MAe8ABr3CO0VW/9vGWs0fz1acPGRxoj++cIfiJpwp3m4nBQkOOb38lHL9t4UoEUfkK6C7KQhLv8QDSot0loGNOAIhu3s9EEzM+0uJlu0lz2Gu9lsFKraOxTvV1AVl32nidsq5kyPwXQUt9jsOdvGWDIbEWetuskLipB1StHmj0j0G6hQtM658EtQhFzijTOLKH/8QP10/9sFW8OkpvFxtNILfGKksppc/N+em7i99c4Gol24RrXZOfhssBJWBXVB3mJU7T5/Hs5VhQRcbBXmHd1wjJZiwcU2l1bUvgrEoZ7nxzIYEoTxS7wjMRy2xuuN0b390ODU6WaBEGRRkYp05sCByCcP1t2UF64o63Rd+UTBUsuJ4FqLs7mA6oD/Hkj87YLQaLrKuQPSet307DXM/JfhZ/NRRm5IPNovFBz/LjvXSU33JxByeu/r9o3WKlu/ApnNJjG5h4d2bxGvmNEmffknQIDAQABAoICAQCQPLDVx0Dq0tIFh0g8WXahtqFsxIwcSmcqTUziVjXi3tPByuGWYtH6hWh4SHvZMfKi+cy/XZwZ4HLIvbWujIIwCHJngJlqXIsBieX4KfG1sehCn3O0nuSrA3Sgnicwkj5WpNMxvTGy3taknYbJ6EZzBGqDCHMdjXUAnMrthqweV1I0BTXhbUc3GHUhUcv1OSmTZ7XwcBnEhLy6QnwEQln7a5e2acnPmsGp1aosCf9cCwyo3Hg4cZYG8/dwF2J8Hx8rce1LHormj7E6Ougez5wl3SznCbPvJlWjDK9H8NAk9EnEkndRQfKNQWiORGwfRP1MgBFqdzxusmOKvd902Gu5tmSpMAztjoBikLSe+4UUtAyYTQ3/6F1e7+cyzemNW8J5NldjP4Uy2+Ds6aPIJK0uLUS37NnFdKvosf63lRAY5FhPA88iLaXSeUBkxPK2isAVKIreWjTZcbu1naBZKjvxwBzeI4ZKymeLRld/OuFB4mwUEbgmr+qtVi65/mC7xFp4k5yo0IqM9u85lAs7zf9zp06HgwbJYgrKme4J6hAEIWVt4PqUHZyD1rhf+Th8I4GshDZ0NAs+RmohHRbvnINLqDLEJBIbJkH1lE5ZecNuRnDkdHGZUUNAqPug1N6rEsakAYMmAhv55uDMRF4y19YwQMOoiNi3aFMbH88CaB0B9QKCAQEA+/AYthQ18uS5QBGDykihBc/2cestrWYRAqy6LkA8ymTtJCnORu6FFyZcwNy+Rctb2jO8DZpShwT4wWj8hw3H+rDFrFUUryjs99OuOgh2N9whCq2637LlPzoR4JiLDpW2w0z4ZiUJVScJ3wKohXRts/b6LC2A9jF2BfHCYYeodKK+ca+CLS7ZCmW52eBgzTBoXM7D0Em2wu8H40QItJqW/********************************/YlkxQBDt2AtlYH85fzffDMiwLtRK13Ir9gXT8wctuggBuBbPlpPf5Qu9pvU1Dm9mCD522q/DMERVFq5Oa+hr63niWCKemkrwKCAQEAwMXGnNogGplTO08uvda7w11OvY0VGLmXBYBiCwKySwDOG4uUwoY+9gQHYrvf9CDSKZJqz+gQuOGiXzBiQiRDHWqarWheW1/XOrwOjkPr/gcEApnGTt0Z40KGUIJgXWOxC4EVF+dvnGC9NZk46eras3FFOwSWzdc/4W89Pu2KZ38yjldYB3EmsawsCcrKf14TUQ6MYl4kHteGU3k5TdkmKZFMvXKazQbH5hhBLSeEY+MTn4+nLVxGtwYckpGmyzQlmYZQ3gWjQJ3N+lq0GGXGH4EO/EK4DmMnXCL5/PmGSfi/BY1CjthQnDJeB/PwPEbgMaTUVYKw3EjdxoUCVJo2cwKCAQAOHLHVqSYod+VkkFaYgx756187nFfPqbL0OmanZpPBJoweBXV3ha7Q0CZL61GBKVX40AMNpAJRa9bfRpk2m3ADO9mmwJFLCnjTnPjaaVTt5he3uGmKVEQBGaicsw6+/2XgZLRZTGPMcEZeRpDYAOCQPxpeVSIvKDAbPyKLQgUOm3sddR5Ol5wyt+YELUxe0gJWiIJxbV+eZbNEaLAwJaSln9YyTYkaRF73ViLx+bDxInJ2GFvXrNyJMaGDLpCsm321OPZzD6Tk/hMHCl/uA+VzfdLRBlQZavH/mwrR5tqdTVyKWT75JMP7MwbQdNUqEVWA6IGbVs1/ULmKU0GKRXsjAoIBAGpMJIrVVKKWLWkk7pdL01mVGBg5tYpevEISWv3LTNlC5mQWAb+KGym5awEsq/+IIZs+25stUhtAgDKREX17VW7hG83HIVCtdpcyn6wfFACNic6NUOan0OCaDEpUuiKFGFlJooH94nOOfIcTqGiEUnhkW1KB4046BUWf+1u5zHnpj80hqK7h2/5Y/CeiJSs6ithFd2++jZhcK/vXepvMw9hkALbufSHVPWFJupbVlZEm4+k2Y28F1RCTVCoWGq0uYhVYFTt4TF8TJUOJxIk3hZpVphfACpIUZt3Ng5/VUBF2Eo9TJ4JxEXIv693iy3PReUqmo1dLq46st24CZhiUuLUCggEAZT9ADuAaDRAV7t4SI78t59uf54NI34eWXdmZvgOcYsd0yH9z0nLddB2QABFAp5/yOeEYMpwBIw/5xzyPVWAPo1lK49zfZZ0akDw2qpvU2fww6Yi4KyXV3nB4fQTgBPWpv+8aJJa0XF5If195zgANyEqf8DmOgvkMk78JMgrWHzW+bW8+SpB0O3cB7p6y/lRhlY/xzriPMGGQ3Acl/8JwjePnqphhnGxWoctebJfrLrBWUaG1r3lLoXmdgaRRj6NbGwzlqPs1weTGqqEFQkiQq4HacQ+ngHA/hsmN51zk4GaDMk6WPA1ScI7YUXEeZGQtMpHkTt80LoEBRcM1w3mFXg==

dxcloud:
  ip: **********:5003
  etaxUrl: http://************:3200/#/login