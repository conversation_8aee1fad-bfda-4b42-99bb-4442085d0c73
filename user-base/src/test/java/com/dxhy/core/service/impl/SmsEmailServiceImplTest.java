package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;

@Slf4j
class SmsEmailServiceImplTest {
    @Test
    void webhookSend() {
        RestTemplate restTemplate =  new RestTemplate();
        JSONObject json = new JSONObject();
        json.put("msgtype","text");
        JSONObject text = new JSONObject();
        text.put("content","测试哈哈哈哈");
        json.put("text",text);
        log.info("【企业微信机器人报警】请求参数：{}",json);
        ResponseEntity<JSONObject> entity = restTemplate.postForEntity("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6ad278ea-aed8-4848-aa20-b852673da5cd",json,JSONObject.class);
        JSONObject response = entity.getBody();
        log.info("【企业微信机器人报警】请求响应：{}",response);
        int errcode = response.getInteger("errcode");
        String errmsg =  response.getString("errmsg");

    }
}