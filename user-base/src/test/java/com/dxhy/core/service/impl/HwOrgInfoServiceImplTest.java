package com.dxhy.core.service.impl;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.UUID;

class HwOrgInfoServiceImplTest {

    @BeforeEach
    void setUp() {
    }

    @AfterEach
    void tearDown() {
    }

    @Test
    void getUUID16() {
        String uuid= UUID.randomUUID().toString().replace("-", "").toUpperCase();
        System.out.println(uuid.substring(0,15));
    }
}