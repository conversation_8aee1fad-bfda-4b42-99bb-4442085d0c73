# API同步模块

本模块提供了API同步相关功能，可以作为可选模块集成到user-service项目中。

## 功能介绍

该模块主要提供以下功能：
- 用户信息同步接口
- 组织信息同步接口
- 登录信息同步接口

## 模块结构

```
user-api-sync/
  ├── src/main/java/com/dxhy/apisync/
  │   ├── config/                  # 配置类
  │   │   └── ApiSyncAutoConfiguration.java
  │   ├── controller/              # 控制器
  │   │   └── ApiSyncController.java
  │   └── service/                 # 服务接口和实现
  │       ├── ApiSyncService.java
  │       └── impl/
  │           └── ApiSyncServiceImpl.java
  └── src/main/resources/
      └── META-INF/
          └── spring.factories     # Spring Boot自动配置
```

## 配置说明

### 1. 启用/禁用API同步模块

在application.yml或特定环境的配置文件中，可以通过以下配置来控制是否启用API同步功能：

```yaml
# API同步模块配置
apisync:
  # 是否启用API同步功能，默认为false
  enabled: true  # 设置为true启用，false禁用
```

### 2. 打包时包含/排除API同步模块

在user-base模块的pom.xml中，API同步模块被设置为可选依赖（`optional=true`）。这意味着：

- 当其他模块依赖user-base时，不会传递依赖user-api-sync
- 在user-base模块中，可以根据配置决定是否启用API同步功能

```bash
mvn clean package -P dev,without-api-sync  #该命令代表打dev环境的包并且不包含对外同步模块
mvn clean package -P dev,with-api-sync  #该命令代表打dev环境的包并且包含对外同步模块
mvn clean package -P dev  #该命令代表打dev环境的包并且包含对外同步模块，因为它是默认启用的
```

或者在父项目pom.xml中临时注释掉user-api-sync模块：

```xml
<modules>
    <module>user-core</module>
    <module>user-base</module>
    <!-- <module>user-api-sync</module> -->
</modules>
```

## 接口说明

API同步模块提供以下接口：

- `/syncapi/{interfaceName}`: API同步通用接口，根据interfaceName参数调用不同的业务逻辑
  - xxLogin: 登录信息同步
  - userInfo: 用户信息同步
  - orgInfo: 组织信息同步

## 开发说明

如需扩展API同步功能，可以在ApiSyncService接口中添加新的方法，并在ApiSyncServiceImpl中实现相应的业务逻辑。 