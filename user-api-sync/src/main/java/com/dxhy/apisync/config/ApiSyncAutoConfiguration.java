package com.dxhy.apisync.config;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

/**
 * API同步模块自动配置类
 * 通过apisync.enabled属性控制是否启用API同步功能
 */
@Configuration
@ConditionalOnProperty(prefix = "apisync", name = "enabled", havingValue = "true", matchIfMissing = false)
@ComponentScan(basePackages = {"com.dxhy.apisync"})
public class ApiSyncAutoConfiguration {
    // 配置类，通过条件注解控制是否启用此模块
} 