/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.apisync.service;

import com.dxhy.core.common.response.Result;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 * API同步服务接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/14 15:11
 */
public interface ApiSyncService {

	/**
	 * 获取登录信息
	 * @param request HTTP请求
	 * @param response HTTP响应
	 * @param tenantId 租户ID
	 * @param commonDecrypt 解密后的内容
	 * @return 登录结果
	 */
	Result getLogin(HttpServletRequest request, HttpServletResponse response, String tenantId, String commonDecrypt);

	/**
	 * 获取用户信息
	 * @param request HTTP请求
	 * @param response HTTP响应
	 * @param tenantId 租户ID
	 * @param commonDecrypt 解密后的内容
	 * @return 用户信息结果
	 */
	Result userInfo(HttpServletRequest request, HttpServletResponse response, String tenantId, String commonDecrypt);

	/**
	 * 获取组织信息
	 * @param request HTTP请求
	 * @param response HTTP响应
	 * @param tenantId 租户ID
	 * @param commonDecrypt 解密后的内容
	 * @return 组织信息结果
	 */
	Result orgInfo(HttpServletRequest request, HttpServletResponse response, String tenantId, String commonDecrypt);
} 