/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.apisync.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.dxhy.apisync.service.ApiSyncService;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.handler.TokenHandleUtil;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.DTO.EntUserDTO;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.SysUserRoleVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.BSystemLogicService;
import com.dxhy.core.service.SigninService;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.utils.Generator;
import com.dxhy.core.utils.MycstUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.dxhy.core.utils.GenerateRoleCodeUtil.generateNum;

/**
 * <p>
 * API同步服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/14 15:11
 */
@Service
@Slf4j
public class ApiSyncServiceImpl implements ApiSyncService {

	private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

	@Autowired
	private SysUserMapper sysUserMapper;

	@Autowired
	private SysDeptMapper sysDeptMapper;
	@Autowired
	private SysRoleMapper sysRoleMapper;
	@Autowired
	private SysTenantProductMapper sysTenantProductMapper;
	@Autowired
	private ProductMenuMapper productMenuMapper;

	@Autowired
	private SysUserService sysUserService;

	@Autowired
	private RedisTemplate redisTemplate;

	@Autowired
	private NacosParam nacosParam;

	@Resource
	private SigninService signinService;

	@Resource
	private BSystemLogicService bSystemLogicService;
	
	@Autowired
	private MycstUtil mycstUtil;

	@SneakyThrows
	@Override
	public Result getLogin(HttpServletRequest request, HttpServletResponse response, String tenantId, String commonDecrypt) {
		JSONObject jsonObject = JSONObject.parseObject(commonDecrypt);
		if (!jsonObject.containsKey("username") || StringUtils.isBlank(jsonObject.getString("username"))) {
			return Result.error("账号不能为空！");
		}
		if (jsonObject.getString("username").length() < 3) {
			return Result.error("账号长度不能小于3！");
		}
		if (!jsonObject.containsKey("taxname") || StringUtils.isBlank(jsonObject.getString("taxname"))) {
			return Result.error("局端账号不能为空！");
		}
		if (!jsonObject.containsKey("kxzh") || StringUtils.isBlank(jsonObject.getString("kxzh"))) {
			return Result.error("可信账号不能为空！");
		}
		// 校验登录账号
		String username = jsonObject.getString("username");
		UserVO localUserInfo = sysUserService.findUserByUsername(username);
		if (localUserInfo == null || localUserInfo.getUserId() == null) {
			log.error("单点登录 getLocalUser error, userName: {}, localUserInfo: {}", username, localUserInfo);
			return Result.error("账号不存在！");
		}

		if (null != localUserInfo.getDeptId()) {
			SysDept sysDept = sysDeptMapper.selectByDeptId(localUserInfo.getDeptId());
			if (sysDept != null && StrUtil.isNotEmpty(sysDept.getTaxpayerCode())) {
				// 保存河北航信可信账号
				this.redisTemplate.opsForValue().set(SystemConstants.HBHX_TAX_NAME + sysDept.getTaxpayerCode(), jsonObject.getString("kxzh"));
			}
		}

		EntUserDTO entUserDTO = new EntUserDTO();
		entUserDTO.setClientId("fatc");
		entUserDTO.setClientSecret("fatc");
		entUserDTO.setUsername(username);
		String token = signinService.getToken(request, entUserDTO);
		log.info("==> token:" + token);
		//token重新存入redis 方便续期
		String access_token = (String) this.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
		if (access_token == null) {
			log.info("==>单点登录：redis中不存在USER_TOKEN_CODE_KEY，重新存入");
			TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
			DecodedJWT djwt = tokenHandleUtil.verify(token, CommonConstant.SIGN_KEY);
			if (djwt != null) {
				Long exp = Long.valueOf(djwt.getClaim("exp").asInt());
				Long time = exp - System.currentTimeMillis() / 1000;
				log.info("剩余时间：" + time);
				this.redisTemplate.opsForValue().set(SystemConstants.USER_TOKEN_CODE_KEY + token, token, time * 2, TimeUnit.SECONDS);
			}
		}

		JSONObject json = new JSONObject();
		json.put("token", token);
		json.put("redirectUrl", nacosParam.dxEtaxUrl);
		return Result.ok().put("data", json);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Result userInfo(HttpServletRequest request, HttpServletResponse response, String tenantId, String commonDecrypt) {
		SysUser sysUser = JSONObject.parseObject(commonDecrypt, SysUser.class);
		if (StrUtil.hasEmpty(sysUser.getUsername(), sysUser.getNickname(), sysUser.getDeptId()) || (sysUser.getDeptList().size() < 1)) {
			return Result.error("必填参数不能为空！");
		}
		if (sysUser.getUsername().length() < 3) {
			return Result.error("账号长度不能小于3！");
		}
		try {
			Integer deptCount = sysDeptMapper.selectCount(new EntityWrapper<SysDept>().eq("dept_id", sysUser.getDeptId()));
			if (deptCount < 1) {
				return Result.error("所属组织不存在！");
			}
			List<String> ch = this.checkDeptList(sysUser.getDeptList());
			if (ch.size() > 0) {
				return Result.error(String.format("权限组织id[%s]不存在！", ch.stream().collect(Collectors.joining(","))));
			}
			sysUser.setCreateBy(1L);
			sysUser.setUpdateBy(1L);
			sysUser.setStatus(1);//启用
			sysUser.setUserType("4");
			sysUser.setUserSource("6");
			sysUser.setDelFlag("0");
			sysUser.setTenantId(tenantId);
			// 添加该用户所属顶级机构
			SysDept sysDept = sysDeptMapper.selectTopLevelByDeptId(sysUser.getDeptId());
			if (sysDept == null) {//超管用户
				sysUser.setTopLevel(sysUser.getDeptId());
			} else {
				sysUser.setTopLevel(sysDept.getDeptId());
			}
			List<SysUser> users = sysUserMapper.selectList(new EntityWrapper<SysUser>().eq("username", sysUser.getUsername()));
			if (users.size() > 0) {
				sysUser.setUserId(users.get(0).getUserId());
				sysUserService.redisDeleteByUserId(sysUser.getUserId());
				sysUserMapper.deleteUserDeptRelation(sysUser.getUserId());
				sysUserMapper.deleteUserRoleRelation(sysUser.getUserId());
				sysUser.setUpdateTime(new Date());
				sysUserMapper.updateById(sysUser);
			} else {
				sysUser.setPassword(ENCODER.encode("88888888"));
				sysUser.setCreateTime(new Date());
				//1、添加用户
				sysUserMapper.addUser(sysUser);
			}

			// 2、添加用户机构关联
			List<String> deptList = sysUser.getDeptList();
			for (String deptId : deptList) {
				sysUserMapper.addUserDeptRelation(sysUser.getUserId(), deptId);
			}
			//3、新建角色
			SysRole sysRole = new SysRole();
			sysRole.setRoleName("管理员");
			sysRole.setRoleDesc("接口创建用户时自动创建角色");
			sysRole.setType(0);
			sysRole.setDeptId(sysUser.getDeptId());
			sysRole.setDeptName(sysDept.getName());
			sysRole.setCreateTime(new Date());
			sysRole.setCreateBy(sysUser.getUserId());
			sysRole.setUpdateTime(new Date());
			sysRole.setUpdateBy(sysUser.getUserId());
			sysRole.setRoleProperty("2");
			sysRole.setTenantId(tenantId);
			sysRole.setDelFlag("0");
			sysRole.setRoleType(11);
			//查到数据库最后一个角色编码
			String roleCode = sysRoleMapper.selectLastRoleCode();
			if(org.apache.commons.lang.StringUtils.isBlank(roleCode)){
				sysRole.setRoleCode(generateNum(4,""));
			}else{
				sysRole.setRoleCode(generateNum(4,roleCode));
			}
			sysRoleMapper.insert(sysRole);
			//查询 SysTenantProduct 获取租户已授权且未过期的所有产品id
			List<Long> productIdList = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>()
					.eq("tenant_id", tenantId)
					.ge("auth_etime", new Date())
					.le("auth_stime", new Date())
					.andNew("dept_id is null OR dept_id = ''")
			).stream().map(SysTenantProduct::getProductId).collect(Collectors.toList());
			//4、添加角色和菜单关联，获取已授权产品的所有菜单,除系统管理中的菜单(系统管理不属于任何产品所以没有产品id，查菜单时自然查不出来了)
			List<String> menuIdList = productMenuMapper.getMenuIdListByProId(productIdList);
			for (String menuId : menuIdList) {
				SysRoleMenuEntity sysRoleMenuEntity = new SysRoleMenuEntity();
				sysRoleMenuEntity.setMenuId(menuId);
				sysRoleMenuEntity.setRoleId(sysRole.getRoleId());
				sysRoleMapper.addRoleMenuRelation(sysRoleMenuEntity);
			}
			//5、添加用户角色关联
			SysUserRoleVo sysUserRoleVo = new SysUserRoleVo();
			sysUserRoleVo.setUserId(sysUser.getUserId());
			sysUserRoleVo.setRoleId(sysRole.getRoleId());
			sysUserMapper.addUserRoleRelation(sysUserRoleVo);
		} catch (Exception e) {
			log.error("Exception:", e);
			return Result.error("系统异常，请联系管理员！");
		}
		return Result.ok();
	}

	private List<String> checkDeptList(List<String> deptList) {
		List<String> list1 = sysUserMapper.checkDeptList(deptList);
		List<String> subtract1 = (List<String>) CollectionUtils.subtract(list1, deptList);
		List<String> subtract2 = (List<String>) CollectionUtils.subtract(deptList, list1);
		subtract1.addAll(subtract2);
		return subtract1;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Result orgInfo(HttpServletRequest request, HttpServletResponse response, String tenantId, String commonDecrypt) {
		SysDept sysDept = JSONObject.parseObject(commonDecrypt, SysDept.class);
		if (StrUtil.hasEmpty(sysDept.getDeptId(), sysDept.getName(), sysDept.getDeptSname(), sysDept.getTaxpayerCode(),sysDept.getTaxpayerProvinceCode()) || null == sysDept.getTaxpayerType()) {
			return Result.error("必填参数不能为空!");
		}
		try {
			sysDept.setDelFlag("0");
			sysDept.setCode("ID-" + "A");
			String parentId = sysDept.getParentId();
			if (StringUtils.isBlank(parentId)) {
				//把该机构绑定到租户所属的顶级机构上
				SysDept tenantdept = sysDeptMapper.selectTopOrgByTenantId(tenantId);
				if (tenantdept == null) {
					return Result.error("租户顶级机构不存在！");
				}
				sysDept.setCode(tenantdept.getCode() + "ID-" + "A");
				sysDept.setParentId(tenantdept.getDeptId());
				sysDept.setLevel(2);
				sysDept.setDeptType(2);
			} else {
				//根据子code 查询 父code 查询到 说明是子级
				SysDept parentDept = sysDeptMapper.selectByDeptIdAndTenantId(parentId, tenantId, null);
				if (parentDept != null) {
					sysDept.setCode(sysDept.getCode() + "ID-" + "A");
					sysDept.setEnterpriseNumbers(this.getEnterpriseNumbers(sysDept.getEnterpriseNumbers()));
					sysDept.setLevel(sysDept.getLevel() + 1);
					sysDept.setDeptType(2);
					sysDept.setParentId(parentId);
				} else {
					sysDept.setEnterpriseNumbers(this.getEnterpriseNumbers());
					sysDept.setLevel(1);
					sysDept.setDeptType(1);
				}
			}
			sysDept.setDataSource("8");//河北航信
			sysDept.setCreateTime(new Date());
			sysDept.setUpdateTime(new Date());
			sysDept.setCreateUser(1L);
			sysDept.setTenantId(tenantId);
			sysDept.setStatus(1);
			SysDept querySysDept = sysDeptMapper.selectByDeptId(sysDept.getDeptId());
			if (querySysDept != null) {
				//修改时若无spid则立即注册终端,无税号不注册
				if(StringUtils.isBlank(querySysDept.getSpId()) && StringUtils.isNotBlank(sysDept.getTaxpayerCode())){
					sysDept.setSpId(mycstUtil.terminalRegist(sysDept));
				}else {
					sysDept.setSpId(querySysDept.getSpId());
				}
				sysDeptMapper.update(sysDept, new EntityWrapper<SysDept>().eq("dept_id", sysDept.getDeptId()));
			} else {
				//新增时直接注册终端,无税号不注册
				if(StringUtils.isNotBlank(sysDept.getTaxpayerCode())){
					sysDept.setSpId(mycstUtil.terminalRegist(sysDept));
				}
				sysDeptMapper.insert(sysDept);
			}
			// 更新code
			sysDeptMapper.updateCodeByTenantIdAndDeptId(sysDept.getDeptId(), tenantId);
			// 如果税号不为空的话则同步税号到销项服务
			if(StringUtils.isNotBlank(sysDept.getTaxpayerCode())){
				JSONObject json = new JSONObject();
				json.put("name", sysDept.getName());
				json.put("taxpayerCode", sysDept.getTaxpayerCode());
				json.put("taxpayerType", sysDept.getTaxpayerType());
				json.put("taxBureaName", "taxCloud");
				json.put("taxBureaPass", "taxCloud123");
				json.put("tenantId", tenantId);
				//开票方式默认是SHRPA，目前已经没用了，需要动态匹配通道
				json.put("kpfs", "SHRPA");
				json.put("spid", sysDept.getSpId());
				CommonRspVo resp = bSystemLogicService.einvoiceAddDept(json);
				if (!resp.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
					log.info("Saas系统同步租户信息到票税系统：全电数据交互失败");
					return Result.error(resp.getCode(), resp.getMessage());
				}
			}
			// 查询租户管理员,增加该机构维护
			List<SysUser> tenantAdminList = sysUserMapper.selectList(new EntityWrapper<SysUser>().eq("dept_id", sysDept.getParentId()));
			if(CollectionUtils.isNotEmpty(tenantAdminList)){
				SysUser tenantAdmin = tenantAdminList.get(0);
				sysUserMapper.addUserDeptRelation(tenantAdmin.getUserId(), sysDept.getDeptId());
				//查询该租户已开通产品
				Wrapper<SysTenantProduct> wrapper = new EntityWrapper<SysTenantProduct>()
						.eq("tenant_id", tenantAdmin.getTenantId())
						//当前时间未超过授权结束时间
						.ge("auth_etime", new Date())
						.le("auth_stime", new Date())
						.andNew("dept_id is null OR dept_id = ''");
				List<SysTenantProduct> sysTenantProducts = sysTenantProductMapper.selectList(wrapper);
				if(CollectionUtils.isNotEmpty(sysTenantProducts)){
					//为该机构开通产品权限
					for (SysTenantProduct sysTenantProduct : sysTenantProducts) {
						SysTenantProduct deptProduct = new SysTenantProduct();
						BeanUtils.copyProperties(sysTenantProduct, deptProduct);
						deptProduct.setId(null);
						deptProduct.setCreateBy("1");
						deptProduct.setUpdateBy("1");
						deptProduct.setCreateTime(new Date());
						deptProduct.setUpdateTime(new Date());
						deptProduct.setDeptId(sysDept.getDeptId());
						sysTenantProductMapper.insert(deptProduct);
					}
				}
			}
		} catch (Exception e) {
			log.error("Exception:", e);
			Result.error("系统异常，请联系管理员");
		}
		return Result.ok();
	}

	public String getEnterpriseNumbers() {
		/**
		 * 生成企业编码
		 */
		String enterpriseNumbers = "";
		for (int i = 1; i > 0; i++) {
			enterpriseNumbers = Generator.getRandomChar();
			SysDept system = sysDeptMapper.queryDeptByQybm(enterpriseNumbers);
			if (system == null) {
				break;
			}
		}
		return Generator.getRandomChar();
	}

	public String getEnterpriseNumbers(String parentEnterNumbers) {
		/**
		 * 生成子企业编码
		 */
		String enterpriseNumbers = "";
		for (int i = 1; i > 0; i++) {
			enterpriseNumbers = parentEnterNumbers;
			SysDept system = sysDeptMapper.queryDeptByQybm(enterpriseNumbers + "0" + i);
			if (system == null) {
				break;
			}
		}
		return Generator.getRandomChar();
	}
} 