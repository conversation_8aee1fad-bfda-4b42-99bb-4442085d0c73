/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.apisync.controller;

import cn.hutool.core.util.StrUtil;
import com.dxhy.apisync.service.ApiSyncService;
import com.dxhy.core.common.openapi.common.ApiResult;
import com.dxhy.core.common.openapi.common.ErrorStatusEnum;
import com.dxhy.core.common.openapi.common.ResponseData;
import com.dxhy.core.common.openapi.common.ResponseStutas;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.ApiConstant;
import com.dxhy.core.pojo.entity.SysTenant;
import com.dxhy.core.service.ITenantService;
import com.dxhy.core.utils.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * <p>
 * API同步控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/14 15:10
 */
@RestController
@RequestMapping
@Slf4j
public class ApiSyncController {

	private static final String LOGGER_MSG = "(对外接口暴露调用)";

	@Autowired
	private ApiSyncService apiSyncService;

	@Autowired
	private ITenantService tenantService;

	@PostMapping("/syncapi/{interfaceName}")
	@ApiOperation("同步用户信息")
	public String tenantSync(HttpServletRequest request, HttpServletResponse response,
	                         @PathVariable("interfaceName") String interfaceName,
	                         @ApiParam(name = "Timestamp", value = "当前时间戳", required = true) @RequestParam(value = "Timestamp") String timestamp,
	                         @ApiParam(name = "Nonce", value = "随机正整数", required = true) @RequestParam(value = "Nonce") String nonce,
	                         @ApiParam(name = "SecretId", value = "标识用户身份的SecretId", required = true) @RequestParam(value = "SecretId") String secretId,
	                         @ApiParam(name = "Signature", value = "请求签名", required = true) @RequestParam(value = "Signature") String signature,
	                         @ApiParam(name = "encryptCode", value = "加密标识 0:不加密,1:加密", required = true) @RequestParam(value = "encryptCode") String encryptCode,
	                         @ApiParam(name = "zipCode", value = "压缩标识 0:不压缩,1:压缩", required = true) @RequestParam(value = "zipCode") String zipCode,
	                         @ApiParam(name = "content", value = "业务请求参数", required = true) @RequestParam(value = "content") String content) {

		log.info("请求的secretId:{}，nonce:{},timestamp:{},zipCode:{},encryptCode:{},签名值sign:{}，内容content:{}", secretId, nonce, timestamp, zipCode, encryptCode, signature, content);
		ApiResult result = new ApiResult();
		ResponseStutas responseStatus = new ResponseStutas();
		ResponseData responseData = new ResponseData();
		try {
			request.setCharacterEncoding(StandardCharsets.UTF_8.name());
			response.setCharacterEncoding(StandardCharsets.UTF_8.name());

			// 校验接口入参是否为空
			result = this.checkInterfaceParam("", interfaceName, timestamp, nonce, secretId, signature, encryptCode, zipCode, content);

			responseStatus = (ResponseStutas) result.get(ApiConstant.RESPONSESTATUS);

			if (!ApiConstant.SUCCSSCODE.equals(responseStatus.getCode())) {
				log.error("数据格式校验未通过.");
				return JsonUtils.getInstance().toJsonString(result);
			}

			result = this.auth(request, response);
			//获取鉴权结果
			responseStatus = (ResponseStutas) result.get(ApiConstant.RESPONSESTATUS);
			//获取密钥
			String secretKey = (String) result.get(ApiConstant.SECRETKEY);
			//获取租户id
			String tenantId = (String) result.get(ApiConstant.TENANTID);

			if (!ApiConstant.SUCCSSCODE.equals(responseStatus.getCode())) {
				log.error("鉴权未通过");
				return JsonUtils.getInstance().toJsonString(result);
			}

			String commonDecrypt2 = this.commonDecrypt(zipCode,encryptCode,content,secretKey);
			log.debug("{}解密日志：{}", LOGGER_MSG, commonDecrypt2);

			// 调用业务逻辑处理方法
			log.debug("{}开始调用业务方法：{}", LOGGER_MSG, interfaceName);
			String returnJsonString = userApiHandingBusiness(request, response, interfaceName, tenantId, commonDecrypt2);
			log.debug("{},调用业务方法完成，返回数据:{}", LOGGER_MSG, returnJsonString);

			String data = null;

			log.debug("{},接口返回数据:{}", LOGGER_MSG, returnJsonString);
			if (!StringUtils.isBlank(returnJsonString)) {
				/**
				 * 加密
				 */
				data = this.commonEncrypt(zipCode,encryptCode,returnJsonString,secretKey);
				result.remove(ApiConstant.SECRETKEY);
				result.remove(ApiConstant.TENANTID);
				log.debug("{},加密后返回数据:{}", LOGGER_MSG, data);
			}

			if (data != null) {
				responseStatus.setCode(ErrorStatusEnum.SUCCESS.getCode());
				responseStatus.setMessage(ErrorStatusEnum.SUCCESS.getMessage());
				responseData.setContent(data);
				responseData.setEncryptCode(encryptCode);
				responseData.setZipCode(zipCode);
				result.put(ApiConstant.RESPONSESTATUS, responseStatus);
				result.put(ApiConstant.RESPONSEDATA, responseData);
				log.info("{},接口:{}调用成功,返回数据:{}", LOGGER_MSG, interfaceName, JsonUtils.getInstance().toJsonString(result));
				return JsonUtils.getInstance().toJsonString(result);
			}

		} catch (Exception e) {
			result.remove(ApiConstant.SECRETKEY);
			result.remove(ApiConstant.TENANTID);
			log.error("接口请求数据出现异常,异常原因为:{}", e);
		}
		responseStatus.setCode(ErrorStatusEnum.FAIL.getCode());
		responseStatus.setMessage(ErrorStatusEnum.FAIL.getMessage());
		result.put(ApiConstant.RESPONSESTATUS, responseStatus);
		result.remove(ApiConstant.SECRETKEY);
		result.remove(ApiConstant.TENANTID);
		log.info("调用失败,返回数据:{}", JsonUtils.getInstance().toJsonString(result));
		return JsonUtils.getInstance().toJsonString(result);
	}

	private String userApiHandingBusiness(HttpServletRequest request, HttpServletResponse response, String interfaceName, String tenantId, String commonDecrypt) {
		//返回参数
		Result result;
		switch (interfaceName) {
			case "xxLogin":
				result = apiSyncService.getLogin(request, response, tenantId, commonDecrypt);
				break;
			case "userInfo":
				result = apiSyncService.userInfo(request, response, tenantId, commonDecrypt);
				break;
			case "orgInfo":
				result = apiSyncService.orgInfo(request, response, tenantId, commonDecrypt);
				break;
			default:
				log.error("{},接口:{}未定义", LOGGER_MSG, interfaceName);
				result =  Result.error(ErrorStatusEnum.CHECK_INTERFACENAME_NULL.getCode(), ErrorStatusEnum.CHECK_INTERFACENAME_NULL.getMessage());
		}
		return JsonUtils.getInstance().toJsonString(result);
	}


	public ApiResult checkInterfaceParam(String interfaceVersion, String interfaceName, String timestamp, String nonce, String secretId, String signature, String encryptCode, String zipCode, String content) {
		log.info("{},数据校验,请求的interfaceVersion:{},interfaceName:{},timestamp:{},nonce:{},secretId:{},signature:{},encryptCode:{},zipCode:{},content:{}", LOGGER_MSG, interfaceVersion, interfaceName, timestamp, nonce, secretId, signature, encryptCode, zipCode, content);

		if (StringUtils.isBlank(interfaceName)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_INTERFACENAME_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_INTERFACENAME_NULL.getCode(), ErrorStatusEnum.CHECK_INTERFACENAME_NULL.getMessage()));
		} else if (StringUtils.isBlank(timestamp)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_TIMESTAMP_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_TIMESTAMP_NULL.getCode(), ErrorStatusEnum.CHECK_TIMESTAMP_NULL.getMessage()));
		} else if (StringUtils.isBlank(nonce)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_NONCE_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_NONCE_NULL.getCode(), ErrorStatusEnum.CHECK_NONCE_NULL.getMessage()));
		} else if (StringUtils.isBlank(secretId)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_SECRETID_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_SECRETID_NULL.getCode(), ErrorStatusEnum.CHECK_SECRETID_NULL.getMessage()));
		} else if (StringUtils.isBlank(signature)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_SIGNATURE_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_SIGNATURE_NULL.getCode(), ErrorStatusEnum.CHECK_SIGNATURE_NULL.getMessage()));
		} else if (StringUtils.isBlank(encryptCode)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_ENCRYPTCODE_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_ENCRYPTCODE_NULL.getCode(), ErrorStatusEnum.CHECK_ENCRYPTCODE_NULL.getMessage()));
		} else if (StringUtils.isBlank(zipCode)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_ZIPCODE_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_ZIPCODE_NULL.getCode(), ErrorStatusEnum.CHECK_ZIPCODE_NULL.getMessage()));
		} else if (StringUtils.isBlank(content)) {
			log.error("{}数据校验失败,错误信息为:{}", LOGGER_MSG, ErrorStatusEnum.CHECK_CONTENT_NULL.getMessage());
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.CHECK_CONTENT_NULL.getCode(), ErrorStatusEnum.CHECK_CONTENT_NULL.getMessage()));
		}
		return ApiResult.ok(new ResponseStutas(ErrorStatusEnum.SUCCESS.getCode(), ErrorStatusEnum.SUCCESS.getMessage()));
	}

	public ApiResult auth(HttpServletRequest request, HttpServletResponse response) {
		response.setCharacterEncoding(StandardCharsets.UTF_8.name());
		response.setContentType("text/json;charset=UTF-8");
		String nonce = request.getParameter(ApiConstant.NONCE);
		String secretId = request.getParameter(ApiConstant.SECRETID);
		String timeStamp = request.getParameter(ApiConstant.TIMESTAMP);
		String zipCode = request.getParameter(ApiConstant.ZIPCODE);
		String encryptCode = request.getParameter(ApiConstant.ENCRYPTCODE);
		String content = request.getParameter(ApiConstant.CONTENT);
		String reqSign = request.getParameter(ApiConstant.SIGNATURE);
		log.info("{},请求的secretId:{}，nonce:{},timestamp:{},zipCode:{},encryptCode:{},签名值sign:{}，内容content:{}", LOGGER_MSG, secretId, nonce, timeStamp, zipCode, encryptCode, reqSign, content);
		StringBuilder url = new StringBuilder();
//		url.append(request.getMethod()).append(request.getServerName());
//		if (ApiConstant.INT_80 != request.getServerPort() && ApiConstant.INT_443 != request.getServerPort()) {
//			url.append(":").append(request.getServerPort());
//		}
		url.append(request.getRequestURI()).append("?");
//		url.append("/user-base/syncapi/orgInfo?");
		log.info("{}生成签名的URL:{}", LOGGER_MSG, url);
		//特定排序
		TreeMap<String, String> sortMap = new TreeMap<>();
		sortMap.put(ApiConstant.NONCE, nonce);
		sortMap.put(ApiConstant.SECRETID, secretId);
		sortMap.put(ApiConstant.TIMESTAMP, timeStamp);
		sortMap.put(ApiConstant.CONTENT, content);
		sortMap.put(ApiConstant.ENCRYPTCODE, encryptCode);
		sortMap.put(ApiConstant.ZIPCODE, zipCode);

		//获取id对应的key
		SysTenant tenant = tenantService.getSecret(secretId);
		//校验产品是否已开通
		boolean validProduct = tenantService.validTenantProduct(secretId, ApiConstant.PRODUCT_KP);

		//获取key为空
		if (null == tenant || StrUtil.isEmpty(tenant.getSecretKey())) {
			log.error("{}根据secretId:{},获取对应的secretKey为空!", LOGGER_MSG, secretId);
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.NOTAUTH.getCode(), ErrorStatusEnum.NOTAUTH.getMessage()));
		}
		if(!validProduct){
			log.error("{}根据secretId:{},校验产品是否已开通,未开通!", LOGGER_MSG, secretId);
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.NOTPRODUCT.getCode(), ErrorStatusEnum.NOTPRODUCT.getMessage()));
		}
		//获取密钥及租户id
		String secretKey = tenant.getSecretKey();
		String tenantId = tenant.getTenantId();
		log.info("{}通过secretId:{},获取的对应的secretKey:{},对应的tenantId:{}", LOGGER_MSG, secretId, secretKey,tenantId);
		if(StringUtils.isBlank(secretKey)){
			log.error("{}根据secretId:{},获取对应的secretKey为空!", LOGGER_MSG, secretId);
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.NOTAUTH.getCode(), ErrorStatusEnum.NOTAUTH.getMessage()));
		}
		String localSign = "";
		try {
			localSign = HmacSha1Util.genSign(url.toString(), sortMap, secretKey);
		} catch (Exception e) {
			log.error("{}鉴权异常,鉴权URL为:{},secretId为:{},secretKey为:{},错误原因为:{}", LOGGER_MSG, url.toString(), secretId, secretKey, e);
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.AUTHFAIL.getCode(), ErrorStatusEnum.AUTHFAIL.getMessage()));
		}
		log.info("{}生成的本地签名值为local:{}，请求签名值:{}", LOGGER_MSG, localSign, reqSign);

		if (StringUtils.isNotBlank(localSign) && StringUtils.isNotBlank(reqSign)) {
			if (localSign.equals(reqSign)) {
				log.info("secretId:{},鉴权成功", secretId);
				Map<String, Object> result = new HashMap<>();
				result.put(ApiConstant.RESPONSESTATUS,new ResponseStutas(ErrorStatusEnum.SUCCESS.getCode(), ErrorStatusEnum.SUCCESS.getMessage()));
				result.put(ApiConstant.SECRETKEY,secretKey);
				result.put(ApiConstant.TENANTID,tenantId);
				return ApiResult.ok(result);
			} else {
				log.error("{}鉴权失败.请求鉴权值为:{},计算后鉴权值为:{}", LOGGER_MSG, reqSign, localSign);
				return ApiResult.error(new ResponseStutas(ErrorStatusEnum.AUTHFAIL.getCode(), ErrorStatusEnum.AUTHFAIL.getMessage()));
			}
		} else {
			log.error("{}鉴权失败.请求鉴权值和计算后鉴权值为空", LOGGER_MSG);
			return ApiResult.error(new ResponseStutas(ErrorStatusEnum.AUTHFAIL.getCode(), ErrorStatusEnum.AUTHFAIL.getMessage()));
		}
	}

	/**
	 * 密钥处理，大于24为截取，不足24位补0至24位
	 * @param key
	 * @return
	 */
	public static byte[] padKey(String key) {
		// 将字符串转换为字节数组
		byte[] keyBytes = key.getBytes();

		// 如果密钥长度不足 24 字节，则补 0
		if (keyBytes.length < 24) {
			byte[] paddedKey = new byte[24];
			System.arraycopy(keyBytes, 0, paddedKey, 0, keyBytes.length);
			// 剩余部分补 0
			for (int i = keyBytes.length; i < 24; i++) {
				paddedKey[i] = 0;
			}
			return paddedKey;
		} else if (keyBytes.length > 24) {
			// 如果密钥长度超过 24 字节，截取前 24 字节
			byte[] truncatedKey = new byte[24];
			System.arraycopy(keyBytes, 0, truncatedKey, 0, 24);
			return truncatedKey;
		} else {
			// 如果密钥长度正好是 24 字节，直接返回
			return keyBytes;
		}
	}

	/**
	 * 参数压缩 加密
	 */
	public String commonEncrypt(String zipCode, String encryptCode, String content, String secretKey) {
		String json = content;
		byte[] de = null;
		// 加密
		if (ApiConstant.ENCRYPTCODE_1.equals(encryptCode)) {
			try {
				// 获取秘钥
				String password = new String(padKey(secretKey));
				// 加密
				de = TripleDesUtil.encryptMode(password, json.getBytes());
			} catch (Exception e) {
				log.error("{}3DES加密出现异常:{}", LOGGER_MSG, e);
			}
		}
		if (ApiConstant.ZIPCODE_1.equals(zipCode)) {
			// 压缩
			try {
				if (de != null) {
					de = GzipUtils.compress(de);
				} else {
					de = GzipUtils.compress(json.getBytes());
				}
			} catch (Exception e) {
				log.error("{}GZIP压缩出现异常:{}", LOGGER_MSG, e);
			}
		}
		try {
			if (de != null) {
				json = Base64Encoding.encodeToString(de);
			} else {
				json = Base64Encoding.encodeToString(content.getBytes());
			}
		} catch (Exception e) {
			log.error("{}base64压缩出现异常:{}", LOGGER_MSG, e);
		}
		return json;
	}

	/**
	 * 参数解压缩 解密
	 *
	 * @return
	 */
	public String commonDecrypt(String zipCode, String encryptCode, String content, String secretKey) {

		String json = content;
		byte[] de = null;
		try {
			json = Base64Encoding.decodeToString(content);
			de = Base64Encoding.decode(content);
		} catch (Exception e) {
			log.error("{}base64解密出现异常:{}", LOGGER_MSG, e);
		}
		if (ApiConstant.ZIPCODE_1.equals(zipCode)) {
			// 解压缩
			try {
				de = GzipUtils.decompress(de);
				json = new String(de, StandardCharsets.UTF_8);
			} catch (Exception e) {
				log.error("{}解压缩出现异常:{}", LOGGER_MSG, e);
			}
		}
		// 解密
		if (ApiConstant.ENCRYPTCODE_1.equals(encryptCode)) {
			try {
				// 获取秘钥
				String password = new String(padKey(secretKey));
				// 解密
				json = new String(TripleDesUtil.decryptMode(password, de), StandardCharsets.UTF_8);
			} catch (Exception e) {
				log.error("{}3DES解密出现异常:{}", LOGGER_MSG, e);
			}
		}
		return json;
	}
} 