# 标普云

## user-service 用户中心
```
编码规范请参考《阿里云JAVA开发手册》
本工程为父子项目，考虑到之后扩容，现在使用两个子工程
user-base 与前端交互，打包后以此工程为主
user-core 业务逻辑处理，与数据库交互，作为依赖被user-base引用
```

### 项目集成和部分功能
```
springboot（基础框架）
nacos（配置管理） 
swagger（接口文档）
redis（缓存）
mysql（数据存储）
mybatis-plus（简单的增删改查，分页查询）
logback（日志管理）
统一响应参数 com.dxhy.core.response
统一异常处理 com.dxhy.base.config.DefaultExceptionHandler
```

## 开发须知

### 关于pojo
```
此包内实体类结尾应该和包一致，大写字母。比如 UserDTO/UserVo/UserPO/UserBO

PO 与数据库表内字段一一对应
    如果要使用mybatisplus，请额外使用@TableName、@TableId、@TableField等注解
VO 响应到页面展示的参数
    需要使用swagger相关注解帮助前端理解字段，例如@ApiModel、@ApiModelProperty等
BO 业务层对象，例如调用接口或者方法时需要自定义对象等，使用此后缀
    自定义的查询数据返回结果也需要使用此类对象
DTO 前端调用后端接口传递的对象
    需要使用swagger相关注解帮助前端理解字段，例如@ApiModel、@ApiModelProperty等
```

### swagger
```
swagger访问路径
http://xx.xx.xx.xx:pppp/user-base/swagger-ui.html
http://xx.xx.xx.xx:pppp/user-base/doc.html
为方便前后端联调，强烈建议完善controller和pojo的swagger注解
```

### 各环境说明
```
DEV 开发环境、内部测试环境    开发阶段功能验证
SIT 客户测试环境    用于客户验证项目功能、第三方系统接口联调等
VER 客户预发环境、客户灰度测试环境    用户上线前漏洞扫描、灰度测试、功能验证等
PRD 客户生产环境    正式上线之后运行的环境
```

### 分页查询
```
mapper接口
    继承BaseMapper类
        例如：public interface TestMapper extends BaseMapper<TempTablePO> {
    入参要增加 Page<E> page
        例如：List<TempTablePO> selectAll(Page<TempTablePO> page);
impl示例
    @Override
    public PageUtils testPage() {
        Page<TempTablePO> tempTablePOPage = new Page<>(2, 5);
        List<TempTablePO> tempTablePOList = testMapper.selectAll(tempTablePOPage);
        tempTablePOPage.setRecords(tempTablePOList);
        PageUtils pageUtils = new PageUtils(tempTablePOPage);
        return pageUtils;
    }
    其中2是当前页，5是每页数量+
```

### 日志
```
代码中有三处控制日志级别的地方
1 application.yml 的 logging.level.com.dxhy.core.mapper参数，控制sql操作的日志级别，当前配置为debug
2 logback.xml 的 root 标签，控制全局日志级别，主要为框架加载和运行时产生的日志，当前配置为info
3 logback.xml 的 logger 标签，控制指定包的日志级别，主要为业务代码运行时产生的日志，当前配置为debug

当前配置已满足开发和各环境运行时打印日志的需要，不建议修改各处日志级别
日志路径 /data/log/user-base 或 C:\data\log\user-base
```

### 配置文件与打包
```
application.yml中spring.profiles.active 是启用的配置文件，打包时将以此配置进行打包
使用@profileActive@时，将会匹配右侧maven中profiles中选择的配置，自动加载application-***.yml
需要打包时可以点击右侧user-service下的package，系统默认jar包，可通过user-base的pom文件进行修改

nacos和application-***.yml
系统使用两部分配置文件，一个是nacos，一个是application-***.yml
修改后需要立即生效的配置放到nacos中，例如其他服务的地址、全局参数等
修改后需要重启生效的配置放到application-***.yml中，例如数据库连接配置、项目占用端口等
nacos托管的配置文件建议按照不同环境保存到nacos-config文件夹内，便于运维直接使用
```

### 项目部署
```
项目部署前需要保证以下插件状态正常
1 java 1.8
2 mysql 5.7+
3 nacos 2.0
4 redis 5.X
5 nginx 1.X
jar包启动命令参考 nohup java -Xms1024m -Xmx2048m -jar itax-base.jar  &
项目接口访问路径：http://xx.xx.xx.xx:pppp/user-base/*****/*****
```

### 其他注意事项
```
为发挥服务器性能，打包前建议手动调整数据库连接数等配置
新开项目时建议将此标品的稳定版本拷贝到新的git路径下，不建议从已有版本下拉分支，容易造成版本混乱且git权限不好控制
启动项目前注意防火墙进站规则中是否配置了所指定的端口
```

### 缺点
```
redis不支持哨兵模式
分页查询不够灵活，复杂查询的分页功能建议手写
为了整合nacos，spring版本不够新，可能通不过安全要求较高企业的漏洞扫描
未集成消息队列、定时任务和权限验证框架（例如SpringCloudGateway或ApacheShiro）
不支持多数据源和主从mysql
未使用nacos或eureka等做服务发现和控制
jar包过大，需要做jar和lib的分离
```

###### Good luck !

###### Tips by zjj
