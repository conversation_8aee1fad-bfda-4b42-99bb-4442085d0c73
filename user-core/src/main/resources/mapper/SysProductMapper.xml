<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysProductMapper">


	<select id="selectByTenantId" resultType="com.dxhy.core.pojo.vo.SysProductVo">
		SELECT
			stp.auth_etime,
			stp.tenant_id,
			sp.`name`,
			sp.detail
		FROM
			sys_tenant_product stp
				LEFT JOIN sys_product sp ON stp.product_id = sp.id
		WHERE
			sp.is_deleted = 0
		  AND stp.tenant_id = #{tenantId}
		ORDER BY
			auth_etime
			LIMIT 1
	</select>



</mapper>
