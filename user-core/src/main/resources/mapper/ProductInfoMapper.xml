<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.ProductInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.ProductInfo">
        <id column="id" property="id"/>
        <id column="uid" property="uid"/>
        <result column="name" property="name"/>
        <result column="distributor_id" property="distributorId"/>
        <result column="type" property="type"/>
        <result column="icon" property="icon"/>
        <result column="product_status" property="productStatus"/>
        <result column="introduction" property="introduction"/>
        <result column="open_dimension" property="openDimension"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="product_class" property="productClass"/>
        <result column="product_source" property="productSource"/>
    </resultMap>

    <resultMap id="ListResultMap" type="com.dxhy.core.pojo.entity.ProductInfo">
        <id column="id" property="id"/>
        <result column="uid" property="uid"/>
        <result column="distributor_id" property="distributorId"/>
        <result column="name" property="name"/>
        <result column="type" property="type"/>
        <result column="icon" property="icon"/>
        <result column="introduction" property="introduction"/>
        <result column="open_dimension" property="openDimension"/>
        <result column="status" property="status"/>
        <result column="superior" property="superior"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
        <result column="product_class" property="productClass"/>
        <result column="product_source" property="productSource"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, type, icon, introduction, open_dimension, product_status,create_time, modify_time,product_class,product_source
    </sql>
    <sql id="List_Column_List">
        pi.id, pi.uid, pi.name, pi.type, pi.icon, pi.introduction, pi.open_dimension,
        pi.create_time, pi.modify_time, ps.status, pi.distributor_id,pi.product_class,pi.product_source
    </sql>
    <!-- 分支机构查询平台产品 -->
    <select id="selectProductList" resultMap="ListResultMap">
        select * from(
        <if test="sign == null">
        SELECT
        <include refid="List_Column_List"/>
        ,1 AS superior
        FROM product_info pi LEFT JOIN product_status ps ON ps.product_id=pi.id
        AND ps.distributor_id=#{distributorId}
        <where>
            del_flag='0'
            <if test="distributorId!=null and distributorId!=''">
                AND pi.distributor_id=#{distributorId}
            </if>
            <if test="productName!=null and productName!=''">
                AND pi.name LIKE CONCAT('%',#{productName},'%')
            </if>
            <if test="status!=null and status!=''">
                <choose>
                    <when test='status=="2"'>
                        AND ps.`status` = '2'
                    </when>
                    <otherwise>
                        AND (ps.`status` = '0' or ps.`status` = '1' or ps.`status`='3' or ps.`status` is null)
                    </otherwise>
                </choose>
            </if>
        </where>
        </if>
        <if test="sign == null">
            <if test="retParent!=null and retParent.size()>0">
                UNION ALL
            </if>
        </if>
        <if test="retParent!=null and retParent.size()>0">
            SELECT
            <include refid="List_Column_List"/>
            ,2 AS superior
            FROM product_info pi LEFT JOIN product_status ps ON ps.product_id=pi.id
            <choose>
                <when test="sign !=null and sign != ''">
                    AND ps.distributor_id != #{distributorId}
                </when>
                <otherwise>
                    AND ps.distributor_id=#{distributorId}
                </otherwise>
            </choose>
            <where>
                del_flag='0'
                AND pi.distributor_id in
                <foreach collection="retParent" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
                AND exists(select id from product_status where status='2' and product_id=pi.id)

                <if test="productName!=null and productName!=''">
                    AND pi.name LIKE CONCAT('%',#{productName},'%')
                </if>
                <if test="status!=null and status!=''">
                    <choose>
                        <when test='status=="2"'>
                            AND ps.`status` = '2'
                        </when>
                        <otherwise>
                            AND (ps.`status` = '0' or ps.`status` = '1' or ps.`status`='3' or ps.`status` is null)
                        </otherwise>
                    </choose>
                </if>
            </where>
        </if>
        <if test="sign == null">
            <if test="retSub!=null and retSub.size()>0">
                UNION ALL
            </if>
        </if>
        <if test="retSub!=null and retSub.size()>0">
            SELECT
            <include refid="List_Column_List"/>
            ,3 AS superior
            FROM product_info pi LEFT JOIN product_status ps ON ps.product_id=pi.id
            <choose>
                <when test="sign !=null and sign != ''">
                    AND ps.distributor_id != #{distributorId}
                </when>
                <otherwise>
                    AND ps.distributor_id=#{distributorId}
                </otherwise>
            </choose>
            <where>
                del_flag='0'
                AND pi.distributor_id in
                <foreach collection="retSub" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
                <if test="productName!=null and productName!=''">
                    AND pi.name LIKE CONCAT('%',#{productName},'%')
                </if>
                <if test="status!=null and status!=''">
                    <choose>
                        <when test='status=="2"'>
                            AND ps.`status` = '2'
                        </when>
                        <otherwise>
                            AND (ps.`status` = '0' or ps.`status` = '1' or ps.`status`='3' or ps.`status` is null)
                        </otherwise>
                    </choose>
                </if>
            </where>
        </if>
        ) p group by p.id
        order by p.superior asc,p.create_time desc
        <if test="page!=null">
            limit #{page},#{limit}
        </if>;
    </select>
    <!-- 分支机构查询平台产品分页总条数 -->
    <select id="selectProductCount" resultType="Integer">
        SELECT
        count(pi.id)
        FROM
        product_info pi
        LEFT JOIN product_status ps ON ps.product_id = pi.id and ps.distributor_id=#{distributorId}
        <where>
            del_flag='0'
            AND (
            <choose>
                <when test="sign !=null and sign != ''">
                    pi.distributor_id != #{distributorId}
                </when>
                <otherwise>
                    pi.distributor_id = #{distributorId}
                </otherwise>
            </choose>
            <if test="retParent!=null and retParent.size()>0">
                OR (pi.distributor_id in
                <foreach collection="retParent" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
                and exists(select ps1.id from product_status ps1 where ps1.status='2' and ps1.product_id=pi.id))
            </if>
            <if test="retSub!=null and retSub.size()>0">
                OR pi.distributor_id in
                <foreach collection="retSub" item="item" open="(" close=")" separator="," index="index">
                    #{item}
                </foreach>
            </if>
            )
            <if test="productName!=null and productName!=''">
                AND pi.name LIKE CONCAT('%',#{productName},'%')
            </if>
            <if test="status!=null and status!=''">
                <choose>
                    <when test='status=="2"'>
                        AND ps.`status` = '2'
                    </when>
                    <otherwise>
                        AND (ps.`status` = '0' or ps.`status` = '1' or ps.`status`='3' or ps.`status` is null )
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>
    <!--根据分销商id查询当前分销商对应状态的产品列表 -->
    <select id="selectListByDistributorId" resultMap="ListResultMap">
        SELECT
        <include refid="List_Column_List"/>
        FROM
        product_info pi,product_status ps
        WHERE ps.product_id = pi.id
            AND pi.del_flag = '0'
        <if test="distributorId!=null">
            AND ps.distributor_id = #{distributorId}
        </if>
        <!--<if test="currentDistributorId!=null">-->
            <!--AND pi.distributor_id = #{currentDistributorId}-->
        <!--</if>-->
        <if test="productName!=null and productName!=''">
            AND pi.name LIKE CONCAT('%',#{productName},'%')
        </if>
        <if test="productClass!=null and productClass!=''">
            AND pi.product_class = #{productClass}
        </if>
        <choose>
            <when test="status!=null">
                AND ps.`status` = #{status}
            </when>
            <otherwise>
                AND ps.`status` != '3'
            </otherwise>
        </choose>
        ORDER BY pi.create_time DESC
    </select>
    <!--根据产品列表查询-->
    <select id="selectListByProductId" resultMap="ListResultMap">
        SELECT
        <include refid="List_Column_List"/>
        FROM
        product_info pi,product_status ps
        WHERE ps.product_id = pi.id
        <if test="productIdList!=null and productIdList.size>0">
            AND ps.product_id IN
            <foreach collection="productIdList" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="status!=null">
                AND ps.`status` = #{status}
            </when>
            <otherwise>
                AND ps.`status` != '3'
            </otherwise>
        </choose>
        ORDER BY pi.create_time DESC
    </select>
    <!--分支机构查询平台产品-->
    <select id="selectProductInfoList" resultMap="ListResultMap">
        SELECT
            <include refid="List_Column_List"/>
        FROM
        product_info pi
        LEFT JOIN product_status ps ON ps.product_id = pi.id
        <if test="distributorId!=null">
            AND pi.distributor_id = #{distributorId}
        </if>
        WHERE
        pi.del_flag = '0'
        <if test="productClass!=null and productClass!=''">
            AND pi.product_class != #{productClass}
        </if>
        <if test="distributorIdLevel !=null">
          AND ps.distributor_id = #{distributorIdLevel}
        </if>
        <choose>
            <when test="status!=null and status!=''">
                AND ps.`status` = #{status}
            </when>
            <otherwise>
                AND ps.`status` != '3'
            </otherwise>
        </choose>
        <if test="productId != null and productId.size() > 0">
            AND pi.id IN
            <foreach collection="productId" separator="," close=")" open="(" item="productId">
                #{productId}
            </foreach>
        </if>
        <if test="productName !=null and productName!= ''">
            AND pi.name like "%" #{productName} "%"
        </if>
        <if test="status != 2">
        UNION ALL
        (
        SELECT
            pi.id,
            pi.uid,
            pi.name,
            pi.type,
            pi.icon,
            pi.introduction,
            pi.open_dimension,
            pi.create_time,
            pi.modify_time,
            1 AS STATUS,
            pi.distributor_id,
            pi.product_class,
            pi.product_source
        FROM product_info pi WHERE NOT EXISTS (
                SELECT
                    <include refid="List_Column_List"/>
                FROM product_status ps
                WHERE
                <if test="distributorIdLevel !=null">
                    ps.distributor_id = #{distributorIdLevel}
                </if>
                AND ps.product_id = pi.id
            )
            <if test="productClass!=null and productClass!=''">
                AND pi.product_class != #{productClass}
            </if>
            <if test="productId != null and productId.size() > 0">
                AND pi.id IN
                <foreach collection="productId" separator="," close=")" open="(" item="productId">
                    #{productId}
                </foreach>
            </if>
        ) order by create_time desc
        </if>
        <if test="page!=null">
            limit ${page},${limit}
        </if>;
  </select>

    <select id="selectCustomerProductTypeByUser" resultType="int">
        select
          product_type
        from
          customer_product
        where
          del_flag=0
        and
          account_info_id=#{accountInfoId}
        and
          product_id=#{productId}
        limit 1
    </select>

    <select id="selectSysDeptByDeptId" resultType="map">
        select
          level,create_user as createUser,dept_type as deptType
        from
          sys_dept
        where
          dept_id=#{deptId}
    </select>

    <select id="selectTaxControlByDeptId" resultType="string">
        select
          sksbbm
        from
          sys_tax_control
        where
          dept_id=#{deptId} and `type`=#{type}
    </select>

</mapper>
