<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: lengleng (<EMAIL>)
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysRoleMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysRole">
		<id column="role_id" property="roleId" />
		<result column="role_name" property="roleName" />
		<result column="role_code" property="roleCode" />
		<result column="role_property" property="roleProperty" />
		<result column="type" property="type" />
		<result column="dept_id" property="deptId" />
		<result column="dept_name" property="deptName" />
		<result column="role_desc" property="roleDesc" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
		<result column="distributor_id" property="distributorId" />
		<result column="del_flag" property="delFlag" />
		<result column="role_type" property="roleType" />
	</resultMap>

	<!-- roleVo -->
	<resultMap id="BaseResultMapRoleVo" type="com.dxhy.core.pojo.vo.SysRoleVo">
		<id column="role_id" property="roleId" />
		<result column="role_name" property="roleName" />
		<result column="role_code" property="roleCode" />
		<result column="role_property" property="roleProperty" />
		<result column="type" property="type" />
		<result column="dept_id" property="deptId" />
		<result column="dept_name" property="deptName" />
		<result column="role_desc" property="roleDesc" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
		<result column="distributor_id" property="distributorId" />
		<result column="del_flag" property="delFlag" />
		<result column="role_type" property="roleType" />
	</resultMap>

	<!--roleDto-->
	<resultMap id="RoleDtoMap" type="com.dxhy.core.pojo.DTO.RoleDTO">
		<id column="role_id" property="roleId" />
		<result column="role_name" property="roleName" />
		<result column="role_code" property="roleCode" />
		<result column="role_property" property="roleCode" />
		<result column="type" property="type" />
		<result column="dept_id" property="deptId" />
		<result column="role_desc" property="roleDesc" />
		<result column="create_time" property="createTime" />
		<result column="create_by" property="createBy" />
		<result column="update_time" property="updateTime" />
		<result column="update_by" property="updateBy" />
		<result column="distributor_id" property="distributorId" />
		<result column="del_flag" property="delFlag" />
		<result column="role_type" property="roleType" />
		<result column="name" property="deptName"/>
	</resultMap>

	<select id="selectRolePage" resultMap="RoleDtoMap">
		SELECT
			*
		FROM
			sys_role r
		LEFT JOIN sys_role_dept rd ON rd.role_id = r.role_id
		LEFT JOIN sys_dept d ON rd.dept_id = d.dept_id
		WHERE r.del_flag = 0
		ORDER BY r.role_id ASC
	</select>
	<select id="selectListByDeptId" resultMap="BaseResultMap">
		SELECT
			r.*
		FROM sys_role r LEFT OUTER JOIN sys_dept_role sdr ON sdr.role_id = r.role_id
		WHERE sdr.dept_id = #{deptId}
	</select>
	
	<!-- 查询角色列表 -->
	<select id="getEntRoleList" resultType="com.dxhy.core.pojo.vo.SysRoleVo">
		SELECT
			r.role_id,r.role_name,r.role_code,r.role_desc,r.create_time,r.role_property,r.dept_id,r.distributor_id
		FROM
			sys_role r
		LEFT JOIN base_ent_role er ON r.role_id = er.role_id
		WHERE
			er.ent_id = #{entId}
		<if test="roleName != null and roleName != ''">
			AND POSITION(#{roleName} IN r.role_name)
		</if>
		order by r.create_time desc
	</select>
	<!-- 查询角色是否存在 -->
	<select id="getRoleName" resultType="com.dxhy.core.pojo.vo.SysRoleVo">
		SELECT
			r.role_id,r.role_name,r.role_code,r.role_desc,r.create_time,r.role_property,r.dept_id,r.distributor_id
		FROM
			sys_role r
		LEFT JOIN base_ent_role er ON r.role_id = er.role_id 
		WHERE
			er.ent_id = #{entId}
			AND r.role_name = #{roleName}
	</select>

	<select id="getUserAndEntRoleList" resultType="com.dxhy.core.pojo.vo.SysRoleVo">
		SELECT
		r.role_id,r.role_name,r.role_code,r.role_desc,r.create_time,r.role_property,r.dept_id,r.distributor_id
		FROM
		sys_role r
		LEFT JOIN sys_user_role ur ON r.role_id= ur.role_id
		WHERE
		 ur.user_id = #{userId}
		order by r.create_time desc
	</select>

	<select id="queryRoleNameCountByDeptId" resultType="int">
		SELECT
		count(1)
		FROM
		sys_role sr
		WHERE
		sr.dept_id = #{deptId}
		<if test="roleName != null and roleName !=''">
			AND sr.role_name = #{roleName}
		</if>
	</select>


	<!--组织id查询改组织下的所有角色  不查询超级管理员-->
	<select id="queryRoleListByDeptId" resultMap="BaseResultMapRoleVo">
		select * from sys_role where 1=1
		<if test="loginUserId != 1 and loginUserId != null">
			AND (role_type &lt;&gt; 10 or role_type is null)
		</if>
		<if test="deptId != '' and deptId != null">
			AND dept_id = #{deptId}
		</if>
		<if test="roleName != '' and roleName != null">
			AND role_name like '%${roleName}%'
		</if>
		AND del_flag='0'
		order by create_time desc
    </select>

	<!--角色id查询用户个数-->
	<select id="queryRoleOfUserCountByRoleId" resultType="java.lang.Integer">
        SELECT count(DISTINCT(ur.user_id)) FROM sys_user_role ur,sys_role sr,sys_user su WHERE ur.role_id=sr.role_id AND ur.user_id=su.user_id AND sr.dept_id=su.top_level AND ur.role_id = #{roleId}
	</select>

	<!--角色id查询详情-->
	<select id="selectByRoleId" resultMap="BaseResultMapRoleVo">
		select * from sys_role where role_id = #{roleId}
	</select>
	<!--角色id查询详情-->
	<select id="selectByRoleId2" resultMap="BaseResultMap">
		select * from sys_role where role_id = #{roleId}
	</select>

	<!--组织id查询角色详情-->
	<select id="selectByDeptId" resultMap="BaseResultMapRoleVo">
		select * from sys_role where
		1=1
		<if test="deptId != '' and deptId != null">
		AND dept_id = #{deptId}
		</if>
		AND del_flag='0'
	</select>

	<!--组织id查询角色详情-->
	<select id="selectRolesByDeptId" resultMap="BaseResultMap">
		select * from sys_role where
		1=1
		<if test="deptId != '' and deptId != null">
			AND dept_id = #{deptId}
		</if>
		AND del_flag='0'
	</select>

	<!--角色id查询menuList-->
	<select id="queryMenuIdListByRoleId" resultType="String">
		select menu_id from sys_role_menu where role_id = #{roleId}
	</select>

	<!--部门id 角色名称查询角色是否重复-->
	<select id="queryRoleByRoleNameAndDeptId" resultType="int">
		select count(1) from sys_role where dept_id = #{deptId} and role_name = #{roleName}
	</select>

	<!--查找平台侧所有角色-->
	<select id="queryListAll" resultMap="BaseResultMapRoleVo">
		select * from sys_role where  del_flag = '0'
	</select>


	<!--角色id 更新-->
	<update id="updateRoleByRoleId" parameterType="com.dxhy.core.pojo.DTO.AdminRoleOperateDto">
		update  sys_role  set
		<if test="roleName != '' and roleName != null">
			role_name = #{roleName},
		</if>
		<if test="describe != '' and describe != null">
			remark = #{describe},
		</if>
		<if test="pUserId != '' and pUserId != null">
			update_by = #{userId},
		</if>
		where role_id = #{roleId}
	</update>

	<!--角色id删除菜单关联-->
	<delete id="deleteRoleMenuByRoleId">
		delete from sys_role_menu where  role_id=#{roleId}
	</delete>

	<!--添加角色菜单之间的关系-->
	<insert id="addRoleMenuRelation"  parameterType="com.dxhy.core.pojo.entity.SysRoleMenuEntity">
		insert into sys_role_menu (role_id,menu_id) values (#{roleId},#{menuId})
	</insert>

	<!--添加角色-->
	<insert id="addRole" parameterType="com.dxhy.core.pojo.DTO.AdminRoleOperateDto" useGeneratedKeys="true" keyProperty="roleId" keyColumn="role_id">
		insert into sys_role (role_name,role_code,role_property,`type`,dept_id,dept_name,role_desc,create_by,create_time,update_by,update_time,distributor_id,del_flag,role_type) values (#{roleName},#{roleCode},#{roleProperty},0,#{deptId},#{deptName},#{describe},#{userId},#{createTime},#{userId},#{updateTime},#{distributorId},'0',#{roleType})
	</insert>

	<!--角色id 查询用户-->
	<select id="queryUserIdListByRoleId" resultType="long">
		SELECT ur.user_id FROM sys_user_role ur,sys_role sr,sys_user su WHERE ur.role_id=sr.role_id AND ur.user_id=su.user_id AND sr.dept_id=su.top_level AND ur.role_id = #{roleId}  group by ur.user_id
	</select>

    <!--组织id 角色名字查询角色信息-->
    <select id="selectRoleByDeptIdAndRoleName" resultMap="BaseResultMap">
		select sr.* from sys_role sr left join sys_dept_role sdr on sr.role_id = sdr.role_id  where sr.role_name = #{roleName}  and sdr.dept_id =#{deptId}
	</select>

	<!--角色id删除组织关联-->
	<delete id="deleteRoleDeptRelation">
		delete from sys_dept_role where  role_id=#{roleId}
	</delete>

	<!--添加角色-->
	<insert id="addRoleDeptRelation"  >
		insert into sys_dept_role (role_id,dept_id) values (#{roleId},#{deptId})
	</insert>

	<!--用户id查询角色信息-->
	<select id="selectRolesByUserId" resultMap="BaseResultMap">
		select
		sr.*
		from sys_role sr
		left join sys_user_role sur on sr.role_id = sur.role_id
		where sur.user_id=#{userId}
	</select>

	<!--用户id  deptid  查询角色信息-->
	<select id="selectRolesByUserIdAndDeptId" resultMap="BaseResultMap">
		select
		sr.*
		from sys_role sr
		left join sys_user_role sur on sr.role_id = sur.role_id
		left join sys_dept_role sdr on sdr.role_id = sur.role_id
		where sur.user_id=#{userId} and sdr.dept_id = #{deptId}
	</select>

	<!--查询最后一个角色编码-->
	<select id="selectLastRoleCode" resultType="java.lang.String">
        select role_code from sys_role WHERE role_code !='' and role_code not like 'C%' order by role_id desc LIMIT 1
    </select>

	<select id="selectRoleByUserId" resultMap="BaseResultMap">
		select * from sys_role where role_name='管理员' and create_by = #{puserId} and del_flag=0
	</select>
	<select id="selectSmRolesByUserId" resultType="com.dxhy.core.pojo.entity.SysRole">
		select
			sr.*
		from sys_role sr
				 left join sys_user_role sur on sr.role_id = sur.role_id
		where sur.user_id=#{userId} and sr.role_type =10 and sr.del_flag=0
	</select>
    <select id="getSuperRoleListByCompanyName" resultType="com.dxhy.core.pojo.entity.SysRole">
		select * from sys_role where dept_name = #{name} and role_type &lt;= 11 and del_flag=0
	</select>
	<select id="queryRoleList" resultType="com.dxhy.core.pojo.entity.SysRole">
		select * from sys_role where 1=1
		<if test="roleType != '' and roleType != null">
			AND role_type = #{roleType}
		</if>
		<if test="roleName != '' and roleName != null">
			AND role_name = #{roleName}
		</if>
		<if test="tenantId != '' and tenantId != null">
			AND distributor_id = #{tenantId}
		</if>
		AND del_flag='0'
	</select>


</mapper>
