<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.ComboAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.ComboAccount">
        <result column="id" property="id" />
        <result column="account_info_id" property="accountInfoId" />
        <result column="combo_distributor_id" property="comboDistributorId" />
        <result column="create_time" property="createTime" />
        <result column="number" property="number" />
        <result column="product_id" property="productId" />
        <result column="status" property="status" />
        <result column="customer_id" property="customerId" />
        <result column="unit" property="unit" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_info_id, combo_distributor_id, create_time, number, product_id,status,customer_id,unit
    </sql>
    <!-- 根据产品id获取列表 -->
    <select id="selectListByProductId" resultMap="BaseResultMap">
        select *
        from combo_account
        where status = 0
          and number is not null
          and product_id in
        <foreach item="productList" collection="productList" open="(" separator="," close=")">
            #{productList}
        </foreach>
    </select>

    <select id="queryComboAccount" parameterType="map" resultType="com.dxhy.core.pojo.entity.ComboAccount">
        SELECT
        ca.id,
        ca.account_info_id as accountInfoId,
        ca.combo_distributor_id as comboDistributorId,
        ca.create_time as createTime,
        ca.number,
        ca.product_id as productId,
        ca.status,
        ca.customer_id as customerId,
        ca.unit,
        pi.name as productName
        FROM
             combo_account ca
        left join
            product_info pi
        on
          ca.product_id=pi.id
        where status=0
        <if test='account_info_id !=null and account_info_id != ""'>
            and ca.account_info_id = #{account_info_id}
        </if>
        <if test='product_id !=null and product_id != ""'>
            and ca.product_id = #{product_id}
        </if>
        <if test='customer_id !=null and customer_id != ""'>
            and ca.customer_id = #{customer_id}
        </if>
    </select>

</mapper>
