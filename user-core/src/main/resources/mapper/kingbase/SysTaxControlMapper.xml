<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysTaxControlMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysTaxControl">
        <id column="id" property="id"/>
        <result column="dept_id" property="deptId"/>
        <result column="sksbbm" property="sksbbm"/>
        <result column="sksbmc" property="sksbmc"/>
        <result column="type" property="type"/>
    </resultMap>
    <select id="selectAceByDeptId" resultType="com.dxhy.core.pojo.entity.SysTaxControl">
        select * from sys_tax_control where dept_id = #{deptId} and `type`=#{type}
    </select>

</mapper>
