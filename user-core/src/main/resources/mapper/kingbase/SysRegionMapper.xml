<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysRegionMapper">

    <!--查询所有启用的所属地区信息-->
    <select id="selectAllArea" resultType="com.dxhy.core.pojo.vo.RegionParent"
            parameterType="java.lang.Integer">
        SELECT
        REGION_CODE as regionCode, REGION_NAME as regionName
        FROM
        SYS_REGION
        <where>
            <if test="isdel != null">
                AND ISDEL = #{isdel,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY REGION_CODE ASC
    </select>

</mapper>