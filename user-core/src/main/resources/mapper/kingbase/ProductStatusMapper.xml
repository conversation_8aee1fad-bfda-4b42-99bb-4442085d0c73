<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.ProductStatusMapper">


    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.ProductStatus" >
        <result column="id" property="id" />
        <result column="product_id" property="productId" />
        <result column="distributor_id" property="distributorId" />
        <result column="uid" property="uid" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
        product_id,
        distributor_id,
        uid,
        status,
        create_time,
        modify_time
    </sql>

    <!--添加角色菜单之间的关系-->
    <insert id="addUserProduct"  parameterType="com.dxhy.core.pojo.entity.ProductStatus" >
        insert into product_status (product_id,distributor_id,auth_stime,auth_etime,uid,`status`,create_time,modify_time)
        values (#{productId},#{distributorId},#{authStime},#{authEtime},#{uid},#{status},now(),now())
    </insert>
    <select id="getProductListByDistrAndProId" resultType="com.dxhy.core.pojo.entity.ProductStatus">
        select * from product_status where `status`=2
        <if test="distributorId!=null and distributorId!=''">
            and distributor_id=#{distributorId}
        </if>
        <if test="list!= null">
            and product_id in
            <!-- for循环, item:循环后的值, index:循环下标列式for循环的 i ,collection:参数名-->
            <!-- open="(" close=")" separator="," 就是把循环的值组成 (item1,item2,item3)的格式-->
            <foreach item="item" index="index" collection="list" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>