<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysIndustryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysIndustry">
        <id column="id" property="id"/>
        <result column="industryCode" property="industryCode"/>
        <result column="industryName" property="industryName"/>
    </resultMap>

    <!--获取行业列表-->
    <select id="getIndustryList" resultMap="BaseResultMap" >
        SELECT * FROM sys_industry
    </select>
    <!--根据行业名称查询对应行业code-->
    <select id="selectIndustryByName"  resultMap="BaseResultMap"  >
        SELECT * FROM sys_industry WHERE industryName=#{industryName}
    </select>
    <!--根据行业编码查询对应行业名称-->
    <select id="selectIndustryByCode"  resultMap="BaseResultMap"  >
        SELECT * FROM sys_industry WHERE industryCode=#{industryCode}
    </select>
</mapper>
