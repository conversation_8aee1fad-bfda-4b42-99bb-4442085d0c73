<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysTaxbureauInfoMapper">

    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysTaxbureauInfo" >
        <result column="id" property="id" />
        <result column="dept_id" property="deptId" />
        <result column="tax_no" property="taxNo" />
        <result column="user_name" property="userName" />
        <result column="user_pass" property="userPass" />
    </resultMap>

    <sql id="Base_Column_List">
        id,
                dept_id,
                tax_no,
                user_name,
                user_pass
    </sql>
    <select id="selectByTaxNo" resultType="com.dxhy.core.pojo.entity.SysTaxbureauInfo">
        select * from sys_taxbureau_info where tax_no = #{taxNo}
    </select>

</mapper>