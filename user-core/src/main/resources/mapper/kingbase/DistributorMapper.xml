<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.DistributorMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.Distributor">
		<id column="id" property="id" />
		<result column="company_name" property="companyName" />
		<result column="location" property="location" />
		<result column="region" property="region" />
		<result column="is_distributor" property="isDistributor" />
		<result column="contact_phone" property="contactPhone" />
		<result column="tax_no" property="taxNo" />
		<result column="bank_name" property="bankName" />
		<result column="bank_no" property="bankNo" />
		<result column="contact_email" property="contactEmail" />
		<result column="superior" property="superior" />
		<result column="level" property="level" />
		<result column="simple_code" property="simpleCode" />
		<result column="del_flag" property="delFlag" />
		<result column="create_time" property="createTime" />
		<result column="modify_time" property="modifyTime" />
		<result column="type" property="type" />
		<result column="taxpayer_type" property="taxpayerType" />
		<result column="accountant_standard" property="accountantStandard" />
		<result column="control_mode" property="controlMode" />
		<result column="trade" property="trade" />
		<result column="trial_days" property="trialDays" />
	</resultMap>

	<!-- 通用查询结果列 -->
	<sql id="Base_Column_List">
		id, company_name, location, region, is_distributor,
		contact_phone,
		tax_no,
		bank_name, bank_no, contact_email, superior,
		level, del_flag, simple_code,
		create_time,
		modify_time,
		type,taxpayer_type,accountant_standard,
		control_mode,trade,trial_days
	</sql>

	<!--根据ID查询渠道信息-->
	<select id="selectDistributorById" parameterType="java.lang.String"
			resultMap="BaseResultMap">
		select * from distributor where id=#{deptId}
	</select>

	<!--根据渠道简码查询渠道信息-->
	<select id="selectDistributorBySimpleCode" parameterType="java.lang.String"
			resultMap="BaseResultMap">
		select * from distributor where simple_code=#{simpleCode}
	</select>

	<!--查询所有子级渠道信息-->
	<select id="selectAllDistributor"  resultMap="BaseResultMap" >
		SELECT * FROM  distributor  WHERE
			1=1
									  AND del_flag ='0' ORDER BY create_time
	</select>

	<!--查询下一级渠道信息-->
	<select id="selectNextDistributor"  resultMap="BaseResultMap" >
		SELECT * FROM  distributor  WHERE
			superior = #{superior}
									  AND del_flag ='0' ORDER BY create_time
	</select>

	<!--根据code查询所有渠道-->
	<select id="queryAllDistributorByName" resultType="java.util.HashMap" >
		SELECT  id AS deptId,superior AS parentId,company_name AS deptName FROM  distributor WHERE
			company_name LIKE '%${deptName}%'  AND del_flag ='0'
	</select>
    <select id="getNextSimpleCode" resultType="java.lang.String">
		select concat(max(simple_code)+1) as simpleCode from distributor;
	</select>
	<select id="selectDistributorByCompanyName" resultType="com.dxhy.core.pojo.entity.Distributor">
		SELECT * FROM  distributor  WHERE
			company_name = #{companyName}
									  AND del_flag ='0'
	</select>
</mapper>
