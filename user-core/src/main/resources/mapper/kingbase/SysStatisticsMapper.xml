<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.core.mapper.StatisticsMapper">

    <select id="cylist" resultType="com.dxhy.core.pojo.DTO.StatisticsDto">
        SELECT
        2 AS serviceType,
        DATE_FORMAT(icr.create_date,'%Y-%m') period,
        tenant_id,
        COUNT(1) count
        FROM
        digital_account.invoice_check_record icr
        LEFT JOIN iomp.sys_dept sd ON icr.gfsh = sd.taxpayer_code
        WHERE
        sd.tenant_id in
        <foreach collection="vo.tenantIdList" item="item" index="index" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        AND icr.create_date BETWEEN #{vo.startTime} AND #{vo.endTime}
        GROUP BY sd.tenant_id, DATE_FORMAT(icr.create_date,'%Y-%M')
    </select>

    <select id="kplist" resultType="com.dxhy.core.pojo.DTO.StatisticsDto">
        SELECT
            3 AS serviceType,
            DATE_FORMAT(oii.kprq,'%Y-%m') period,
            tenant_id,
            COUNT(1) count
        FROM
            bp_e_invoice.order_invoice_info oii
                LEFT JOIN iomp.sys_dept sd ON oii.xhf_nsrsbh = sd.taxpayer_code
        WHERE
            sd.tenant_id in
        <foreach collection="vo.tenantIdList" item="item" index="index" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        AND oii.kprq BETWEEN #{vo.startTime} AND #{vo.endTime}
        GROUP BY DATE_FORMAT(oii.kprq,'%Y-%m'), sd.tenant_id
    </select>

    <select id="rzlist" resultType="com.dxhy.core.pojo.DTO.StatisticsDto">
        SELECT
            1 AS serviceType,
            ici.rzsq period,
            tenant_id,
            COUNT(1) count
        FROM
            digital_account.invoice_certifity_info ici
                LEFT JOIN iomp.sys_dept sd ON ici.gmfsbh = sd.taxpayer_code
        WHERE
            sd.tenant_id in
        <foreach collection="vo.tenantIdList" item="item" index="index" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        AND ici.rzsq BETWEEN #{vo.startTime} AND #{vo.endTime}
        GROUP BY ici.rzsq, sd.tenant_id
    </select>

    <select id="selectDept" resultType="java.lang.String">
        SELECT
            taxpayer_code
        FROM
        sys_dept
        WHERE
            tenant_id in
        <foreach collection="tenantIdList" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
    
</mapper>
