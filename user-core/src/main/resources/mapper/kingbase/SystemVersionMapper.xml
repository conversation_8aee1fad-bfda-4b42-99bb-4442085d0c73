<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SystemVersionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SystemVersion">
        <result column="id" property="id" />
        <result column="version_num" property="versionNum" />
        <result column="parent_id" property="parentId" />
        <result column="release_date" property="releaseDate" />
        <result column="prefix" property="prefix" />
        <result column="text" property="text" />
        <result column="connector" property="connector" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="queryHistoryVersionList" resultType="com.dxhy.core.pojo.entity.SystemVersion">
        select * from system_version order by release_date,create_time desc
        <if test="curFlag == 1">
           limit 1
        </if>
    </select>
    <select id="selectRootVersionList" resultType="com.dxhy.core.pojo.vo.SysVersionVo">
        select version_num as version,release_date as `date` from system_version where parent_id=-1
        group by version_num,release_date
    </select>

</mapper>
