<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.core.mapper.TestMapper">

    <select id="getById" resultType="com.dxhy.core.pojo.PO.TempTablePO">
        SELECT `id`, `name` from temp_table WHERE `id` = #{id} limit 1
    </select>

    <!--<select id="selectAll" resultType="com.dxhy.core.pojo.PO.TempTablePO">
        SELECT `id`, `name` from temp_table order by id
    </select>-->

</mapper>