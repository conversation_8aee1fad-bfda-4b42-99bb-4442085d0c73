<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: lengleng (<EMAIL>)
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysRoleDeptMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysRoleDept">
		<id column="id" property="id" />
		<result column="role_id" property="roleId" />
		<result column="dept_id" property="deptId" />
	</resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, role_id AS roleId, dept_id AS deptId
    </sql>

</mapper>
