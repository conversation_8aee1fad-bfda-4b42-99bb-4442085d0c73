<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: lengleng (<EMAIL>)
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysDeptMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysDept">
        <id column="id" property="id" />
        <result column="dept_id" property="deptId"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="dept_sname" property="deptSname"/>
        <result column="code" property="code"/>
        <result column="level" property="level"/>
        <result column="ein_type" property="einType"/>
        <result column="taxpayer_code" property="taxpayerCode"/>
        <result column="taxpayer_province" property="taxpayerProvince"/>
        <result column="taxpayer_city" property="taxpayerCity"/>
        <result column="taxpayer_county" property="taxpayerCounty"/>
        <result column="taxpayer_province_code" property="taxpayerProvinceCode"/>
        <result column="taxpayer_city_code" property="taxpayerCityCode"/>
        <result column="taxpayer_county_code" property="taxpayerCountyCode"/>
        <result column="taxpayer_address" property="taxpayerAddress"/>
        <result column="taxpayer_phone" property="taxpayerPhone"/>
        <result column="taxpayer_bank" property="taxpayerBank"/>
        <result column="taxpayer_account" property="taxpayerAccount"/>
        <result column="taxpayer_type" property="taxpayerType"/>
        <result column="taxpayer_industry" property="taxpayerIndustry"/>
        <result column="location_code" property="locationCode"/>
        <result column="accounting_principle" property="accountingPrinciple"/>
        <result column="taxpayer_industry_code" property="taxpayerIndustryCode"/>
        <result column="accounting_principle_code" property="accountingPrincipleCode"/>
        <result column="create_time" property="createTime"/>
        <result column="dept_type" property="deptType"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="enterprise_numbers" property="enterpriseNumbers"/>
        <result column="authorization_code" property="authorizationCode"/>
        <result column="source_id" property="sourceId"/>
        <result column="order_num" property="orderNum"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="data_source" property="dataSource"/>
        <result column="status" property="status" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>


    <!-- SysDeptVo映射结果 -->
    <resultMap id="SysDeptVoResultMap" type="com.dxhy.core.pojo.vo.SysDeptVo">
        <result column="dept_id" property="deptId"/>
        <result column="parent_id" property="parentId"/>
        <result column="name" property="name"/>
        <result column="dept_sname" property="deptSname"/>
        <result column="code" property="code"/>
        <result column="level" property="level"/>
        <result column="ein_type" property="einType"/>
        <result column="taxpayer_code" property="taxpayerCode"/>
        <result column="taxpayer_province" property="taxpayerProvince"/>
        <result column="taxpayer_city" property="taxpayerCity"/>
        <result column="taxpayer_county" property="taxpayerCounty"/>
        <result column="taxpayer_province_code" property="taxpayerProvinceCode"/>
        <result column="taxpayer_city_code" property="taxpayerCityCode"/>
        <result column="taxpayer_county_code" property="taxpayerCountyCode"/>
        <result column="taxpayer_address" property="taxpayerAddress"/>
        <result column="taxpayer_phone" property="taxpayerPhone"/>
        <result column="taxpayer_bank" property="taxpayerBank"/>
        <result column="taxpayer_account" property="taxpayerAccount"/>
        <result column="taxpayer_type" property="taxpayerType"/>
        <result column="taxpayer_industry" property="taxpayerIndustry"/>
        <result column="location_code" property="locationCode"/>
        <result column="accounting_principle" property="accountingPrinciple"/>
        <result column="taxpayer_industry_code" property="taxpayerIndustryCode"/>
        <result column="accounting_principle_code" property="accountingPrincipleCode"/>
        <result column="create_time" property="createTime"/>
        <result column="dept_type" property="deptType"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="contact_name" property="contactName"/>
        <result column="contact_phone" property="contactPhone"/>
        <result column="contact_email" property="contactEmail"/>
        <result column="enterprise_numbers" property="enterpriseNumbers"/>
        <result column="authorization_code" property="authorizationCode"/>
        <result column="source_id" property="sourceId"/>
        <result column="order_num" property="orderNum"/>
        <result column="update_time" property="updateTime"/>
        <result column="del_flag" property="delFlag"/>
        <result column="data_source" property="dataSource"/>
        <result column="status" property="status" />
        <result column="tenant_id" property="tenantId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
    dept_id  AS  deptId,parent_id  AS  parentId,name  AS  name,dept_sname  AS  deptSname,code  AS  code,level  AS  level,ein_type AS einType,taxpayer_code  AS  taxpayerCode,taxpayer_province   AS taxpayerProvince,taxpayer_city  AS  taxpayerCity,taxpayer_county  AS  taxpayerCounty,taxpayer_province_code   AS taxpayerProvinceCode,taxpayer_city_code  AS  taxpayerCityCode,taxpayer_county_code  AS  taxpayerCountyCode,taxpayer_address  AS  taxpayerAddress,taxpayer_phone  AS  taxpayerPhone,taxpayer_bank  AS  taxpayerBank,taxpayer_account  AS  taxpayerAccount,taxpayer_type  AS  taxpayerType,taxpayer_industry   AS taxpayerIndustry,accounting_principleAS accountingPrinciple,taxpayer_industry_code   AS taxpayerIndustryCode,accounting_principle_code accountingPrincipleCode,create_time  AS  createTime,dept_type  AS  deptType,create_user  AS  createUser,update_user  AS  updateUser,contact_name  AS  contactName,contact_phone  AS  contactPhone,contact_email  AS  contactEmail,enterprise_numbers  AS enterpriseNumbers,location_code  AS locationCode,authorization_code  AS authorizationCode,source_id  AS  sourceId,order_num  AS  orderNum,update_time  AS  updateTime,del_flag  AS  delFlag,data_source  AS  dataSource
    </sql>

    <!--查询自己及以下组织-->
<!--    <select id="listMyselfAll" resultType="com.dxhy.core.pojo.entity.SysDept" >-->
    <select id="listMyselfAll" resultMap="BaseResultMap" >
        select d.* from sys_dept d
        left join sys_tenant t on d.tenant_id = t.tenant_id
        where 1=1
        <choose>
            <when test="isDxAdmin == true">
                and d.dept_type != 4
            </when>
            <when test="isAdmin == true">
                and (
                  d.code like '${code}%'
                  or exists (select 1 from sys_tenant st
                     where st.parent_id = (select tenant_id from sys_dept where code = '${code}' and del_flag = '0' limit 1 )
                     and st.tenant_id = d.tenant_id)
                  )
                <if test="isContainBm == false">
                    and d.dept_type != 4
                </if>
            </when>
            <otherwise>
                and d.code like '${code}%'
                <if test="isContainBm == false">
                    and d.dept_type != 4
                </if>
            </otherwise>
        </choose>
        and d.del_flag ='0'
        order by d.create_time asc
    </select>


    <!--查询自己级别以下组织-->
    <select id="listUnlessMyselfAll" resultType="com.dxhy.core.pojo.entity.SysDept" >
        select * from sys_dept where  code like '${code}%'
        <if test="isContainBm == false">
            and dept_type != 4
        </if>
        and del_flag ='0' AND `level` = #{level}
        order by create_time asc
    </select>

    <!--查询每个组织对应的人数-->
    <select id="selectAllUserCount" resultType="java.lang.Integer" >
        select count(*) from sys_user u left join sys_dept d on u.dept_id = d.dept_id
        where d.code like '${code}%' and d.del_flag ='0'
    </select>

   <!--查询自己下一级组织-->
    <!--<select id="listMyselfOneLevel" resultType="com.dxhy.core.pojo.vo.SysDeptVo" >-->
    <select id="listMyselfOneLevel" resultMap="SysDeptVoResultMap" >
        select d.*,u.`username` as createUserName from sys_dept d left join sys_user u on d.create_user = u.user_id
        where 1=1
        <if test="deptId != null and deptId != ''">
            and (d.dept_id =#{deptId} or d.parent_id = #{deptId})
        </if>
        <if test="isContainBm == false">
            and d.dept_type != 4
        </if>
        <if test="entName != null and entName != ''">
            and `name` like '%${entName}%'
        </if>
        <if test="nsrsbh != null and nsrsbh != ''">
            and `taxpayer_code` like '%${nsrsbh}%'
        </if>
        and d.del_flag ='0'
        order by d.create_time desc
    </select>

    <!--查询自己下一级组织及人员组成tree结构-->
    <select id="listOneLevelBmAndUserDept" resultType="com.dxhy.core.pojo.vo.CommonTreeVo" >
        select CONCAT('u',t.user_id) as id, t.dept_id as parentId, t.username as name, 'U' as type, t.user_id as realId from sys_user t where t.dept_id = #{deptId}
    </select>

    <!--查询自己下一级组织及人员组成tree结构-->
    <select id="listOneLevelBmAndUserParent" resultType="com.dxhy.core.pojo.vo.CommonTreeVo" >
        select dept_id as id, parent_id as parentId, name, 'D' as type, dept_id as realId from sys_dept where parent_id = #{deptId}
    </select>

    <!--查询自己下所有组织及人员组成tree结构-->
    <select id="listMyselfAllBmAndUserDept" resultType="com.dxhy.core.pojo.vo.CommonTreeVo" >
        SELECT CONCAT('u', t.user_id) AS id,
	t.dept_id AS parentId,
	t.username AS NAME,
	'U' AS type,
	t.user_id AS realId FROM sys_user t,(SELECT
		dept_id
	FROM
		sys_dept
	WHERE
		CODE LIKE '${code}%') r  WHERE t.dept_id=r.dept_id AND t. STATUS = 1
    </select>

    <select id="listMyselfAllBmAndUserParent" resultType="com.dxhy.core.pojo.vo.CommonTreeVo" >
        select dept_id as id, parent_id as parentId, name, 'D' as type, dept_id as realId from sys_dept where code like '${code}%'
    </select>

    <!--查询自己下所有组织及角色组成tree结构-->
    <select id="listMyselfAllBmAndRoleDept" resultType="com.dxhy.core.pojo.vo.CommonTreeVo"  >
        SELECT CONCAT('r',t.role_id) as id, t.dept_id as parentId, t.role_name as name, 'R' as type, t.role_id as realId FROM sys_role t,(select dept_id from sys_dept where  code like '${code}%') r  WHERE t.dept_id=r.dept_id
    </select>

    <select id="listMyselfAllBmAndRoleParent" resultType="com.dxhy.core.pojo.vo.CommonTreeVo"  >
        select dept_id as id, parent_id as parentId, name, 'D' as type, dept_id as realId from sys_dept where  code like '${code}%'
    </select>

    <!--根据code查询-->
    <select id="selectByCode" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE
        code=#{code,jdbcType=VARCHAR} and del_flag ='0'
    </select>


    <!--根据code查询所有组织-->
    <select id="queryAllDeptByName" resultType="java.util.HashMap" >
        SELECT  dept_id AS deptId,parent_id AS parentId,`name` AS deptName FROM  sys_dept WHERE
        `name` LIKE '%${deptName}%'  AND del_flag ='0'
    </select>

    <!--根据code查询-->
    <select id="queryDeptByNameAndDisId" resultType="java.util.HashMap" >
        SELECT  dept_id AS deptId,parent_id AS parentId,`name` AS deptName FROM  sys_dept WHERE
        `name` LIKE '%${deptName}%' AND source_id=#{distributorId} AND del_flag ='0'
    </select>

    <!--根据组织名称和税号查询组织信息-->
    <select id="queryDeptByTaxpayerNameAndCode" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE del_flag ='0'
        <if test="name != null and name != ''">
            AND name=#{name,jdbcType=VARCHAR}
        </if>
        <if test="taxpayerCode != null and taxpayerCode != ''">
            AND taxpayer_code=#{taxpayerCode,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>

    <!--根据纳税人名称和税号查询企业信息-->
    <select id="queryDeptByTaxpayerCode" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE
        taxpayer_code=#{taxpayerCode,jdbcType=VARCHAR} AND del_flag ='0'
    </select>

    <!--根据名称模糊查询组织信息-->
    <select id="queryDeptLikeByName" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE
        name LIKE CONCAT(#{name,jdbcType=VARCHAR},'%') AND del_flag ='0'
    </select>


    <!--根据纳税人名称查询企业信息-->
    <select id="queryDeptByName" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE
        name=#{name,jdbcType=VARCHAR} AND del_flag ='0'
    </select>

    <!--根据地区code查询对应地区信息-->
    <select id="getAddressByCode"  resultType="com.dxhy.core.pojo.vo.AddressVo" >
        select * from sys_address where addressCode=#{addressCode}
    </select>

    <!--获取省地址列表-->
    <select id="getAddressCodeBySf" resultType="com.dxhy.core.pojo.vo.AddressVo" >
      select * from sys_address where length(addressCode)=6 AND addressCode LIKE '%0000'
    </select>

    <!--获取市地址列表-->
    <select id="getAddressCodeBySq" resultType="com.dxhy.core.pojo.vo.AddressVo" >
        select * from sys_address where addressName NOT IN('北京市','天津市','上海市','重庆市') AND length(addressCode)=6 AND addressCode LIKE '${addressCode}%' AND addressCode LIKE '%00' AND addressCode NOT LIKE '%0000'
    </select>

    <!--获取直辖市列表-->
    <select id="getAddressCodeByZxs" resultType="com.dxhy.core.pojo.vo.AddressVo" >
        select * from sys_address where addressName  IN('北京市','天津市','上海市','重庆市')
    </select>

    <!--获取直辖市所属区列表-->
    <select id="getAddressCodeByZxsSsq" resultType="com.dxhy.core.pojo.vo.AddressVo" >
        select * from sys_address where addressName NOT IN('北京市','天津市','上海市','重庆市') AND length(addressCode)=6 AND addressCode LIKE '${addressCode}%'
    </select>

    <!--获取县地址列表-->
    <select id="getAddressCodeByXq" resultType="com.dxhy.core.pojo.vo.AddressVo" >
        select * from sys_address where length(addressCode)=6 AND addressCode LIKE  '${addressCode}%' AND addressCode != CONCAT(#{addressCode},'00') AND addressCode != CONCAT(#{addressCode},'01');
    </select>


    <!--获取行业列表-->
    <select id="getIndustry" resultType="com.dxhy.core.pojo.vo.IndustryVo" >
        SELECT * FROM sys_industry
    </select>


    <!--获取企业准则列表-->
    <select id="getAccountant" resultType="com.dxhy.core.pojo.vo.AccountantVo" >
        SELECT * FROM sys_accountant
    </select>

    <!--根据企业名称查询对应行业code-->
    <select id="selectAccountantByName" resultType="com.dxhy.core.pojo.vo.AccountantVo" >
        SELECT * FROM sys_accountant WHERE accountantName=#{name}
    </select>

    <!--根据行业名称查询对应行业code-->
    <select id="selectIndustryByName" resultType="com.dxhy.core.pojo.vo.IndustryVo" >
        SELECT * FROM sys_industry WHERE industryName=#{name}
    </select>

    <!--根据行业Code查询对应行业名称-->
    <select id="selectIndustryByCode" resultType="com.dxhy.core.pojo.vo.IndustryVo" >
        SELECT * FROM sys_industry WHERE industryCode=#{code}
    </select>

    <select id="querySetMeal" resultType="com.dxhy.core.pojo.vo.SetMealVo">
        SELECT d.dept_id deptId,d.ein_number einNumber,s.product_id productId,s.product_name productName,m.menu_id menuId FROM sys_dept_set_meal d,sys_set_meal_product s,sys_product_menu m WHERE dept_id =#{deptId} AND d.set_meal_id=s.set_meal_id AND s.product_id=m.product_id AND m.del_flag=0
    </select>

    <!--根据组织ID查询对应的套餐信息-->
   <select id="getSetMeal" resultType="com.dxhy.core.pojo.entity.SysDeptSetMeal">
       SELECT  * FROM  sys_dept_set_meal WHERE dept_id=#{deptId}
   </select>

    <!--根据组织id更新对应套餐信息-->
    <update id="updateDeptSetMeal">
        update sys_dept_set_meal set ein_number=#{einNumber} WHERE dept_id=#{deptId}
    </update>


    <!--根据渠道对应删除标记-->
    <update id="updateDistributor">
        update distributor set del_flag=#{delFlag} WHERE id=#{id}
    </update>

    <!--根据组织id更新对应禁用启用状态-->
    <update id="updateDeptStatus">
        update sys_dept set status=#{status} WHERE dept_id=#{deptId}
    </update>
    <update id="updateCodeByTenantIdAndDeptId">
        update sys_dept set code=REPLACE(`code`,'ID-',id) WHERE del_flag=0
        <if test="deptId != null and deptId!=''">
            AND dept_id = #{deptId}
        </if>
        AND tenant_id = #{tenantId}
    </update>


    <!--删除组织对应菜单Id-->
    <delete id="deleteMenuByDeptId">
        DELETE  FROM  sys_dept_menu WHERE dept_id=#{deptId}
    </delete>

    <!--插入dept_menu对应关系表-->
    <insert id="insertSysDeptMenu" parameterType="com.dxhy.core.pojo.entity.SysDeptMenu">
        insert into sys_dept_menu (dept_id,menu_id) values (#{deptId},#{menuId})
    </insert>

    <!--插入role_menu对应关系表-->
    <insert id="insertSysdeptRole" parameterType="com.dxhy.core.pojo.entity.SysRoleDept">
        insert into sys_dept_role(dept_id,role_id) VALUES (
        #{deptId},
        #{roleId})
    </insert>

    <!--根据企业授权码查询对应企业信息-->
    <select id="queryDeptBySqm" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE
        authorization_code=#{authorizationCode,jdbcType=VARCHAR} AND del_flag ='0'
    </select>

    <!--根据企业编码查询对应企业信息-->
    <select id="queryDeptByQybm" resultType="com.dxhy.core.pojo.entity.SysDept" >
        SELECT  * FROM  sys_dept WHERE
        enterprise_numbers=#{enterpriseNumbers,jdbcType=VARCHAR} AND del_flag ='0'
    </select>


    <!--查询对应条数-->
    <select id="queryDeptCount"  resultType="java.lang.Integer" >
        SELECT COUNT(*) FROM  sys_dept  WHERE
        1=1
        <if test="deptId!=null">
            AND  dept_id != #{deptId}
        </if>
        <if test="code!=null and code !=''">
            AND  `code` like  '${code}%'
        </if>
        <if test="name != null and name!=''">
            AND  `name` = #{name}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="deptSname != null and deptSname!=''">
            AND dept_sname = #{deptSname}
        </if>
        <if test="taxpayerCode != null and taxpayerCode!=''">
            AND taxpayer_code = #{taxpayerCode}
        </if>
        AND del_flag ='0'
    </select>


    <delete id="deleteDeptRealtion">
        DELETE
        FROM
            sys_dept_relation
        WHERE
            descendant IN (
                SELECT
                    temp.descendant
                FROM
                    (
                        SELECT
                            descendant
                        FROM
                            sys_dept_relation
                        WHERE
                            ancestor = #{id}
                    ) temp
            )
    </delete>

    <!--关联查询部门列表-->
    <select id="selectDeptDtoList" resultType="com.dxhy.core.pojo.entity.SysDept">
		SELECT
			t.*
		FROM
			sys_dept t
		LEFT JOIN sys_dept_relation dr ON t.dept_id = dr.descendant
		WHERE dr.ancestor = 0 AND t.del_flag ='0'
	</select>


    <!--根据渠道ID查询所有组织-->
    <select id="selectDeptBySourceId" resultType="com.dxhy.core.pojo.entity.SysDept">
        SELECT
        t.*
        FROM
        sys_dept t
        WHERE t.source_id = #{sourceId} AND t.del_flag ='0'
    </select>


    <!--根据渠道ID查询所有1级组织-->
    <select id="selectOneLevelDeptBySourceId" resultType="com.dxhy.core.pojo.entity.SysDept">
        SELECT
        t.*
        FROM
        sys_dept t
        WHERE t.source_id = #{sourceId} AND t.del_flag ='0' AND  t.level=1
    </select>

    <!--查询所有子级企业大于1条的渠道id-->
    <select id="selectDeptCountCondion" resultType="java.lang.String">
        SELECT
	         aa.source_id
        FROM
	(
		SELECT
			COUNT(source_id) bb,
			source_id
		FROM
			sys_dept
		WHERE
			`level` = 1 AND del_flag='0'
		GROUP BY
			source_id
	) aa
       WHERE
	aa.bb > 0
    </select>

    <!--查询所有子级企业大于1条的顶级企业id-->
    <select id="selectNextDeptCountCondion" resultType="java.lang.String">
        SELECT
        aa.parent_id
        FROM
        (
        SELECT
        COUNT(dept_id) bb,
        dept_id,parent_id
        FROM
        sys_dept
        WHERE
        `level` = 2 AND del_flag='0'
        GROUP BY
        dept_id
        ) aa
        WHERE
        aa.bb > 0
    </select>

    <select id="queryUserListByDeptId" resultType="com.dxhy.core.pojo.entity.SysUserDept">
        SELECT
        sud.dept_id,sud.user_id
        FROM
        sys_user_dept sud
        WHERE
        sud.dept_id = #{deptId}
    </select>

    <select id="queryDeptAndRoleById" resultType="com.dxhy.core.pojo.entity.SysRoleDept">
        SELECT * FROM sys_dept_role WHERE dept_id=#{deptId}
    </select>

    <select id="queryDeptsByDeptId" resultMap="SysDeptVoResultMap">
        SELECT * FROM sys_dept WHERE code like CONCAT(#{deptId},'%') and del_flag ='0'
    </select>

    <select id="selectByDeptId" resultMap="BaseResultMap">
        SELECT * FROM sys_dept WHERE dept_id=#{deptId} and del_flag ='0'
    </select>

    <select id="selectList" resultMap="BaseResultMap">
        SELECT * FROM sys_dept
    </select>

    <select id="queryAllDepts" resultMap="SysDeptVoResultMap">
        SELECT * FROM sys_dept WHERE del_flag='0'
    </select>

    <!--新增删除部门对应的数据权限关系-->
    <delete id="deleteUserDeptByDeptId">
        DELETE FROM sys_user_dept  WHERE dept_id = #{deptId}
    </delete>

    <!--新增删除部门对应的数据权限关系-->
    <delete id="deleteDeptMenuByDeptId">
        DELETE FROM sys_dept_menu  WHERE dept_id = #{deptId}
    </delete>

    <!--新增删除部门对应的数据权限关系-->
    <delete id="deleteDeptRoleByDeptId">
        DELETE FROM sys_dept_role  WHERE dept_id = #{deptId}
    </delete>

    <!--查询sys_dept_role对应关系表-->
    <select id="selectSysdeptRole" resultType="com.dxhy.core.pojo.entity.SysRoleDept">
        SELECT  * FROM  sys_dept_role WHERE role_id = #{roleId,jdbcType=VARCHAR}
    </select>

    <select id="queryDeptList" resultMap="BaseResultMap">
        select *
        from sys_dept s
        where 1=1
        <if test="params.name != null and params.name != ''">
            and `name` = #{params.name}
        </if>
        <if test="params.contactPhone != null and params.contactPhone != ''">
            and `contact_phone` = #{params.contactPhone}
        </if>
        <if test="params.parentId != null and params.parentId != ''">
            <if test="params.parentId != '0'.toString()">
                and `parent_id` = #{params.parentId}
            </if>
            <if test="params.parentId == '0'.toString()">
                and parent_id is not null and parent_id &lt;&gt; ''
            </if>
            and dept_id in (select dept_id from  sys_user_dept where user_id=#{params.userId})
        </if>
        and del_flag ='0'
    </select>
    <select id="queryUserDeptList" resultMap="BaseResultMap">
        select t.* from sys_dept t
                            left join sys_user_dept d on t.dept_id=d.dept_id
        where d.user_id =#{userId} and t.del_flag ='0'
        GROUP BY  d.dept_id

    </select>
    <select id="selectByDeptId2" resultMap="BaseResultMap">
        SELECT * FROM sys_dept WHERE dept_id=#{deptId} and parent_id is not null and parent_id &lt;&gt; '' and del_flag ='0'
    </select>
    <!-- 根据deptId查询顶级机构-->
    <select id="selectTopLevelByDeptId" resultMap="BaseResultMap" >
        select * from sys_dept where
                                     (select `code` from sys_dept where dept_id=#{deptId})
            like concat(`code`,'%') and `level`=1 and del_flag='0'
    </select>
    <!-- 根据deptId查询顶级机构-->
    <select id="selectUnlessByDeptId" resultMap="BaseResultMap" >
        select * from sys_dept where
                (select `code` from sys_dept where dept_id=#{deptId})
                like concat(`code`,'%') and `level` &lt;>1 and del_flag='0'
    </select>
    <select id="queryDeptByTaxpayerNameAndCodeAndTenat" resultType="com.dxhy.core.pojo.entity.SysDept">
        SELECT  * FROM  sys_dept WHERE del_flag ='0'
        <if test="name != null and name != ''">
            AND name=#{name,jdbcType=VARCHAR}
        </if>
        <if test="taxpayerCode != null and taxpayerCode != ''">
            AND taxpayer_code=#{taxpayerCode,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id=#{tenantId,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>
    <select id="queryDeptByTaxpayerNameOrCode" resultType="com.dxhy.core.pojo.entity.SysDept">
        SELECT  * FROM  sys_dept WHERE del_flag ='0'
            AND ( name=#{name,jdbcType=VARCHAR} or taxpayer_code=#{taxpayerCode,jdbcType=VARCHAR}
                )
        limit 1
    </select>
    <select id="selectUnlessByTenantId" resultType="com.dxhy.core.pojo.entity.SysDept">
        select * from sys_dept where tenant_id = #{tenantId} and  `level` &lt;> 1 and del_flag='0'
    </select>
    <select id="selectTopOrgByTaxNo" resultType="com.dxhy.core.pojo.entity.SysDept">
        select * from sys_dept where
                (select `code` from sys_dept where taxpayer_code=#{taxNo} and tenant_id=#{tenantId})
                like concat(`code`,'%') and del_flag='0' and `level`=1
    </select>
    <select id="selectByDeptIdAndTenantId" resultType="com.dxhy.core.pojo.entity.SysDept">
        SELECT  * FROM  sys_dept WHERE del_flag ='0'
        <if test="deptId != null and deptId != ''">
            AND dept_id=#{deptId,jdbcType=VARCHAR}
        </if>
        <if test="tenantId != null and tenantId != ''">
            AND tenant_id=#{tenantId,jdbcType=VARCHAR}
        </if>
        <if test="parentId != null and parentId != ''">
            AND parent_id=#{parentId,jdbcType=VARCHAR}
        </if>
    </select>
    <select id="selectTopOrgByTenantId" resultType="com.dxhy.core.pojo.entity.SysDept">
        select * from sys_dept where tenant_id=#{tenantId} and del_flag='0' and `level`=1
    </select>
    <select id="selectProductIdByDeptId" resultType="java.lang.Long">
        <![CDATA[
            SELECT DISTINCT product_id FROM sys_tenant_product stp
            WHERE stp.dept_id = #{deptId}
            AND stp.auth_stime <= SYSDATE()
            AND stp.auth_etime >= SYSDATE()
        ]]>
    </select>
</mapper>
