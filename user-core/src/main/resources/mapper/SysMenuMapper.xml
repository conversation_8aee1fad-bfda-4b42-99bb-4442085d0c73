<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: lengleng (<EMAIL>)
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysMenu">
        <id column="menu_id" property="menuId" />
        <result column="name" property="name" />
        <result column="permission" property="permission" />
        <result column="url" property="url" />
        <result column="path" property="path" />
        <result column="method" property="method" />
        <result column="parent_id" property="parentId" />
        <result column="icon" property="icon" />
        <result column="component" property="component" />
        <result column="sort" property="sort" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
    </resultMap>

    <resultMap id="MenuVoResultMap" type="com.dxhy.core.pojo.vo.MenuVO">
        <id column="menu_id" property="menuId" />
        <result column="name" property="name" />
        <result column="permission" property="permission" />
        <result column="url" property="url" />
        <result column="method" property="method" />
        <result column="parent_id" property="parentId" />
        <result column="icon" property="icon" />
        <result column="path" property="path" />
        <result column="component" property="component" />
        <result column="sort" property="sort" />
        <result column="type" property="type" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="del_flag" property="delFlag" />
        <result column="product_name" property="productName" />
    </resultMap>

    <select id="findMenuByRoleName" resultMap="BaseResultMap">
        SELECT
            sys_menu.*
        FROM
        sys_role
        LEFT JOIN sys_role_menu ON sys_role_menu.role_id = sys_role.role_id
        LEFT JOIN sys_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        WHERE
        sys_menu.del_flag = 0
        AND sys_role.role_code = #{role}
        ORDER BY sys_menu.sort DESC
    </select>

    <!-- 查询用户在企业下的权限列表，角色有相同菜单情况，需去重-->
    <select id="findMenuByEntUser" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.menu_id,
            sys_menu.parent_id,
            sys_menu.icon,
            sys_menu.name,
            sys_menu.url,
            sys_menu.path,
            sys_menu.sort,
            sys_menu.type
        FROM
        sys_role
        LEFT JOIN sys_role_menu ON sys_role_menu.role_id = sys_role.role_id
        LEFT JOIN sys_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        LEFT JOIN base_ent_role ON base_ent_role.role_id = sys_role.role_id
        LEFT JOIN base_ent_user ON base_ent_user.ent_id = base_ent_role.ent_id
        LEFT JOIN sys_user_role ON sys_user_role.role_id = sys_role.role_id
        WHERE
        sys_user_role.user_id = base_ent_user.user_id
        AND sys_menu.del_flag = 0
        AND base_ent_user.ent_id = #{entId}
        AND base_ent_user.user_id = #{userId}
        ORDER BY sys_menu.sort DESC
    </select>
    
    <!-- 查询角色已勾选菜单树 -->
    <select id="getMenuList" resultType="com.dxhy.core.pojo.vo.MenuTreeVO">
    	SELECT
			rm.role_id,m.menu_id id,m.parent_id,
			m.icon,m.name,m.url,m.path,m.sort
		FROM
			sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		LEFT JOIN base_ent_role er ON rm.role_id = er.role_id
		WHERE
			er.ent_id = #{entId}  
		AND m.del_flag = '0' 
		AND rm.role_id = #{roleId}
    </select>

    <!-- 查询用户在企业下的产品-->
    <select id="findProductByEntUser" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.menu_id,
            sys_menu.parent_id,
            sys_menu.icon,
            sys_menu.name,
            sys_menu.url,
            sys_menu.path,
            sys_menu.sort,
            sys_menu.type
        FROM
        sys_role
        LEFT JOIN sys_role_menu ON sys_role_menu.role_id = sys_role.role_id
        LEFT JOIN sys_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        LEFT JOIN base_ent_role ON base_ent_role.role_id = sys_role.role_id
        LEFT JOIN base_ent_user ON base_ent_user.ent_id = base_ent_role.ent_id
        LEFT JOIN sys_user_role ON sys_user_role.role_id = sys_role.role_id
        WHERE
        sys_user_role.user_id = base_ent_user.user_id
        AND sys_menu.del_flag = 0
        AND base_ent_user.ent_id = #{entId}
        AND base_ent_user.user_id = #{userId}
        AND sys_menu.menu_id in (11000,14000,15000,16000,17000)
        ORDER BY sys_menu.sort DESC
    </select>


    <!-- 通过角色id查询角色已勾选菜单树 -->
    <select id="getMenuListByRoleId" resultType="com.dxhy.core.pojo.vo.MenuTreeVO">
    	SELECT
			rm.role_id,m.menu_id id,m.parent_id,
			m.icon,m.name,m.url,m.path,m.sort
		FROM
			sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		WHERE
		    m.del_flag = '0'
		AND rm.role_id = #{roleId}
    </select>


    <!-- 通过角色id查询角色已勾选菜单树 -->
    <select id="getProductByRoleId" resultType="com.dxhy.core.pojo.vo.MenuTreeVO">
    	SELECT
			rm.role_id,m.menu_id id,m.parent_id,
			m.icon,m.name,m.url,m.path,m.sort
		FROM
			sys_menu m
		LEFT JOIN sys_role_menu rm ON m.menu_id = rm.menu_id
		WHERE
		    m.del_flag = '0'
		AND rm.role_id = #{roleId}
		AND m.menu_id in (11000,14000,15000,16000,17000)
    </select>


    <!-- 查询用户在渠道下的门户菜单列表，角色有相同菜单情况，需去重-->
    <select id="findMenuByUserIdAndSourceId" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.menu_id,
            sys_menu.parent_id,
            sys_menu.icon,
            sys_menu.name,
            sys_menu.url,
            sys_menu.path,
            sys_menu.sort,
            sys_menu.type
        FROM
        sys_menu
        LEFT JOIN sys_role_menu ON sys_role_menu.menu_id = sys_menu.menu_id
        LEFT JOIN sys_source_menu ON sys_menu.menu_id = sys_source_menu.menu_id
        WHERE
        sys_role_menu.role_id = #{roleId}
        AND sys_source_menu.source_id = #{sourceId}
        AND sys_menu.del_flag = 0
        ORDER BY sys_menu.create_time
    </select>


    <!-- 查询门户菜单列表，角色有相同菜单情况，需去重-->
    <select id="findMenuBySourceId" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.menu_id,
            sys_menu.parent_id,
            sys_menu.icon,
            sys_menu.name,
            sys_menu.url,
            sys_menu.path,
            sys_menu.sort,
            sys_menu.type
        FROM
        sys_menu
        LEFT JOIN sys_source_menu ON sys_menu.menu_id = sys_source_menu.menu_id
        WHERE
        sys_source_menu.source_id = #{sourceId}
        AND sys_menu.del_flag = 0
        ORDER BY sys_menu.create_time
    </select>


    <!-- 查询主键最大值-->
    <select id="findMaxMenuId" resultType="int">
        SELECT
            sys_menu.menu_id
        FROM
        sys_menu
        ORDER BY sys_menu.menu_id DESC  LIMIT 1
    </select>


    <!-- 查询门户菜单列表，角色有相同菜单情况，需去重-->
    <select id="getMenuVoById" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.menu_id,
            sys_menu.parent_id,
            sys_menu.icon,
            sys_menu.name,
            sys_menu.url,
            sys_menu.path,
            sys_menu.sort,
            sys_menu.type
        FROM
        sys_menu
        WHERE
        sys_menu.menu_id = #{menuId}
    </select>


    <select id="findMenuByRoleNameAndVersion" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.*
        FROM
        sys_role
        LEFT JOIN sys_role_menu ON sys_role_menu.role_id = sys_role.role_id
        LEFT JOIN sys_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        LEFT JOIN sys_version_source_menu ON sys_version_source_menu.menu_id = sys_role_menu.menu_id
        WHERE
        sys_menu.del_flag = 0
        AND sys_role.role_code = #{role}
        AND sys_version_source_menu.version_id = #{version}
        AND sys_version_source_menu.source_id = #{sourceId}
        ORDER BY sys_menu.sort DESC
    </select>


    <!-- 查询用户在企业下的权限列表，角色有相同菜单情况，需去重-->
    <select id="findMenuByEntUserAndVersion" resultMap="MenuVoResultMap">
        SELECT
            sys_menu.menu_id,
            sys_menu.parent_id,
            sys_menu.icon,
            sys_menu.name,
            sys_menu.url,
            sys_menu.path,
            sys_menu.sort,
            sys_menu.type
        FROM
        sys_role
        LEFT JOIN sys_role_menu ON sys_role_menu.role_id = sys_role.role_id
        LEFT JOIN sys_menu ON sys_menu.menu_id = sys_role_menu.menu_id
        LEFT JOIN sys_user_role ON sys_user_role.role_id = sys_role.role_id
        LEFT JOIN sys_version_source_menu ON sys_version_source_menu.menu_id = sys_role_menu.menu_id
        WHERE
        sys_user_role.user_id = #{userId}
        AND sys_menu.del_flag = 0
        AND sys_version_source_menu.version_id = #{version}
        AND sys_version_source_menu.source_id = #{sourceId}
        ORDER BY sys_menu.sort DESC
    </select>


    <!--角色id 查询关联菜单-->
    <select id="selectMenusByRoleId" resultMap="BaseResultMap">
		select
		*
		from sys_menu sm
		left join sys_role_menu srm on srm.menu_id = sm.menu_id
		where srm.role_id = #{roleId}
	</select>


    <!--部门id 查询关联菜单-->
    <select id="queryMenusByDeptId" resultMap="BaseResultMap">
		SELECT
		*
		from sys_menu  sm
        LEFT JOIN product_menu pr ON pr.id=sm.menu_id
		LEFT JOIN customer_product cu ON cu.product_id=pr.product_id
		WHERE cu.customer_id = #{deptId} AND  sm.del_flag='0'
	</select>


    <!--查询所有的一级菜单-->
    <select id="querySysMenus" resultMap="BaseResultMap">
		select
		sm.*
		from sys_menu  sm
		where sm.parent_id = 0 and  sm.del_flag='0'
	</select>

    <!--查询多有菜单权限列表-->
    <select id="selectMenusAll" resultMap="BaseResultMap">
        select
        sm.*
        from sys_menu  sm WHERE  sm.del_flag='0'
    </select>

    <!--查询所有系统级菜单-->
    <select id="queryMenusSystem" resultMap="BaseResultMap">
        select
        sm.*
        from sys_menu  sm WHERE  sm.system_sign IN("1","2","4") AND sm.`name` !='本机构设置' AND sm.del_flag='0'
    </select>


    <!--根据渠道ID查询菜单权限-->
    <select id="queryMenuByDistributorId" resultMap="BaseResultMap">
       select
        sm.*
        from sys_menu  sm
        LEFT JOIN sys_product_menu pr ON pr.menu_id=sm.menu_id
        LEFT JOIN product_status st ON st.product_id=pr.product_id
        WHERE  st.distributor_id=#{id} AND  sm.del_flag='0'
    </select>

    <!--菜单id查询菜单id-->
    <select id="selectMenuByMenuId" resultMap="BaseResultMap">
		select
		sm.*
		from sys_menu  sm
		where sm.menu_id = #{menuId} AND sm.del_flag='0'
	</select>


    <!--菜单id查询菜单id-->
    <select id="selectProductMenuByMenuId" resultType="com.dxhy.core.pojo.entity.ProductMenuTree">
        select
            id,`name`,permission,`path`,url,icon,`method`,system_sign as systemSign,parent_id as parentId,sort,`type`,product_id as productId,status
        from product_menu  sm
        where sm.id = #{id}
    </select>


    <!--产品id查询产品信息-->
    <select id="selectSysProductByMenuId" resultType="com.dxhy.core.pojo.entity.SysProduct">
        select
        sp.*
        from sys_product sp
        where sp.id = #{id}
    </select>

    <!--system_sign查询菜单id-->
    <select id="queryMenusBySystemSign" resultMap="BaseResultMap">
        select
        sm.*
        from sys_menu  sm
        where sm.system_sign = #{systemSign} AND sm.del_flag='0'
    </select>


    <!--根据渠道ID查询对应产品list-->
   <!--   select
         sm.product_id
        from product_status  sm
        where sm.distributor_id = #{distributorId} AND sm.status='2' GROUP  BY  sm.product_id-->
    <select id="queryProductsByDistributorId" resultType="java.lang.String">

        select sm.product_id from product_status  sm
                              left join sys_dept d on sm.distributor_id=d.source_id
        where d.dept_id = #{deptId} AND sm.status='2' GROUP  BY  sm.product_id
    </select>

    <!--部门id 查询关联角色-->
    <select id="selectMenusByUserId" resultType="java.lang.String">
		select
		srm.menu_id
	    from sys_role_menu srm
		left join sys_user_role sur on sur.role_id=srm.role_id
		left join sys_user su on su.user_id=sur.user_id
		where su.user_id = #{userId} group by srm.menu_id
	</select>


    <!--用户id 部门id 查询菜单信息-->
    <select id="selectMenusByUserIdAndDeptId" resultType="java.lang.String">
		select
		srm.menu_id
	    from sys_role_menu srm
		left join sys_user_role sur on sur.role_id=srm.role_id
		left join sys_dept_role sdr on sdr.role_id=sur.role_id
		where sur.user_id = #{userId} and sdr.dept_id = #{deptId} group by srm.menu_id
	</select>



    <select id="listMenusByParentMenuId"  resultType="com.dxhy.core.pojo.entity.SysMenu" >
        SELECT
        *
        FROM
        sys_menu sm
        WHERE
        sm.parent_id = #{menuId}
        ORDER BY sm.create_time DESC
    </select>

    <select id="queryMenuNameCount"  resultType="int" >
        SELECT
        count(1)
        FROM
        sys_menu sm
        WHERE
        sm.name = #{name}
    </select>

    <select id="querySonMenuCountByMenuId"  resultType="int" >
        SELECT
        count(1)
        FROM
        sys_menu sm
        WHERE
        sm.parent_id = #{menuId}
    </select>

    <delete id="deleteRoleMenuByMenuId">
        delete  from  sys_role_menu where menu_id = #{menuId}
    </delete>


    <insert id="insertRoleMenu">
        insert into sys_role_menu (role_id,menu_id) values (#{roleId},#{menuId})
    </insert>



</mapper>
