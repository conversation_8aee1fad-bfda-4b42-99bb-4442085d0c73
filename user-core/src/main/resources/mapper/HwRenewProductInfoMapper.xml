<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.core.mapper.HwRenewProductInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.core.pojo.entity.HwRenewProductInfoEntity" id="hwRenewProductInfoMap">
        <result property="id" column="id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="orderId" column="order_id"/>
        <result property="productId" column="product_id"/>
        <result property="expireTime" column="expire_time"/>
        <result property="trialToFormal" column="trial_to_formal"/>
        <result property="periodType" column="period_type"/>
        <result property="periodNumber" column="period_number"/>
        <result property="orderAmount" column="order_amount"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>