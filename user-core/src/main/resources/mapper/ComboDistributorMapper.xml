<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.ComboDistributorMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.ComboDistributor">
        <result column="id" property="id" />
        <result column="template_id" property="templateId" />
        <result column="distributor_id" property="distributorId" />
        <result column="status" property="status" />
        <result column="combo_name" property="comboName" />
        <result column="preferential_price" property="preferentialPrice" />
        <result column="combo_price" property="comboPrice" />
        <result column="base_price" property="basePrice" />
        <result column="combo_type" property="comboType" />
        <result column="proportion" property="proportion" />
        <result column="line_price" property="linePrice" />
        <result column="sale_price" property="salePrice" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="validTime" property="validTime" />
        <result column="describe" property="describe"/>
        <result column="sellLablelId" property="sellLablelId" />
        <result column="sellLabelName" property="sellLabelName" />
        <result column="comboNum" property="comboNum" />
        <result column="sort" property="sort" />
    </resultMap>
    <!-- 通用查询映射结果 -->
    <resultMap id="ComboInfoResultMap" type="com.dxhy.core.pojo.entity.ComboInfo">
        <result column="combo_id" property="comboId"/>
        <collection property="charges" ofType="com.dxhy.core.pojo.entity.ChargeInfo">
            <id column="charge_id" property="chargeId"/>
            <result column="name" property="name"/>
            <result column="normal_price" property="normalPrice"/>
            <result column="unit" property="unit"/>
            <result column="describe" property="describe"/>
            <result column="discount_rate" property="discountRate"/>
            <result column="border_price" property="borderPrice"/>
            <result column="beyond_price" property="beyondPrice"/>
        </collection>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, template_id, distributor_id, status, combo_name, preferential_price, combo_price, base_price, combo_type, proportion, line_price, sale_price, create_time, modify_time
    </sql>
    <!-- 获取产品套餐 -->
    <select id="selectCombos" resultMap="ComboInfoResultMap">
        select
            cta.id           combo_id,
            ac.charge_id     charge_id,
            ac.discount_rate discount_rate,
            ac.border_price  border_price,
            ac.beyond_price  beyond_price,
            ci.`name`          `name`,
            ci.normal_price  normal_price,
            ci.unit          unit,
            ci.`describe`      `describe`
            from combo_template_account cta left join account_charge ac on ac.account_id = cta.id
            left join charge_item ci on ac.charge_id = ci.id
        where cta.product_id = #{productId}
    </select>
    <!-- 通过产品ID和分销商ID查询套餐 -->
    <select id="getComboDistributorByproductId" resultMap="BaseResultMap">
        select
          cd.id,
          cd.template_id,
          cd.distributor_id,
          cd.status,
          cd.combo_name,
          cd.preferential_price,
          cd.combo_price,
          cd.base_price,
          cd.combo_type,
          cd.proportion,
          cd.line_price,
          cd.sale_price,
          cd.create_time,
          cd.modify_time,
          ct.valid_time AS validTime,
          ct.combo_num AS comboNum,
          ct.describe,
          sl.name AS sellLabelName,
          sl.sort AS sort,
          sl.id AS sellLablelId
        from combo_distributor cd
        left join combo_template ct on cd.template_id = ct.id
        left join sell_label sl on ct.sell_label_id = sl.id
        where
        1 = 1
        <if test='distributorId !=null and distributorId != ""'>
            and cd.distributor_id = #{distributorId}
        </if>
        <if test='productId !=null and productId != ""'>
            and ct.product_id = #{productId}
        </if>
        <if test='templateId !=null and templateId != ""'>
            and cd.template_id = #{templateId}
        </if>
        <if test='id !=null and id != ""'>
            and cd.id = #{id}
        </if>
        <if test='status !=null and status != ""'>
            and cd.status = #{status}
        </if>
        <if test='sort !=null and sort != ""'>
            and sl.sort &gt; #{sort}
        </if>
        ORDER BY sl.sort ASC
    </select>

    <select id="getComboDistByProductId" resultMap="BaseResultMap">
        SELECT cb.* FROM combo_distributor cb LEFT JOIN combo_template ct ON ct.id = cb.template_id AND
        cb.distributor_id = #{distributorId} AND ct.`status` = '0'
        WHERE ct.product_id = #{productId} AND ct.sell_label_id = #{sellLabelId} limit 1
    </select>

    <select id="getChargeItmesByComboTemplateId" resultType="string">
        select
          ci.id
        from
          charge_item ci
        left join
          combo_charge cc
        on ci.id = cc.charge_id
        where
          ci.status='0'
        and
          cc.template_id=#{templateId}
    </select>

    <select id="getSellLabelIdByComboDistributorId" resultType="string">
        select
          ct.sell_label_id
        from
          combo_distributor cd
        left join
          combo_template ct
        on
          cd.template_id=ct.id
        where
          cd.status='0'
        and
          ct.status='0'
        and
          cd.id=#{comboDistributorId}
    </select>

    <select id="getProductIdAndSellLabelIdByDistributorId" resultType="map">
      select
          ct.product_id as productId,
          ct.sell_label_id as sellLabelId
        from
          combo_distributor cd
        left join
          combo_template ct
        on
          cd.template_id=ct.id
        where
          cd.status='0'
        and
          ct.status='0'
        and
          cd.distributor_id=#{distributorId}
    </select>
</mapper>
