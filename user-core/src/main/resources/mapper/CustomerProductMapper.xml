<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.CustomerProductMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.CustomerProduct">
        <id column="id" property="id" />
        <result column="customer_id" property="customerId" />
        <result column="product_id" property="productId" />
        <result column="customer_type" property="customerType" />
        <result column="begin_time" property="beginTime" />
        <result column="used" property="used" />
        <result column="total" property="total" />
        <result column="product_type" property="productType" />
        <result column="status" property="status" />
        <result column="unit" property="unit" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="account_info_id" property="accountInfoId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, customer_id, product_id, customer_type, begin_time, used, total, product_type, status, unit,
        del_flag, create_time, modify_time,account_info_id
    </sql>
    <!--insert into sys_role_menu
            (role_id,menu_id)
        select sy.role_id,pm.id from product_menu pm
                                         left join customer_product cp on pm.product_id = cp.product_id
                                         left join sys_role sy on sy.dept_id=cp.customer_id
        where sy.role_type in (10,11) and pm.`status`=0
          and cp.customer_id = #{deptId}-->
    <insert id="insertRoleMenuRelation">

        insert into sys_role_menu
            (role_id,menu_id)
        select sr.role_id,pm.id from product_menu pm
                                         left join product_status cp on pm.product_id = cp.product_id
                                         left join sys_dept sy on sy.source_id=cp.distributor_id
                                         left join sys_role sr on sr.dept_id=sy.dept_id
        where sr.role_type in (10,11) and pm.`status`=0 and sy.`level`=1 and sy.dept_type=1 AND cp.status='2'
          and sr.dept_id=#{deptId}

    </insert>
    <select id="selectOpenstatus" resultMap="BaseResultMap">
            SELECT
              <include refid="Base_Column_List"/>
            FROM
            customer_product
            WHERE del_flag = 0
            <if test="companyId != '' and companyId != null">
                AND account_info_id = #{companyId}
            </if>
            AND product_id IN
          <foreach collection="productIds" item="item" index="index" open="("
                 separator="," close=")">
            #{item}
         </foreach>
    </select>

    <update id="updateCustomerProduct">
        update customer_product set total = #{total} WHERE customer_id = #{customerId} and product_id = #{productId}
                  and account_info_id = #{accountInfoId} and del_flag = 0
    </update>

    <update id="updateCustomerProductTotal">
        update customer_product set total = #{total},modify_time=now() WHERE customer_id = #{customerId} and product_id = #{productId}
                                                       and account_info_id = #{accountInfoId} and del_flag = 0
    </update>

    <select id="getProductId" resultType="java.lang.String">
        SELECT
        cp.id
        FROM
        customer_product cp
        left join product_info pi on cp.product_id=pi.id
        WHERE cp.del_flag = 0
        AND cp.id = #{id}
        AND pi.product_class != '1'
    </select>

    <select id="getSellLabelId" resultType="java.lang.String">
        SELECT ct.sell_label_id
        FROM combo_distributor cd
        INNER JOIN combo_template ct ON cd.template_id = ct.id
        WHERE cd.id = #{comboDistributorId}
    </select>

    <select id="getProductIdByComboId" resultType="java.lang.String">
        SELECT ct.product_id
        FROM combo_distributor cd
                 INNER JOIN combo_template ct ON cd.template_id = ct.id
        WHERE cd.id = #{comboDistributorId}
    </select>

    <select id="getCustomerProductByDis" resultMap="BaseResultMap">
        SELECT cp.id, cp.customer_id, cp.product_id, cp.customer_type, cp.begin_time, cp.used, cp.total, cp.product_type, cp.status, cp.unit,
        cp.del_flag, cp.create_time, cp.modify_time,cp.account_info_id FROM customer_product cp
        LEFT JOIN sys_user ai ON cp.account_info_id = ai.user_id
        AND cp.product_id = #{productId} AND cp.del_flag = 0
        WHERE distributor_id = #{distributorId} GROUP BY cp.id
    </select>

    <select id="getCustomerProductByUserId" resultType="com.dxhy.core.pojo.entity.CustomerProduct"
            parameterType="java.lang.Long">
        select c.* from customer_product c
                            left join sys_user u
                                      on c.customer_id=u.top_level
        where u.user_id=#{userId}
    </select>

    <update id="updateProductStatus">
        update customer_product set `status` = #{status}
        WHERE customer_id = #{customerId} and product_id = #{productId}
              and account_info_id = #{accountInfoId} and del_flag = 0
    </update>

</mapper>
