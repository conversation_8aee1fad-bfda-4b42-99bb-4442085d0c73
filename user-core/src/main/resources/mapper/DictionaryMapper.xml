<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.DictionaryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.Dictionary">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="parent" property="parent"/>
        <result column="flag" property="flag"/>
    </resultMap>
    <!-- 通用查询映射结果 -->
    <resultMap id="TreeResultMap" type="com.dxhy.core.pojo.entity.Dictionary">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="desc" property="desc"/>
        <result column="parent" property="parent"/>
        <result column="flag" property="flag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, code, name, `desc`, parent, flag
    </sql>
    <select id="getTree" parameterType="String" resultMap="TreeResultMap">
        select
        <include refid="Base_Column_List"/>
        from dictionary where parent=#{code};
    </select>

    <select id="getByFlag" parameterType="String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dictionary
        where flag like concat('%',#{flag},'%') limit 1;
    </select>

    <select id="selectDictionary" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from dictionary
        where 1=1
        <if test="id != null and id != ''">
            and `id` = #{id}
        </if>
        <if test="code != null and code != ''">
            and `code` = #{code}
        </if>
        <if test="name != null and name != ''">
            and `name` = #{name}
        </if>
        <if test="desc != null and desc != ''">
            and `desc` = #{desc}
        </if>
        <if test="parent != null and parent != ''">
            and parent = #{parent}
        </if>
        <if test="flag != null and flag != ''">
            and flag like '${flag}%'
        </if>
    </select>
    <select id="selectMaxId" resultType="java.lang.Long">
        select max(id) from dictionary
    </select>

</mapper>
