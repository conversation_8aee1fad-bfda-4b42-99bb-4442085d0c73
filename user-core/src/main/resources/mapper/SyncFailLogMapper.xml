<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SyncFailLogMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SyncFailLog">
		<id column="id" property="id" />
		<result column="dept_id" property="deptId" />
		<result column="tax_no" property="taxNo" />
		<result column="content" property="content" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="flag" property="flag" />
		<result column="description" property="description" />
	</resultMap>

	<!--插入role_menu对应关系表-->
	<insert id="insertSysnFailLog" parameterType="com.dxhy.core.pojo.entity.SyncFailLog">
		insert into sync_fail_log(dept_id,tax_no,content,flag,create_time,update_time,`description`)
			VALUES (#{deptId},#{taxNo},#{content},#{flag},now(),now(),#{description})
	</insert>

</mapper>
