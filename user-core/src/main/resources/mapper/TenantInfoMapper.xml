<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.TenantInfoMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.dxhy.core.pojo.hw.TenantInfoEntity">
		<id column="id" property="id" />
		<result column="instance_id" property="instanceId" />
		<result column="order_id" property="orderId" />
		<result column="tenant_id" property="tenantId" />
		<result column="tenant_code" property="tenantCode" />
		<result column="name" property="name" />
		<result column="domain_name" property="domainName" />
		<result column="flag" property="flag" />
		<result column="test_flag" property="testFlag" />
		<result column="create_time" property="createTime" />
		<result column="update_time" property="updateTime" />
		<result column="time_stamp" property="timeStamp" />
	</resultMap>
	<select id="getAccessTokenParam" resultType="com.dxhy.core.pojo.hw.TenantTokenVo"
			parameterType="java.lang.String">
		select t.tenant_id as tenantId,a.client_id as clientId,
		       a.client_secret clientSecret,
		       t.domain_name as domainName
		from tenant_info t
		left join tenant_app_info a on t.tenant_id=a.tenant_id
		where t.tenant_id=#{tenantId}
	</select>
	<select id="getAppKeyAndSecret" resultType="com.dxhy.core.pojo.hw.TenantInfoEntity">
		select t.* from tenant_info t,sys_dept a
		where t.tenant_id=a.tenant_id and taxpayer_code=#{taxNo}
	</select>
	<select id="getSecretKey" resultType="com.dxhy.core.pojo.hw.TenantInfoEntity"
			parameterType="java.lang.String">
		select t.* from tenant_info t where app_key = #{secretId} limit 1
	</select>


</mapper>
