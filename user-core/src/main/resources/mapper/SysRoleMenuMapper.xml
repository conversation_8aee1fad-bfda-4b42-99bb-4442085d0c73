<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: lengleng (<EMAIL>)
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysRoleMenuMapper">

	<!-- 通用查询映射结果 -->
	<resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysRoleMenu">
		<id column="role_id" property="roleId" />
		<result column="menu_id" property="menuId" />
	</resultMap>


	<!--Roleid查询菜单详情-->
	<select id="getRoleMenuByRoleId" resultMap="BaseResultMap">
		select * from sys_role_menu where role_id = #{roleId}
	</select>
	<select id="getJxeMenuByRoleId" resultType="com.dxhy.core.pojo.entity.SysRoleMenu">
		select * from sys_role_menu where menu_id=#{menuId}
		<if test="list!= null">
			and role_id in
			<!-- for循环, item:循环后的值, index:循环下标列式for循环的 i ,collection:参数名-->
			<!-- open="(" close=")" separator="," 就是把循环的值组成 (item1,item2,item3)的格式-->
			<foreach item="item" index="index" collection="list" open="(" close=")" separator=",">
				#{item}
			</foreach>
		</if>
	</select>


	<delete id="delRoleMenu">
		delete  from  sys_role_menu where 1=1
		<if test="roleId != null and roleId != ''">
			AND role_id = #{roleId}
		</if>
		<if test="menuId != null and menuId != ''">
			AND menu_id = #{menuId}
		</if>
	</delete>

	<!--新增角色菜单之间的关系-->
	<insert id="addRoleMenuRelation" parameterType="com.dxhy.core.pojo.entity.SysRoleMenu">
		insert into sys_role_menu (role_id,menu_id) values (#{roleId},#{menuId})
	</insert>

</mapper>
