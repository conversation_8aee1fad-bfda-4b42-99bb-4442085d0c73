<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.ProductMenuSellMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.ProductMenuSell">
        <result column="sell_label_id" property="sellLabelId" />
        <result column="product_menu_id" property="productMenuId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        sell_label_id, product_menu_id
    </sql>

</mapper>
