<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.EmailLogMapper">

    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.EmailLog" >
        <result column="id" property="id" />
        <result column="content" property="content" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        id,content,create_time
    </sql>

</mapper>