<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.ProductMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.ProductMenu">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="permission" property="permission" />
        <result column="path" property="path" />
        <result column="url" property="url" />
        <result column="icon" property="icon" />
        <result column="method" property="method" />
        <result column="system_sign" property="systemSign" />
        <result column="parent_id" property="parentId" />
        <result column="sort" property="sort" />
        <result column="type" property="type" />
        <result column="status" property="status" />
        <result column="product_id" property="productId" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, permission, path, url, icon, method,system_sign, parent_id, sort, type, status, create_time, modify_time
    </sql>

    <select id="getProductMenuByProductIdAndSellLabelId" parameterType="string" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from product_menu m
        <if test="sellLabelId!=null and sellLabelId!=''">
            left join product_menu_sell s on
            m.id=s.product_menu_id
        </if>
        where
          m.status='0'
        and
         m.product_id=#{productId}
        <if test="sellLabelId!=null and sellLabelId!=''">
            and
            s.sell_label_id=#{sellLabelId}
        </if>


    </select>
    <select id="getProMenuListByDeptId" parameterType="string" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"></include>
        from product_menu p
        where id in (
            select menu_id from sys_role_menu m
                left join sys_role r on m.role_id=r.role_id
                where   r.del_flag=0
                <if test="deptId!=null and deptId!=''">
                    and r.dept_id=#{deptId}
                </if>
                <if test="roleType!=null and roleType!=''">
                    and r.role_type=#{roleType}
                </if>
            )
        and p.status='0'
    </select>

    <select id="selectMenuId" resultType="com.dxhy.core.pojo.entity.ProductMenu">
        select * from product_menu where  `name` like concat('%',#{name},'%') and system_sign=#{systemSign} limit 1;
    </select>
    <select id="getMenuIdListByProId" resultType="java.lang.String"
            parameterType="java.util.List">
        select id from product_menu where status='0'
        <if test="list!= null">
            and product_id in
            <!-- for循环, item:循环后的值, index:循环下标列式for循环的 i ,collection:参数名-->
            <!-- open="(" close=")" separator="," 就是把循环的值组成 (item1,item2,item3)的格式-->
            <foreach item="item" index="index" collection="list" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>
