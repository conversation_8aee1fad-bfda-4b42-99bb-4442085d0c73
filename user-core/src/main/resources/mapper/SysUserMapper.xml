<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~    Copyright (c) 2018-2025, lengleng All rights reserved.
  ~
  ~ Redistribution and use in source and binary forms, with or without
  ~ modification, are permitted provided that the following conditions are met:
  ~
  ~ Redistributions of source code must retain the above copyright notice,
  ~ this list of conditions and the following disclaimer.
  ~ Redistributions in binary form must reproduce the above copyright
  ~ notice, this list of conditions and the following disclaimer in the
  ~ documentation and/or other materials provided with the distribution.
  ~ Neither the name of the pig4cloud.com developer nor the names of its
  ~ contributors may be used to endorse or promote products derived from
  ~ this software without specific prior written permission.
  ~ Author: lengleng (<EMAIL>)
  -->

<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dxhy.core.mapper.SysUserMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.dxhy.core.pojo.entity.SysUser">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="email" property="email" />
        <result column="user_type" property="userType" />
        <result column="del_flag" property="delFlag"/>
        <result column="phone" property="phone" />
        <result column="status" property="status" />
        <result column="dept_id" property="deptId"/>
        <result column="create_time" property="createTime"/>
        <result column="nickname" property="nickname" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="create_by" property="createBy" />
        <result column="avatar" property="avatar" />
        <result column="user_source" property="userSource" />
        <result column="update_time" property="updateTime"/>
        <result column="update_by" property="updateBy" />
        <result column="distributor_id" property="distributorId" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="top_level" property="topLevel" />
    </resultMap>

    <!-- userVo结果集 -->
    <resultMap id="userVoResultMap" type="com.dxhy.core.pojo.vo.UserVO">
        <id column="user_id" property="userId"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="email" property="email" />
        <result column="user_type" property="userType" />
        <result column="del_flag" property="delFlag"/>
        <result column="phone" property="phone" />
        <result column="status" property="status" />
        <result column="dept_id" property="deptId"/>
        <result column="dept_name" property="deptName"/>
        <result column="ucreate_time" property="createTime"/>
        <result column="nickname" property="nickname" />
        <result column="last_login_time" property="lastLoginTime" />
        <result column="ucreate_by" property="createBy" />
        <result column="avatar" property="avatar" />
        <result column="user_source" property="userSource" />
        <result column="uupdate_time" property="updateTime"/>
        <result column="uupdate_by" property="updateBy" />
        <result column="distributor_id" property="distributorId" />
        <result column="remark" property="remark" />
        <result column="tenant_id" property="tenantId" />
        <result column="top_level" property="topLevel" />
        <collection property="roleList" ofType="com.dxhy.core.pojo.entity.SysRole">
            <id column="role_id" property="roleId" />
            <result column="role_name" property="roleName" />
            <result column="role_code" property="roleCode" />
            <result column="role_property" property="roleProperty" />
            <result column="type" property="type" />
            <result column="role_desc" property="roleDesc" />
            <result column="rcreate_time" property="createTime" />
            <result column="rupdate_time" property="updateTime" />
            <result column="rdistributor_id" property="distributorId" />
        </collection>
    </resultMap>

    <sql id="selectUserVo">
        SELECT
            `user`.user_id,
            `user`.username,
            `user`.`password`,
            `user`.salt,
            `user`.email,
            `user`.user_type,
            `user`.del_flag,
            `user`.phone,
            `user`.status,
            `user`.dept_id,
            `user`.create_time AS ucreate_time,
            `user`.nickname,
            `user`.last_login_time,
            `user`.create_by AS ucreate_by,
            `user`.avatar,
            `user`.user_source,
            `user`.update_time AS uupdate_time,
            `user`.update_by AS uupdate_by,
            `user`.distributor_id,
            `user`.remark,
            `user`.tenant_id,
            `user`.top_level,
             r.role_id,
             r.role_name,
             r.role_code,
             r.role_property,
             r.type,
             r.role_desc,
             r.create_time AS rcreate_time,
             r.update_time AS rupdate_time,
             r.distributor_id AS rdistributor_id
        FROM
            sys_user AS `user`
            LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
            LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
    </sql>

    <select id="selectUserVoByUsername" resultMap="userVoResultMap">
          <include refid="selectUserVo"/>
          WHERE `user`.username = #{username} OR `user`.phone = #{username} OR `user`.email = #{username}
    </select>

    <select id="selectUserVoByMobile" resultMap="userVoResultMap">
        <include refid="selectUserVo"/>
        WHERE `user`.phone = #{mobile}
    </select>

    <select id="selectUserVoByOpenId" resultMap="userVoResultMap">
        <include refid="selectUserVo"/>
        WHERE `user`.salt = #{openId}
    </select>

    <!--根据渠道ID查询所有渠道所属用户-->
    <select id="selectUserVoByDistributorId" resultMap="BaseResultMap">
        <include refid="selectUserVo"/>
        WHERE  `user`.dept_id=#{distributorId}  AND `user`.status=#{status}
    </select>

    <select id="selectUserVoById" resultMap="userVoResultMap">
         SELECT
            `user`.user_id,
            `user`.username,
            `user`.`password`,
            `user`.salt,
            `user`.email,
            `user`.user_type,
            `user`.del_flag,
            `user`.phone,
            `user`.status,
            `user`.dept_id,
            `user`.create_time AS ucreate_time,
            `user`.nickname,
            `user`.last_login_time,
            `user`.create_by AS ucreate_by,
            `user`.avatar,
            `user`.user_source,
            `user`.update_time AS uupdate_time,
            `user`.update_by AS uupdate_by,
            `user`.distributor_id,
            `user`.remark,
            `user`.tenant_id,
            `user`.top_level,
             r.role_id,
             r.role_name,
             r.role_code,
             r.role_property,
             r.type,
             r.role_desc,
             r.create_time AS rcreate_time,
             r.update_time AS rupdate_time,
             r.distributor_id AS rdistributor_id,
             d.name AS dept_name,
             d.dept_id  AS dept_id
        FROM
            sys_user AS `user`
            LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
            LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
            LEFT JOIN sys_dept AS d ON d.dept_id = `user`.dept_id
        WHERE
           `user`.user_id = #{id}
    </select>

    <select id="selectUserVoPageDataScope" resultMap="userVoResultMap" >
        SELECT
           `user`.user_id,
           `user`.username,
           `user`.`password`,
           `user`.salt,
           `user`.email,
           `user`.user_type,
           `user`.del_flag,
           `user`.phone,
           `user`.status,
           `user`.dept_id,
           `user`.create_time AS ucreate_time,
           `user`.nickname,
           `user`.last_login_time,
           `user`.create_by AS ucreate_by,
           `user`.avatar,
           `user`.user_source,
           `user`.update_time AS uupdate_time,
           `user`.update_by AS uupdate_by,
           `user`.distributor_id,
           `user`.remark,
           `user`.tenant_id,
           `user`.top_level,
            r.role_id,
            r.role_name,
            r.role_code,
            r.role_property,
            r.type,
            r.role_desc,
            r.create_time AS rcreate_time,
            r.update_time AS rupdate_time,
            r.distributor_id AS rdistributor_id,
            d.name AS dept_name,
            d.dept_id  AS dept_id
        FROM
            sys_user AS `user`
            LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
            LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
            LEFT JOIN sys_dept AS d ON d.dept_id = `user`.dept_id
        WHERE
            r.del_flag = 0
            <if test="username != null and username != ''">
                and `user`.username LIKE CONCAT('%',#{username},'%')
            </if>
            ORDER BY `user`.create_time DESC
    </select>

    <!-- 查询企业用户列表-->
    <select id="getEntUserPage" resultType="java.util.Map">
        select
          u.user_id as id,
          u.phone,
          u.email,
          DATE_FORMAT(u.create_time,"%Y-%m-%d %H:%i:%S") AS createTime,
          u.del_flag as delFlag
        from
          sys_user u left join sys_user_role r
        on
          u.user_id = r.user_id
        where
          r.role_id=15
        <if test="phone !=null and phone != ''">
            AND u.phone LIKE CONCAT('%',#{phone},'%')
        </if>
        <if test="delFlag !=null and delFlag != ''">
            AND u.del_flag = #{delFlag}
        </if>
          order by u.create_time desc
    </select>
    
    
    <!-- 查询子账号列表 ##################################################################### -->
    <select id="getSubUserList" resultType="com.dxhy.core.pojo.vo.SysUserVo">
        SELECT
			u.user_id,u.username,u.nickname,u.create_time,u.update_time,u.del_flag,u.email,
		  	u.phone
		FROM
			sys_user u
		WHERE   u.dept_id = #{entId} AND u.user_type = 2
		GROUP BY u.user_id order by u.create_time desc
    </select>

	<!-- 添加子账户 缺少租户租户ID和顶级机构 后期用到需要添加-->
	<insert id="addSubUser" parameterType="com.dxhy.core.pojo.DTO.UserDTO"
	useGeneratedKeys="true" keyProperty="userId">
		INSERT INTO sys_user
		(user_id,
        username,
        password,
        salt,
        email,
        user_type,
        del_flag,
        phone,
        status,
        dept_id,
        create_time,
        nickname,
        last_login_time,
        create_by,
        avatar,
        user_source,
        update_time,
        update_by,
        distributor_id,
        remark
        )
		VALUES 
		(#{userId},
		#{username},
		#{password},
		#{salt},
		#{email},
		#{userType},
		#{delFlag},
		#{phone},
		#{status},
		#{deptId},
		now(),
		#{nickname},
		now(),
		#{createBy},
		#{avatar},
        #{userSource},
        now(),
		#{createBy},
		#{distributorId},
		#{remark});
	</insert>
	<!-- 修改子账户 -->
	<update id="editSubUser" parameterType="com.dxhy.core.pojo.DTO.UserDTO">
		UPDATE sys_user
		SET
		  update_time = #{updateTime},
		  nickname = #{nickname}
		WHERE user_id = #{userId}
	</update>

    <!-- 查看企业下所有主账户 -->
    <select id="getUserForAosp" resultType="com.dxhy.core.pojo.vo.SysUserAospVo">
    	SELECT
			u.user_id,u.phone,u.nickname,u.email,b.del_flag
		FROM
		    sys_user u , base_ent_user b where u.user_id = b.user_id and b.ent_id = #{entId}
		    and b.type = 1
    </select>

    <!-- 通过企业id和主账号id查询所有分账号账户 -->
    <select id="getSubUserListForAosp" resultType="com.dxhy.core.pojo.vo.SysUserAospVo">
    	SELECT
			u.user_id,u.phone,u.nickname,u.email,u.del_flag
		FROM
		    sys_user u , base_ent_user b where u.user_id = b.user_id and b.ent_id = #{entId}
		    and b.type = 2 and create_by = #{userId}
    </select>

    <!-- 修改企业下主账号对应子账号的状态 -->
    <update id="updateSubUserDelFlag">
		UPDATE sys_user u, base_ent_user b
		SET
		  u.del_flag = #{delFlag},
		  b.del_flag = #{delFlag}
		WHERE u.user_id = b.user_id and b.ent_id = #{entId}
		    and b.type = 2 and create_by = #{userId}
	</update>

    <!-- 修改企业下主账号的状态 -->
    <update id="updateUserDelFlag">
		UPDATE sys_user u, base_ent_user b
		SET
		  u.del_flag = #{delFlag},
		  b.del_flag = #{delFlag}
		WHERE u.user_id = b.user_id and b.ent_id = #{entId}
		    and b.type = 1 and  b.user_id = #{userId}
	</update>

    <!-- 查询子账号角色列表 -->
    <select id="getSubUserRole" resultType="com.dxhy.core.pojo.vo.SysUserVo">
    	SELECT
            GROUP_CONCAT( r.role_id ) role_ids,
            GROUP_CONCAT( r.role_name ) role_names
        FROM
            sys_user_role ur,
            sys_role r
        WHERE
            ur.user_id=#{userId}
            AND ur.role_id = r.role_id
    </select>


    <!-- 修改子账户 -->
    <update id="updateSubUser" parameterType="com.dxhy.core.pojo.DTO.UserDTO">
		UPDATE sys_user
		SET
		  nickname = #{nickname},
		  username = #{username},
		  email = #{email},
		  phone = #{phone}
		WHERE user_id = #{userId}
	</update>


    <!-- 修改用户状态 -->
    <update id="updateUserStatus" >
		UPDATE sys_user
		SET
		  status = #{status}
		WHERE user_id = #{userId}
	</update>

    <!--查询部门下人员列表-->
<!--    <select id="listUsersByDeptIdList" resultType="com.dxhy.core.pojo.entity.SysUser" >-->
    <select id="listUsersByDeptIdList" resultMap="BaseResultMap" >
        SELECT
        *
        FROM
        sys_user su
        WHERE  1 = 1
        <if test="deptIdList != null and deptIdList.size() > 0">
            AND su.dept_id in
            <foreach item="deptId" collection="deptIdList" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>

        <if test="email != null and email != ''">
            AND su.email LIKE concat('%', #{email},'%')
        </if>
        <if test="phone != null and phone != ''">
            AND su.phone LIKE concat('%', #{phone}, '%')
        </if>
        <if test="username != null and username != ''">
            AND su.username LIKE concat('%', #{username},'%')
        </if>
        ORDER BY su.create_time DESC
    </select>

    <!--根据用户id查询用户角色信息-->
    <select id="queryRoleIdByUserId" resultType="com.dxhy.core.pojo.entity.SysRole" >
        SELECT
        *
        FROM
        sys_role sr
        left join sys_user_role sur on sr.role_id = sur.role_id
        WHERE
        sur.user_id = #{VALUE}
    </select>


    <!--更新用户信息-->
    <update id="updateUserByUserId" parameterType="com.dxhy.core.pojo.DTO.AdminUserOperateDto">
		UPDATE sys_user
		<set>
            <if test="email != null and email != ''">
                email = #{email},
            </if>
            <if test="name != null and name != ''">
                nickname = #{name},
            </if>
            <if test="userName != null and userName != ''">
                username = #{userName},
            </if>
            <if test="deptId != null and deptId != ''">
                dept_id = #{deptId},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
<!--            <if test="userId != null and userId != ''">-->
<!--                create_by = #{userId},-->
<!--            </if>-->
            <if test="puserId != null and puserId != ''">
                update_by = #{puserId},
            </if>
            <if test="mobile != null and mobile != ''">
                phone = #{mobile},
            </if>
            <if test="sourceId != null and sourceId != ''">
                distributor_id = #{sourceId},
            </if>
            <if test="type != null and type != ''">
                user_type = #{type},
            </if>
            <if test="userSource != null and userSource != ''">
                user_source = #{userSource}
            </if>
        </set>
		WHERE user_id = #{userId}
	</update>

    <!--删除用户角色之间的关系-->
    <delete id="deleteUserRoleRelation">
        delete from sys_user_role where user_id = #{userId}
    </delete>


    <!--删除用户组织之间的关系-->
    <delete id="deleteUserDeptRelation">
        delete from sys_user_dept where user_id = #{userId}
    </delete>

    <!--新增用户角色之间的关系-->
    <insert id="addUserRoleRelation" parameterType="com.dxhy.core.pojo.vo.SysUserRoleVo">
        insert into sys_user_role (user_id,role_id) values (#{userId},#{roleId})
    </insert>

    <!--新增用户-->
    <insert id="addUser" parameterType="com.dxhy.core.pojo.entity.SysUser" useGeneratedKeys="true" keyProperty="userId" keyColumn="user_id">
        insert into sys_user (username,
        password,
        salt,
        email,
        user_type,
        del_flag,
        phone,
        status,
        dept_id,
        create_time,
        nickname,
        last_login_time,
        create_by,
        avatar,
        user_source,
        update_time,
        update_by,
        distributor_id,
        remark,
        tenant_id,
        top_level)
        values (#{username},
		#{password},
		#{salt},
		#{email},
		#{userType},
		0,
		#{phone},
		1,
		#{deptId},
		now(),
		#{nickname},
		now(),
		#{createBy},
		#{avatar},
        #{userSource},
        now(),
		#{createBy},
		#{distributorId},
		#{remark},
        #{tenantId},
        #{topLevel})
    </insert>

    <!--新增用户所属部门之间的关系-->
    <insert id="addUserDeptRelation" >
        insert into sys_user_dept (user_id,dept_id) values (#{userId},#{deptId})
    </insert>


    <!--根据用户id 获取到当前所有数据权限-->
    <select id="selectDeptIdsByUserId" resultType="com.dxhy.core.pojo.entity.SysUserDept">
        select id,user_id as userId,dept_id as deptId from sys_user_dept where user_id = #{userId} GROUP BY  dept_id
    </select>


    <!--根据用户id 获取到当前所有角色-->
    <select id="selectRoleIdsByUserId" resultType="com.dxhy.core.pojo.entity.SysUserRole">
        select id,user_id as userId,role_id as roleId from sys_user_role where user_id = #{userId}
    </select>


    <!--新增用户渠道之间的关系-->
    <insert id="addBaseSourceUser" parameterType="com.dxhy.core.pojo.entity.BaseSourceUser">
        insert into base_source_user (user_id,source_id,`type`,create_time) values (#{userId},#{sourceId},#{type},now())
    </insert>



    <!--根据组织id 获取到当前组织下所有用户-->
    <select id="selectByDeptId" resultMap="BaseResultMap">
        select * from sys_user where dept_id = #{deptId}
    </select>

    <!--主账号切换-->
    <update id="updateUserType">
        update  sys_user   set user_type = #{type}   where user_id = #{userId}
    </update>

    <update id="updateBaseSourceUser">
        update  base_source_user   set source_id = #{sourceId}   where user_id = #{userId}
    </update>

    <!--根据用户id 分页查询用户信息-->
    <select id="queryPageUserByUserIdList" resultMap="BaseResultMap">
        select * from sys_user
        WHERE  1 = 1
        <if test="userIdList != null and userIdList.size() > 0">
            AND user_id in
            <foreach item="userId" collection="userIdList" open="(" separator="," close=")">
                #{userId}
            </foreach>
        </if>
    </select>


    <!--根据角色id集合获取菜单id-->
    <select id="queryMenuIdListByRoles" resultType="java.lang.String">
        select  menu_id from sys_role_menu
        WHERE  1 = 1
        <if test="roleIdList != null and roleIdList.size() > 0">
            AND role_id in
            <foreach item="roleId" collection="roleIdList" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
    </select>

    <!--新增查询以下组织有没有人员-->
    <select id="querySysUserStatus" resultType="com.dxhy.core.pojo.entity.SysUser">
        SELECT
        *
        FROM
        sys_user su
        WHERE
        su.dept_id = #{deptId,jdbcType=VARCHAR}   AND  su.status = #{status,jdbcType=TINYINT}
    </select>

    <!--账户 邮箱查询用户是否存在-->
    <select id="selectByUserNameAndEmail" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        sys_user su
        WHERE  1 = 1
        <if test="userName != null and userName != '' ">
            AND  su.username = #{userName}
        </if>
        <if test="email != null and email != '' ">
            AND  su.email = #{email}
        </if>
        AND  del_flag=0
    </select>
    <!--账户 邮箱查询用户是否存在-->
    <select id="selectByUserNameAndMobile" resultMap="BaseResultMap">
        SELECT
        *
        FROM
        sys_user su
        WHERE  1 = 1
        <if test="userName != null and userName != '' ">
            AND  su.username = #{userName}
        </if>
        <if test="phone != null and phone != '' ">
            AND  su.phone = #{phone}
        </if>
        AND  del_flag=0
    </select>

    <!--查询用户渠道是否存在-->
    <select id="selectBaseSourceUserByUserIdAnd" resultType="com.dxhy.core.pojo.entity.BaseSourceUser">
        SELECT
        *
        FROM
        base_source_user bsu
        WHERE
        bsu.user_id = #{userId}   and  bsu.source_id = #{sourceId}

    </select>


    <!--用户查询组织-->
    <select id="selectDeptByUserId" resultType="com.dxhy.core.pojo.vo.SysDeptVo">
         SELECT
         	sd.*
         FROM
         	sys_dept sd
         LEFT JOIN sys_user_dept su ON su.dept_id = sd.dept_id
         LEFT JOIN sys_user se ON se.user_id=su.user_id
         WHERE
         	se.user_id = #{userId}
         GROUP BY
         	sd.dept_id
    </select>

    <!--根据主企业查询绑定用户-->
    <select id="selectUserByDeptId" resultType="com.dxhy.core.pojo.entity.SysUser">
        SELECT
            su.*
        FROM
            sys_user su
                LEFT JOIN sys_user_dept sd ON su.dept_id = sd.dept_id
        WHERE
            sd.dept_id = #{deptId}
            and sd.dept_type="1"
    </select>

    <!--新增根据deptId和状态删除对应的用户信息-->
    <delete id="deleteByDeptIdAndStatus">
        DELETE  FROM sys_user  WHERE dept_id = #{deptId} and status = #{status}
    </delete>

    <!--根据条件查询用户信息-->
    <select id="selectByConditions" parameterType="com.dxhy.core.pojo.DTO.AdminUserOperateDto" resultMap="BaseResultMap">
        select * from sys_user
        where 1=1
            <if test=" userId != null and userId !='' ">
                and user_id = #{userId}
            </if>
            <if test=" userName != null and userName !='' ">
                and username = #{userName}
            </if>
            <if test=" mobile != null and mobile !='' ">
                and phone = #{mobile}
            </if>
            <if test=" email != null and email !='' ">
                and email = #{email}
            </if>
            <if test=" name != null and name !='' ">
                and nickname = #{name}
            </if>

    </select>

    <!--逻辑删除用户-->
    <update id="deleteUser" parameterType="java.lang.Long">
        update sys_user set del_flag = '1' where user_id = #{userId}
    </update>
    <!--根据userId查询渠道id-->
    <select id="getSourceIdByUserId" resultType="java.lang.String">
        select source_id from base_source_user where user_id = #{userId} AND  type=1
    </select>

    <select id="selectUserVoByDeptId" resultMap="userVoResultMap">
        <include refid="selectUserVo"/>
        WHERE `user`.dept_id = #{deptId} and `user`.user_type=1 GROUP BY `user`.user_id
    </select>

    <update id="updateUserPhoneOrEmailByUserId" parameterType="com.dxhy.core.pojo.DTO.UserDTO">
		UPDATE sys_user
		SET
		  username = #{username},
		  phone = #{phone},
		  email = #{email},
		  nickname=#{nickname}
		WHERE user_id = #{userId}
	</update>

    <!--辅助运营mybatis-->
    <select id="selectUserByEmail" resultMap="BaseResultMap">
        <include refid="selectUserVo"/>
        WHERE `user`.email = #{email} GROUP BY `user`.user_id
    </select>

    <!--根据用户ID和产品ID查看对应开通状态-->
    <select id="selectByUserAndProductId" resultType="java.lang.Integer">
        select status from customer_product where account_info_id = #{accountInfoId} AND  product_id = #{productId} AND  del_flag='0'
    </select>

    <!--查询字典获取默认开通的产品id-->
    <select id="getDictionaryByParent" parameterType="Integer" resultType="java.lang.String">
        select
        `desc`
        from dictionary where parent=#{code}
    </select>

    <!--企业客户开通产品-->
    <insert id="insertCustomerProduct" parameterType="java.util.Map">
        INSERT INTO customer_product (
	`id`,
	`account_id`,
	`customer_id`,
	`product_id`,
	`customer_type`,
	`begin_time`,
	`used`,
	`total`,
	`product_type`,
	`status`,
	`unit`,
	`del_flag`,
	`create_time`,
	`modify_time`,
	`account_info_id`
     )
       VALUES
	(
		#{id},
		NULL,
		#{customerId},
		#{productId},
		#{customerType},
		#{beginTime},
		#{used},
		#{total},
		#{productType},
		#{status},
		#{unit},
		#{delFlag},
		#{createTime},
		#{modifyTime},
		#{accountInfoId}
	)
    </insert>

    <!-- 修改企业客户产品详情 -->
    <update id="updateCustomerProduct" parameterType="java.util.Map">
        UPDATE customer_product
        SET
        modify_time = #{modifyTime},
        total = #{total},
        status = #{status}
        WHERE account_info_id = #{accountInfoId}
    </update>

    <!--先查询对应产品渠道是否已存在-->
    <select id="selectCustomerProductByuserId" resultType="java.lang.String">
        select id from customer_product where account_info_id = #{accountInfoId} AND  product_id = #{productId} AND  del_flag='0'
    </select>

    <!--根据产品ID查询产品信息-->
    <select id="selectProductInfoById" resultType="java.lang.String">
        select `name` from product_info where id = #{id}  AND  del_flag='0'
    </select>

    <!--查询sort-->
    <select id="selectSortByAccountInfoId" resultType="java.lang.Integer">
        SELECT
	sl.sort
        FROM
	combo_account ca
      LEFT JOIN combo_distributor cd ON ca.combo_distributor_id = cd.id
      LEFT JOIN combo_template ct ON cd.template_id = ct.id
      LEFT JOIN sell_label sl ON ct.sell_label_id=sl.id
      WHERE  ca.account_info_id = #{accountInfoId}  AND  ca.product_id=#{productId}
    </select>

    <!--根据uri查询对应distributor_productlink-->
    <select id="selectSourceIdProIdByUri" resultType="java.util.HashMap">
        select source_id,product_id from distributor_productlink where uri = #{uri}
    </select>


    <!--根据source_id,product_id查询对应URI-->
    <select id="selectUriBySourceIdProId" resultType="java.lang.String">
        select uri from distributor_productlink where source_id=#{sourceId} AND product_id=#{productId}
    </select>

    <!--根据source_id,product_id查询对应URI-->
    <select id="selectUriByDistributorIdProId" resultType="java.lang.String">
        select uri from distributor_productlink where distributor_id=#{distributorId} AND product_id=#{productId}
    </select>

    <!--根据username,tenant_id查询对应DX渠道简码-->
    <select id="getSimpeCodeByUnameAndTenid" resultType="java.lang.String">
        select d.simple_code from sys_user t
                                      left join distributor d
                                                on t.dept_id=d.id
        where t.username=#{username} and tenant_id=#{tenantId}
    </select>
    <select id="selectUserVoByContactPhone" resultMap="userVoResultMap">
        SELECT
            `user`.user_id,
            `user`.username,
            `user`.`password`,
            `user`.salt,
            `user`.email,
            `user`.user_type,
            `user`.del_flag,
            `user`.phone,
            `user`.status,
            `user`.dept_id,
            `user`.create_time AS ucreate_time,
            `user`.nickname,
            `user`.last_login_time,
            `user`.create_by AS ucreate_by,
            `user`.avatar,
            `user`.user_source,
            `user`.update_time AS uupdate_time,
            `user`.update_by AS uupdate_by,
            `user`.distributor_id,
            `user`.remark,
            `user`.tenant_id,
            `user`.top_level,
            r.role_id,
            r.role_name,
            r.role_code,
            r.role_property,
            r.type,
            r.role_desc,
            r.create_time AS rcreate_time,
            r.update_time AS rupdate_time,
            r.distributor_id AS rdistributor_id,
            d.name AS dept_name,
            d.dept_id  AS dept_id
        FROM
            sys_user AS `user`
                LEFT JOIN sys_user_role AS ur ON ur.user_id = `user`.user_id
                LEFT JOIN sys_role AS r ON r.role_id = ur.role_id
                LEFT JOIN sys_dept AS d ON d.dept_id = `user`.dept_id
        WHERE
            `d`.contact_phone = #{contactPhone}
          and d.create_user=`user`.user_id
    </select>

    <!--根据企业Id查询税控设备编码-->
    <select id="selectSksbbmByDeptId" resultType="java.lang.String">
        select sksbbm from sys_tax_control where dept_id = #{deptId} and `type`=#{type}
    </select>

    <!--根据userId查询是否开通了销项产品-->
    <select id="selectDxProduct" resultType="java.lang.Integer">
        select count(1) from sys_role_menu r
        left join sys_user_role u on r.role_id=u.role_id
        where u.user_id=#{userId}
          and r.menu_id in (select id from product_menu where system_sign=#{systemSign}  and parent_id='-1')
    </select>
    <select id="selectVersionStatus" resultType="java.lang.Integer">
        select version_status as versionStatus from sys_user where user_id=#{userId}
    </select>
    <select id="selectSmrByDeptId" resultType="com.dxhy.core.pojo.entity.SysUser"
            parameterType="java.lang.String">
        SELECT
            su.*
        FROM
            sys_user_role ur,
            sys_role sr,
            sys_user su
        WHERE
            ur.role_id = sr.role_id
          AND ur.user_id = su.user_id
          AND sr.dept_id = su.dept_id
          and su.dept_id=#{deptId} and sr.role_name='超级管理员'
    </select>

    <update id="updateVersionStatus" >
            UPDATE sys_user
            SET
                version_status = #{versionStatus}
            WHERE user_id = #{userId}
    </update>

    <select id="selectDeptIds" resultType="java.lang.String">
        <!--   SELECT distinct dept_id FROM sys_user WHERE create_by=#{userId}  and del_flag ='0'-->
        SELECT distinct u.dept_id FROM sys_user u,sys_dept d
        WHERE u.dept_id=d.dept_id
            and u.top_level=#{topLevel}
            <if test="userId != null and userId != ''">
                and u.create_by=#{userId}
            </if>
            <if test="deptName != null and deptName != ''">
                and d.`name`=#{deptName}
            </if>
          and u.del_flag ='0'
          and d.del_flag='0'
    </select>
    <select id="getAuthNameByDeptId" resultType="java.lang.String">
        select sy.nickname  from sys_user sy
                                     left join sys_user_dept ud on sy.user_id=ud.user_id
        where ud.dept_id=#{deptId}
        <if test="topLevel != null and topLevel != ''">
            and sy.`top_level`=#{topLevel}
        </if>
        group by sy.nickname
    </select>
    <select id="selectUserOrgByUserId" resultType="com.dxhy.core.pojo.third.DxJxUserOrg"
            parameterType="java.lang.Long">
        select d.`name` as taxName,d.taxpayer_code as taxno from sys_user_dept ud
                                                                     left join sys_dept d
                                                                               on ud.dept_id=d.dept_id
        where ud.user_id=#{userId}
    </select>
    <select id="getMinUserId" resultType="java.lang.Long">
        select min(user_id) as userId from sys_user
    </select>
    <select id="selectSmrBySubLevleDeptId" resultType="com.dxhy.core.pojo.entity.SysUser"
            parameterType="java.lang.String">
        select su.* from sys_user su
                             left join sys_dept sd on su.tenant_id=sd.tenant_id and su.distributor_id=sd.source_id
        where sd.dept_id=#{deptId} and user_type=1
    </select>
    <select id="getAccountTotalByName" resultType="java.lang.Integer">
        select count(1) from sys_user user WHERE `user`.username = #{username} OR `user`.phone = #{username} OR `user`.email = #{username}
    </select>
    <select id="getUserIdList" resultType="java.lang.Long">
        select user_id from sys_user where  tenant_id = #{tenantId}
    </select>
    <select id="queryUserIdByNameList" resultType="java.lang.Long">
        select user_id from sys_user where tenant_id = #{tenantId}
        <if test="namelist != null and namelist.size() > 0">
            AND username in
            <foreach item="username" collection="namelist" open="(" separator="," close=")">
                #{username}
            </foreach>
        </if>
    </select>
    <select id="selectDeptIdsHw" resultType="java.lang.String">
        SELECT distinct u.dept_id FROM sys_user u
        WHERE u.dept_id IN (
        SELECT d.dept_id FROM sys_dept d
        WHERE d.code LIKE CONCAT((SELECT code FROM sys_dept WHERE dept_id = #{topLevel}), '%')
        <if test="deptName != null and deptName != ''">
            and d.`name`=#{deptName}
        </if>
        AND d.del_flag = '0'
        )
        AND u.del_flag = '0'
    </select>

    <select id="checkDeptList" resultType="java.lang.String">
        SELECT
            dept_id
        FROM
            sys_dept
        WHERE
                dept_id IN
        <foreach item="deptId" collection="deptList" open="(" separator="," close=")">
            #{deptId}
        </foreach>
    </select>

</mapper>
