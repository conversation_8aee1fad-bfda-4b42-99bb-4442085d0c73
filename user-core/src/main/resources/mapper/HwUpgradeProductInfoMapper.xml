<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.dxhy.core.mapper.HwUpgradeProductInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.dxhy.core.pojo.entity.HwUpgradeProductInfoEntity" id="hwUpgradeProductInfoMap">
        <result property="id" column="id"/>
        <result property="instanceId" column="instance_id"/>
        <result property="orderId" column="order_id"/>
        <result property="skuCode" column="sku_code"/>
        <result property="productId" column="product_id"/>
        <result property="amount" column="amount"/>
        <result property="diskSize" column="disk_size"/>
        <result property="bandWidth" column="band_width"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>