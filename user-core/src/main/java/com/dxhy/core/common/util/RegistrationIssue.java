package com.dxhy.core.common.util;

import com.dxhy.core.enums.sensorsRegisterSuccessEnum;
import com.dxhy.core.enums.sensorsYsxy_imEnum;
import com.dxhy.core.enums.sensorsYszj_firstOpenEnum;
import com.dxhy.core.enums.sensorsYszj_sendSearchRequestEnum;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Auther: 李永强
 * @Date: 2019/4/12 11:39
 * @Description:
 */

public class RegistrationIssue {

    static String sensorUrl = "http://efats.utax360.cn:8106/sa?project=";
    /**
     * 保存日志更新用户属性
     * @param map
     * @throws Exception
     */
    public void userAttributeUpdateLog(HashMap<String, Object> map) throws Exception {

        //注册事件日志保存路径
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.ConcurrentLoggingConsumer("/data/logs/user_base/registerSuccess_log"));

        // 使用神策分析记录用户行为数据
        this.sensorsAnalytics(sa,map);

        sa.flush();  //立即发送缓存中的所有日志
        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush
    }


    /**
     * 保存日志注册事件
     * @param map
     * @throws Exception
     */
    public void registerSuccessLog(HashMap<String, Object> map) throws Exception {

        //注册事件日志保存路径
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.ConcurrentLoggingConsumer("/data/logs/user_base/registerSuccess_log"));

        // 使用神策分析记录用户行为数据
        this.registerSuccess(sa,map);
        this.sensorsAnalytics(sa,map);
        //注册时间
        sa.profileSet(String.valueOf(map.get("userId")), true, "registerTime", new Date());

        sa.flush();  //立即发送缓存中的所有日志
        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush
    }

    /**
     * 保存登录事件日志
     * @param map
     * @throws Exception
     */
    public void loginSuccessLog(HashMap<String, Object> map) throws Exception {

        //注册事件日志保存路径
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.ConcurrentLoggingConsumer("/data/logs/user_base/registerSuccess_log"));

        // 使用神策分析记录用户行为数据
        this.loginSuccess(sa,map);

        sa.flush();  //立即发送缓存中的所有日志
        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush
    }


    /**
     * 发送更新用户属性
     * @param map
     * @throws Exception
     */
    public void userAttributeUpdateHttp(HashMap<String, Object> map,String project) throws Exception {



        // 从神策分析获取的数据接收的 URL
        final String SA_SERVER_URL = sensorUrl+project;
        // 使用 Debug 模式，并且导入 Debug 模式下所发送的数据
        final boolean SA_WRITE_DATA = true;

        // 使用 DebugConsumer 初始化 SensorsAnalytics
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.DebugConsumer(SA_SERVER_URL, SA_WRITE_DATA));

        // 使用神策分析记录用户行为数据
        this.sensorsAnalytics(sa,map);

        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush
    }

    /**
     * 发送注册事件
     * @param map
     * @throws Exception
     */
    public void registerSuccessHttp(HashMap<String, Object> map,String project) throws Exception {

        // 从神策分析获取的数据接收的 URL
        final String SA_SERVER_URL = sensorUrl+project;
        // 使用 Debug 模式，并且导入 Debug 模式下所发送的数据
        final boolean SA_WRITE_DATA = true;

        // 使用 DebugConsumer 初始化 SensorsAnalytics
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.DebugConsumer(SA_SERVER_URL, SA_WRITE_DATA));

        // 使用神策分析记录用户行为数据
        this.registerSuccess(sa,map);
        this.sensorsAnalytics(sa,map);

        //注册时间
        sa.profileSet(String.valueOf(map.get("userId")), true, "registerTime", new Date());

        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush
    }


    /**
     * 发送登录事件
     * @param map
     * @throws Exception
     */
    public void loginSuccessHttp(HashMap<String, Object> map,String project) throws Exception {

        // 从神策分析获取的数据接收的 URL
        final String SA_SERVER_URL = sensorUrl+project;
        // 使用 Debug 模式，并且导入 Debug 模式下所发送的数据
        final boolean SA_WRITE_DATA = true;

        // 使用 DebugConsumer 初始化 SensorsAnalytics
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.DebugConsumer(SA_SERVER_URL, SA_WRITE_DATA));

        // 使用神策分析记录用户行为数据
        this.loginSuccess(sa,map);

        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush
    }


    /**
     * 给用户表属性赋值
     * @param sa
     * @param map
     * @throws Exception
     */
    public void sensorsAnalytics (SensorsAnalytics sa , HashMap<String, Object> map) throws Exception{

        //用户id
        String distinctId = String.valueOf(map.get("userId"));
        //用户属性

        if(map.get("account")!=null){
            if(String.valueOf(map.get("account")).contains("@")){
                sa.profileSet(distinctId, true, "email", map.get("account"));
            }else{
                sa.profileSet(distinctId, true, "phone", map.get("account"));
            }
        }

        //企业名称
        List companyNameLsit = (List)map.get("companyNameLsit");
        if(companyNameLsit!=null&&companyNameLsit.size()>0){
            sa.profileSet(distinctId, true, "companyName",companyNameLsit);
        }

        //是否企业账号
        if(map.get("isPrimaryAccount")!=null){
            sa.profileSet(distinctId, true, "isPrimaryAccount", map.get("isPrimaryAccount"));
        }

        //是否主账号
        if(map.get("isMainAccount")!=null){
            sa.profileSet(distinctId, true, "isMainAccount", map.get("isMainAccount"));
        }

        //是否分账号
        if(map.get("isMinorAccount")!=null){
            sa.profileSet(distinctId, true, "isMinorAccount", map.get("isMinorAccount"));
        }

        //首次访问来自服务商id
        if(map.get("facilitatorId")!=null){
            sa.profileSet(distinctId, true, "facilitatorId",map.get("facilitatorId"));
        }

        //首次访问来自服务商
        if(map.get("facilitator")!=null){
            sa.profileSet(distinctId, true, "facilitator", map.get("facilitator"));
        }

        //是否注册
        sa.profileSet(distinctId, true, "IsRegister", true);
        //用户类型

        //用户类型
        if(map.get("platform") != null){
            sa.profileSet(distinctId, true, "userType", "大B");
        }else{
            sa.profileSet(distinctId, true, "userType", "小B");
        }

    }

    /**
     * 注册事件赋值
     * @param sa
     * @param map
     * @throws Exception
     */
    public void registerSuccess (SensorsAnalytics sa , HashMap<String, Object> map) throws Exception{

        //事件用户id
        String registerId = String.valueOf(map.get("userId"));

        Map<String, Object> properties = new HashMap<String, Object>(3);
        properties.clear();

        String registMethod = "手机号";
        if(String.valueOf(map.get("account")).contains("@")){
            registMethod = "邮箱";
        }

        //事件属性

        //注册方式  （测试修改字段名称，上线前改为 registMethod ）
        properties.put("registration",registMethod);
        //账号
        properties.put("account",map.get("account"));
        //是否成功  （测试修改字段名称，上线前改为 isSuccess ）
        properties.put("succeed",true);
        //用户类型
        if(map.get("platform") != null){
            properties.put("userType","大B");
        }else{
            properties.put("userType","小B");
        }


        //公共属性
        Map<String, Object> publicProperties = new HashMap<String, Object>();

        // 产品名称
        if(map.get("productName")!=null){
            publicProperties.put("productName", map.get("productName"));
        }

        // 企业名称
        List companyNameLsit = (List)map.get("companyNameLsit");
        if(companyNameLsit!=null&&companyNameLsit.size()>0){
            publicProperties.put("companyName", companyNameLsit.get(0));
        }

        // 平台类型
        if(map.get("platformType")!=null){
            publicProperties.put("platformType", map.get("platformType"));
        }else{
            publicProperties.put("platformType", "web");
        }

        // 应用版本
        if(map.get("version")!=null){
            publicProperties.put("version", map.get("version"));
        }

        //是否首日访问
        publicProperties.put("$is_first_day", true);

        // 设置事件公共属性
        sa.registerSuperProperties(publicProperties);

        //注册事件
        sa.track(registerId, true, "registerSuccess", properties);

    }

    /**
     * 登录事件赋值
     * @param sa
     * @param map
     * @throws Exception
     */
    public void loginSuccess (SensorsAnalytics sa , HashMap<String, Object> map) throws Exception{

        //事件用户id
        String registerId = String.valueOf(map.get("userId"));


        Map<String, Object> properties = new HashMap<String, Object>(3);
        properties.clear();


        //事件属性

        //登录方式
        properties.put("login_method",map.get("login_method"));
        //账号
        properties.put("account",map.get("account"));
        //是否成功
        properties.put("succeed",true);


        //公共属性
        Map<String, Object> publicProperties = new HashMap<String, Object>();

        // 产品名称
        if(map.get("productName")!=null){
            publicProperties.put("productName", map.get("productName"));
        }

        // 平台类型
        if(map.get("platformType")!=null){
            publicProperties.put("platformType", map.get("platformType"));
        }else{
            publicProperties.put("platformType", "web");
        }

        // 应用版本
        if(map.get("version")!=null){
            publicProperties.put("version", map.get("version"));
        }

        //用户类型
        if(map.get("platform")!=null){
            publicProperties.put("userType", "大B");
        }else{
            publicProperties.put("userType", "小B");
        }

        // 设置事件公共属性
        sa.registerSuperProperties(publicProperties);

        if(map.get("device_id")!=null){
            sa.trackSignUp(registerId,String.valueOf(map.get("device_id")));
        }

        //登录事件
        sa.track(registerId, true, "loginSuccess", properties);

    }


    /**
     * 事件赋值
     * @param project
     * @param map
     * @throws Exception
     */
    public void aospSensors (HashMap<String, Object> map,String project) throws Exception{

        // 从神策分析获取的数据接收的 URL
        final String SA_SERVER_URL = sensorUrl+project;
        // 使用 Debug 模式，并且导入 Debug 模式下所发送的数据
        final boolean SA_WRITE_DATA = true;

        // 使用 DebugConsumer 初始化 SensorsAnalytics
        final SensorsAnalytics sa = new SensorsAnalytics(new SensorsAnalytics.DebugConsumer(SA_SERVER_URL, SA_WRITE_DATA));

        HashMap<String, Object> presetSensorsMap = new HashMap<String, Object>();//预置属性
        HashMap<String, Object> parameterSensorsMap = new HashMap<String, Object>();//事件参数

        HashMap<String, String> presetMap = (HashMap)map.get("preset");//预置属性
        HashMap<String, Object> parameterMap = (HashMap)map.get("parameter");//事件参数
        HashMap<String, Object>  userMap = (HashMap)map.get("userInfo");//用户属性
        String eventName = (String)map.get("eventName");//事件名称
        String registerId = String.valueOf(map.get("userId"));//事件用户id


        //预置属性赋值
        if(presetMap.get("productName") != null){
            presetSensorsMap.put("productName",presetMap.get("productName"));
        }
        if(presetMap.get("platformType") != null){
            presetSensorsMap.put("platformType",presetMap.get("platformType"));
        }
        if(presetMap.get("version") != null){
            presetSensorsMap.put("version",presetMap.get("version"));
        }

        //事件属性参数赋值
        if(eventName.equals("registerSuccess")){ //注册事件
            sensorsRegisterSuccessEnum[] values = sensorsRegisterSuccessEnum.values();
            for (sensorsRegisterSuccessEnum p :values){
                if(parameterMap.get(p.getParameterName()) != null){
                    parameterSensorsMap.put(p.getParameterName(),parameterMap.get(p.getParameterName()));
                }
            }
            //是否成功
            parameterSensorsMap.put("succeed",true);
            parameterSensorsMap.put("userType","专家");
        }else if (eventName.equals("yszj_firstOpen")){ //首次开通产品
            sensorsYszj_firstOpenEnum[] values = sensorsYszj_firstOpenEnum.values();
            for (sensorsYszj_firstOpenEnum p :values){
                if(parameterMap.get(p.getParameterName()) != null){
                    parameterSensorsMap.put(p.getParameterName(),parameterMap.get(p.getParameterName()));
                }
            }
        }else if (eventName.equals("yszj_im")){ //im咨询
            sensorsYsxy_imEnum[] values = sensorsYsxy_imEnum.values();
            for (sensorsYsxy_imEnum p :values){
                if(parameterMap.get(p.getParameterName()) != null){
                    parameterSensorsMap.put(p.getParameterName(),parameterMap.get(p.getParameterName()));
                }
            }
        }else if (eventName.equals("yszj_sendSearchRequest")){ //搜索结果
            sensorsYszj_sendSearchRequestEnum[] values = sensorsYszj_sendSearchRequestEnum.values();
            for (sensorsYszj_sendSearchRequestEnum p :values){
                if(parameterMap.get(p.getParameterName()) != null){
                    parameterSensorsMap.put(p.getParameterName(),parameterMap.get(p.getParameterName()));
                }
            }
        }else if (eventName.equals("ysxy_teacher")){
            if(parameterMap.get("teacher") != null){
                parameterSensorsMap.put("teacher",parameterMap.get("teacher"));
            }
        }

        // 设置事件公共属性
        sa.registerSuperProperties(parameterSensorsMap);

        //同步事件
        sa.track(registerId, true, eventName, presetSensorsMap);

        //用户属性
        if(userMap != null){
            //邮箱
            if(userMap.get("email") != null){
                sa.profileSet(registerId, true, "email", userMap.get("email"));
            }
            //手机号
            if(userMap.get("phone") != null){
                sa.profileSet(registerId, true, "phone", userMap.get("phone"));
            }
            //税号
            if(userMap.get("tax") != null){
                sa.profileSet(registerId, true, "tax", userMap.get("tax"));
            }
            //首次访问服务商id
            if(userMap.get("facilitatorId") != null){
                sa.profileSet(registerId, true, "facilitatorId", userMap.get("facilitatorId"));
            }
            //首次访问来自服务商
            if(userMap.get("facilitator") != null){
                sa.profileSet(registerId, true, "facilitator", userMap.get("facilitator"));
            }
            sa.profileSet(registerId, true, "registerTime", new Date());
            //是否注册
            sa.profileSet(registerId, true, "IsRegister", true);
            //是否专家
            sa.profileSet(registerId, true, "userType", "专家");
        }
        sa.flush();  //立即发送缓存中的所有日志
        sa.shutdown();   // 关闭 API，关闭时会自动调用 flush


    }
}