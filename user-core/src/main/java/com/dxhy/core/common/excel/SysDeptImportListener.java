package com.dxhy.core.common.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.excel.SysDeptImportDto;
import com.dxhy.core.pojo.vo.SysDeptResqVo;
import com.dxhy.core.service.SysDeptService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 机构导入Excel监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class SysDeptImportListener implements ReadListener<SysDeptImportDto> {

    /**
     * 每隔100条存储数据库，然后清理list，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    
    /**
     * 数据列表
     */
    private List<SysDeptImportDto> dataList = new ArrayList<>();
    
    /**
     * 部门服务
     */
    private SysDeptService sysDeptService;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 导入结果
     */
    private List<ImportResult> importResults = new ArrayList<>();
    
    /**
     * Nacos参数
     */
    private NacosParam nacosParam;
    
    /**
     * 当前租户组织数量
     */
    private int currentOrgCount;
    
    /**
     * 成功导入计数器
     */
    private AtomicInteger successCounter = new AtomicInteger(0);
    
    /**
     * 构造方法
     *
     * @param sysDeptService 部门服务
     * @param userId 用户ID
     * @param tenantId 租户ID
     * @param nacosParam Nacos参数
     * @param currentOrgCount 当前租户组织数量
     */
    public SysDeptImportListener(SysDeptService sysDeptService, String userId, String tenantId, NacosParam nacosParam, int currentOrgCount) {
        this.sysDeptService = sysDeptService;
        this.userId = userId;
        this.tenantId = tenantId;
        this.nacosParam = nacosParam;
        this.currentOrgCount = currentOrgCount;
    }
    
    /**
     * 每一条数据解析都会调用
     */
    @Override
    public void invoke(SysDeptImportDto data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        dataList.add(data);
        // 达到BATCH_COUNT，则调用保存方法，防止数据几万条数据在内存，容易OOM
        if (dataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            dataList.clear();
        }
    }
    
    /**
     * 所有数据解析完成会调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 确保最后遗留的数据也被保存
        saveData();
        log.info("所有数据解析完成！");
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return true;
    }
    
    /**
     * 保存数据
     */
    private void saveData() {
        log.info("{}条数据，开始保存数据库！", dataList.size());
        for (SysDeptImportDto deptDto : dataList) {
            ImportResult result = new ImportResult();
            result.setName(deptDto.getName());
            result.setTaxpayerCode(deptDto.getTaxpayerCode());
            
            try {
                // 数据校验
                if (StringUtils.isBlank(deptDto.getName())) {
                    result.setSuccess(false);
                    result.setMessage("组织名称不能为空");
                    importResults.add(result);
                    continue;
                }
                
                if (StringUtils.isBlank(deptDto.getTaxpayerCode())) {
                    result.setSuccess(false);
                    result.setMessage("纳税人税号不能为空");
                    importResults.add(result);
                    continue;
                }
                
                // 检查是否超过最大组织数量限制
                if (currentOrgCount + successCounter.get() >= nacosParam.maxOrgCount) {
                    result.setSuccess(false);
                    result.setMessage("租户组织数量已达到上限");
                    importResults.add(result);
                    continue;
                }
                
                // 构建SysDeptResqVo对象
                SysDeptResqVo sysDeptResqVo = new SysDeptResqVo();
                SysDept sysDept = new SysDept();
                BeanUtils.copyProperties(deptDto, sysDept);
                
                // 设置必要字段
                sysDept.setCreateTime(new Date());
                sysDept.setUpdateTime(new Date());
                sysDept.setCreateUser(Long.parseLong(userId));
                sysDept.setTenantId(tenantId);
                sysDept.setDataSource("4"); // 默认数据来源
                sysDept.setDelFlag("0"); // 未删除
                
                sysDeptResqVo.setApiDeptEntity(sysDept);
                
                // 调用添加部门方法
                Result addResult = sysDeptService.addDept(sysDeptResqVo, userId);
                
                if (addResult != null && (Integer.parseInt(addResult.get("code").toString()) == 0 || "success".equals(addResult.get("msg").toString()))) {
                    result.setSuccess(true);
                    result.setMessage("导入成功");
                    successCounter.incrementAndGet(); // 成功导入计数
                } else {
                    result.setSuccess(false);
                    result.setMessage(addResult != null ? addResult.get("msg").toString() : "导入失败");
                }
            } catch (Exception e) {
                log.error("导入机构异常", e);
                result.setSuccess(false);
                result.setMessage("导入异常: " + e.getMessage());
            }
            
            importResults.add(result);
        }
        log.info("数据库保存成功！");
    }
    
    /**
     * 获取导入结果
     */
    public List<ImportResult> getImportResults() {
        return importResults;
    }
    
    /**
     * 获取成功导入数量
     */
    public int getSuccessCount() {
        return successCounter.get();
    }
    
    /**
     * 导入结果类
     */
    @lombok.Data
    public static class ImportResult {
        /**
         * 组织名称
         */
        private String name;
        
        /**
         * 纳税人税号
         */
        private String taxpayerCode;
        
        /**
         * 是否成功
         */
        private boolean success;
        
        /**
         * 消息
         */
        private String message;
    }
} 