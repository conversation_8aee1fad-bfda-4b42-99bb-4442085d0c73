package com.dxhy.core.common.response;


import com.dxhy.core.enums.ResponseCodeEnum;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: CommonRspVo
 * @projectName user_base
 * @date 2020-10-2819:02
 */

public class CommonRspVo<T>
        implements Serializable
{

    private static final long serialVersionUID = -6811718099950709617L;
    private String code;
    private String message;
    private T data;

    @Override
    public int hashCode()
    {
        int PRIME = 59;int result = 1;Object $code = getCode();result = result * 59 + ($code == null ? 43 : $code.hashCode());Object $message = getMessage();result = result * 59 + ($message == null ? 43 : $message.hashCode());Object $data = getData();result = result * 59 + ($data == null ? 43 : $data.hashCode());return result;
    }

    protected boolean canEqual(Object other)
    {
        return other instanceof CommonRspVo;
    }

    @Override
    public boolean equals(Object o)
    {
        if (o == this) {
            return true;
        }
        if (!(o instanceof CommonRspVo)) {
            return false;
        }
        CommonRspVo<?> other = (CommonRspVo)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Object this$code = getCode();Object other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        Object this$message = getMessage();Object other$message = other.getMessage();
        if (this$message == null ? other$message != null : !this$message.equals(other$message)) {
            return false;
        }
        Object this$data = getData();Object other$data = other.getData();return this$data == null ? other$data == null : this$data.equals(other$data);
    }

    public void setCode(String code)
    {
        this.code = code;
    }

    public void setMessage(String message)
    {
        this.message = message;
    }

    public void setData(T data)
    {
        this.data = data;
    }

    @Override
    public String toString()
    {
        return "CommonRspVo(code=" + getCode() + ", message=" + getMessage() + ", data=" + getData() + ")";
    }

    public String getCode()
    {
        return this.code;
    }

    public String getMessage()
    {
        return this.message;
    }

    public T getData()
    {
        return this.data;
    }

    public CommonRspVo(String code, String message)
    {
        this.code = code;
        this.message = message;
    }

    public CommonRspVo(ResponseCodeEnum code)
    {
        this.code = code.getCode();
        this.message = code.getMessage();
    }

    public CommonRspVo(T data, ResponseCodeEnum code)
    {
        this.code = code.getCode();
        this.message = code.getMessage();
        this.data = data;
    }

    public CommonRspVo(T data)
    {
        this(ResponseCodeEnum.SUCCESS);
        this.data = data;
    }

    public boolean successfull()
    {
        return ResponseCodeEnum.SUCCESS.getCode().equals(this.code);
    }

    public static <C> CommonRspVo success(C data)
    {
        CommonRspVo success = new CommonRspVo(ResponseCodeEnum.SUCCESS);
        success.data = data;
        return success;
    }

    public static CommonRspVo faild()
    {
        CommonRspVo faild = new CommonRspVo(ResponseCodeEnum.SYSTEM_ERROR);
        return faild;
    }

    public CommonRspVo() {}
}
