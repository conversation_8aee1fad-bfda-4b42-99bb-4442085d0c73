/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.common.assist;

import com.dxhy.core.pojo.vo.SysRole;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**原辅助运营对外提供VO
 * <AUTHOR>
 * @date 2017/10/29
 */
@Data
public class UserVo implements Serializable {

    private static final long serialVersionUID = -7822679114953758875L;
    private Long id;
    private Long puserId;
    private String username;
    private String password;
    private String userType;
    private String phone;
    private String email;
    private String status;
    private String remark;
    private String distributorId;
    private Date createTime;
    private Date modifyTime;


    private List<SysRole> sysRoles;


}
