
package com.dxhy.core.common.response;

import com.dxhy.core.enums.ResponseCodeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午9:59:27
 */
public class Result extends HashMap<String, Object> {
	private static final long serialVersionUID = 1L;

	public Result() {
		put("code", "0000");
		put("msg", "success");
	}
	
	public static Result error() {
		return error("9999", "未知异常，请联系管理员");
	}
	
	public static Result error(String msg) {
		return error("9999", msg);
	}
	
	public static Result error(int code, String msg) {
		Result r = new Result();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public static Result error(String code, String msg) {
		Result r = new Result();
		r.put("code", code);
		r.put("msg", msg);
		return r;
	}

	public static Result ok(String msg) {
		Result r = new Result();
		r.put("msg", msg);
		return r;
	}
	
	public static Result ok(Map<String, Object> map) {
		Result r = new Result();
		r.putAll(map);
		return r;
	}
	
	public static Result ok() {
		return new Result();
	}

	@Override
	public Result put(String key, Object value) {
		super.put(key, value);
		return this;
	}

	public Result (ResponseCodeEnum code)
	{
		put("code", code.getCode());
		put("msg", code.getMessage());
	}
}
