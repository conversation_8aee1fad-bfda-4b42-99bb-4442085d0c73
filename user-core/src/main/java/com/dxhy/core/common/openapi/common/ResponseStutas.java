package com.dxhy.core.common.openapi.common;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 接口返回外层
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ResponseStutas implements Serializable {


    private String code;
    private String message;

    public ResponseStutas(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public ResponseStutas() {
    }
}
