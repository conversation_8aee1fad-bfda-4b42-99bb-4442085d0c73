package com.dxhy.core.common.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/6 10:15
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Result2 {

    public static final String SUCCESS_CODE = "0000";
    public static final String SUCCESS_MSG = "成功";

    public static final String ERROR_CODE = "9999";
    public static final String ERROR_MSG = "失败";

    private String code;

    private String msg;

    public static Result2 success(){
        return success(SUCCESS_MSG);
    }

    public static Result2 success(String msg){
        return new Result2(SUCCESS_CODE, msg);
    }

    public static Result2 error(){
        return error(ERROR_MSG);
    }

    public static Result2 error(String msg){
        return new Result2(ERROR_CODE, msg);
    }

}
