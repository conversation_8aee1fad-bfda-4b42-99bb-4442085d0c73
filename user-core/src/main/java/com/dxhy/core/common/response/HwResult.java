
package com.dxhy.core.common.response;

import com.dxhy.core.enums.ResponseCodeEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2016年10月27日 下午9:59:27
 */
public class HwResult extends HashMap<String, Object> {
	private static final long serialVersionUID = 1L;

	public HwResult() {
		put("resultCode", "000000");
		put("resultMsg", "success");
	}

	public static HwResult error() {
		return error("9999", "未知异常，请联系管理员");
	}

	public static HwResult error(String msg) {
		return error("9999", msg);
	}

	public static HwResult error(int code, String msg) {
		HwResult r = new HwResult();
		r.put("resultCode", code);
		r.put("resultMsg", msg);
		return r;
	}

	public static HwResult error(String code, String msg) {
		HwResult r = new HwResult();
		r.put("resultCode", code);
		r.put("resultMsg", msg);
		return r;
	}

	public static HwResult ok(String msg) {
		HwResult r = new HwResult();
		r.put("resultMsg", msg);
		return r;
	}

	public static HwResult ok(Map<String, Object> map) {
		HwResult r = new HwResult();
		r.putAll(map);
		return r;
	}

	public static HwResult ok() {
		return new HwResult();
	}

	@Override
	public HwResult put(String key, Object value) {
		super.put(key, value);
		return this;
	}

	public HwResult(ResponseCodeEnum code)
	{
		put("resultCode", code.getCode());
		put("resultMsg", code.getMessage());
	}
}
