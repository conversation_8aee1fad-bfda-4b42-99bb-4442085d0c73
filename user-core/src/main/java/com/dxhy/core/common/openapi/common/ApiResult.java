package com.dxhy.core.common.openapi.common;

import com.dxhy.core.constants.ApiConstant;

import java.util.HashMap;
import java.util.Map;

/**
 * 返回数据
 *
 * <AUTHOR>
 */
public class ApiResult extends HashMap<String, Object> {
    
    
    public ApiResult() {
    }
    
    
    public static ApiResult error(ResponseStutas responseStatus) {
        ApiResult r = new ApiResult();
        r.put(ApiConstant.RESPONSESTATUS, responseStatus);
        return r;
    }
    
    
    public static ApiResult error(ResponseStutas responseStatus, ResponseData responseData) {
        ApiResult r = new ApiResult();
        r.put(ApiConstant.RESPONSEDATA, responseData);
        r.put(ApiConstant.RESPONSESTATUS, responseStatus);
        return r;
    }
    
    public static ApiResult ok(ResponseStutas responseStatus) {
        ApiResult r = new ApiResult();
        r.put(ApiConstant.RESPONSESTATUS, responseStatus);
        return r;
    }
    
    public static ApiResult ok(ResponseStutas responseStatus, ResponseData responseData) {
        ApiResult r = new ApiResult();
        r.put(ApiConstant.RESPONSEDATA, responseData);
        r.put(ApiConstant.RESPONSESTATUS, responseStatus);
        return r;
    }
    
    public static ApiResult ok(Map<String, Object> map) {
        ApiResult r = new ApiResult();
        r.putAll(map);
        return r;
    }
    
    public static ApiResult ok() {
        return new ApiResult();
    }
    
    public static ApiResult ok(String contet) {
        ApiResult r = new ApiResult();
        ResponseData responseData = new ResponseData("0", "0", contet);
        r.put(ApiConstant.RESPONSEDATA, responseData);
        r.put(ApiConstant.RESPONSESTATUS, ErrorStatusEnum.SUCCESS);
        return r;
    }
    
    @Override
    public ApiResult put(String key, Object value) {
        super.put(key, value);
        return this;
    }
}
