package com.dxhy.core.common;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: zhangjinjing
 * @Date: 2022/4/27 20:01
 * @Version 1.0
 */
@Component
public class NacosParam {

    @Value(value = "${test.age}")
    public String dataAge;
    @Value(value = "${myname}")
    public String myname;
    @Value(value = "${sms.expire}")
    public Long smsExpire;

    @Value(value = "${ele-cloud.ssoUrl}")
    public String ssoUrl;
    @Value(value = "${ele-cloud.sourceId}")
    public String sourceId;
    @Value(value = "${ele-cloud.ssoSourceId}")
    public String ssoSourceId;

    @Value(value = "${dx_switch}")
    public int dxSwitch;

    @Value(value = "${cookie.expire}")
    public Long cookieExpire;

    @Value(value = "${productId.XXXT}")
    public String XxProId;

    @Value(value = "${productId.JXXT}")
    public String JxProId;

    @Value(value = "${productId.QDKP}")
    public String QdKpProId;

    @Value(value = "${productId.QDYP}")
    public String QdYPProId;

    @Value(value = "${productId.SBXT}")
    public String SbProId;

    @Value(value = "${productId.BXXT}")
    public String BxProId;

    @Value(value = "${productId.FKXT}")
    public String FKProId;



    @Value(value = "${demouids}")
    public String demouids;

    @Value(value = "${ele-cloud.jxsys.core}")
    public String jxcore;

    @Value(value = "${ele-cloud.jxsys.signAceKey}")
    public String jxSignAceKey;

    @Value(value = "${ele-cloud.jxsys.psw}")
    public String jxPsw;

    @Value(value = "${ele-cloud.jxsys.appSecKey}")
    public String jxAppSecKey;

    @Value(value = "${ele-cloud.jxsys.syncUserUrl}")
    public String syncUserUrl;

    @Value(value = "${ele-cloud.jxsys.syncTaxUrl}")
    public String syncTaxUrl;

    @Value(value = "${ele-cloud.jxsys.aceId}")
    public String aceId;

    @Value(value = "${ele-cloud.jxsys.aceKey}")
    public String aceKey;

    @Value(value = "${ele-cloud.jxsys.jxSsoUrl}")
    public String jxSsoUrl;

    @Value(value = "${saas.secretId}")
    public String secretId;

    @Value(value = "${saas.secretKey}")
    public String secretKey;

    @Value(value = "${maxOrgCount}")
    public int maxOrgCount;

    @Value(value = "${qdUserCheckUrl}")
    public String qdUserCheckUrl;

    @Value(value = "${registerUrl}")
    public String registerUrl;

    @Value(value = "${qdDataJhkUrl}")
    public String qdDataJhkUrl;
    @Value(value = "${jxDataJhkUrl}")
    public String jxDataJhkUrl;
    @Value(value = "${hwcloud.accessKey}")
    public String accessKey;
    @Value(value = "${hwcloud.clientId}")
    public String hwClientId;
    @Value(value = "${hwcloud.clientSecret}")
    public String hwClientSecret;
    @Value(value = "${hwcloud.domainName}")
    public String hwDomainName;
    @Value(value = "${hwcloud.accessTokenUrl}")
    public String hwAccTokenUrl;
    @Value(value = "${hwcloud.redirectUri}")
    public String hwRedirectUri;
    @Value(value = "${hwcloud.logoutUrl}")
    public String hwLogoutUrl;
    @Value(value = "${hwcloud.userInfoUrl}")
    public String hwUserInfoUrl;
    @Value(value = "${hwcloud.authTokenCheck}")
    public String hwAuthTokenCheck;


    @Value(value = "${hwcloud.frontEndUrl}")
    public String frontEndUrl;
    @Value(value = "${hwcloud.adminUrl}")
    public String adminUrl;

    @Value(value = "${hwcloud.publicKey}")
    public String publicKey;
    @Value(value = "${hwcloud.privateKey}")
    public String privateKey;

    @Value(value = "${dxcloud.etaxUrl}")
    public String dxEtaxUrl;
}

