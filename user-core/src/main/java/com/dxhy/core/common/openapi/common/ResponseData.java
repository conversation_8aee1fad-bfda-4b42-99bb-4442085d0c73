package com.dxhy.core.common.openapi.common;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 返回外层报文
 *
 * <AUTHOR>
 */
@Setter
@Getter
public class ResponseData implements Serializable {
    private String encryptCode;
    private String zipCode;
    private String content;


    public ResponseData(String encryptCode, String zipCode, String content) {
        this.encryptCode = encryptCode;
        this.zipCode = zipCode;
        this.content = content;
    }

    public ResponseData() {
    }
}
