package com.dxhy.core.common.excel;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.fastjson.JSON;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.AdminUserOperateDto;
import com.dxhy.core.pojo.excel.SysUserImportDto;
import com.dxhy.core.service.SysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 用户导入Excel监听器
 *
 * <AUTHOR>
 */
@Slf4j
public class SysUserImportListener implements ReadListener<SysUserImportDto> {

    /**
     * 每隔100条存储数据库，然后清理list，方便内存回收
     */
    private static final int BATCH_COUNT = 100;
    
    /**
     * 数据列表
     */
    private List<SysUserImportDto> dataList = new ArrayList<>();
    
    /**
     * 用户服务
     */
    private SysUserService sysUserService;
    
    /**
     * 操作用户ID
     */
    private String userId;
    
    /**
     * 租户ID
     */
    private String tenantId;
    
    /**
     * 导入结果
     */
    private List<ImportResult> importResults = new ArrayList<>();
    
    /**
     * 成功导入计数器
     */
    private AtomicInteger successCounter = new AtomicInteger(0);
    
    /**
     * 密码加密器
     */
    private BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    /**
     * 构造方法
     *
     * @param sysUserService 用户服务
     * @param userId 操作用户ID
     * @param tenantId 租户ID
     */
    public SysUserImportListener(SysUserService sysUserService, String userId, String tenantId) {
        this.sysUserService = sysUserService;
        this.userId = userId;
        this.tenantId = tenantId;
    }
    
    /**
     * 每一条数据解析都会调用
     */
    @Override
    public void invoke(SysUserImportDto data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        dataList.add(data);
        // 达到BATCH_COUNT，则调用保存方法，防止数据几万条数据在内存，容易OOM
        if (dataList.size() >= BATCH_COUNT) {
            saveData();
            // 存储完成清理 list
            dataList.clear();
        }
    }
    
    /**
     * 所有数据解析完成会调用
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        // 确保最后遗留的数据也被保存
        saveData();
        log.info("所有数据解析完成！");
    }

    @Override
    public boolean hasNext(AnalysisContext context) {
        return true;
    }
    
    /**
     * 保存数据
     */
    private void saveData() {
        log.info("{}条数据，开始保存数据库！", dataList.size());
        for (SysUserImportDto userDto : dataList) {
            ImportResult result = new ImportResult();
            result.setUsername(userDto.getUserName());
            result.setName(userDto.getName());
            result.setPhone(userDto.getMobile());
            
            try {
                AdminUserOperateDto adminUserOperateDto = new AdminUserOperateDto();
                BeanUtils.copyProperties(userDto, adminUserOperateDto);
                adminUserOperateDto.setPuserId(1L);
                //页面上传的用户类型为2
                adminUserOperateDto.setType("2");
                adminUserOperateDto.setCreateTime(new Date());
                adminUserOperateDto.setUserSource("4");
                if(StringUtils.isNotBlank(userDto.getDeptIds())){
                    adminUserOperateDto.setDeptList(Arrays.asList(userDto.getDeptIds().split(",")));
                }
                if(StringUtils.isNotBlank(userDto.getRoleIds())){
                    String[] split = userDto.getRoleIds().split(",");
                    //字符串数组转为Long集合
                    List<Long> list = Arrays.stream(split).map(Long::parseLong).collect(Collectors.toList());
                    adminUserOperateDto.setRoleIdList(list);
                }

                Result addUserResult = sysUserService.addUser(adminUserOperateDto);
                if (addUserResult != null && (Integer.parseInt(addUserResult.get("code").toString()) == 0 || "success".equals(addUserResult.get("msg").toString()))) {
                    result.setSuccess(true);
                    result.setMessage("导入成功");
                    successCounter.incrementAndGet(); // 成功导入计数
                } else {
                    result.setSuccess(false);
                    result.setMessage(addUserResult != null ? addUserResult.get("msg").toString() : "导入失败");
                }
            } catch (Exception e) {
                log.error("导入用户异常", e);
                result.setSuccess(false);
                result.setMessage("导入异常: " + e.getMessage());
            }
            importResults.add(result);
        }
        log.info("数据库保存成功！");
    }
    
    /**
     * 获取导入结果
     */
    public List<ImportResult> getImportResults() {
        return importResults;
    }
    
    /**
     * 获取成功导入数量
     */
    public int getSuccessCount() {
        return successCounter.get();
    }
    
    /**
     * 导入结果类
     */
    @lombok.Data
    public static class ImportResult {
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 姓名
         */
        private String name;
        
        /**
         * 手机号
         */
        private String phone;
        
        /**
         * 是否成功
         */
        private boolean success;
        
        /**
         * 消息
         */
        private String message;
    }
}