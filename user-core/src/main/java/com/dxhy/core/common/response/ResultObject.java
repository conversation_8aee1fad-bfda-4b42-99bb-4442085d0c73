package com.dxhy.core.common.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/6 10:28
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultObject extends Result2 {

    private Object data;

    public static ResultObject success(Object object){
        ResultObject result = new ResultObject();
        result.setCode(Result2.SUCCESS_CODE);
        result.setMsg(Result2.SUCCESS_MSG);
        result.setData(object);
        return result;
    }

    public static ResultObject error(Object object){
        ResultObject result = new ResultObject();
        result.setCode(Result2.ERROR_CODE);
        result.setMsg(Result2.ERROR_MSG);
        result.setData(object);
        return result;
    }

}
