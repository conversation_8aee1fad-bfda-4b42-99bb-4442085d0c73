package com.dxhy.core.common.openapi.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 〈响应code码枚举〉
 *
 * <AUTHOR>
 * @create 2022/9/12
 */
@Getter
@AllArgsConstructor
public enum ErrorStatusEnum {


	AUTHFAIL("0001", "签名验证失败"),
	NOTAUTH("0002", "签名验证失败:secretId对应的secretKey为空!"),
	NOTPRODUCT("0003", "未开通该产品或产品授权已过期!"),
	SUCCESS("0000", "数据接收成功"),
	FAIL("9999", "数据接收失败"),
	CHECK_INTERFACEVERSION_NULL("9999", "接口参数-版本号为空"),
	CHECK_INTERFACEVERSION_DATA_ERROR("9999", "接口参数-版本号错误"),
	CHECK_INTERFACENAME_NULL("9999", "接口参数-接口名称为空"),
	CHECK_TIMESTAMP_NULL("9999", "接口参数-时间戳为空"),
	CHECK_NONCE_NULL("9999", "接口参数-随机数为空"),
	CHECK_SECRETID_NULL("9999", "接口参数-加密ID为空"),
	CHECK_SIGNATURE_NULL("9999", "接口参数-签名值为空"),
	CHECK_ENCRYPTCODE_NULL("9999", "接口参数-加密方式为空"),
	CHECK_ENCRYPTCODE_DATA_ERROR("9999", "接口参数-加密方式错误"),
	CHECK_ZIPCODE_NULL("9999", "接口参数-压缩方式为空"),
	CHECK_ZIPCODE_NULL_DATA_ERROR("9999", "接口参数-压缩方式错误"),
	CHECK_CONTENT_NULL("9999", "接口参数-数据为空"),
	TRANSACTION_ERROR("5000", "事务异常！"),
	ILLEGALARGUMENT_ERROR("5000", "参数格式不合法！"),
	NULLPOINTER_ERROR("5000", "空指针异常！"),
	SERIALIZABLE_ERROR("5000", "缺少请求主体或反序列化失败！"),
	PARAMS_ERROR("5001", "请求参数错误！"),
	SYSTEM_ERROR("5000", "系统错误，请联系管理员！");

	private String code;

	private String message;

}
