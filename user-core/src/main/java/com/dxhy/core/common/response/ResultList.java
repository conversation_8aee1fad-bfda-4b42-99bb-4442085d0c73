package com.dxhy.core.common.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/6 10:28
 * @Version 1.0
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultList<E> extends Result2 {

    private List<E> dataList;

    public static ResultList success(List<?> dataList){
        ResultList result = new ResultList();
        result.setCode(Result2.SUCCESS_CODE);
        result.setMsg(Result2.SUCCESS_MSG);
        result.setDataList(dataList);
        return result;
    }

    public static ResultList error(List<?> dataList){
        ResultList result = new ResultList();
        result.setCode(Result2.ERROR_CODE);
        result.setMsg(Result2.ERROR_MSG);
        result.setDataList(dataList);
        return result;
    }

}
