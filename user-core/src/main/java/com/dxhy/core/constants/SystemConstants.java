package com.dxhy.core.constants;




public class SystemConstants implements SecurityConstants {

    public static String DEFAULT_EMAIL_CODE_KEY = "heaven_code:email:";
    public static String DEFAULT_EMAIL_CODE_NUM_KEY = "heaven_code:email:num:";

    public static String DEFAULT_SMS_CODE_KEY = "heaven_code:sms:";
    public static String DEFAULT_SMS_CODE_NUM_KEY = "heaven_code:sms:num:";

    public static String LOGIN_WECHAT_CODE_KEY = "heaven_code:wechat:";

    public static String LOGIN_PRODUCT_CODE_KEY = "heaven_code:product:";

    public static String AUTHENTICATION_LOGIN = "/authentication/login";

    public static String AUTHENTICATION_LOGUT = "/authentication/logout";

    public static String USER_CURRENT_SELECT = "heaven_user_currentSelect:";

    public static String MOBILE_LOGIN_URL = "/mobileLogin";

    public static String USER_INFO_CODE_KEY = "heaven_code:user_info:";

    public static String DEPT_INFO_CODE_KEY = "heaven_code:dept_info:";

    public static String SSO_COOKIE_CODE_KEY = "heaven_code:sso_cookie:";

    public static String USER_TOKEN_CODE_KEY = "heaven_code:user_token:";
    public static String MYCST_TOKEN_CODE_KEY = "heaven_code:mycst_token:";

    public static String USER_SIGN_CODE_KEY = "heaven_code:sign_code:";

    public static String USER_PASSWORD_CODE_KEY = "heaven_code:user_password:";

    public static String USER_SKMENU_CODE_KEY = "heaven_code:user_skmenu:";

    public static String USER_ELE_SSO_TOKEN_KEY = "heaven_code:ele_cloud:sso_token:";

    public static String USER_ELE_OPEN_TOKEN_KEY = "heaven_code:ele_cloud:open_token";

    public static String HBHX_TAX_NAME = "HBHX:TAX_NAME:";

    /**  系统默认角色CODE  */
    public static String SYSTEM_DEFAULT_ROLE_CODE = "biz";
    /**  系统默认跟菜单ID */
    public static String SYSTEM_DEFAULT_MENU_ROOT_ID = "10000";
    /** 学院产品菜单根ID*/
    public static String SYSTEM_DEFALUT_PRODUCT_ID_BEGIN = "14000";

    /** 门户产品菜单根ID*/
    public static String PRODUCT_MENU_ROOT_ID_PORTAL = "17000";

    /** 优税专家产品菜单根ID*/
    public static String PRODUCT_MENU_ROOT_ID_EXPERT    = "15000";

    /** 优税宝典产品菜单根ID*/
    public static String PRODUCT_MENU_ROOT_ID_BOOK = "16000";


    /** 优税测评产品菜单ID*/
    public static String PRODUCT_MENU_ROOT_ID_CEPING = "11000";

    /** 优税测评产品采集数据菜单ID*/
    public static String PRODUCT_MENU_ROOT_ID_CAIJI = "12000";

    /** 优税测评产品功能权限菜单ID*/
    public static String PRODUCT_MENU_ROOT_ID_GONGNENG = "13000";

    /** 票税管家菜单根ID*/
    public static String SYSTEM_DEFALUT_PRODUCT_ID_PIAOSHUI = "18000";
}
