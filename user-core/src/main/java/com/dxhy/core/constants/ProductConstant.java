package com.dxhy.core.constants;/**
 * Created by thinkpad on 2019-03-08.
 */

/**
 * <AUTHOR>
 * @create 2019-03-08 17:43
 **/
public class ProductConstant {
    /**
     * 初始状态
     */
    public static final String STATUS_0 = "0";
    /**
     * 下架
     */
    public static final String STATUS_1 = "1";
    /**
     * 上架
     */
    public static final String STATUS_2 = "2";
    /**
     * 删除
     */
    public static final String STATUS_3 = "3";

    //套餐消耗 -- redis前缀
    public static final String COMBO_CONSUME_REDIS = "aosp.combo.consume:%s%s";

    //套餐消耗 -- redis 锁
    public static final String COMBO_CONSUME_REDIS_LOCK = "aosp.combo.consume.lock:%s%s";
    //设置redis失效
    public static final int COMBO_CONSUME_EXPRIE_TIME = 86400;

    public static final int ZERO = 0;

    public static final int ONE = 1;

    /**
     *  销项开票产品ID
     */
    public static final String PRODUCTID_XIAOXIANGKAIPIAO="54cc3fa8b64941d38fd75a1722e43be7";

    /**
     *  税控管理菜单REDIS前缀
     */
    public static final String OMP_SKGL_PERFIX = "omp:skgl:";
}
