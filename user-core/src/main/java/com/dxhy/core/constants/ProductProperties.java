package com.dxhy.core.constants;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 16:112019/6/28 0028
 */
@ConfigurationProperties(prefix = "aosp.product")
@Component
@Data
public class ProductProperties {
    /**
     * web产品路径模板，baseurl_sourceid
     */
    private String webProductUrlPattern="%s_%s";

    private String clientProductUrlPattern="%s?appid=%s&sourceid=%s";

    //套餐消耗--交换器
    private String aospComboExchange;

    //套餐消耗-- routingKey
    private String aospComboRoutingKey;

    private String filePath;

    //设置默认版本（由于服务商需求，根据产品渠道设置默认的售卖版本（级次））
    private Integer activitySellDict;
}
