package com.dxhy.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.mycst.TerminalRegistParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class MycstUtil {

    private static final String LOGGER_MSG = "(税航票帮手请求)";
    @Autowired
    private RedisTemplate redisTemplate;
    /**
     * 税航票帮手获取token地址
     */
    @Value("${mycst.getTokenUrl}")
    private String mycstGetTokenUrl;
    /**
     * 税航票帮手终端注册地址
     */
    @Value("${mycst.terminalRegistUrl}")
    private String terminalRegistUrl;
    /**
     * 税航票帮手用户编码
     */
    @Value("${mycst.userCode}")
    private String mycstUserCode;
    /**
     * 税航票帮手数电发票开具地址
     */
    @Value("${mycst.userPwd}")
    private String mycstUserPwd;
    /**
     * 终端注册类型 0试用 1正式
     */
    @Value("${mycst.zclx}")
    private String zclx;

    /**
     * 票帮手 获取token
     * @return
     */
    public String getToken(){
        log.info("{}，获取token入参，url：{}，userCode：{}，userPwd：{}", LOGGER_MSG, mycstGetTokenUrl, mycstUserCode, mycstUserPwd);
        String token = (String) redisTemplate.opsForValue().get(SystemConstants.MYCST_TOKEN_CODE_KEY+mycstUserCode);
        if(StringUtils.isBlank(token)){
            Map<String,String> paramMap = new HashMap<>();
            paramMap.put("UserName",mycstUserCode);
            paramMap.put("Password1",mycstUserPwd);
            //请求参数拼接形式调用票帮手获取token接口
            String result = HttpUtils.doPost(mycstGetTokenUrl, paramMap);
            log.info("请求票帮手获取token接口地址{},返回结果{}",mycstGetTokenUrl,result);
            JSONObject jsonObject = JSONObject.parseObject(result);
            token = jsonObject.getString("ID");
            this.redisTemplate.opsForValue().set(SystemConstants.MYCST_TOKEN_CODE_KEY+mycstUserCode,token,1, TimeUnit.DAYS);
        }
        return token;
    }

    /**
     * 终端注册
     */
    public String terminalRegist(SysDept sysDept){
        TerminalRegistParam param = new TerminalRegistParam();
        param.setToken(getToken());
        param.setQymc(sysDept.getDeptSname());
        param.setQysh(sysDept.getTaxpayerCode());
        param.setSsdq(sysDept.getTaxpayerProvinceCode());
        param.setZdlx("8");
        param.setFhr("");
        param.setKpr("");
        param.setSkr("");
        param.setDzdh(sysDept.getTaxpayerAddress()+"-"+sysDept.getTaxpayerPhone());
        param.setZclx(zclx);
        //转换为&拼接的形式
        String postParam = convertObjectToQueryString(param);
        log.info("请求票帮手终端注册接口地址{},传入参数{}",terminalRegistUrl,postParam);
        String result = HttpUtils.doPost(terminalRegistUrl, postParam);
        log.info("请求票帮手终端注册接口地址{},返回结果{}",terminalRegistUrl,result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        String resultCode = jsonObject.getString("Result");
        String spid = "";
        if("1".equals(resultCode)){
            spid = jsonObject.getString("spid");
        }
        return spid;
    }


    /**
     * 传入对象将字段名和值转换为&拼接的形式
     * @param obj
     * @return
     */
    public String convertObjectToQueryString(Object obj) {
        StringBuilder queryString = new StringBuilder();
        // 获取对象的类类型
        Class<?> clazz = obj.getClass();
        // 获取类中的所有字段
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true); // 设置可访问性
            try {
                // 获取字段名和字段值
                String key = field.getName();
                Object value = field.get(obj);

                // 处理null值和拼接字符串
                if (value != null) {
                    if (queryString.length() > 0) {
                        queryString.append("&");
                    }
                    queryString.append(key).append("=").append(value.toString());
                }

            } catch (IllegalAccessException e) {
                e.printStackTrace(); // 处理异常
            }
        }
        return queryString.toString();
    }

}
