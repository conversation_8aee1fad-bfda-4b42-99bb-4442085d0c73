package com.dxhy.core.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Excel工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class ExcelUtils {

    /**
     * 读取Excel
     *
     * @param inputStream Excel输入流
     * @param clazz 实体类
     * @param listener 监听器
     * @param <T> 泛型
     */
    public static <T> void readExcel(InputStream inputStream, Class<T> clazz, ReadListener<T> listener) {
        EasyExcel.read(inputStream, clazz, listener).sheet().doRead();
    }

    /**
     * 导出Excel
     *
     * @param response HttpServletResponse
     * @param data 数据列表
     * @param fileName 文件名
     * @param sheetName sheet名
     * @param clazz 实体类
     * @param <T> 泛型
     */
    public static <T> void exportExcel(HttpServletResponse response, List<T> data, String fileName, String sheetName, Class<T> clazz) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            
            // 这里需要设置不关闭流
            if (sheetName != null && !sheetName.isEmpty()) {
                EasyExcel.write(response.getOutputStream(), clazz)
                        .autoCloseStream(Boolean.FALSE)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet(sheetName)
                        .doWrite((List<T>) data);
            } else {
                EasyExcel.write(response.getOutputStream(), clazz)
                        .autoCloseStream(Boolean.FALSE)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("Sheet1")
                        .doWrite((List<T>) data);
            }
        } catch (UnsupportedEncodingException e) {
            log.error("导出Excel异常", e);
        } catch (IOException e) {
            log.error("导出Excel IO异常", e);
        }
    }
    
    /**
     * 生成Excel模板
     *
     * @param response HttpServletResponse
     * @param fileName 文件名
     * @param sheetName sheet名
     * @param clazz 实体类
     * @param <T> 泛型
     */
    public static <T> void generateTemplate(HttpServletResponse response, String fileName, String sheetName, Class<T> clazz) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            // 这里URLEncoder.encode可以防止中文乱码
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName + ".xlsx");
            
            // 这里需要设置不关闭流
            List<T> emptyList = new ArrayList<>();
            if (sheetName != null && !sheetName.isEmpty()) {
                EasyExcel.write(response.getOutputStream(), clazz)
                        .autoCloseStream(Boolean.FALSE)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet(sheetName)
                        .doWrite(emptyList);
            } else {
                EasyExcel.write(response.getOutputStream(), clazz)
                        .autoCloseStream(Boolean.FALSE)
                        .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                        .sheet("Sheet1")
                        .doWrite(emptyList);
            }
        } catch (UnsupportedEncodingException e) {
            log.error("生成Excel模板异常", e);
        } catch (IOException e) {
            log.error("生成Excel模板IO异常", e);
        }
    }
} 