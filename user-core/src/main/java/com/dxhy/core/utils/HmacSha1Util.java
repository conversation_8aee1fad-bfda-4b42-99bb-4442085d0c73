package com.dxhy.core.utils;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Map;
import java.util.TreeMap;

/**
 * HmacSHA1工具类
 *
 * <AUTHOR>
 * @date 创建时间: 2020-08-14 15:04
 */
@Slf4j
public class HmacSha1Util {
    
    /**
     * HmacSHA1生成签名值
     *
     * @param data 数据
     * @param key  密钥
     * @return 签名值
     */
    public static byte[] hmacsha1(byte[] data, byte[] key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key, "HmacSHA1");
            Mac mac = Mac.getInstance("HmacSHA1");
            mac.init(signingKey);
            return mac.doFinal(data);
        } catch (NoSuchAlgorithmException e) {
            log.error("NoSuchAlgorithmException", e);
        } catch (InvalidKeyException e) {
            log.error("InvalidKeyException", e);
        } catch (Exception e) {
            log.error("未知异常", e);
        }
        return null;
    }
    
    /**
     * 生成签名
     *
     * @param url       url请求+？
     * @param signMap   map参数
     * @param secretKey 加密key
     * @return 签名
     * @throws Exception
     */
    public static String genSign(String url, TreeMap<String, String> signMap, String secretKey) {
        boolean flag = Boolean.TRUE;
        StringBuilder keyVal = new StringBuilder();
        for (Map.Entry<String, String> entry : signMap.entrySet()) {
            // 值为空的不参与签名
            if (!entry.getValue().isEmpty()) {
                keyVal.append(String.format("%s=%s&", entry.getKey(), entry.getValue()));
            } else {
                flag = Boolean.FALSE;
                break;
            }
        }
        String signStr = StringUtils.EMPTY;
        if (flag) {
            // 2、拼接API密钥
            String content = url + keyVal.substring(0, keyVal.length() - 1);
            log.debug("签名生成的String:{}", content);
            byte[] signBytes = hmacsha1(content.getBytes(StandardCharsets.UTF_8), secretKey.getBytes(StandardCharsets.UTF_8));
            signStr = Base64.encodeBase64URLSafeString(signBytes);
        }
        return signStr;
    }
    
    
    public static int getRandNum(int min, int max) {
        return min + (int) (Math.random() * ((max - min) + 1));
    }


    public static void main(String[] args) {
        String secretKey = "zngbytudhmqd5xrcv95fbmnjvegltavxlli3";
        String content = "POST172.24.18.128:18108/order-api/invoice/api/v6/GenerateQdInvoice?content=jx0GdnXfEJdLSAcDeLGTW774NQxDKHuUQYAcDI1pRaljZWIEu7dKQB2AtHCevTd+Snyvk1uLPUIq8yZWzJ0zGvsZS6cMZGgXRPRoevOrVA2FVNJ9z8Std0tbsMPIIR9nK6/Eg95D8/fXmXwZpaClRiH/kof7k1Zgmv5BnBKrZPQ2DetXty4AkLUkqfxsBtreZfS9jTHIJFHZfJJT5h007xta2q95MKcicRZdURNMpvgoeC9u7F4w/AScLRpHxRVKkWRS4mWX/1I1RBkl9rNCsQsOZBijUwV6s7EgK8/XY3sToGWH1xhDdhNC32MKXsc3Zd6azLMHNjStQBmH7T1aduQH65b5KUFjgku/oDgEFUPYE+sA1jjSiJvFTyT3AgRdLaBjrWzpxkfbhUIE/vcTfQOdIwb4envjA7rXGyWm0mWg/0S0lfqAg30k3e46zvWew6Pif4sv0pj8l9nCRLYYOed1/VnqHsEks7EgK8/XY3v+ZXD5omwbwq8FOZSLnIGrmi0mmggZGMVIxrczXeKe8N0SpGmeMArelZSnV9Ua9LT40AaVru3blmLl7fsQMAYsMVPEkAslpdKzsSArz9djewbEeDFbEVuqU7nPbSDdUFwIUBZ1N23zJu6MQ3m7xYHuD3B9GNnZrZ1UHYNOzW7InqsOjUbieulyD3B9GNnZrZ2mIXah87GaP29P+ZTj/qq+dKKSgyOqeMGEiI9V7qrrgKdXuh9yZjluxxD5pDCm1V7fCpFX+G5fGbov7o0AMj5mbEjKKflsaqyzsSArz9dje0VxXhEqo9pvU7nPbSDdUFybl84lF65jvqhDDy8enmCVD3B9GNnZrZ1SIHQgu9fxm+UVYG7xdTqTIDtiFO7JCAB12lPrP2V3iNa3PfxNpcXgfSTd7jrO9Z6JZKsITT5lygnJfXzHskKmUlPQOrP4ymEqeAzkRdWa4eyNEWea2igeVibL/b7bkDgm364K6hDjdSwTpOms1m5s/NvjJZuhBsZIUH9LSrXGGUTH5DEnjBEAopLCnJh6hGnVVYqAV4IiyF8jkwU7g+MM0Anr7jt/TcR4qIOQkEfMHr94oagzfS2rJmHG3EGaf8AsE6TprNZubKzyAGrvnTy+leLBe6nKT7KzsSArz9dje7l/ehBN7Mr7fSTd7jrO9Z7XX9qMVi8jLQ7snQuYPshVD3B9GNnZrZ3jhAtCOGCqRWG6CR3f/GsnopLCnJh6hGmo/Y5FIuPAysvgZMFLH7pUh20ekptf4kWsqogSrT+MBmNTKs4jhsaGMpwTPHuICLvzBNF/5EPz6+w7AWCyF/xu29ZnHVhFpQMI8+Evpv8eCVqV7UzJL3k6&dataExchangeId=202309111929178635215207&encryptCode=1&secretId=tzkt2c0cis46lo6e87aplemhfa2c3w4s&zipCode=0";
        byte[] signBytes = hmacsha1(content.getBytes(StandardCharsets.UTF_8), secretKey.getBytes(StandardCharsets.UTF_8));
        //正确方式
        String signStr = Base64.encodeBase64URLSafeString(signBytes);
        System.out.println(signStr);

        //错误方式
        String signStr1 = new String(Base64.encodeBase64(signBytes));
        System.out.println(signStr1);
    }
}
