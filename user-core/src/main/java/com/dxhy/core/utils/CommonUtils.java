package com.dxhy.core.utils;

import com.dxhy.core.config.SocialPropertiesConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.web.savedrequest.HttpSessionRequestCache;
import org.springframework.security.web.savedrequest.RequestCache;
import org.springframework.security.web.savedrequest.SavedRequest;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *   通用工具类
 */
@Component
public class CommonUtils {

    private static RequestCache requestCache = new HttpSessionRequestCache();

    private static SocialPropertiesConfig socialPropertiesConfig;

    /**
     * 计算日期相隔天数
     * @param beginTime
     * @param endTime
     * @return
     */
    public static int getRemainDay(String beginTime, String endTime) throws ParseException {
        SimpleDateFormat format= new SimpleDateFormat("yyyy-MM-dd");
        Date btime = format.parse(beginTime);
        Date entime = format.parse(endTime);

        Calendar cal = Calendar.getInstance();
        cal.setTime(btime);
        long time1 = cal.getTimeInMillis();
        cal.setTime(entime);
        long time2 = cal.getTimeInMillis();
        long between_days=(time2-time1)/(1000*3600*24);
        return Integer.parseInt(String.valueOf(between_days));
    }

    @Autowired
    public void init(SocialPropertiesConfig socialPropertiesConfig) {
        CommonUtils.socialPropertiesConfig = socialPropertiesConfig;
    }


    /**
     *   从请求缓存中获取请求参数
     * @param request
     * @param response
     * @return
     */
    public static Map<String, String> getRequestParamsFromCache(HttpServletRequest request, HttpServletResponse response) {
        SavedRequest savedRequest = requestCache.getRequest(request, response);
        Map map = new HashMap<>();
        if(savedRequest == null) {
            map.put("client_id", socialPropertiesConfig.getClientId());
            map.put("redirect_uri", socialPropertiesConfig.getRedirectUri());
        } else {
            String[] redirect_uris = savedRequest.getParameterValues("redirect_uri");
            if(redirect_uris == null || redirect_uris.length == 0) {
                map.put("client_id", socialPropertiesConfig.getClientId());
                map.put("redirect_uri", socialPropertiesConfig.getRedirectUri());
                return map;
            }
            String[] client_ids = savedRequest.getParameterValues("client_id");
            String[] device_id = savedRequest.getParameterValues("device_id");
            String[] pages = savedRequest.getParameterValues("page");
            String[] activityId = savedRequest.getParameterValues("activityId");
            map.put("redirect_uri", redirect_uris[0]);
            map.put("client_id", client_ids[0]);
            if(device_id != null && device_id.length > 0){
                map.put("device_id", device_id[0]);
            }
            if(pages != null && pages.length >0) {
                map.put("page", pages[0]);
            }
            if(activityId != null && activityId.length >0) {
                map.put("activityId", activityId[0]);
            }
        }
        return map;
    }

    public static void removeRequestFromCache(HttpServletRequest request, HttpServletResponse response) {
        requestCache.removeRequest(request, response);
    }

    public static String getDomain(String str) {
        URL url = null;
        try {
            url = new URL(str);
            return url.getHost();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        return null;
    }
}
