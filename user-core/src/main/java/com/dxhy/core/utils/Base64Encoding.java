package com.dxhy.core.utils;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @Description ：base64加密工具类
 * @date 创建时间: 2022-06-29 09:42
 */
public class Base64Encoding {
    
    /**
     * BASE64加密
     * String==>String
     *
     * @param key
     * @return
     */
    public static String encode(String key) {
        
        
        return Base64.encodeBase64String(key.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * @param @param  str
     * @param @return
     * @return byte[]
     * @throws
     * @Title : decode(base64解密)
     * @Description ：base64解密
     */
    public static byte[] decode(byte[] key) {
        return Base64.decodeBase64(key);
    }
    
    /**
     * BASE64解密
     * String==>byte
     *
     * @param key
     * @return
     */
    public static byte[] decode(String key) {
    
        return Base64.decodeBase64(key);
    }
    
    /**
     * BASE64加密
     * byte==>String
     *
     * @param key
     * @return
     */
    public static String encodeToString(byte[] key) {
        return Base64.encodeBase64String(key);
    }
    
    /**
     * BASE64解密
     * String==>byte
     *
     * @param key
     * @return
     */
    public static String decodeToString(String key) {
        if (StringUtils.isBlank(key)) {
            key = "";
        }
        return new String(decode(key), StandardCharsets.UTF_8);
    }
}
