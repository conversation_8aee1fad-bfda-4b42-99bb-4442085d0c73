package com.dxhy.core.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * @author: z<PERSON><PERSON>jing
 * @Date: 2022/5/5 15:08
 * @Version 1.0
 */
@Component
public class RedisUtil {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    public String get(final String key){
        return redisTemplate.opsForValue().get(key);
    }

    public void set(final String key, String value){
        redisTemplate.opsForValue().set(key, value);
    }

    public void set(final String key, String value, int seconds){
        redisTemplate.opsForValue().set(key, value, seconds, TimeUnit.SECONDS);
    }

    public Boolean delete(final String key){
        return redisTemplate.delete(key);
    }

    public Boolean expire(final String key, int seconds){
        return redisTemplate.expire(key, seconds, TimeUnit.SECONDS);
    }

    public Boolean hasKey(final String key){
        return redisTemplate.hasKey(key);
    }

    public Long increment(final String key){
        return redisTemplate.opsForValue().increment(key);
    }


}
