package com.dxhy.core.utils;

import cn.hutool.core.codec.Base64;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysUser;

import java.util.Arrays;

/**
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:25
 * @Version 1.0
 */
public class TestUtil {

    public static void main(String[] args){
        tbqy();
        tbyh();
    }
    /**
     * 测试方法
     * @param
     * @return void
     * <AUTHOR>
     **/
    public static void tbyh(){
        SysUser user = new SysUser();
        user.setUsername("16536278936");
        user.setDeptId("5787dc50-b22d-4e62-aac6-645367321ttdwa");
        user.setNickname("16536278936");
        user.setPhone("16536278936");
        user.setDeptList(Arrays.asList("5787dc50-b22d-4e62-aac6-645367321ttdwa"));
        user.setEmail("<EMAIL>");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(user));
        System.out.println("用户："+encode);
    }

    /**
     * 同步企业信息
     */
    private static void tbqy(){
        SysDept bo = new SysDept();
        bo.setDeptId("5787dc50-b22d-4e62-aac6-645367321ttdwa");
        bo.setName("saas测试4");
        bo.setDeptSname("saas测试4");
        bo.setTaxpayerCode("4401122501107654213ggdya");
        bo.setTaxpayerProvinceCode("1100");
        bo.setTaxpayerType("0");
        String encode = Base64.encode(JsonUtils.getInstance().toJsonString(bo));
        System.out.println("机构:"+encode);
    }

}
