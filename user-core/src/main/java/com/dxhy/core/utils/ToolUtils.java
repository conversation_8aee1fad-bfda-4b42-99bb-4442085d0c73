package com.dxhy.core.utils;

import java.util.Random;

/**
 *
 * <AUTHOR>
 * @date 2022-08-23
 */
public class ToolUtils {
    /**
     * 随机密码
     * @return
     */
    public static String generatePassWord() {

        Random random = new Random();

        StringBuffer valSb = new StringBuffer();

        String charStr = "abcdefghijklmnopqrstuvwxyz";

        String numStr = "0123456789";

        int charLength = charStr.length();

        int numStrLength = numStr.length();

        for (int i = 0; i < 3; i++) {

            int index = random.nextInt(numStrLength);

            valSb.append(numStr.charAt(index));

        }

        for (int i = 0; i < 3; i++) {

            int index = random.nextInt(charLength);

            valSb.append(charStr.charAt(index));

        }

        return valSb.toString();

    }
}
