package com.dxhy.core.utils;

import java.text.DecimalFormat;

/**
 * <AUTHOR>
 * @Description ：生成角色编码
 * @date ：2020/4/8
 */
public class GenerateRoleCodeUtil {



    public static String generateNum(int digit, String currentStr){

        char[] alphatableb = { 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I',
                'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U',
                'V', 'W', 'X', 'Y', 'Z' };
        //流水序号，默认4位
        String formatStr1 = "0000";
        int maxNum = 9999;
        StringBuffer tmpBuff = new StringBuffer();
        StringBuffer tmpBuff1 = new StringBuffer();
        for(int i=1;i<digit;i++){
            tmpBuff.append(0);
            tmpBuff1.append(9);
        }
        formatStr1 = tmpBuff.toString()+"0";
        maxNum = Integer.parseInt(tmpBuff1.append(9).toString());

        String initStr = "";
        if(currentStr==null){
            initStr = maxNum+"";
        }else{
            initStr = currentStr;
        }

        //找出字符串最后一位字母
        int sign=0;
        boolean isfind = false;
        for(int i=initStr.length()-1;i>=0;i--){
            if(initStr.charAt(i)>='A' && initStr.charAt(i)<='Z'){
                sign=i;
                isfind = true;
                break;
            }
        }
        //分隔字母和数字
        String letterStr="";
        String numStr="";
        if(isfind){
            letterStr=initStr.substring(0, sign+1);
            numStr=initStr.substring(sign+1);
        }else{
            numStr=initStr;
        }

        //如果数字部分不全为9，则直接加一，返回
        boolean isAllNine=false;
        for(int i =0;i<numStr.length();i++){
            if(numStr.charAt(i)=='9'){
                isAllNine = true;
            }else{
                isAllNine = false;
                break;
            }
        }
        if(!isAllNine && !"".equals(numStr)){
            int nextNum = Integer.parseInt(numStr)+1;
            return letterStr+addNumPrefix(nextNum,numStr.length());
        }else{
            //数字部分全为9或者数字部分为空，需要分情况分析
            String nextNum = "";
            if("".equals(letterStr)){
                //字母部分为空
                String nineStr = numStr.substring(1);
                nextNum = "A"+revertNineToFirst(nineStr);
            }else{
                //字母部分不为空
                //判断字母部分的最后一位字母是否有下一位，比如A，则有下一位B，Z则没有。
                char lastLetter =  letterStr.charAt(letterStr.length()-1);
                int pos = -1;
                //顺序查找
                for(int i=0;i<alphatableb.length;i++){
                    if(alphatableb[i]==lastLetter){
                        pos = i;
                        break;
                    }
                }
                if(pos==-1){
                    System.out.println("查找字母出错");
                    return "error";
                }
                if('Z'==alphatableb[pos]){
                    //最后一位字母为'Z'
                    if(!"".equals(numStr)){
                        //Z999
                        nextNum = letterStr.substring(0, letterStr.length())
                                + 'A' + revertNineToFirst(numStr.substring(1));
                    }else{
                        //ZZZZ
                        System.out.println("商户序号已达到最大值，请联系管理员");
                        return "error";
                    }
                }else{
                    //最后一位字母不为'Z'
                    if(!"".equals(numStr)){
                        //ZA99
                        nextNum = letterStr.substring(0, letterStr.length()-1)
                                + alphatableb[pos+1] + revertNineToFirst(numStr);
                    }else{
                        //ZZZA
                        nextNum = letterStr.substring(0, letterStr.length()-1)
                                + alphatableb[pos+1];
                    }
                }

            }
            return nextNum;

        }

    }

    /**
     * 补充数字前缀
     * @param nextNum
     * @param numLen
     * @return
     */
    private static String addNumPrefix(int nextNum, int numLen) {
        StringBuffer tmpBuff = new StringBuffer();
        for(int i=0;i<numLen;i++){
            tmpBuff.append(0);
        }
        DecimalFormat format = new DecimalFormat(tmpBuff.toString());
        return format.format(nextNum);
    }

    /**
     * 转化999为001
     * @param nineStr
     * @return
     */
    public static String revertNineToFirst(String nineStr){
        if("".equals(nineStr)) {
            return nineStr;
        }
        String zeroStr = nineStr.replace('9', '0');
        DecimalFormat format = new DecimalFormat(zeroStr);
        int firstNum = Integer.parseInt(zeroStr)+1;
        return format.format(firstNum);
    }


}
