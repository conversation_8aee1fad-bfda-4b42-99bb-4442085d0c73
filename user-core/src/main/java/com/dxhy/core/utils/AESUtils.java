package com.dxhy.core.utils;

import com.xiaoleilu.hutool.codec.Base64;
import com.xiaoleilu.hutool.util.CharsetUtil;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Map;

/**
 * <AUTHOR>
 * @class AESUtils
 * @date 15:35 2019/3/4 0004
 */
public class AESUtils {
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/CBC/NOPadding";
    private static final String KEY_ALGORITHM = "AES";

    public static String decryptAES(String data, String pass) throws Exception {
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        SecretKeySpec keyspec = new SecretKeySpec(pass.getBytes(), KEY_ALGORITHM);
        IvParameterSpec ivspec = new IvParameterSpec(pass.getBytes());
        cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);
        byte[] result = cipher.doFinal(Base64.decode(data.getBytes(CharsetUtil.UTF_8)));
        return new String(result, CharsetUtil.UTF_8).trim();
    }

    public static String encryptAES(String data, String pass) throws Exception {

        SecretKeySpec secretKeySpec = new SecretKeySpec(pass.getBytes(), KEY_ALGORITHM);
        IvParameterSpec ivParameterSpec = new IvParameterSpec(pass.getBytes());
        // 指定加密的算法、工作模式和填充方式
        Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
        int blockSize = cipher.getBlockSize();
        byte[] dataBytes = data.getBytes();
        int plaintextLength = dataBytes.length;
        if (plaintextLength % blockSize != 0) {
            plaintextLength = plaintextLength + (blockSize - (plaintextLength % blockSize));
        }
        byte[] plaintext = new byte[plaintextLength];
        System.arraycopy(dataBytes, 0, plaintext, 0, dataBytes.length);
        cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
        byte[] encryptedBytes = cipher.doFinal(plaintext);
        // 同样对加密后数据进行 base64 编码
        String base64 = Base64.encode(encryptedBytes);
        //进行url编码 去掉= ? &
        return base64;
    }

    protected static String generateKey(Map<String, String> values) {
        MessageDigest digest;
        try {
            digest = MessageDigest.getInstance("MD5");
            byte[] bytes = digest.digest(values.toString().getBytes("UTF-8"));
            return String.format("%032x", new BigInteger(1, bytes));
        } catch (NoSuchAlgorithmException nsae) {
            throw new IllegalStateException("MD5 algorithm not available.  Fatal (should be in the JDK).", nsae);
        } catch (UnsupportedEncodingException uee) {
            throw new IllegalStateException("UTF-8 encoding not available.  Fatal (should be in the JDK).", uee);
        }
    }

    public static void main(String[] args) {
        try {
//            String content="eyJ1c2VybmFtZSI6IjEzNDMwNTIyNzg4IiwidGVuYW50SWQiOiIxNTgyMjYyMzcxMjIyMjY1ODU2IiwiZW1haWwiOiJoZWxsb0AxNjkuY29tIiwicGhvbmUiOiIxMzQzMDUyMjc4OCIsInRvcE9yZyI6eyJuYW1lIjoi5rex5Zyz5YmN5rW36IKh5Lu95pyJ6ZmQ5YWs5Y+4IiwidGF4cGF5ZXJDb2RlIjoiUURYNTU1U1NERkY1OTkyMiIsInRheHBheWVyQWRkcmVzcyI6IuWuneWuieWMuuWJjea1t+S4lui0uOWkp+WOpjk5OTnlj7ciLCJ0YXhwYXllclBob25lIjoiMTM0MzA1MjI3ODgiLCJ0YXhwYXllckJhbmsiOiLmi5vllYbpk7booYzliY3mtbfmlK/ooYwiLCJ0YXhwYXllckFjY291bnQiOiI2MTIyMjI4Mjg4ODg5OTk5IiwidGF4cGF5ZXJQcm92aW5jZSI6bnVsbCwidGF4cGF5ZXJDaXR5IjpudWxsLCJ0YXhwYXllckNvdW50eSI6bnVsbCwidGF4cGF5ZXJUeXBlIjoiMCIsInRheHBheWVySW5kdXN0cnkiOiIwMyIsImFjY291bnRpbmdQcmluY2lwbGVDb2RlIjoiMSIsImNvbnRhY3RQaG9uZSI6IiIsImNvbnRhY3ROYW1lIjpudWxsLCJjb250YWN0RW1haWwiOiJoZWxsb0AxNjkuY29tIiwic2tzYmJtIjoiMDA2IiwiYWNlSWQiOm51bGwsImFjZUtleSI6bnVsbH0sInByb2R1Y3RMaXN0IjpbIjE1ODAwMzk0ODQyNjAxOTYzNTYiXX0=";
//            String key= DigestUtils.md5Hex("4b530cd24ba411edbb4b52540079e9e2" +content+"5517745f4ba411edbb4b52540079e9e2");
//            System.out.println("*****************:"+key);
//            Map md5Map = new LinkedHashMap<String, String>();
//            md5Map.put("username", "13116659883");
//            md5Map.put("client_id","pig");
//            md5Map.put("scope","server");
//            String md5 = generateKey(md5Map);
//            System.out.println(md5);
//
//            String entNmae = URLDecoder.decode("%e5%a4%a7%e8%b1%a1%e6%85%a7%e4%ba%91%e4%bf%a1%e6%81%af%e6%8a%80%e6%9c%af%e6%9c%89%e9%99%90%e5%85%ac%e5%8f%b8","UTF-8");
//
//            System.out.println(entNmae);
//
//
//            String enc = encryptAES("username=13851317185&sourceId=&entName=%e5%a4%a7%e8%b1%a1%e6%85%a7%e4%ba%91%e4%bf%a1%e6%81%af%e6%8a%80%e6%9c%af%e6%9c%89%e9%99%90%e5%85%ac%e5%8f%b8&redirectURI=http%3a%2f%2ftest.5ifapiao.com%3a8888%2fconsult-0%2f%23%2fexpert&taxNo=91110108MA004CPN95&dxhyhxytCode=91110108MA004CPN95", "1234567887654321");
//            System.out.println(enc);
//
//            String str = decryptAES("dVDcRJJigUXhjkWz9CLvCUM3fntwZ1//w/yw+PeC3rPmW9nPCU7kiCZFw95l0wbk1et23gJIn2Kp0FSGxKfszSQI4Rs7T72SzmPKEO/H0b4DrCs4Fh9471dfdj65IQvmLezoxZXfdBkl/4N4zNiTsmvRUK9tkYSws1pchmCrH3izzPCuajyG9Sbk1WCDrwKhxkknQBECl58FJ0Zy59J84L2xQh+Yz4G1WYZfiE5taXGahefE77Sg7xECmp7ORWoZjBhFacE7kapUMNPdBIICTwPBEKIyhTyHOgddeUQohscf5RzYdlCep+NxDj/H1R+JKemy40MqOPBwiHecWobVd1yGuHPz2MP97WudTDde2fI","1234567887654321");
//            System.out.println(str);


            String enc = encryptAES("88888888", "1234567887654321");
            System.out.println("前端加密："+enc);
            String password = decryptAES(enc, "1234567887654321");
            System.out.println("后端解密："+password);
//            ezlmnkEWVQSAXHa7irpqhA==
            System.out.println("入库："+ encryptParam(password));




        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String encryptParam(String param) {
        return new BCryptPasswordEncoder().encode(param);
    }
}
