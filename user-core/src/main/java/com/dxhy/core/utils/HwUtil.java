package com.dxhy.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.NacosParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;

/**
 * 华为通用工具类校验
 * @author: zhangjinjing
 * @Date: 2022/6/23 16:38
 * @Version 1.0
 */
@Component
@Slf4j
public class HwUtil {

    @Autowired
    private NacosParam nacosParam;

    /**
     * get请求校验authToken
     * @param
     * @return boolean
     * <AUTHOR>
     **/
    public boolean checkAuthTokenForGet(HttpServletRequest request){
        Map<String, String[]> paramsMap = request.getParameterMap();
        String timeStamp = null;
        String authToken = null;
        String[] timeStampArray = paramsMap.get("timeStamp");
        if (null != timeStampArray && timeStampArray.length > 0) {
            timeStamp = timeStampArray[0];
        }
        String[] authTokenArray = paramsMap.get("authToken");
        if (null != authTokenArray && authTokenArray.length > 0) {
            authToken = authTokenArray[0];
        }
        //对剩下的参数进行排序，拼接成加密内容
        Map<String, String[]> sortedMap = new TreeMap<String, String[]>();
        sortedMap.putAll(paramsMap);
        sortedMap.remove("authToken");
        StringBuffer strBuffer = new StringBuffer();
        Set<String> keySet = sortedMap.keySet();
        Iterator<String> iter = keySet.iterator();
        while(iter.hasNext()) {
            String key = iter.next();
            String value = sortedMap.get(key)[0];
            strBuffer.append("&").append(key).append("=").append(value);
        }
        //修正消息体,去除第一个参数前面的&
        String reqParams = strBuffer.toString().substring(1);
        String key = nacosParam.accessKey + timeStamp;
        String signature = null;
        try{
            signature = generateResponseBodySignature(key, reqParams);
        }catch(Exception e) {

        }
        return authToken.equals(signature);
    }

    /**
     * post请求校验authToken
     * @param
     * @return boolean
     * <AUTHOR>
     **/
    public boolean checkAuthTokenForPost(@RequestBody RequestBody requestBody, HttpServletRequest request){
        //解析出url内容
        Map<String, String> paramsMap = new HashMap<>();
        try{
            paramsMap = BeanUtils.describe(requestBody);
        }
        catch(Exception e){
        }
        String timeStamp = paramsMap.get("timeStamp");
        String authToken = request.getHeader("authToken");
        //对剩下的参数进行排序，拼接成加密内容
        Map<String, String> sortedMap = new TreeMap<String, String>();
        sortedMap.putAll(paramsMap);
        StringBuffer strBuffer = new StringBuffer();
        Set<String> keySet = sortedMap.keySet();
        Iterator<String> iter = keySet.iterator();
        while(iter.hasNext()) {
            String key = iter.next();
            String value = sortedMap.get(key);
            if (StringUtils.isEmpty(value)) {
                continue;
            }
        }
        //修正消息体,去除第一个参数前面的&
        String reqParams = strBuffer.toString().substring(1);
        String key = nacosParam.accessKey + timeStamp;
        String signature = null;
        try{
            signature = generateResponseBodySignature(key, reqParams);
        }
        catch(Exception e){
        }
        return authToken.equals(signature);
    }

    /**
     * 对响应的body进行签名
     * @param
     * @return void
     * <AUTHOR>
     **/
    public void signForResp(HttpServletResponse response, String json){
        String signature = "";
        try{
            signature = generateResponseBodySignature(nacosParam.accessKey, json);
        }catch (Exception e){

        }
        String headerStr = "sign_type=\"HMAC-SHA256\", signature= \"" + signature + "\"";
        response.addHeader("Body-Sign", headerStr);
    }

    /**
     * 生成http响应消息体签名示例
     * @param
     * @return java.lang.String
     * <AUTHOR>
     **/
    public static String generateResponseBodySignature(String key, String body) throws Exception {
        return base_64(hmacSHA256(key, body));
    }

    /**
     * hamcSHA256加密算法
     * @param
     * @return byte[]
     * <AUTHOR>
     **/
    public static byte[] hmacSHA256(String macKey, String macData) throws Exception {
        SecretKeySpec secret = new SecretKeySpec(macKey.getBytes(), "HmacSHA256");
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(secret);
        byte[] doFinal = mac.doFinal(macData.getBytes("UTF-8"));
        return doFinal;
    }

    /**
     * 字节数组转字符串
     * @param
     * @return java.lang.String
     * <AUTHOR>
     **/
    public static String base_64(byte[] bytes){
        return new String(Base64.encodeBase64(bytes));
    }

    /**
     *
     * @param jsonObject
     * @param request
     * @return
     * <AUTHOR>
     * @date 2023-04-25
     */
    public Boolean checkAuthTokenForPost2(JSONObject jsonObject, HttpServletRequest request) {
        //解析出url内容
        Map<String, Object> paramsMap = new HashMap<>();
        try{
            paramsMap = JSON.parseObject(jsonObject.toString(),HashMap.class);
            String timeStamp = (String) paramsMap.get("timeStamp");
            String authToken = request.getHeader("authToken");
            //对剩下的参数进行排序，拼接成加密内容
            Map<String, Object> sortedMap = new TreeMap<String, Object>();
            sortedMap.putAll(paramsMap);
            StringBuffer strBuffer = new StringBuffer();
            Set<String> keySet = sortedMap.keySet();
            Iterator<String> iter = keySet.iterator();
            while(iter.hasNext()) {
                String key = iter.next();
//            log.info("**************key:"+key);
                String value = sortedMap.get(key)+"";
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                strBuffer.append("&").append(key).append("=").append(value);
            }
            //修正消息体,去除第一个参数前面的&
            String reqParams = strBuffer.toString().substring(1);
            log.info(reqParams);
            String key = nacosParam.accessKey + timeStamp;
            String signature = null;
            try{
                signature = generateResponseBodySignature(key, reqParams);
                log.info("【鉴权】华为authToken:{},本地生成令牌：{}",authToken,signature);
            }
            catch(Exception e){
            }
            return authToken.equals(signature);
        }
        catch(Exception e){
            e.printStackTrace();
            return false;
        }

    }

    public JSONObject dualUrlCode(JSONObject jsonObject) {
        JSONObject json =  new JSONObject();
        //遍历key和value
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            //log.info("这条JSON的Key是："+entry.getKey()+"这条JSON的Value是："+ entry.getValue());
            try {
                String value = URLDecoder.decode(entry.getValue().toString(),"UTF-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            json.put(entry.getKey(),entry.getValue());
        }
        return json;
    }
}
