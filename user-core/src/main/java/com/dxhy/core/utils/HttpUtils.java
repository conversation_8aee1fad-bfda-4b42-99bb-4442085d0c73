package com.dxhy.core.utils;

import cn.hutool.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
@Slf4j
public class HttpUtils {
    private static final String LOGGER_MSG = "(请求http访问)";
    /**
     * post请求，传map参数
     *
     * @param url
     * @param paramMap
     * @return
     * @throws IOException
     */
    public static String doPost(String url, Map<String, ?> paramMap) {
        Map<String, Object> requestMap = new HashMap<>(paramMap);
        long startTime = System.currentTimeMillis();
        log.debug("{}以Map调用post请求url:{}",LOGGER_MSG,url);
        String body = HttpRequest.post(url).form(requestMap).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Map调用post请求url:{},耗时:{}", LOGGER_MSG, url, endTime - startTime);
        return body;
    }

    /**
     * post请求，传字符串参数
     * @param url
     * @param request
     * @return
     */
    public static String doPost(String url, String request) {
        long startTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},参数:{}", LOGGER_MSG, url,request);
        String body = HttpRequest.post(url).body(request).timeout(600000).execute().body();
        long endTime = System.currentTimeMillis();
        log.debug("{}以Json字符串调用post请求url:{},耗时:{},返回参数:{}", LOGGER_MSG, url, endTime - startTime,body);
        return body;
    }
}
