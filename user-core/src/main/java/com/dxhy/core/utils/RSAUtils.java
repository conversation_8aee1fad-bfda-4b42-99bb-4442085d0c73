package com.dxhy.core.utils;


import org.apache.commons.codec.binary.Base64;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.spec.OAEPParameterSpec;
import javax.crypto.spec.PSource;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.interfaces.RSAPrivateKey;
import java.security.interfaces.RSAPublicKey;
import java.security.spec.MGF1ParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * RSA加密解密
 *
 * <AUTHOR>
 **/
public class RSAUtils {
    // Rsa 私钥 ：秘钥长度位数 、格式
    public static String privateKey= "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2KRpqMfleuyL5J2NVwFKbUV9AZf5Cxeei0VxMlNSb6qmPRdsVGlALBCKiW27xIHXbEoqjzmI7pIuEGgNkbgOz470fPlFqyDzSsXW66FqW6JxLXkzpSSUy6Z0v52Q3RLOg47JGGHkKEk7VSLKt2ZJKnnZqqzHiQEpEWD+cn8GBvAgMBAAECgYBqvq5GqesZnKEHsfVBN08aKaqO011pctpSeQY1DRZjLna5oqT+M2J2Lpqev99eqUqWseVdu1rm2VvAWXZFT10KoIYvtxM8ZLCjIbNSlbmLLK8sDnzXy+sy6JBBGQncf+d94DPV5dFfH3QXQdAgDfGaPnjBsSR3VXhnhVLKpCwGwQJBANyyAf8S83HmlnHDbTWnBOWSwu+kXiEBhnuwJ7fH0iJAlV+GmNtGlnhR0xDpAaZdEJgE7kjLWk2+OBsP4OOuPCECQQDb3GOqRIz9YsN8lJR7whyRD+f3Pc91rtnlcPkCngXXRtb0Nv5H7Tcy3I0aRIDllPAyYVuZpo8LpCAQ01C+04qPAkBKiTwvX8EkuNIavfwGYNBAkN6RfRvlXdSDtazUXwJTWyiXyKebdy2emVQFpAxQmaHfFds8bqGjHBlq2mQDwXbBAkEAre8p7b7zp1Xl/33wBgRn4x8pTUDaCmj8uvZoGPj49/lz/povCqoQ/CzdeEVvj7EHYWQCOok5K2V5dLYob/8c4wJAEwcehdVEXYmQVf+3GvQ8YtjdCu8EfcXaTY4Sb/MFwqhyaBXXXDXAPFgijnNd40Z+GWnGQDwflPdRDgDoci93sw==";
    //公钥
    public static String publicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9ikaajH5Xrsi+SdjVcBSm1FfQGX+QsXnotFcTJTUm+qpj0XbFRpQCwQioltu8SB12xKKo85iO6SLhBoDZG4Ds+O9Hz5Rasg80rF1uuhaluicS15M6UklMumdL+dkN0SzoOOyRhh5ChJO1UiyrdmSSp52aqsx4kBKRFg/nJ/BgbwIDAQAB";

    /**
     * 私钥解密
     *
     * @param text 待解密的文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String text) throws Exception
    {
        return decryptByPrivateKey(privateKey, text);
    }

    /**
     * 公钥解密
     *
     * @param publicKeyString 公钥
     * @param text 待解密的信息
     * @return 解密后的文本
     */
    public static String decryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 私钥加密
     *
     * @param privateKeyString 私钥
     * @param text 待加密的信息
     * @return 加密后的文本
     */
    public static String encryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 私钥解密
     *
     * @param privateKeyString 私钥
     * @param text 待解密文本
     * @return 解密后的文本
     */
    public static String decryptByPrivateKey(String privateKeyString, String text) throws Exception
    {
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec5 = new PKCS8EncodedKeySpec(Base64.decodeBase64(privateKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privateKey = keyFactory.generatePrivate(pkcs8EncodedKeySpec5);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        byte[] result = cipher.doFinal(Base64.decodeBase64(text));
        return new String(result);
    }

    /**
     * 公钥加密
     *
     * @param publicKeyString 公钥
     * @param text 待加密的文本
     * @return 加密后的文本
     */
    public static String encryptByPublicKey(String publicKeyString, String text) throws Exception
    {
        X509EncodedKeySpec x509EncodedKeySpec2 = new X509EncodedKeySpec(Base64.decodeBase64(publicKeyString));
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec2);
        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);
        byte[] result = cipher.doFinal(text.getBytes());
        return Base64.encodeBase64String(result);
    }

    /**
     * 构建RSA密钥对
     *
     * @return 生成后的公私钥信息
     */
    public static RsaKeyPair generateKeyPair() throws NoSuchAlgorithmException
    {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
        keyPairGenerator.initialize(1024);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();
        RSAPublicKey rsaPublicKey = (RSAPublicKey) keyPair.getPublic();
        RSAPrivateKey rsaPrivateKey = (RSAPrivateKey) keyPair.getPrivate();
        String publicKeyString = Base64.encodeBase64String(rsaPublicKey.getEncoded());
        String privateKeyString = Base64.encodeBase64String(rsaPrivateKey.getEncoded());
        return new RsaKeyPair(publicKeyString, privateKeyString);
    }

    /**
     * RSA密钥对对象
     */
    public static class RsaKeyPair
    {
        private final String publicKey;
        private final String privateKey;

        public RsaKeyPair(String publicKey, String privateKey)
        {
            this.publicKey = publicKey;
            this.privateKey = privateKey;
        }

        public String getPublicKey()
        {
            return publicKey;
        }

        public String getPrivateKey()
        {
            return privateKey;
        }
    }


    /**
     * 只需调用一次 生成/打印新的公钥私钥  并测试是否可用
     * 控制台打印结果，解密成功 则将打印的公钥私钥重新赋值给工具类的 privateKey 、 publicKey
     * @throws NoSuchAlgorithmException
     */
    public static void printNewPubKeypriKey() {
        //调用 RsaUtils.generateKeyPair() 生成RSA公钥秘钥
        String tmpPriKey = "";//私钥
        String tmpPubKey = "";//公钥
        try{
            RSAUtils.RsaKeyPair rsaKeyPair = RSAUtils.generateKeyPair();
            tmpPriKey = rsaKeyPair.getPrivateKey();
            tmpPubKey = rsaKeyPair.getPublicKey();
            System.out.println("私钥：" + tmpPriKey);
            System.out.println("公钥：" + tmpPubKey);
        }catch (NoSuchAlgorithmException exception){
            System.out.println("生成秘钥公钥失败");
        }
        //公钥加密、私钥解密
        try {
            String txt = "123456789,13000000001,oUpF8uMuAJO_M2pxb1Q9zNjWeS6oob1Q9zNjWeS6oQ9zNjW,1672914158,1672914158,啊";//注意需要加密的原文长度不要太长 过长的字符串会导致加解密失败
            System.out.println("加密前原文：" + txt);//加密后文本
            String rsaText = RSAUtils.encryptByPublicKey(tmpPubKey, txt);//公钥加密 ！！！
            System.out.println("密文：" + rsaText);//加密后文本
            System.out.println("解密后原文：" + RSAUtils.decryptByPrivateKey(tmpPriKey, rsaText));//私钥解密 ！！！
        }catch (BadPaddingException e){
            System.out.println(e.getStackTrace());
            System.out.println("加解密失败");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用固定的 privateKey 、 publicKey 进行加解密测试
     * 注意 需要加密的原文长度不要太长 过长的字符串会导致加解密失败
     */
    public static void tryEncryptAndDecrypt(){
        //公钥加密、私钥解密
        try {
            String txt = "123456789,13000000001,oUpF8uMuAJO_M2pxb1Q9zNjWeS6oob1Q9zNjWeS6oQ9zNjW,1672914158,1672914158,啊";//注意需要加密的原文长度不要太长 过长的字符串会导致加解密失败
            System.out.println("加密前原文：" + txt);//加密后文本
            String rsaText = RSAUtils.encryptByPublicKey(RSAUtils.publicKey, txt);//RsaUtils.publicKey 公钥加密 ！！！
            System.out.println("密文：" + rsaText);//加密后文本
            System.out.println("解密后原文：" + RSAUtils.decryptByPrivateKey(RSAUtils.privateKey, rsaText));//RsaUtils.privateKey 私钥解密 ！！！
        }catch (BadPaddingException e){
            System.out.println(e.getStackTrace());
            System.out.println("加解密失败");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

   /**
    * 使用填充模式的加密
    * @param data
    * @return
    * <AUTHOR>
    * @date 2023-05-09
    */
    public static String rsaEncrypt(String publick_key, String data) throws Exception {
        Cipher oaepFromInit = Cipher.getInstance("RSA/ECB/OAEPWITHSHA-1ANDMGF1PADDING");
        OAEPParameterSpec oaepParams = new OAEPParameterSpec("SHA1", "MGF1", new MGF1ParameterSpec("SHA-1"), PSource.PSpecified.DEFAULT);
        byte[] publicBytes = Base64.decodeBase64(publick_key);
        X509EncodedKeySpec x509EncodedKeySpec = new X509EncodedKeySpec(publicBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(x509EncodedKeySpec);
        oaepFromInit.init(Cipher.ENCRYPT_MODE, publicKey, oaepParams);
        byte[] bytes = oaepFromInit.doFinal( data.getBytes("UTF-8"));
        return Base64.encodeBase64String(bytes);
    }
    /**
     * 使用填充模式的解密
     * @param sourceBase64RSA
     * @return
     * <AUTHOR>
     * @date 2023-05-09
     */
    public static String rsaDecode(String private_key,String sourceBase64RSA) throws Exception{
        Cipher oaepFromInit = Cipher.getInstance("RSA/ECB/OAEPWITHSHA-1ANDMGF1PADDING");
        //OAEPParameterSpec oaepParams = new OAEPParameterSpec("SHA1", "MGF1", new MGF1ParameterSpec("SHA-1"), PSource.PSpecified.DEFAULT);
        /*
        华为云界面方式：RSA解密方式（填充方式OAEP，掩码函数SHA-256，掩码参数SHA-1）
         */
        OAEPParameterSpec oaepParams = new OAEPParameterSpec("SHA-256", "MGF1", new MGF1ParameterSpec("SHA-1"), PSource.PSpecified.DEFAULT);
        byte[] privateBytes = Base64.decodeBase64(private_key.getBytes());
        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(privateBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PrivateKey privkey = keyFactory.generatePrivate(pkcs8EncodedKeySpec);
        oaepFromInit.init(Cipher.DECRYPT_MODE, privkey, oaepParams);
        byte[] ct = Base64.decodeBase64(sourceBase64RSA);
        byte[] pt = oaepFromInit.doFinal(ct);
        return new String(pt, StandardCharsets.UTF_8);
    }

    public static void main(String[] args) throws Exception {
        String key="MIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggkoAgEAAoICAQC9trXBo1vzgOqSdtNGFTa6f1xuPwuHQS4gHQ4Z8O1J49kkGz9hiFKPiTrfSEtZVvn/qcQhXda4cFCwk+CucvTWoPVhRLZS9vBYeic7fWhHk9l9bVQUgLfcKNVM4WyR8NaEWugwdiU6iZZQKRQfK9ULZ7liMWfW8RvCf/BFD1g/HAzHhxZJKvsVxwCuo0ZbWFvDe804Dvj4NWXu+Cfx5MAe8ABr3CO0VW/9vGWs0fz1acPGRxoj++cIfiJpwp3m4nBQkOOb38lHL9t4UoEUfkK6C7KQhLv8QDSot0loGNOAIhu3s9EEzM+0uJlu0lz2Gu9lsFKraOxTvV1AVl32nidsq5kyPwXQUt9jsOdvGWDIbEWetuskLipB1StHmj0j0G6hQtM658EtQhFzijTOLKH/8QP10/9sFW8OkpvFxtNILfGKksppc/N+em7i99c4Gol24RrXZOfhssBJWBXVB3mJU7T5/Hs5VhQRcbBXmHd1wjJZiwcU2l1bUvgrEoZ7nxzIYEoTxS7wjMRy2xuuN0b390ODU6WaBEGRRkYp05sCByCcP1t2UF64o63Rd+UTBUsuJ4FqLs7mA6oD/Hkj87YLQaLrKuQPSet307DXM/JfhZ/NRRm5IPNovFBz/LjvXSU33JxByeu/r9o3WKlu/ApnNJjG5h4d2bxGvmNEmffknQIDAQABAoICAQCQPLDVx0Dq0tIFh0g8WXahtqFsxIwcSmcqTUziVjXi3tPByuGWYtH6hWh4SHvZMfKi+cy/XZwZ4HLIvbWujIIwCHJngJlqXIsBieX4KfG1sehCn3O0nuSrA3Sgnicwkj5WpNMxvTGy3taknYbJ6EZzBGqDCHMdjXUAnMrthqweV1I0BTXhbUc3GHUhUcv1OSmTZ7XwcBnEhLy6QnwEQln7a5e2acnPmsGp1aosCf9cCwyo3Hg4cZYG8/dwF2J8Hx8rce1LHormj7E6Ougez5wl3SznCbPvJlWjDK9H8NAk9EnEkndRQfKNQWiORGwfRP1MgBFqdzxusmOKvd902Gu5tmSpMAztjoBikLSe+4UUtAyYTQ3/6F1e7+cyzemNW8J5NldjP4Uy2+Ds6aPIJK0uLUS37NnFdKvosf63lRAY5FhPA88iLaXSeUBkxPK2isAVKIreWjTZcbu1naBZKjvxwBzeI4ZKymeLRld/OuFB4mwUEbgmr+qtVi65/mC7xFp4k5yo0IqM9u85lAs7zf9zp06HgwbJYgrKme4J6hAEIWVt4PqUHZyD1rhf+Th8I4GshDZ0NAs+RmohHRbvnINLqDLEJBIbJkH1lE5ZecNuRnDkdHGZUUNAqPug1N6rEsakAYMmAhv55uDMRF4y19YwQMOoiNi3aFMbH88CaB0B9QKCAQEA+/AYthQ18uS5QBGDykihBc/2cestrWYRAqy6LkA8ymTtJCnORu6FFyZcwNy+Rctb2jO8DZpShwT4wWj8hw3H+rDFrFUUryjs99OuOgh2N9whCq2637LlPzoR4JiLDpW2w0z4ZiUJVScJ3wKohXRts/b6LC2A9jF2BfHCYYeodKK+ca+CLS7ZCmW52eBgzTBoXM7D0Em2wu8H40QItJqW/********************************/YlkxQBDt2AtlYH85fzffDMiwLtRK13Ir9gXT8wctuggBuBbPlpPf5Qu9pvU1Dm9mCD522q/DMERVFq5Oa+hr63niWCKemkrwKCAQEAwMXGnNogGplTO08uvda7w11OvY0VGLmXBYBiCwKySwDOG4uUwoY+9gQHYrvf9CDSKZJqz+gQuOGiXzBiQiRDHWqarWheW1/XOrwOjkPr/gcEApnGTt0Z40KGUIJgXWOxC4EVF+dvnGC9NZk46eras3FFOwSWzdc/4W89Pu2KZ38yjldYB3EmsawsCcrKf14TUQ6MYl4kHteGU3k5TdkmKZFMvXKazQbH5hhBLSeEY+MTn4+nLVxGtwYckpGmyzQlmYZQ3gWjQJ3N+lq0GGXGH4EO/EK4DmMnXCL5/PmGSfi/BY1CjthQnDJeB/PwPEbgMaTUVYKw3EjdxoUCVJo2cwKCAQAOHLHVqSYod+VkkFaYgx756187nFfPqbL0OmanZpPBJoweBXV3ha7Q0CZL61GBKVX40AMNpAJRa9bfRpk2m3ADO9mmwJFLCnjTnPjaaVTt5he3uGmKVEQBGaicsw6+/2XgZLRZTGPMcEZeRpDYAOCQPxpeVSIvKDAbPyKLQgUOm3sddR5Ol5wyt+YELUxe0gJWiIJxbV+eZbNEaLAwJaSln9YyTYkaRF73ViLx+bDxInJ2GFvXrNyJMaGDLpCsm321OPZzD6Tk/hMHCl/uA+VzfdLRBlQZavH/mwrR5tqdTVyKWT75JMP7MwbQdNUqEVWA6IGbVs1/ULmKU0GKRXsjAoIBAGpMJIrVVKKWLWkk7pdL01mVGBg5tYpevEISWv3LTNlC5mQWAb+KGym5awEsq/+IIZs+25stUhtAgDKREX17VW7hG83HIVCtdpcyn6wfFACNic6NUOan0OCaDEpUuiKFGFlJooH94nOOfIcTqGiEUnhkW1KB4046BUWf+1u5zHnpj80hqK7h2/5Y/CeiJSs6ithFd2++jZhcK/vXepvMw9hkALbufSHVPWFJupbVlZEm4+k2Y28F1RCTVCoWGq0uYhVYFTt4TF8TJUOJxIk3hZpVphfACpIUZt3Ng5/VUBF2Eo9TJ4JxEXIv693iy3PReUqmo1dLq46st24CZhiUuLUCggEAZT9ADuAaDRAV7t4SI78t59uf54NI34eWXdmZvgOcYsd0yH9z0nLddB2QABFAp5/yOeEYMpwBIw/5xzyPVWAPo1lK49zfZZ0akDw2qpvU2fww6Yi4KyXV3nB4fQTgBPWpv+8aJJa0XF5If195zgANyEqf8DmOgvkMk78JMgrWHzW+bW8+SpB0O3cB7p6y/lRhlY/xzriPMGGQ3Acl/8JwjePnqphhnGxWoctebJfrLrBWUaG1r3lLoXmdgaRRj6NbGwzlqPs1weTGqqEFQkiQq4HacQ+ngHA/hsmN51zk4GaDMk6WPA1ScI7YUXEeZGQtMpHkTt80LoEBRcM1w3mFXg==";
        String secret ="cNTGnMJLuG8luf6qyuPqNm4aVJ4UeX6GrPh2K92aHd1xJsMeE9tTcJJJ8qD5L8cnTq5DrwXzo4KBur98pNXgpaK4J/Vbut3bnpQ7XGs0WtBgTCHsBwwoKjzfrwCGt0vVGV9lbqnh4j54mwdErlepjqEthC2h3kOnIFtNg5EPd3JthsJFZ6izYOAltCN674OvxMnGbmy/h3dR3pWDxrrIBgs47c10Van9XlDTuive35g1n0e5QXu8F5nUQTWtKRuhJgWGN2YD7Kf2btBOFjhnEh4RZC9RnBhHdcYUEwV1BhnGaTcxNdoUq41hGb3eS6SChv/Ajx+etq31fuukIQDR0aCVmaLBgItrAkrzU1t0cfk20M4g7CEC14sNZRRqQhdOCb/UWQAzLrV5YYpD6rvKPY2jPWuwXC6gd781tx4cdjY1fFQH3t3KeoJ11Bp9XJDEk7ipNHziDnhzoaQicSVLKbA8rjop3rU9mhdYML3nsrA0n5ubsDg53OZ9ugfloXRKMDYOZJ5mLCMPszAgVdTe0SUa6z74FOKmM4GlthSgwOZSRMw/JvATTtfn+gSqT3QBOo6jyob4B5lQi5/4Sp+YU29aI9eUoOD4sTRhb3KQICXd8Lz8zIzMIzN1xsKbuVL7ac11iGyQs/d4gxuS6NW6uUiXsj6tAy2LxCs8WlbqWfw=";
        String s = rsaDecode(key,secret);
        System.out.println(s);
    }
}
