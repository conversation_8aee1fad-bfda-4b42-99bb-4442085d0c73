/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.utils;

import com.google.common.collect.Maps;

import java.security.SecureRandom;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/12 15:09
 */
public class KeyGenerator {

	// 定义字符池（数字 + 大写字母 + 小写字母）
	private static final String CHAR_POOL = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";
	private static final SecureRandom random = new SecureRandom();

	public static Map<String, String> getGenerateKey() {
		Map<String, String> map = Maps.newHashMap();
		// 生成私钥（28位）
		String privateKey = generateRandomString(28);
		map.put("secretId", privateKey);
		// 生成公钥（30位）
		String publicKey = generateRandomString(30);
		map.put("secretKey", publicKey);
		return map;
	}

	private static String generateRandomString(int length) {
		StringBuilder sb = new StringBuilder(length);
		for (int i = 0; i < length; i++) {
			// 从字符池中随机选取字符
			int index = random.nextInt(CHAR_POOL.length());
			sb.append(CHAR_POOL.charAt(index));
		}
		return sb.toString();
	}
}