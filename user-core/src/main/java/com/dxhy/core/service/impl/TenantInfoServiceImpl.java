package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.DistributorMapper;
import com.dxhy.core.mapper.TenantInfoMapper;
import com.dxhy.core.pojo.entity.Distributor;
import com.dxhy.core.pojo.hw.TenantInfoEntity;
import com.dxhy.core.pojo.hw.TenantTokenVo;
import com.dxhy.core.service.TenantInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class TenantInfoServiceImpl  extends ServiceImpl<TenantInfoMapper, TenantInfoEntity> implements TenantInfoService {
    @Resource
    private DistributorMapper distributorMapper;
    @Resource
    private TenantInfoMapper tenantInfoMapper;
    @Override
    public Result transfromDisData(TenantInfoEntity tenantInfo) {
        Distributor distr = distributorMapper.selectDistributorBySimpleCode(tenantInfo.getTenantCode());
        Distributor distributor = new Distributor();
        distributor.setCompanyName(tenantInfo.getName());
        String simpleCode = distributorMapper.getNextSimpleCode();
        distributor.setSimpleCode(simpleCode);
        //distributor.setTaxNo();
        //distributor.setBankName(topOrg.getTaxpayerBank());
        //distributor.setBankNo(topOrg.getTaxpayerAccount());
        //distributor.setContactEmail(email);
        distributor.setType("0");
        distributor.setTrialDays("1");
        if (distr == null) {
            distributor.setId(tenantInfo.getTenantId());
            distributor.setIsDistributor(1);
            distributor.setSuperior("0");
            distributor.setLevel("1");
            distributor.setDelFlag(0);
            distributor.setCreateTime(new Date());
            distributor.setModifyTime(new Date());
            //查询渠道信息是否存在 存在-》修改 不存在-》新增
            int num = distributorMapper.insert(distributor);
            if (num>0) log.info("华为同步租户信息到票税系统：渠道信息建立成功 num:{}",num);
            return Result.ok();
        }else{
            int num = distributorMapper.update(distributor, new EntityWrapper<Distributor>().eq("id",distr.getId()));
            if (num>0) log.info("华为同步租户信息到票税系统：渠道信息修改成功,num:{}",num);
            return Result.ok();
        }
    }

    @Override
    public TenantTokenVo getAccessTokenParam(String tenantId) {
        TenantTokenVo tenantTokenVo = tenantInfoMapper.getAccessTokenParam(tenantId);
        return tenantTokenVo;
    }

    @Override
    public TenantInfoEntity getAppKeyAndSecret(String taxNo) {
        return tenantInfoMapper.getAppKeyAndSecret(taxNo);
    }

    @Override
    public TenantInfoEntity getSecretKey(String secretId) {
        return tenantInfoMapper.getSecretKey(secretId);
    }

}
