package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.ComboAccountMapper;
import com.dxhy.core.pojo.entity.ComboAccount;
import com.dxhy.core.service.IComboAccountService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
@Service
public class ComboAccountServiceImpl extends ServiceImpl<ComboAccountMapper, ComboAccount> implements IComboAccountService {
    @Autowired
    private ComboAccountMapper comboAccountMapper;


    @Override
    public List<ComboAccount> selectListByProductId(List<String> productList) {
        return  comboAccountMapper.selectListByProductId(productList);
    }

    @Override
    public List<ComboAccount> queryComboAccount(Map map) {
        return comboAccountMapper.queryComboAccount(map);
    }

}
