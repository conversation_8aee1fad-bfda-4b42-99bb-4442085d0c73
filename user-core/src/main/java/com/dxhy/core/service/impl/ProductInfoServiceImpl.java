package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.ProductInfoMapper;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.Distributor;
import com.dxhy.core.pojo.entity.ProductInfo;
import com.dxhy.core.service.IDictionaryService;
import com.dxhy.core.service.IProductInfoService;
import com.dxhy.core.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-08
 */
@Slf4j
@Service
public class ProductInfoServiceImpl extends ServiceImpl<ProductInfoMapper, ProductInfo> implements IProductInfoService {
    @Autowired
    private IDictionaryService dictionaryService;
    @Autowired
    private ProductService productService;

    @Override
    public Map selectSysDeptByDeptId(String deptId) {
        return this.baseMapper.selectSysDeptByDeptId(deptId);
    }

    public String selectTaxControlByDeptId(String deptId) {
        return this.baseMapper.selectTaxControlByDeptId(deptId,0);
    }

    @Override
    public Dictionary getByFlag(String flag) {
        return dictionaryService.getByFlag(flag);
    }

    /**
     * 根据分销商id列表查询产品列表
     * @param distributorId
     * @param status
     * @return
     */
    @Override
    public List<ProductInfo> selectListByDistributorId(String distributorId, String status, String currentDistributorId,String productName,String productClass) {
        log.info("IProductInfoService.selectListByDistributorId(distributorId={},status={},currentDistributorId={},productName={},productClass={})",
                distributorId,status,currentDistributorId,productName,productClass);
        List<ProductInfo> productInfoList = this.baseMapper.selectListByDistributorId(distributorId,status,currentDistributorId,productName,productClass);
        // // 只有平台和大象自营渠道存在接口类产品，其他渠道均不展示
        Distributor distributor = productService.getDistributorByID(distributorId);
        if(distributor != null && !"0".equals(distributorId) && !distributor.getCompanyName().contains("大象慧云")) {
            // 只有大象自营渠道存在接口类产品，其他渠道均不展示
            if(Optional.ofNullable(productInfoList).isPresent()) {
                productInfoList = productInfoList.stream().filter(productInfo -> !"3".equals(productInfo.getProductClass())).collect(Collectors.toList());
            }
        }
        return productInfoList;
    }
}
