package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.pojo.entity.ComboAccount;
import com.dxhy.core.pojo.entity.ComboDistributor;
import com.dxhy.core.pojo.entity.ComboInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
public interface IComboAccountService extends IService<ComboAccount> {

    List<ComboAccount> selectListByProductId(List<String> productList);

    List<ComboAccount> queryComboAccount(Map map);

    /**
     * <p>
     *  服务类
     * </p>
     *
     * <AUTHOR>
     * @since 2019-04-24
     */
    interface IComboDistributorService extends IService<ComboDistributor> {

        /**
         * 获取产品套餐
         * @param productId
         * @return
         */
        List<ComboInfo> selectCombos(String productId);

        /**
         * 获取产品套餐
         * @param params
         * @return
         */
        List<ComboDistributor> getComboDistributorByproductId(Map<String,Object> params);

        ComboDistributor getComboDistByProductId(String productId, String distributorId,String sellLabelId);

        List<String> getChargeItmesByComboTemplateId(String templateId);

        String getSellLabelIdByComboDistributorId(String comboDistributorId);

        List<Map> getProductIdAndSellLabelIdByDistributorId(String distributorId);
    }
}
