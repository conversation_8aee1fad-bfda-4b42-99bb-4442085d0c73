package com.dxhy.core.service.impl;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.lang.func.VoidFunc0;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.third.*;
import com.dxhy.core.pojo.vo.SysDeptResqVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.BSystemLogicService;
import com.dxhy.core.service.ISysTaxControlService;
import com.dxhy.core.service.SmsEmailService;
import com.dxhy.core.service.SysUserService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.net.URI;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;


@Slf4j
@Service("BSystemLogicService")
public class BSystemLogicServiceImpl implements BSystemLogicService {
    @Resource
    private RestTemplate restTemplate;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private SysDeptMapper sysDeptMapper;

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SyncFailLogMapper syncFailLogMapper;

    @Resource
    private DistributorMapper distributorMapper;

    @Resource
    private SysTaxControlMapper sysTaxControlMapper;

    @Value("${ele-cloud.tokenUrl}")
    private String tokenUrl;

    @Value("${ele-cloud.registerEntUrl}")
    private String registerEntUrl;

    @Value("${ele-cloud.thridSynUrl}")
    private String thridSynUrl;

    @Value("${ele-cloud.thirdAddUserUrl}")
    private String thirdAddUserUrl;

    @Value("${ele-cloud.SKSBUrl}")
    private String SKSBUrl;

    @Value("${ele-cloud.appKey}")
    private String appKey;
    @Value("${ele-cloud.appSecret}")
    private String appSecret;
    @Value("${ele-cloud.entCode}")
    private String entCode;
//    @Value("${ele-cloud.sourceId}")
//    private String sourceId;
    @Value("${ele-cloud.tcId}")
    private String tcId;

    @Value("${system.sign}")
    private String systemSign;
    private VoidFunc0 voidFunc0;

    @Autowired
    private NacosParam nacosParam;

    @Resource
    private SysMenuMapper sysMenuMapper;

    @Resource
    private ISysTaxControlService sysTaxControlService;

    @Resource
    private SysUserService sysUserService;

    private ThreadFactory threadFactory = Executors.defaultThreadFactory();

    @Resource
    private SmsEmailService smsEmailService;

    /**
     * 获取DX侧开放平台token
     *
     * @return
     * <AUTHOR>
     * @date 2022-08-15
     */
    public String getDxToken() {
        JSONObject json = new JSONObject();
        json.put("appKey", appKey);
        json.put("appSecret", appSecret);
        String token = (String) redisTemplate.opsForValue().get(SystemConstants.USER_ELE_OPEN_TOKEN_KEY);
        if (StringUtils.isBlank(token)) {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(tokenUrl, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("请求响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                log.info("请求发送，响应成功");
                String result = (String) response.get("access_token");
                Long expiresIn = response.getLong("expires_in");
                redisTemplate.opsForValue().set(SystemConstants.USER_ELE_OPEN_TOKEN_KEY, result, expiresIn-60, TimeUnit.SECONDS);
                return result;
            } else {
                log.error("请求发送，响应失败");
                return null;
            }
        }
        log.info("获取开放平台token：{}", token);
        return  token;
    }

    /**
     * @param deptId 企业id
     * @param userId
     * @param type   1新增 2编辑
     * @return
     * <AUTHOR>
     * @date 2022-08-15
     */
    @Override
    public CommonRspVo synchronizationDXData(String deptId, Long userId, int type) throws Exception {
        log.info("当前企业ID:{} 用户ID:{} type:{} 已开通了DX销项系统，开始进行数据同步", deptId, userId, type);
        //2.根据deptId查询部门信息 用户信息 部门类型
        SysDept sysDept = sysDeptMapper.selectByDeptId(deptId);
        if (sysDept != null) {
            //校验数据 六要素
            Boolean flagcheck=comcheckData(sysDept);
            if (flagcheck) {
                int deptTpye = sysDept.getDeptType();
                if (deptTpye == 1 || deptTpye == 4 || deptTpye == 6) {//如果部门类型是总公司 部门 独立机构 只同步一个接口
                    //统一中心注册
                    this.synchronizationDXRegisterData(sysDept, type,userId);
                } else if (deptTpye == 2) {//子公司
                    //同步主子企业信息
                    this.synchronizationThirdData(sysDept, type);
                }
            }else{
                log.info("企业信息六要素为空，信息同步失败");
                addSyncDxFailLog(deptId, "", "校验失败", 0, "企业信息六要素为空，信息同步失败");
                smsEmailService.webhookSend("【销项数据同步校验异常】企业ID："+deptId+"，校验失败：企业信息六要素为空，信息同步失败");
                return new CommonRspVo("9999","校验失败:企业信息六要素为空，信息同步失败");
            }

        }else{
            addSyncDxFailLog(deptId, "", "企业不存在", 0, "根据企业ID在数据库中没有找到对应数据");
            smsEmailService.webhookSend("【销项数据同步校验异常】企业ID："+deptId+"，企业不存在：根据企业ID在数据库中没有找到对应数据");
            return new CommonRspVo("9999","企业不存在:根据企业ID在数据库中没有找到对应数据");
        }

       return new CommonRspVo(ResponseCodeEnum.SUCCESS);
    }

    private Boolean comcheckData(SysDept sysDept) {
        if (StringUtils.isBlank(sysDept.getName())) {
            log.info("六要素：企业名称为空");
            return false;
        }
        if (StringUtils.isBlank(sysDept.getTaxpayerCode())) {
            log.info("六要素：税号为空");
            return false;
        }
        if (StringUtils.isBlank(sysDept.getTaxpayerAccount())) {
            log.info("六要素：银行账号为空");
            return false;
        }
        if (StringUtils.isBlank(sysDept.getTaxpayerAddress())) {
            log.info("六要素：开户行地址为空");
            return false;
        }
        if (StringUtils.isBlank(sysDept.getTaxpayerPhone())) {
            log.info("六要素：纳税人手机号为空");
            return false;
        }
        if (StringUtils.isBlank(sysDept.getTaxpayerBank())) {
            log.info("六要素：开户行为空");
            return false;
        }
        return true;
    }

    /**
     * Dx统一注册中心注册
     *
     * @param sysDept
     * @param type
     */
    private void synchronizationDXRegisterData(SysDept sysDept, int type,Long userId) {
        log.info("【DX统一注册中心注册】接收参数 type: {} sysDept:{}", type, sysDept);
        String content = getRegisterParam(sysDept, type,userId);
        if (StringUtils.isNotBlank(content)) {
            JSONObject json = getMainParam(entCode, content);
            CommonRspVo result = sendRegisterAccountPost(json, this.getDxToken());
            if (result.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                log.info("【DX统一注册中心注册】数据同步成功！" + result);
            } else {
                log.error("【DX统一注册中心注册】数据同步失败！入库" + result);

                JSONObject jsonObject = (JSONObject) JSON.toJSON(sysDept);
                addSyncDxFailLog(sysDept.getDeptId(), sysDept.getTaxpayerCode(), jsonObject.toString(), 0, result.toString());
                smsEmailService.webhookSend("【销项统一注册接口同步异常】企业ID："+sysDept.getDeptId()+"，企业税号："+sysDept.getTaxpayerCode()+"，大象调用结果："+result.toString());
            }
        }
    }

    /**
     * DX开放平台公共报文
     *
     * @param entCode
     * @param content
     * @return
     */
    public JSONObject getMainParam(String entCode, String content) {
        //封装层报文
        DxPubMsg dxPubMsg = new DxPubMsg();
        //时间(yyyyMMddHHmmssSSS)+15位的随机数
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String timestamp = format.format(new Date());
//        String dcid =  RandomStringUtils.random(15);
        String uuid = UUID.randomUUID().toString();
        String dcid = uuid.replaceAll("-", "").substring(0, 15);
        dxPubMsg.setDataExchangeId(timestamp + dcid);
        dxPubMsg.setEntCode(entCode);
        dxPubMsg.setContent(content);
        JSONObject json = (JSONObject) JSON.toJSON(dxPubMsg);
        log.info("DX接口公共报文-外层报文封装：{}", json);
        return json;
    }

    /**
     * 统一注册接口 注册数据封装
     *
     * @return
     */
    public String getRegisterParam(SysDept sysDept, int type,Long userId) {
        //根据userId 获取email字段 用于单点的登录
        //封装层报文
        SysUser sysUser = sysUserMapper.selectById(userId);
        if (sysUser == null || "".equals(sysUser)) {
            log.error("未查到用户信息");
            return null;
        }
        //查询税控设备类型
        String sksbbm = sysUserMapper.selectSksbbmByDeptId(sysDept.getDeptId(),0);
        if (StringUtils.isBlank(sksbbm)) {
            sksbbm = "001";//默认金税盘
        }
        DxRegisterPara dxRegisterPara = new DxRegisterPara();
        dxRegisterPara.setYHID(CommonConstant.UID_PREFIX + sysUser.getUsername());
        dxRegisterPara.setQYID(CommonConstant.DEPTId_PREFIX + sysDept.getDeptId());
        dxRegisterPara.setQDID(nacosParam.sourceId);
        dxRegisterPara.setXHFMC(sysDept.getName());
        dxRegisterPara.setXHFSBH(sysDept.getTaxpayerCode());
        dxRegisterPara.setXHFZH(sysDept.getTaxpayerBank());
        dxRegisterPara.setXHFYHZH(sysDept.getTaxpayerAccount());
        dxRegisterPara.setXHFDH(sysDept.getTaxpayerPhone());
        dxRegisterPara.setXHFDZ(sysDept.getTaxpayerAddress());
        dxRegisterPara.setLXRXM(StringUtils.isBlank(sysDept.getContactName())?"":sysDept.getContactName());
        dxRegisterPara.setLXRDH(StringUtils.isBlank(sysDept.getContactPhone())?"":sysDept.getContactPhone());
        dxRegisterPara.setLXRYX(StringUtils.isBlank(sysUser.getEmail())?"":sysUser.getEmail());
        dxRegisterPara.setSKSBDM(sksbbm);
        if (type == 1) {
            dxRegisterPara.setGXBS("0");//新增
        } else {
            dxRegisterPara.setGXBS("1");//更新
        }

        //对象转json
        JSONObject json = (JSONObject) JSON.toJSON(dxRegisterPara);
        log.info("DX统一注册接口内层报文封装：{}", json);
        //base64加密
        String content = Base64.encode(json.toString());
        return content;
    }

    /**
     * DX同步主子企业信息
     *
     * @param sysDept
     * @param type
     */
    private void synchronizationThirdData(SysDept sysDept, int type) throws Exception {
        if (sysDept.getDeptType()!=2) {
            this.addSyncDxFailLog(sysDept.getDeptId(), sysDept.getTaxpayerCode(), "同步主子企业失败，企业不是子企业", 0, "企业类型："+sysDept.getDeptType());
            smsEmailService.webhookSend("【销项主子企业过程同步异常】企业ID："+sysDept.getDeptId()+"，企业税号："+sysDept.getTaxpayerCode()+"。（同步主子企业失败，企业不是子企业）。企业类型："+sysDept.getDeptType());
            return;
        }
        log.info("【DX同步主子企业信息】接收参数 type: {} sysDept:{}", type, sysDept);
        //1.封装数据
        JSONObject json = new JSONObject();
        json.put("deptId", CommonConstant.DEPTId_PREFIX + sysDept.getDeptId());
//        json.put("parentId", CommonConstant.DEPTId_PREFIX + sysDept.getParentId());
        if (sysDept.getParentId().equals("77d78e4b1f6f11edbb4b52540079e9e2") ) {
            json.put("parentId", sysDept.getParentId());
        }else{
            json.put("parentId", CommonConstant.DEPTId_PREFIX+sysDept.getParentId());
        }


        json.put("name", sysDept.getName());
        json.put("taxpayerCode", sysDept.getTaxpayerCode());
        json.put("taxpayerAddress", sysDept.getTaxpayerAddress());
        json.put("taxpayerPhone", sysDept.getTaxpayerPhone());
        json.put("taxpayerBank", sysDept.getTaxpayerBank());
        json.put("taxpayerAccount", sysDept.getTaxpayerAccount());
        json.put("deptType", sysDept.getDeptType());
        json.put("dataSource", "16");//在DX侧配置定义
        JSONObject dept = new JSONObject();
        dept.put("dept", json);

        //2.发起请求
        CommonRspVo result = sendThirdSynDept(dept);
        if (result.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("【DX主子企业信息同步】数据同步成功！{}，{}", sysDept.getDeptId(), result);
            CommonRspVo sxxResult = this.syncXiaoxiang(sysDept, type);
            if (!sxxResult.getCode().equals("0000")) {
                log.error("【同步DX销项税控设备类型】数据同步失败！入库 {},{}", sysDept.getDeptId(), result);
                this.addSyncDxFailLog(sysDept.getDeptId(), sysDept.getTaxpayerCode(), "同步DX销项税控设备类型异常", 0, sxxResult.toString());
                smsEmailService.webhookSend("【销项税控设备类型同步异常】企业ID："+sysDept.getDeptId()+"，企业税号："+sysDept.getTaxpayerCode()+"，（同步DX销项税控设备类型异常）大象调用结果："+sxxResult.toString());
            }
        } else {
            log.error("【DX主子企业信息同步】数据同步失败！入库 {},{}", sysDept.getDeptId(), result);
            //入库
            //失败数据
            JSONObject jsonObject = (JSONObject) JSON.toJSON(sysDept);
            this.addSyncDxFailLog(sysDept.getDeptId(), sysDept.getTaxpayerCode(), jsonObject.toString(), 0, result.toString());
            smsEmailService.webhookSend("【销项主子企业信息同步异常】企业ID："+sysDept.getDeptId()+"，企业税号："+sysDept.getTaxpayerCode()+"，大象调用结果："+result.toString());
        }

    }

    public  void addSyncDxFailLog(String deptId,String taxNo,String content,int flag,String result){
        //入库
        int num = syncFailLogMapper.insertSysnFailLog(deptId, taxNo, content, flag, result);
        if (num > 0) {
            log.info("企业Id：{}，失败入库成功", deptId);
        }
    }

    /**
     * 1.企业注册
     * 发起请求 与响应解析
     *
     * @param json
     * @return
     */
    public CommonRspVo sendRegisterAccountPost(JSONObject json, String token) {

        try {
            String url = registerEntUrl + "access_token=" + token;
            log.info("【DX数据同步-注册接口】{} to {}", json, url);
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("【DX数据同步-注册接口】请求响应：{},{}", response, entity.getStatusCode());
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                JSONObject result = response.getJSONObject("returnStateInfo");
                String returnCode = (String) result.get("returnCode");
                if (returnCode.equals("0000")) {
                    String content = (String) response.get("content");
                    JSONObject resncbw = JSON.parseObject(Base64.decodeStr(content));
                    if (!resncbw.get("code").equals("0000")) {
                        log.info("【DX数据同步-注册接口】大象内层接口返回失败错误码,{}",resncbw);
                        String code = resncbw.get("code").toString();
                        String msg = resncbw.get("message").toString();
                        return new CommonRspVo(code, msg);
                    }
                    return new CommonRspVo(ResponseCodeEnum.SUCCESS);
                } else {
                    String code = returnCode;
                    String msg = result.get("returnMessage").toString();
                    return new CommonRspVo(code, msg);
                }
            } else {
                log.error("【DX数据同步-注册接口】请求发送，响应失败 {}", entity.getStatusCode());
                return CommonRspVo.faild();
            }
        } catch (Exception e) {
//            e.printStackTrace();
            log.error("【DX数据同步-注册接口】请求异常{}", e);
            return CommonRspVo.faild();
        }
    }

    /**
     * 2.添加子企业
     * 发起请求 与响应解析
     *
     * @param json
     * @return
     */
    public CommonRspVo sendThirdSynDept(JSONObject json) {
        log.info("【DX数据同步-主子企业同步-请求】{} to {}", json, thridSynUrl);
        try {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(thridSynUrl, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("请求响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                log.info("请求发送，接口响应成功");
                String msg = (String) response.get("msg");
                String code = (String) response.get("code");
                if (code.equals("0000")) {
                    log.info("请求DX 主子企业信息同步成功");
                    return new CommonRspVo(ResponseCodeEnum.SUCCESS);
                } else {
                    log.info("接口返回失败");
                    return new CommonRspVo(code, msg);
                }
            } else {
                log.error("请求发送，响应失败{}", entity.getStatusCode());
                return CommonRspVo.faild();
            }
        } catch (Exception e) {
            log.error("请求异常{}", e);
//            e.printStackTrace();
            return CommonRspVo.faild();
        }
    }

    /**
     * 3.同步DX销项税控设备类型
     *
     * @param sysDept
     * @param type
     * @return
     * <AUTHOR>
     * @date 2022-08-16
     */
    @Async
    @Override
    public CommonRspVo syncXiaoxiang(SysDept sysDept, int type) {
        if (sysDept != null) {
            //查询税控设备类型
            String sksbbm = sysUserMapper.selectSksbbmByDeptId(sysDept.getDeptId(),0);
            if (StringUtils.isBlank(sksbbm)) {
                sksbbm = "001";//默认金税盘
            }
            RestTemplate restTemplate = new RestTemplate();
            Map<String, String> map = new HashMap<>();
            map.put("XHFSBH", sysDept.getTaxpayerCode());
            map.put("XHFMC", sysDept.getName());
            map.put("SKSBXH","");
            map.put("SKSBDM", sksbbm);
            if (type == 1) {
                map.put("CZLX", "0");//0:新增,1:更新,2:删除
            } else if (type == 2) {
                map.put("CZLX", "1");
            }

            map.put("GLSJ", new DateTime().toString("yyyy-MM-dd HH:mm:ss"));
//            map.put("CZLX", "0");

            String url = SKSBUrl + "access_token=" + this.getDxToken();

            List<Map<String, String>> list = new ArrayList<>();
            list.add(map);
            JSONArray array = JSONArray.parseArray(JSON.toJSONString(list));
            String ecodecont= Base64.encode(array.toString());
            JSONObject json = this.getMainParam(entCode, ecodecont);

            try {
                log.info("【同步税控设备类型到DX】{} to {}", json, url);
                ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
                JSONObject response = entity.getBody();
                log.info("请求响应：{},{}", response, entity.getStatusCode());
                String rcode = entity.getStatusCode().toString();
                if (rcode.equals("200 OK")) {
                    log.info("请求发送，响应成功");
                    JSONObject result = response.getJSONObject("returnStateInfo");
                    String returnCode = (String) result.get("returnCode");
                    if (returnCode.equals("0000")) {
                        String content = (String) response.get("content");
                        JSONArray jsonArray = JSON.parseArray(Base64.decodeStr(content));
                        if (jsonArray != null) {
                            final JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(jsonArray.get(0)),
                                    JSONObject.class);
                            Object ztdm = jsonObject.get("ZTDM");
                            if ("193000".equals((String) ztdm)) {
                                log.info("同步给销项税控设备成功！");
                                return new CommonRspVo(ResponseCodeEnum.SUCCESS);
                            } else {
                                log.info("同步给销项税控设备失败！");
                                return new CommonRspVo(ztdm.toString(), jsonObject.get("ZTXX").toString());
                            }
                        } else {
                            log.info("返回的内存jsonArray报文为空");
                            return CommonRspVo.faild();
                        }
                    } else {
                        log.info("接口返回失败错误码");
                        String code = returnCode;
                        String msg = result.get("returnMessage").toString();
                        return new CommonRspVo(code, msg);
                    }
                } else {
                    log.error("请求发送，响应失败 {}", entity.getStatusCode());
                    return CommonRspVo.faild();
                }
            } catch (Exception e) {
//            e.printStackTrace();
                log.error("请求异常{}", e);
                return CommonRspVo.faild();
            }
        }
        return CommonRspVo.faild();
    }

    /**
     * 同步大象进项用户数据
     * @param userId
     * @return
     * <AUTHOR>
     * @date 2022-09-20
     */
    @Override
    public CommonRspVo synchronDXjxUserData(Long userId) {
        log.info("【DX进项用户信息同步】接收参数：{}",userId);
        //1.根据userId查询要同步的用户信息
        UserVO userVO = sysUserMapper.selectUserVoById(userId);
        //获取集团名称（渠道名称）
        Distributor distributor = distributorMapper.selectDistributorById(userVO.getDistributorId());
        userVO.setDeptName(distributor.getCompanyName());

        //2.用户权限信息
        List<DxJxUserOrg> userOrgList = sysUserMapper.selectUserOrgByUserId(userId);
        userOrgList.forEach(dxJxUserOrg -> dxJxUserOrg.setCompany(distributor.getSimpleCode()));
        DxJxUserData dxJxUserData = new DxJxUserData(userVO);
        dxJxUserData.setUserOrg(userOrgList);
        dxJxUserData.setCompany(distributor.getSimpleCode());
        JSONObject json = (JSONObject) JSON.toJSON(dxJxUserData);
        log.info("【DX进项用户信息同步】data数据：{}",json.toString());
        String ecodeData = Base64.encode(json.toString().getBytes());

        //3.封装数据
        DxJxSyncPo dxJxUserPo = new DxJxSyncPo(nacosParam.jxcore,nacosParam.jxPsw,nacosParam.jxSignAceKey,ecodeData);
        JSONObject syncUserJson = (JSONObject) JSON.toJSON(dxJxUserPo);
//        log.info("【DX进项用户信息同步】POST数据：{}",syncUserJson);

        //4.发起请求
        CommonRspVo result = dxJxSendPost(syncUserJson,nacosParam.syncUserUrl);
        if (result.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("【DX进项用户信息同步】数据同步成功！{}，{}", userVO.getUsername(), result);
        } else {
            log.error("【DX进项用户信息同步】数据同步失败！入库 {},{}", userVO.getUsername(), result);
            this.addSyncDxFailLog(userVO.getUsername(), "", syncUserJson.toString(), 0, result.toString());
            smsEmailService.webhookSend("【进项用户权限信息同步异常】账号："+userVO.getUsername()+"，大象同步结果："+result.toString());
        }
        return result;
    }


    /**
     * 同步大象进项税号信息 支持批量提交 一批次是同一集团下的 一批中有一个失败全部失败
     * @param deptIdList
     * @param userId
     * @return
     */
    @Override
    public CommonRspVo synchronDXjxTaxData(List<String> deptIdList, Long userId) {
        log.info("【DX进项税号信息同步】接收参数：deptIdList：{} userId：{}",deptIdList,userId);
        //1.封装税号列表 并加密
        List<DxJxTaxPo> dxJxTaxPoList = new ArrayList<>();
        for (int i = 0; i < deptIdList.size(); i++) {
            SysDept sysDept = sysDeptMapper.selectByDeptId(deptIdList.get(i));
            if (sysDept != null) {
                //获取集团名称
                Distributor distributor = distributorMapper.selectDistributorById(sysDept.getSourceId());
                sysDept.setDeptSname(distributor.getCompanyName());
                sysDept.setSourceId(distributor.getSimpleCode());
                SysTaxControl aceBean = sysTaxControlMapper.selectAceByDeptId(sysDept.getDeptId(),1);
                dxJxTaxPoList.add(new DxJxTaxPo(sysDept, aceBean.getSksbbm(), aceBean.getSksbmc()));
            }
        }
        JSONArray jsonArray = JSONArray.parseArray(JSON.toJSONString(dxJxTaxPoList));
//        log.info("【DX进项税号信息同步】data数据：{}",jsonArray.toString());
        String ecodeData = Base64.encode(jsonArray.toString().getBytes());

        //2.封装数据
        DxJxSyncPo dxJxUserPo = new DxJxSyncPo(nacosParam.jxcore,nacosParam.jxPsw,nacosParam.jxSignAceKey,ecodeData);
        JSONObject syncUserJson = (JSONObject) JSON.toJSON(dxJxUserPo);
        log.info("【DX进项税号信息同步】POST数据：{}",syncUserJson);

        //3.发起请求
        CommonRspVo result = dxJxSendPost(syncUserJson,nacosParam.syncTaxUrl);
        if (result.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("【DX进项税号信息同步】数据同步成功！{}", result);
        } else {
            log.error("【DX进项税号信息同步】数据同步失败！入库 {}", result);
            this.addSyncDxFailLog("DXJX批量数据同步", "", syncUserJson.toString(), 0, result.toString());
            smsEmailService.webhookSend("【进项税号信息批量同步异常】DXJX批量数据同步，大象同步结果："+result.toString());
        }
        return result;
    }

    @Override
    public Result tbThirdsysInfo(Result result, Long userId, SysDeptResqVo sysDept, int updateType, Result res) {
        String deptId= result.get("data").toString();
        List<String> deptIdList = new ArrayList<>();
        deptIdList.add(deptId);
        //查询超级管理员购买的产品 同步企业信息（这里有问题，应该是渠道购买的产品）
        if (nacosParam.dxSwitch == 1) {//1开 0 关
//                List<CustomerProduct> cproList = iCustomerProductService.getCustomerProductByUserId(userId);
            List<String> cproList=sysMenuMapper.queryProductsByDistributorId(deptId);
            for (int i = 0; i < cproList.size(); i++) {
                String productId = cproList.get(i);

                // 异步同步DX销项
                threadFactory.newThread(new Runnable() {
                    @SneakyThrows
                    @Override
                    public void run() {
                        Thread.sleep(500);
                        boolean isSyncSucc = false;
                        //同步销项税号数据
                        if (productId.equals(nacosParam.XxProId)){
                            try {
                                CommonRspVo commonRspVo1 = synchronizationDXData(deptId, userId, updateType);//销项产品
                                if (commonRspVo1.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                                    isSyncSucc=true;
                                }
                            }catch (Exception e){
                                e.printStackTrace();
                            }
                        }
                        //同步进项税号数据
                        if (productId.equals(nacosParam.JxProId)) {
                            CommonRspVo commonRspVo2 =  synchronDXjxTaxData(deptIdList, userId);//进项产品
                            if (commonRspVo2.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                                isSyncSucc=true;
                                if (sysDept.getApiDeptEntity().getDeptType()!=1) {
                                    //新增下级组织信息后需要调用同步用户信息接口（针对超管账号）
                                    SysUser sysUser = sysUserMapper.selectSmrBySubLevleDeptId(deptId);
                                    if (sysUser != null) {
                                        log.info("新增下级机构时，同步权限到超管账号");
                                        synchronDXjxUserData(sysUser.getUserId());
                                    }
                                    isSyncSucc=false;
                                }
                            }


                        }
                        //租户接口（非票税页面）同步税号数据时，需要同步用户数据
                        if (isSyncSucc&&res!=null) {
                            log.info("[租户接口同步时]");
                            sysUserService.syncUserInfo2(res,productId);
                        }

                    }
                }).start();
            }
        }
        return Result.ok();
    }

    @Override
    public CommonRspVo tbThirdsysInfoBatch(String deptId, Long userId, String productId,String tenantId) throws Exception {
        if((productId.equals(nacosParam.XxProId) || productId.equals(nacosParam.JxProId)) && nacosParam.dxSwitch==1){
            List<SysUser> userlist = sysUserMapper.selectList(new EntityWrapper<SysUser>().eq("top_level",deptId).eq("tenant_id",tenantId).eq("user_type",2));
            List<SysDept> deptList = sysDeptMapper.selectUnlessByTenantId(tenantId);

            if (productId.equals(nacosParam.XxProId)) {
                log.info("开始批量同步销项产品 子用户和税号信息,{},{}",userlist.size(),deptList.size());
                this.batchSyncXXData(userlist,deptList,userId);
            }
            if (productId.equals(nacosParam.JxProId)) {
                log.info("开始批量同步进项产品 子用户和税号信息,{},{}",userlist.size(),deptList.size());
                this.batchSyncJXData(userlist,deptList,userId);
            }
        }
        return null;
    }

    @Override
    public Result accountCheck(JSONObject json, String url) {
        log.info("[全电RPA账号验证] POST 请求{} to {}", json, url);
        try {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("[全电RPA账号验证] 响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                String code = response.get("code").toString();
                if (rcode.equals("000")) {
                    return Result.ok(response.get("message").toString()).put("taxpayerName",response.get("taxpayerName"));
                }else{
                    return Result.error(code,response.get("message").toString()).put("taxpayerName",response.get("taxpayerName"));
                }
            } else {
                log.error("[全电RPA账号验证] 请求发送，响应失败{}", entity.getStatusCode());
                return Result.error();
            }
        } catch (Exception e) {
            log.error("[全电RPA账号验证] 请求异常{}", e);
            return Result.error();
        }
    }

    @Override
    public   CommonRspVo einvoiceAddDept(JSONObject json) {
        String url = nacosParam.qdDataJhkUrl;
        log.info("[票税全电数据交互] POST 请求{} to {}", json, url);
        try {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("[票税全电数据交互] 响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                log.info("[票税全电数据交互] 请求发送，接口响应成功");
                String msg = (String) response.get("msg");
                String code = (String) response.get("code");
                if (code.equals("0000")) {
                    log.info("[CommonRspVo] 接口请求成功");
                    return new CommonRspVo(ResponseCodeEnum.SUCCESS);
                } else {
                    log.info("[CommonRspVo] 接口返回失败");
                    return new CommonRspVo(code, msg);
                }
            } else {
                log.error("[票税全电数据交互] 请求发送，响应失败{}", entity.getStatusCode());
                return CommonRspVo.faild();
            }
        } catch (Exception e) {
            log.error("[票税全电数据交互] 请求异常{}", e);
            return CommonRspVo.faild();
        }
    }


    @Override
    public   CommonRspVo jxInvoiceAddDept(JSONObject json) {
        String url = nacosParam.jxDataJhkUrl;
        log.info("[票税全电数据交互] POST 请求{} to {}", json, url);
        try {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("[票税全电数据交互] 响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                log.info("[票税全电数据交互] 请求发送，接口响应成功");
                String msg = (String) response.get("msg");
                String code = (String) response.get("code");
                if (code.equals("0000")) {
                    log.info("[CommonRspVo] 接口请求成功");
                    return new CommonRspVo(ResponseCodeEnum.SUCCESS);
                } else {
                    log.info("[CommonRspVo] 接口返回失败");
                    return new CommonRspVo(code, msg);
                }
            } else {
                log.error("[票税全电数据交互] 请求发送，响应失败{}", entity.getStatusCode());
                return CommonRspVo.faild();
            }
        } catch (Exception e) {
            log.error("[票税全电数据交互] 请求异常{}", e);
            return CommonRspVo.faild();
        }
    }

    /**
     * 销项重定向地址处理
     * @param redirectUrl
     * @param taxNo
     * @return
     * <AUTHOR>
     * @date 2022-11-28
     */
    @Override
    public String dualRedirectUrl(String redirectUrl,String taxNo,String redirectURI,String username) {
        //首先获取redis中token
//        String token = (String) redisTemplate.opsForValue().get(SystemConstants.USER_ELE_SSO_TOKEN_KEY+username);
//        if (StringUtils.isBlank(token)) {
//            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(redirectUrl,null,JSONObject.class);
//            HttpHeaders h = entity.getHeaders();
//            URI location = h.getLocation();
//            log.info("[销项重定向地址处理]获取重定向地址:{}",location.toString());
//            token = StringUtils.substringBetween(location.toString() , "?token=", "&nsrsbh=");
//            log.info("[销项重定向地址处理]获取到 token: {}" ,token);
//            JSONObject jsonObject =JSONObject.parseObject(com.xiaoleilu.hutool.codec.Base64.decodeStr(token.split("\\.")[1]));
//            long nowTime = System.currentTimeMillis()/1000;
//            Long exp = jsonObject.getLong("exp");
//            Long invalidTime = (exp-nowTime)/60;
//            log.info("[销项重定向地址处理]token有效期: exp = {} ,时间：{} 失效剩余时间：{}分" ,exp,nowTime,invalidTime);
//            redisTemplate.opsForValue().set(SystemConstants.USER_ELE_SSO_TOKEN_KEY + username, token, invalidTime-1, TimeUnit.MINUTES);
//        }
        //拼接新的地址
//        String url = redirectURI + "?token=" + token + "&nsrsbh=" + taxNo ;
//        log.info("[销项重定向地址处理]最新地址 ：{}" ,url);
        ResponseEntity<JSONObject> entity = restTemplate.postForEntity(redirectUrl,null,JSONObject.class);
        HttpHeaders h = entity.getHeaders();
        URI location = h.getLocation();
        log.info("[销项重定向地址处理]获取重定向地址:{}",location.toString());
        String url = location.toString();
        return url;
    }

    /**
     * 进项项批量同步方法
     * 必须先同步税号信息 再同步用户信息
     * @param userlist
     * @param deptList
     * @param userId
     * @throws Exception
     */
    private void batchSyncJXData(List<SysUser> userlist, List<SysDept> deptList, Long userId) throws Exception {

        List<String> deptIdList = new ArrayList<>();
        for (int i = 0; i < deptList.size() ; i++) {
            deptIdList.add(deptList.get(i).getDeptId());
        }
        if (deptIdList.size()>0 ) {
            this.synchronDXjxTaxData(deptIdList, userId);//同步税号
        }


        for (int i = 0; i < userlist.size(); i++) {
            this.synchronDXjxUserData(userId);//同步用户权限
        }
    }

    /**
     * 销项批量同步方法
     * 必须先同步税号信息 再同步用户信息
     * @param userlist
     * @param deptList
     * @param userId
     * @throws Exception
     */
    private void batchSyncXXData(List<SysUser> userlist, List<SysDept> deptList, Long userId) throws Exception {

        for (int i = 0; i < deptList.size() ; i++) {
            this.synchronizationDXData(deptList.get(i).getDeptId(), userId, 2);//同步税号
        }
        for (int i = 0; i < userlist.size(); i++) {
            this.synchronUserAuth(userlist.get(i).getUserId());//同步用户权限
        }
    }


    /**
     * 同步账号和数据权限到DX
     * @param userId
     */
    @Override
    public CommonRspVo synchronUserAuth(Long userId) {
        log.info("【DX销项数据同步-权限分配】接收参数：{}",userId);
        //1.根据用户Id查询 已新增的用户信息
        UserVO userVO = sysUserMapper.selectUserVoById(userId);
        //2.根据用户Id查询数据权限列表
        List<SysUserDept> sysUserDeptList = sysUserMapper.selectDeptIdsByUserId(userId);
        List<Map<String,String>> list = new ArrayList<>();
        sysUserDeptList.stream().forEach(sysUserDept -> {
            Map<String,String> map = new HashMap<>();
            map.put("deptId",CommonConstant.DEPTId_PREFIX+sysUserDept.getDeptId());
            list.add(map);
        });

        //3.封装数据
        JSONObject json = new JSONObject();
        String parentId = userVO.getTopLevel();
        if (!parentId.equals("77d78e4b1f6f11edbb4b52540079e9e2") ) {
            parentId=CommonConstant.DEPTId_PREFIX+userVO.getTopLevel();
        }
        json.put("username", CommonConstant.UID_PREFIX+userVO.getUsername());
        json.put("nickname", userVO.getNickname());
        json.put("password", "");
        json.put("parentId", parentId);
        json.put("sourceId", nacosParam.sourceId);
        //json.put("userId", "");
        json.put("deptIdList", list.toArray());

        //4.发起请求
        CommonRspVo result = sendPost(json,thirdAddUserUrl);
        if (result.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("【DX销项数据同步-权限分配】数据同步成功！{}，{}", userVO.getUsername(), result);
        } else {
            log.error("【DX销项数据同步-权限分配】数据同步失败！入库 {},{}", userVO.getUsername(), result);
            this.addSyncDxFailLog(userVO.getUsername(), "", json.toString(), 0, result.toString());
            smsEmailService.webhookSend("【销项用户权限信息同步异常】账号："+userVO.getUsername()+"，大象同步结果："+result.toString());
        }
        return result;
    }


    private CommonRspVo sendPost(JSONObject json,String url) {
        log.info("[DX销项数据同步-权限分配] POST 请求{} to {}", json, url);
        try {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("[DX销项数据同步-权限分配] 响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                log.info("[DX销项数据同步-权限分配] 请求发送，接口响应成功");
                String msg = (String) response.get("msg");
                String code = (String) response.get("code");
                if (code.equals("0000")) {
                    log.info("[DX销项数据同步-权限分配] 接口请求成功，data:{}",response.get("data"));
                    return new CommonRspVo(response.get("data")+"");
                } else {
                    log.info("[DX销项数据同步-权限分配] 接口返回失败");
                    return new CommonRspVo(code, msg);
                }
            } else {
                log.error("[DX销项数据同步-权限分配] 请求发送，响应失败{}", entity.getStatusCode());
                return CommonRspVo.faild();
            }
        } catch (Exception e) {
            log.error("[DX销项数据同步-权限分配] 请求异常{}", e);
            return CommonRspVo.faild();
        }
    }

    /**
     * 大象进项产品用户税号信息同步 请求发送和响应解析
     * @param json
     * @param url
     * @return
     * <AUTHOR>
     * @date 2022-09-20
     */
    private CommonRspVo dxJxSendPost(JSONObject json, String url) {
        log.info("[DX进项信息同步] POST 请求{} to {}", json, url);
        try {
            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
            JSONObject response = entity.getBody();
            log.info("[DX进项信息同步] 响应：{}", response);
            String rcode = entity.getStatusCode().toString();
            if (rcode.equals("200 OK")) {
                log.info("[DX进项信息同步] 请求发送，接口响应成功");
                String msg = (String) response.get("message");
                String code = (String) response.get("code");
                if (code.equals("0000")) {
                    return CommonRspVo.success(null);
                } else {
                    return new CommonRspVo(code, msg);
                }
            } else {
                log.error("[DX进项信息同步] 请求发送，响应失败{}", entity.getStatusCode());
                return CommonRspVo.faild();
            }
        } catch (Exception e) {
            log.error("[DX进项信息同步] 请求异常{}", e);
            return CommonRspVo.faild();
        }
    }

    public static void main(String[] args) {

        //        JSONObject json = new JSONObject();
//        json.put("name","大3456789象01");
//        json.put("taxpayerCode","45678987654567876567876");
//        json.put("tenantId","34567898765");
//        String url = "http://172.16.60.111:18113/einvoice/user/addDept";
//        log.info("[票税全电数据交互] POST 请求{} to {}", json, url);
//        try {
//            RestTemplate restTemplate = new RestTemplate();
//            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(url, json, JSONObject.class);
//            JSONObject response = entity.getBody();
//            log.info("[票税全电数据交互] 响应：{}", response);
//            String rcode = entity.getStatusCode().toString();
//            if (rcode.equals("200 OK")) {
//                log.info("[票税全电数据交互] 请求发送，接口响应成功");
//                String msg = (String) response.get("msg");
//                String code = (String) response.get("code");
//                if (code.equals("0000")) {
//                    log.info("[CommonRspVo] 接口请求成功");
//
//                } else {
//                    log.info("[CommonRspVo] 接口返回失败");
//
//                }
//            } else {
//                log.error("[票税全电数据交互] 请求发送，响应失败{}", entity.getStatusCode());
//
//            }
//        } catch (Exception e) {
//            log.error("[票税全电数据交互] 请求异常{}", e);
//        }
    }
}
