package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.DistributorMapper;
import com.dxhy.core.pojo.entity.Distributor;
import com.dxhy.core.service.IDistributorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 10:582019/4/24 0024
 */
@Slf4j
@Service("distributorService")
public class DistributorServiceImpl extends ServiceImpl<DistributorMapper, Distributor> implements IDistributorService {
    /**
     *  根据id查询信息
     * @param id
     * @return
     */
    @Override
    public Distributor selectId(String id) {
        return baseMapper.selectById(id);
    }
}
