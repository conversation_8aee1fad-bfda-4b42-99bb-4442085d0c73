package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.pojo.entity.ProductMenu;

import java.util.List;

/**
 * <p>
 * sys_permission 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
public interface IProductMenuService extends IService<ProductMenu> {

    void deleteCache(String id);

    void delCacheByProductId(String productId);

    List<ProductMenu> getProductMenuByProductIdAndSellLabelId(String productId, String sellLabelId);

    List<ProductMenu> getProMenuListByDeptId(String deptId,Integer roleType);

    ProductMenu selectMenuId(String name, String systemSign);

    List<ProductMenu> getProductMenu(String id);
}
