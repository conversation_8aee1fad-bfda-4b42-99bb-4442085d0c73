package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.CustomerProductMapper;
import com.dxhy.core.pojo.entity.CustomerProduct;
import com.dxhy.core.service.ICustomerProductService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-06
 */
@Service
public class CustomerProductServiceImpl extends ServiceImpl<CustomerProductMapper, CustomerProduct> implements ICustomerProductService {


    @Resource
    private CustomerProductMapper customerProductMapper;


    @Override
    public String getProductId(String id) {
        return baseMapper.getProductId(id);
    }

    @Override
    public boolean updateCustomerProduct(String accountInfoId, String customerId, String productId,String total) {
        return customerProductMapper.updateCustomerProduct(accountInfoId,customerId,productId, total);
    }

    @Override
    public List<CustomerProduct> getCustomerProductByDis(String distributorId, String productId){
        return baseMapper.getCustomerProductByDis(distributorId, productId);
    }

    @Override
    public Boolean updateProductStatus(String accountInfoId, String productId, String customerId, int status) {
        return customerProductMapper.updateProductStatus(accountInfoId,productId,customerId, status);
    }

    @Override
    public int updateCustomerProductTotal(String accountInfoId, String total, String customerId, String productId) {
        return customerProductMapper.updateCustomerProductTotal(accountInfoId,customerId,productId, total);
    }

    @Override
    public String getProductIdByComboId(String comboDistributorId) {
        return customerProductMapper.getProductIdByComboId(comboDistributorId);
    }

    @Override
    public List<CustomerProduct> getCustomerProductByUserId(Long userId) {
        return baseMapper.getCustomerProductByUserId(userId);
    }

}
