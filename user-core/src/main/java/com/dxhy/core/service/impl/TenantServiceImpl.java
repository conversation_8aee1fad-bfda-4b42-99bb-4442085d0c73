/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.DTO.AdminUserOperateDto;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.SysDeptResqVo;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.PageUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.dxhy.core.utils.GenerateRoleCodeUtil.generateNum;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 11:46
 */
@Service
@Slf4j
public class TenantServiceImpl extends ServiceImpl<SysTenantMapper, SysTenant> implements ITenantService {

	@Autowired
	private SysTenantProductMapper tenantProductMapper;
	@Autowired
	private SysDeptService sysDeptService;
	@Autowired
	private SysDeptMapper sysDeptMapper;
	@Autowired
	private SysRoleMapper sysRoleMapper;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private SysUserRoleService sysUserRoleService;
	@Autowired
	private SysRoleMenuService sysRoleMenuService;
	@Autowired
	private ProductMenuMapper productMenuMapper;
	@Autowired
	private ChannelManagementDao channelManagementDao;
	@Autowired
	private SysUserService sysUserService;

	private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

	@Override
	public Result listByPage(Page<SysTenant> page, Map<String, Object> params) {
		String tenantId = (String) params.get("tenantId");
		List<String> tenantIdList = getTenantIdList(tenantId);
		String tenantName = (String) params.get("tenantName");
		page = this.selectPage(page, new EntityWrapper<SysTenant>()
				.in("tenant_id", tenantIdList)
				.like(StrUtil.isNotEmpty(tenantName), "tenant_name", tenantName)
				.eq("is_deleted", "0")
				.orderDesc(Collections.singleton("create_time"))
		);
		page.getRecords().forEach(e -> {
			Wrapper<SysTenantProduct> wrapper = new EntityWrapper<SysTenantProduct>().eq("tenant_id", e.getTenantId());
			List<SysTenantProduct> tenantProducts = tenantProductMapper.selectList(wrapper);
			e.setTenantProducts(tenantProducts);
			if (StringUtils.isNotBlank(e.getCreateBy())) {
				SysUser sysUser = sysUserMapper.selectById(e.getCreateBy());
				if (sysUser != null) {
					e.setCreateBy(sysUser.getUsername());
				}
			}
			if (StringUtils.isNotBlank(e.getUpdateBy())) {
				SysUser sysUser = sysUserMapper.selectById(e.getUpdateBy());
				if (sysUser != null) {
					e.setUpdateBy(sysUser.getUsername());
				}
			}
		});
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data", pageUtils);
	}

	@Override
	public List<String> getTenantIdList(String tenantId) {
		if (StrUtil.isEmpty(tenantId)) {
			throw new RuntimeException("tenantId is required！");
		}
		List<String> list = Lists.newArrayList();
		list.add(tenantId);
		List<SysTenant> sysTenantList = this.selectList(new EntityWrapper<SysTenant>()
				.eq("is_deleted", "0")
		);
		recursionTenantId(list, sysTenantList, Arrays.asList(tenantId));
		return list;
	}

	@Override
	public SysTenant getTenantInfo(String tenantId) {
		SysTenant sysTenant = this.selectOne(new EntityWrapper<SysTenant>().eq("tenant_id", tenantId));
		if (null != sysTenant) {
			Wrapper<SysTenantProduct> wrapper = new EntityWrapper<SysTenantProduct>()
					.andNew("dept_id is null OR dept_id = ''")
					.andNew("auth_stime <= now() and auth_etime >= now()")
					.eq("tenant_id", tenantId);
			List<SysTenantProduct> tenantProducts = tenantProductMapper.selectList(wrapper);
			sysTenant.setTenantProducts(tenantProducts);
			return sysTenant;
		}
		return null;
	}

	@Override
	public SysTenant getSecret(String secretId) {
		SysTenant sysTenant = baseMapper.selectOne(new SysTenant().setSecretId(secretId));
		return sysTenant;
	}

	@Override
	public boolean validTenantProduct(String secretId, String productId) {
		if (StrUtil.isNotEmpty(secretId)) {
			SysTenant sysTenant = this.getSecret(secretId);
			if (null != sysTenant) {
				Wrapper<SysTenantProduct> wrapper = new EntityWrapper<SysTenantProduct>()
						.le("auth_stime", new Date())
						.ge("auth_etime", new Date())
						.eq("product_id", productId)
						.eq("tenant_id", sysTenant.getTenantId());
				List<SysTenantProduct> tenantProducts = tenantProductMapper.selectList(wrapper);
				if (tenantProducts.size() > 0) {
					return true;
				}
			}
		}
		return false;
	}

	private void recursionTenantId(List<String> list, List<SysTenant> sysTenantList, List<String> tenantIds) {
		if (tenantIds.size() > 0) {
			for (String tenantId : tenantIds) {
				List<String> collect = sysTenantList.stream().filter(e -> e.getParentId().equals(tenantId)).map(i -> i.getTenantId()).collect(Collectors.toList());
				list.addAll(collect);
				recursionTenantId(list, sysTenantList, collect);
			}
		}
	}

	@Override
	@Transactional
	public Result saveTenant(String username, SysTenant tenant) {
		String checkStr = this.checkTenantProduct(tenant);
		if (StringUtils.isNotEmpty(checkStr)) {
			return Result.error(checkStr);
		}
		boolean insertFlag = false;
		if (null == (tenant.getId())) {
			baseMapper.insert(tenant
					.setTenantId(getTenantId())
					.setIsDeleted(CommonConstant.DB_NOT_DELETED)
					.setCreateBy(username)
					.setCreateTime(new Date()));
			insertFlag = true;
		} else {
			//根据id查询tenantId补全
			SysTenant sysTenant = this.selectById(tenant.getId());
			tenant.setTenantId(sysTenant.getTenantId());
			//更新原数据
			baseMapper.updateById(tenant
					.setUpdateBy(username)
					.setUpdateTime(new Date()));
		}
		//先删后插(只删该租户数据，机构授权不动)
		tenantProductMapper.delete(new EntityWrapper<SysTenantProduct>()
				.eq("tenant_id", tenant.getTenantId())
				.andNew()
				.isNull("dept_id")
				.or()
				.eq("dept_id", ""));
		if (CollectionUtils.isNotEmpty(tenant.getTenantProducts())) {
			for (SysTenantProduct tenantProduct : tenant.getTenantProducts()) {
				tenantProduct.setTenantId(tenant.getTenantId());
				tenantProduct.setUpdateBy(username);
				tenantProduct.setUpdateTime(new Date());
				tenantProduct.setCreateBy(username);
				tenantProduct.setCreateTime(new Date());
				tenantProductMapper.insert(tenantProduct);
			}
		}
		// 创建/更新租户顶级机构、管理员角色和管理员账号
		createDeptAndRoleUser(tenant,insertFlag);
		return Result.ok();
	}

	/**
	 * 校验租户授权产品是否符合规范
	 *
	 * @param tenant
	 * @return
	 */
	private String checkTenantProduct(SysTenant tenant) {
		String checkResult = "";
		if (CollectionUtils.isNotEmpty(tenant.getTenantProducts())) {
			//校验配置的授权信息每个通道类别都只能存在一条通道
			Map<String, List<SysTenantProduct>> collect = tenant.getTenantProducts().stream().collect(Collectors.groupingBy(SysTenantProduct::getChannelType));
			for (Map.Entry<String, List<SysTenantProduct>> entry : collect.entrySet()) {
				if (entry.getValue().size() > 1) {
					checkResult = "每个通道类别都只能存在一条通道！";
				}
			}
			//不同通道配置的业务类型不能重复,获取前台传过来的通道ID，根据通道id获取通道信息，然后获取通道的业务类型，将所有业务类型放到一个list集合中，判断是否有重复项，有重复项则说明业务类型重复，无重复项则说明业务类型不重复
			List<String> businessTypeList = new ArrayList<>();
			for (SysTenantProduct tenantProduct : tenant.getTenantProducts()) {
				ChannelManagement channelManagement = channelManagementDao.selectById(tenantProduct.getChannelId());
				if (channelManagement != null) {
					if (StringUtils.isNotEmpty(channelManagement.getBusinessType())) {
						List<String> businessTypes = JSON.parseArray(channelManagement.getBusinessType(), String.class);
						businessTypeList.addAll(businessTypes);
					}
				}
			}
			if (businessTypeList.size() > new HashSet<>(businessTypeList).size()) {
				checkResult = "选择的多个通道中配置的业务类型有重复，请重新选择通道！";
			}
		}
		return checkResult;
	}

	/**
	 * 创建租户顶级机构、管理员角色和管理员账号
	 *
	 * @param tenant
	 */
	private void createDeptAndRoleUser(SysTenant tenant,boolean isInsert) {
		//更新时，更新角色菜单数据
		if(!isInsert){
			//查询该租户的超级管理员角色
			SysRole sysRole = new SysRole();
			sysRole.setTenantId(tenant.getTenantId());
			sysRole.setRoleType(10);
			SysRole superAdmin = sysRoleMapper.selectOne(sysRole);
			//删除角色菜单关系
			SysRoleMenu sysRoleMenu = new SysRoleMenu();
			sysRoleMenu.setRoleId(superAdmin.getRoleId());
			sysRoleMenuService.delRoleMenu(sysRoleMenu);
			// 3. 重新分配权限
			// 3.1 获取租户已授权产品的所有菜单权限
			List<String> menuIds = new ArrayList<>();
			if (CollectionUtils.isNotEmpty(tenant.getTenantProducts())) {
				List<Long> productIdList = tenant.getTenantProducts().stream().map(SysTenantProduct::getProductId).collect(Collectors.toList());
				menuIds = productMenuMapper.getMenuIdListByProId(productIdList);
			}
			// 3.2 添加系统管理菜单权限
			menuIds.add("1000000060");
			menuIds.add("1000000061");
			menuIds.add("1000000063");
			menuIds.add("1000000064");

			// 3.3 为角色重新分配菜单权限
			for (String menuId : menuIds) {
				SysRoleMenu roleMenu = new SysRoleMenu();
				roleMenu.setRoleId(superAdmin.getRoleId());
				roleMenu.setMenuId(menuId);
				sysRoleMenuService.insert(roleMenu);
			}
			return;
		}
		// 1. 创建顶级机构
		SysDept sysDept = new SysDept();
		sysDept.setName(tenant.getTenantName());
		sysDept.setDeptSname(tenant.getTenantName());
		sysDept.setDeptId(UUID.randomUUID().toString().replace("-", ""));
		sysDept.setParentId(null); // 顶级机构的父ID为空
		sysDept.setLevel(1); // 顶级机构层级为1
		sysDept.setDelFlag("0"); // 未删除
		sysDept.setCreateTime(new Date());
		sysDept.setTenantId(tenant.getTenantId());
		sysDept.setStatus(1); // 正常状态
		sysDept.setCreateUser(Long.valueOf(tenant.getCreateBy()));
		sysDept.setContactName(tenant.getLinkman());
		sysDept.setContactPhone(tenant.getContactNumber());
		sysDept.setTaxpayerAddress(tenant.getAddress());
		sysDept.setTaxpayerType("1"); //默认一般纳税人
		sysDept.setDataSource("4");
		//无税号
		sysDept.setEinType("3");
		//税号设为空
		sysDept.setTaxpayerCode(StringUtils.isNotBlank(tenant.getNsrsbh()) ? "" : tenant.getNsrsbh());
		//虚拟机构
		sysDept.setDeptType(5);
		SysDeptResqVo sysDeptResqVo = new SysDeptResqVo();
		sysDeptResqVo.setApiDeptEntity(sysDept);
		try {
			Result result = sysDeptService.addDept(sysDeptResqVo, tenant.getCreateBy());
			if (!result.get("code").equals("0000")) {
				throw new RuntimeException("添加顶级机构失败,原因为：" + result.get("msg"));
			}
		} catch (Exception e) {
			throw new RuntimeException("添加顶级机构异常,原因为：" + e.getMessage());
		}

		// 2. 创建顶级机构管理员角色
		SysRole sysRole = new SysRole();
		sysRole.setRoleName("超级管理员");
		sysRole.setRoleDesc("页面创建租户时自动创建角色");
		sysRole.setDeptId(sysDept.getDeptId());
		sysRole.setDeptName(sysDept.getName());
		sysRole.setCreateTime(new Date());
		sysRole.setDelFlag("0");
		sysRole.setRoleType(10); // 超级管理员角色类型为10
		sysRole.setTenantId(tenant.getTenantId()); // 租户id
		sysRole.setRoleProperty("2");
		sysRole.setType(0);
		sysRole.setCreateBy(Long.valueOf(tenant.getCreateBy()));
		//查到数据库最后一个角色编码
		String roleCode = sysRoleMapper.selectLastRoleCode();
		if (StringUtils.isBlank(roleCode)) {
			sysRole.setRoleCode(generateNum(4, ""));
		} else {
			sysRole.setRoleCode(generateNum(4, roleCode));
		}
		sysRoleMapper.insert(sysRole);

		// 3. 给角色分配权限
		// 3.1 获取租户已授权产品的所有菜单权限
		List<String> menuIds = new ArrayList<>();
		if (CollectionUtils.isNotEmpty(tenant.getTenantProducts())) {
			List<Long> productIdList = tenant.getTenantProducts().stream().map(SysTenantProduct::getProductId).collect(Collectors.toList());
			menuIds = productMenuMapper.getMenuIdListByProId(productIdList);
		}
		// 3.2 添加系统管理菜单权限
		menuIds.add("1000000060");
		menuIds.add("1000000061");
		menuIds.add("1000000063");
		menuIds.add("1000000064");

		// 3.3 为角色分配菜单权限
		for (String menuId : menuIds) {
			SysRoleMenu roleMenu = new SysRoleMenu();
			roleMenu.setRoleId(sysRole.getRoleId());
			roleMenu.setMenuId(menuId);
			sysRoleMenuService.insert(roleMenu);
		}

		// 4. 创建超级管理员账号
		AdminUserOperateDto adminUserOperateDto = new AdminUserOperateDto();
		adminUserOperateDto.setPuserId(Long.valueOf(tenant.getCreateBy()));
		adminUserOperateDto.setUserName(tenant.getContactNumber()); // 用户名为租户联系电话
		adminUserOperateDto.setPassword(ENCODER.encode("88888888")); // 默认密码为88888888
		adminUserOperateDto.setDeptId(sysDept.getDeptId());// 所属部门为刚创建的顶级机构
		adminUserOperateDto.setTenantId(tenant.getTenantId());
		adminUserOperateDto.setCreateTime(new Date());
		adminUserOperateDto.setRoleIdList(Arrays.asList(sysRole.getRoleId()));
		adminUserOperateDto.setDeptList(Arrays.asList(sysDept.getDeptId()));
		adminUserOperateDto.setMobile(tenant.getContactNumber());
		adminUserOperateDto.setName(tenant.getLinkman());
		adminUserOperateDto.setType("1");
		adminUserOperateDto.setUserSource("4");
		try {
			Result result = sysUserService.addUser(adminUserOperateDto);
			if (!result.get("code").equals("0000")) {
				throw new RuntimeException("添加用户失败,原因为：" + result.get("msg"));
			}
		} catch (Exception e) {
			throw new RuntimeException("添加用户异常,原因为：" + e.getMessage());
		}
		// 5. 将用户与角色关联
		/*SysUserRoleVo sysUserRoleVo = new SysUserRoleVo();
		sysUserRoleVo.setUserId(sysUser.getUserId());
		sysUserRoleVo.setRoleId(sysRole.getRoleId());
		SysUserRole userRole = new SysUserRole();
		userRole.setUserId(sysUser.getUserId());
		userRole.setRoleId(sysRole.getRoleId());
		sysUserRoleService.insert(userRole);*/
		// 6. 将用户与机构关联
//		sysUserMapper.addUserDeptRelation(sysUser.getUserId(), sysDept.getDeptId());
	}

	private synchronized String getTenantId() {
		List<SysTenant> sysTenants = baseMapper.selectList(new EntityWrapper<SysTenant>().last("order By id desc limit 1"));
		if (sysTenants.size() > 0) {
			return new BigDecimal(sysTenants.get(0).getTenantId()).add(BigDecimal.valueOf(1)).toString();
		} else {
			return "10000000";
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public Result remove(String ids) {
		//逗号分隔字符串转为集合
		String[] split = ids.split(",");
		List<String> strings = Arrays.asList(split);
		if (strings.size() > 0) {
			List<String> idlist = Lists.newArrayList();
			for (String id : strings) {
				SysTenant sysTenant = baseMapper.selectById(id);
				//级联查询该租户下的所有子租户包括孙子租户等等
				List<String> tenantIdList = getTenantIdList(sysTenant.getTenantId());
				if (tenantIdList.size() > 1) {
					return Result.error("租户ID："+sysTenant.getTenantId()+"下还存在子租户，请将级联信息删除后再删除租户！");
				}
				idlist.addAll(tenantIdList);
			}
			//查询所有租户id关联的机构
			// 由于EntityWrapper存在问题，直接使用Map参数查询
			for (String tenantId : idlist) {
				Map<String, Object> params = new HashMap<>();
				params.put("tenant_id", tenantId);
				params.put("del_flag", "0");
				List<SysDept> deptList = sysDeptService.selectByMap(params);
				log.info("租户ID：{}，查询到的机构数量：{}", tenantId, deptList != null ? deptList.size() : 0);
				if (CollectionUtils.isNotEmpty(deptList)) {
					return Result.error("租户ID："+tenantId+"下还存在级联的机构，请将级联信息删除后再删除租户！");
				}
			}
			//根据idlist查询租户tenentId
			//List<String> tenantIdList = baseMapper.selectBatchIds(idlist).stream().map(SysTenant::getTenantId).collect(Collectors.toList());
			//删除租户授权产品信息
			tenantProductMapper.delete(new EntityWrapper<SysTenantProduct>().in("tenant_id", idlist));
			//改为逻辑删除
			baseMapper.update(new SysTenant().setIsDeleted(CommonConstant.DB_IS_DELETED), new EntityWrapper<SysTenant>().in("tenant_id", idlist));
		}
		return Result.ok();
	}
}