package com.dxhy.core.service.impl;/**
 * @Auther: 李永强
 * @Date: 2019/12/4 16:54
 * @Description:
 */

import com.alibaba.fastjson.JSON;
import com.dxhy.core.common.response.R;
import com.dxhy.core.service.SensorsService;
import com.dxhy.core.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 *@program: accountcenter
 *@description: li<PERSON><PERSON><PERSON><PERSON>
 *@author: liu yan
 *@create: 2019-12-04 16:54
 */
@Slf4j
@Service("SensorsService")
public class SensorsServiceImpl implements SensorsService {

    @Autowired
    private UserService userService;

    @Override
    public Boolean sensorsLogin(HashMap<String,String> map){

        try{
            String redirectURI = map.get("redirectURI");
            String productId = map.get("productId");

            if(StringUtils.isBlank(productId)){
                R<Map<String,String>> sourceInfoByUri = userService.getSourceInfoByUri(redirectURI);
                if(sourceInfoByUri==null|| sourceInfoByUri.getData() ==null){
                    log.error("未获取到产品来源关系，请联系后台工作人员进行基础配置,产品来源:{}", redirectURI);
                }else{
                    productId = sourceInfoByUri.getData().get("productId");
                }
            }

            R<String> productInfoByDetail = userService.getProductInfoById(productId);
            if(productInfoByDetail!=null|| StringUtils.isNotBlank(productInfoByDetail.getData())){
                map.put("productName",productInfoByDetail.getData());
            }
            map.put("productId",productId);
            log.info("单点登录调用神策埋点:参数:{}", JSON.toJSONString(map));
            userService.sensorsLogin(map);
        }catch (Exception e){
            log.error("神策同步登录事件异常:{}",e.toString());
        }

        return true;

    }
}