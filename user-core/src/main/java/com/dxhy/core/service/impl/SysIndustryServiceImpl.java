package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.SysIndustryMapper;
import com.dxhy.core.pojo.entity.SysIndustry;
import com.dxhy.core.service.ISysIndustryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysIndustryServiceImpl extends ServiceImpl<SysIndustryMapper, SysIndustry> implements ISysIndustryService {

    @Autowired
    private SysIndustryMapper sysIndustryMapper;

    @Override
    public List<SysIndustry> getIndustryList() {
        return sysIndustryMapper.getIndustryList();
    }

    @Override
    public SysIndustry selectIndustryByName(String industryName) {
        return sysIndustryMapper.selectIndustryByName(industryName);
    }

    @Override
    public SysIndustry selectIndustryByCode(String industryCode) {
        return sysIndustryMapper.selectIndustryByCode(industryCode);
    }
}
