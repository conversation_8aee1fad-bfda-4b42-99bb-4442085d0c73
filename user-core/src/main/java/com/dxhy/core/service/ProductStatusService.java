package com.dxhy.core.service;

import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.SaasProductInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @description product_status
 * <AUTHOR>
 * @date 2022-10-13
 */
@Service
public interface ProductStatusService {



    Result addUserProductBatch(List<SaasProductInfo> productList, String distributorId);
}