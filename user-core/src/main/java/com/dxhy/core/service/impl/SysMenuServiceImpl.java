/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.common.util.ResponseCodeUtil;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.DTO.AdminMenuInfoDto;
import com.dxhy.core.pojo.DTO.AdminMenuListDto;
import com.dxhy.core.pojo.DTO.MenuTree;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.MenuTreeVO;
import com.dxhy.core.pojo.vo.MenuVO;
import com.dxhy.core.pojo.vo.SysRoleVo;
import com.dxhy.core.service.ProductService;
import com.dxhy.core.service.SysMenuService;
import com.dxhy.core.service.SysRoleMenuService;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.utils.Assert;
import com.dxhy.core.utils.PageUtils;
import com.dxhy.core.utils.TreeUtil;
import com.google.common.collect.Lists;
import com.xiaoleilu.hutool.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * 菜单权限表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Service
@Slf4j
public class SysMenuServiceImpl extends ServiceImpl<SysMenuMapper, SysMenu> implements SysMenuService {
    @Autowired
    private SysMenuMapper sysMenuMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Autowired
    private DistributorMapper distributorMapper;

    @Autowired
    private SysUserRoleMapper sysUserRoleMapper;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysRoleMenuService sysRoleMenuService;


    @Autowired
    private ProductService productService;

    @Override
//    @Cacheable(value = "menu_details", key = "#role  + '_menu'")
    public List<SysMenu> findMenuByRoleName(String role) {
        return sysMenuMapper.findMenuByRoleName(role);
    }

    @Override
//    @CacheEvict(value = "menu_details", allEntries = true)
    public Boolean deleteMenu(Integer id) {
        Assert.isNull(id, "菜单ID不能为空");
        // 删除当前节点
        SysMenu condition1 = new SysMenu();
        condition1.setMenuId(id.toString());
        condition1.setDelFlag(CommonConstant.STATUS_DEL);
        this.updateById(condition1);

        // 删除父节点为当前节点的节点
        SysMenu conditon2 = new SysMenu();
        conditon2.setParentId(String.valueOf(id));
        SysMenu sysMenu = new SysMenu();
        sysMenu.setDelFlag(CommonConstant.STATUS_DEL);
        return this.update(sysMenu, new EntityWrapper<>(conditon2));
    }

    @Override
    @CacheEvict(value = "menu_details", allEntries = true)
    public Boolean updateMenuById(SysMenu sysMenu) {
        return this.updateById(sysMenu);
    }

    @Override
    public Set<MenuVO> findMenuByEntUser(Long userId, String entId) {
        return sysMenuMapper.findMenuByEntUser(userId, entId);
    }

    @Override
    public Set<MenuVO> findMenuByUserIdAndSourceId(Long roleId, String sourceId) {
        return sysMenuMapper.findMenuByUserIdAndSourceId(roleId, sourceId);
    }

    @Override
    public Set<MenuVO> findMenuBySourceId(String sourceId) {
        return sysMenuMapper.findMenuBySourceId(sourceId);
    }

    @Override
    public int findMaxMenuId() {
        return sysMenuMapper.findMaxMenuId();
    }

    @Override
    public MenuVO getMenuVoById(Integer menuId) {
        return sysMenuMapper.getMenuVoById(menuId);
    }

    /**
     * 查询角色已勾选菜单树
     */
    @Override
    public List<MenuTreeVO> getMenuList(String entId, String roleId) {
        //获取已勾线菜单集合
        List<MenuTreeVO> sysMenuList = sysMenuMapper.getMenuList(entId, roleId);
        //用户根菜单集合
        List<MenuTreeVO> menuTreeRootList = Lists.newArrayList();
        MenuTreeVO menuTreeRoot = null;
        //获取菜单信息中的根菜单
        for (MenuTreeVO sysMenu : sysMenuList) {
            //父菜单编号为空，说明是根节点
            if (SystemConstants.SYSTEM_DEFAULT_MENU_ROOT_ID == sysMenu.getParentId()) {
                //实例化
                menuTreeRoot = new MenuTreeVO();
                menuTreeRoot.setId(sysMenu.getId());
                menuTreeRoot.setParentId(sysMenu.getParentId());
                menuTreeRoot.setIcon(sysMenu.getIcon());
                menuTreeRoot.setName(sysMenu.getName());
                menuTreeRoot.setUrl(sysMenu.getUrl());
                menuTreeRoot.setPath(sysMenu.getPath());
                menuTreeRoot.setSort(sysMenu.getSort());
                menuTreeRootList.add(menuTreeRoot);
            }
        }
        // 根据MenuTree类的order排序
        CollUtil.sort(menuTreeRootList, Comparator.comparingInt(MenuTreeVO::getSort));
        //为根菜单设置子菜单，getChildrenMenuList是递归调用的
        for (MenuTreeVO nav : menuTreeRootList) {

            //获取根节点下的所有子节点 使用getChildrenMenuList方法
            List<MenuTreeVO> childList = getChildrenMenuList(nav.getId(), sysMenuList);
            //给根节点设置子节点
            nav.setChildren(childList);

        }

        //返回菜单信息
        return menuTreeRootList;
    }

    private List<MenuTreeVO> getChildrenMenuList(String id, List<MenuTreeVO> sysMenuList) {
        //子菜单
        List<MenuTreeVO> menuTreeChildList = Lists.newArrayList();
        MenuTreeVO menuTreeChildren = null;
        for (MenuTreeVO sysMenu : sysMenuList) {
            // 遍历所有节点，将所有菜单的父id与传过来的根节点的id比较 相等说明：为该根节点的子节点。
            if (id == sysMenu.getParentId()) {
                menuTreeChildren = new MenuTreeVO();
                menuTreeChildren.setId(sysMenu.getId());
                menuTreeChildren.setParentId(sysMenu.getParentId());
                menuTreeChildren.setIcon(sysMenu.getIcon());
                menuTreeChildren.setName(sysMenu.getName());
                menuTreeChildren.setUrl(sysMenu.getUrl());
                menuTreeChildren.setPath(sysMenu.getPath());
                menuTreeChildren.setSort(sysMenu.getSort());
                menuTreeChildList.add(menuTreeChildren);
            }
        }
        //递归
        for (MenuTreeVO nav : menuTreeChildList) {
            nav.setChildren(getChildrenMenuList(nav.getId(), sysMenuList));
        }
        //如果节点下没有子节点，返回一个空List（递归退出）
        if (menuTreeChildList == null || menuTreeChildList.size() == 0) {
            return new ArrayList<MenuTreeVO>();
        } else {
            //排序
            CollUtil.sort(menuTreeChildList, Comparator.comparingInt(MenuTreeVO::getSort));
        }
        return menuTreeChildList;
    }


    @Override
    public Set<MenuVO> findProductByEntUser(Long userId, String entId) {
        return sysMenuMapper.findProductByEntUser(userId, entId);
    }


    @Override
    public List<MenuTreeVO> getMenuListByRoleId(Long roleId) {
        return sysMenuMapper.getMenuListByRoleId(roleId);
    }


    @Override
    public List<MenuTreeVO> getProductByRoleId(Long roleId) {
        return sysMenuMapper.getProductByRoleId(roleId);
    }


    @Override
    public List<SysMenu> findMenuByRoleNameAndVersion(String role, String version, String sourceId) {
        return sysMenuMapper.findMenuByRoleNameAndVersion(role, version, sourceId);
    }

    @Override
    public Set<SysMenu> findMenuByEntUserAndVersion(Long userId, String entId, String version, String sourceId) {
        return sysMenuMapper.findMenuByEntUserAndVersion(userId, entId, version, sourceId);
    }

    @Override
    public Result queryMenusByUserIdAndDeptId(String userId, String deptId) {
        log.info("根据用户id查询当前用户购买的套餐信息,请求参数userId为:{} deptId为:{}", userId, deptId);
        String sourceId = sysUserMapper.getSourceIdByUserId(Long.valueOf(userId));
        if("11010000X".equals(sourceId)){
            log.info("开始查询本地数据库菜单信息");
            //本地化部署查询所有菜单
            final List<SysMenu> menus = sysMenuMapper.selectMenusAll();
            return Result.ok().put("data",menus);
        }else{
            log.info("开始查询从辅助运营查询菜单信息");
            List<SysMenu> menus=sysMenuMapper.queryMenusByDeptId(deptId);

            return Result.ok().put("data",menus);
        }


    }

    @Override
    public Result querySysMenus() {
        return Result.ok().put("data", sysMenuMapper.querySysMenus());
    }

    @Override
    public Result queryMenusByPropertyAndDeptId(String roleProperty, String deptId) {

        if(StringUtils.isBlank(roleProperty)){
            return Result.error("角色类型不允许为空");
        }

        if(!roleProperty.equals("1")&&!roleProperty.equals("2")){
            return Result.error("角色类型只允许1和2");
        }

        if(roleProperty.equals("2")){
           if(StringUtils.isBlank(deptId)){
               return Result.error("角色类型为2时，组织ID不允许为空");
           }
        }
        List<SysMenu>  menus=new ArrayList<>();
        if(roleProperty.equals("1")){
            /**
             * 如果是中台，所属组织是平台，则类型为1，deptId为0，查询所有系统级菜单，product_id为空的菜单集合
             * 如果是中台，所属组织是渠道，则类型为1，deptId非空，查询所有渠道对应菜单
             */
            if("0".equals(deptId)){
                menus= sysMenuMapper.queryMenusSystem();
            }else{
//                Distributor distributor=distributorMapper.selectDistributorById(deptId);
//                if(distributor!=null){
//                    menus= sysMenuMapper.queryMenuByDistributorId(distributor.getId());
//                }else{
//                    return Result.error("对应渠道不存在");
//                }

                menus=sysMenuMapper.queryMenusBySystemSign("4");

                List<SysMenu>  menuList=sysMenuMapper.queryMenusBySystemSign("2");

                /**
                 * 根据渠道ID查询对应开通的产品ID
                 */
                List<String> products=sysMenuMapper.queryProductsByDistributorId(deptId);

                List<SysMenu> mentions=new ArrayList<SysMenu>();
                /**
                 * 以下产品ID为特殊判断处理
                 */
                List<String> productIds=new ArrayList<String>();
                productIds.add("a2cc30f6552542ad8d22155f96adba30");
                productIds.add("27d8f87724924b3089cc6fbd7d3684c7");
                productIds.add("036a01086f1342d48f9af733a4d521d6");
                productIds.add("ac5463eec58f4fca80d1a00802581ddc");

                if (menuList != null && menuList.size() > 0) {
                    for (SysMenu sysMenu : menuList) {
                        if (org.apache.commons.lang.StringUtils.isNotBlank(sysMenu.getProductId())) {
                            /**
                             * 判断是否开通以上4种产品，如开通才能展示对应菜单，如未开通，则不展示
                             */
                            if(products!=null&&products.size()>0){
                                for(String productId:productIds) {
                                    for (String produc:products) {
                                        if (produc.equals(productId)) {
                                            if (sysMenu.getProductId().equals(productId)) {
                                                mentions.add(sysMenu);
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            mentions.add(sysMenu);
                        }
                    }
                }

                if(mentions!=null&&mentions.size()>0){
                    menus.addAll(mentions);
                }
            }
        }else{
            /**
             * 如果是门户，所属组织是企业，则类型为2，deptId非空，查询该组织对应渠道
             */
           SysDept sysDept= sysDeptMapper.selectByDeptId(deptId);
            if(sysDept!=null){

                Map map=productService.getProductMenuByDeptId(deptId);
                if (map != null) {
                    String resp = JSON.toJSONString(map);
                    log.info("调用产品中心获取当前企业开通的套餐接口返回为：" + resp);
                    R r = JSON.parseObject(resp, R.class);
                    if (ResponseCodeUtil.CODE_SUCCESS.equals(map.get("code").toString())) {
                        if (r.getData() != null) {

                            List<ProductMenuTree>  templates = JSONArray.parseArray(r.getData().toString(),ProductMenuTree.class);

                            for(ProductMenuTree prov:templates){
                                SysMenu sysMenu= this.packageSysMenuByProductMenu(prov);
                                menus.add(sysMenu);
                            }
                        } else {
                            log.info("调用辅助运营获取当前账号已经购买的套餐信息为空");
                            return Result.error("获取套餐信息为空");
                        }
                    } else {
                        log.error("调用产品中心获取当前企业开通的套餐接口失败，返回信息" + r.getMsg());
                        return Result.error("获取套餐信息失败" + r.getMsg());
                    }
                }else{
                    log.info("根据企业ID查询对应开通产品返回为空，对应企业ID："+deptId);
                }
            }else{
                return Result.error("对应企业不存在");
            }
        }

        return Result.ok().put("data",menus);
    }

    /**
     *
     * @param prov
     * @return
     */
    public SysMenu  packageSysMenuByProductMenu(ProductMenuTree prov){

        SysMenu sysMenu=new SysMenu();

        sysMenu.setMenuId(prov.getId());
        sysMenu.setName(prov.getName());
        sysMenu.setParentId(prov.getParentId());
        sysMenu.setPermission(prov.getPermission());
        sysMenu.setPath(prov.getPath());
        sysMenu.setUrl(prov.getUrl());
        sysMenu.setIcon(prov.getIcon());
        sysMenu.setMethod(prov.getMethod());
        sysMenu.setSort(prov.getSort());
        sysMenu.setType(prov.getType());
        sysMenu.setProductId(prov.getProductId());

        return sysMenu;
    }

    @Override
    public Result queryMenusByType() {
        return Result.ok().put("data", sysMenuMapper.selectList(new EntityWrapper<SysMenu>().eq("type", "0").or().
                eq("type", "1").or().eq("type", "2")));

    }

    @Override
    public Result allMenus() {
        List<SysMenu> sysMenuEntityList = sysMenuMapper.selectList(null);
        return Result.ok().put("data", sysMenuEntityList);
    }

    @Override
    public Result listMenus(Map<String, Object> params) {
        //创建返回实体
        List<AdminMenuListDto> adminMenuListDtoList = new ArrayList<AdminMenuListDto>();

        String menuId = params.get("menuId").toString();
        int pageNo = Integer.parseInt(params.get("pageNo").toString());
        int pageSize = Integer.parseInt(params.get("pageSize").toString());
        Page page = new Page(pageNo, pageSize);
        //通过父级节点查询该节点下菜单列表
        List<SysMenu> sysMenuEntityList = sysMenuMapper.listMenusByParentMenuId(page, menuId);
        for (SysMenu sysMenuEntity : sysMenuEntityList) {
            Long userId = sysMenuEntity.getPuserId();
            SysUser sysUser = sysUserMapper.selectById(userId);
            AdminMenuListDto adminMenuListDto = toAdminMenuListDtoList(sysMenuEntity, sysUser ==null ?"":sysUser.getUsername());
            adminMenuListDtoList.add(adminMenuListDto);
        }
        //分页操作
        page.setRecords(adminMenuListDtoList);
        PageUtils pageUtils = new PageUtils(page);
        return Result.ok().put("data", pageUtils);
    }

    @Override
    public Result addMenu(SysMenu sysMenu) {
        //给管理员关联上新增菜单
        SysRole sysRole = sysRoleMapper.selectRoleByUserId(sysMenu.getPuserId());
        if(sysRole == null){
            return  Result.error("主账号没有管理员角色,不能关联菜单");
        }
        //查询菜单名称是否存在
        int num = sysMenuMapper.queryMenuNameCount(sysMenu.getName());
        if (num > 0) {
            return Result.error("名称已存在");
        }
        String menuId = getCharAndNumr(5);
        sysMenu.setMenuId(menuId);
        sysMenu.setCreateTime(new Date());
        sysMenuMapper.insert(sysMenu);

        sysMenuMapper.insertRoleMenu(sysRole.getRoleId(),menuId);

        return Result.ok();
    }

    @Override
    public Result updateMenu(SysMenu sysMenu) {
        SysMenu pMenuEntity = sysMenuMapper.selectById(sysMenu.getMenuId());
        //比较菜单名称是否变化，如有变查询新菜单名称的总记录数
        if (!sysMenu.getName().equals(pMenuEntity.getName())) {
            int num = sysMenuMapper.queryMenuNameCount(sysMenu.getName());
            if (num > 0) {
                return Result.error("名称已存在");
            }
        }
        sysMenu.setCreateTime(pMenuEntity.getCreateTime());
        sysMenu.setPuserId(pMenuEntity.getPuserId());
        sysMenuMapper.updateById(sysMenu);
        return Result.ok();
    }

    @Override
    public Result deleteMenuByMenuId(String menuId) {
        //判断是否存在子集菜单
        int num = sysMenuMapper.querySonMenuCountByMenuId(menuId);
        if (num > 0) {
            return Result.error("要删除的菜单存在级联子菜单或按钮，请先删除！");
        }
        sysMenuMapper.deleteRoleMenuByMenuId(menuId);
        sysMenuMapper.deleteById(menuId);
        return Result.ok();
    }

    @Override
    public Result menuInfo(String menuId) {
        List<SysMenu> sysMenuEntityList = new ArrayList<SysMenu>();
        SysMenu sysMenu = sysMenuMapper.selectById(menuId);
        sysMenuEntityList = queryParentMenus(sysMenu.getParentId(), sysMenuEntityList);
        sysMenuEntityList.add(sysMenu);
        AdminMenuInfoDto adminMenuInfoDto = toSysMenuEntityList(sysMenu, sysMenuEntityList);
        return Result.ok().put("data", adminMenuInfoDto);
    }


    private AdminMenuListDto toAdminMenuListDtoList(SysMenu sysMenuEntity, String pUusername) {
        AdminMenuListDto adminMenuListDto = new AdminMenuListDto();
        adminMenuListDto.setCreateTime(sysMenuEntity.getCreateTime());
        adminMenuListDto.setMenuId(sysMenuEntity.getMenuId());
        adminMenuListDto.setName(sysMenuEntity.getName());
        adminMenuListDto.setType(sysMenuEntity.getType());
        adminMenuListDto.setUrl(sysMenuEntity.getUrl());
        adminMenuListDto.setPUsername(pUusername);
        return adminMenuListDto;

    }

    private AdminMenuInfoDto toSysMenuEntityList(SysMenu sysMenu,List<SysMenu> sysMenuEntityList) {
        AdminMenuInfoDto adminMenuInfoDto = new AdminMenuInfoDto();
        adminMenuInfoDto.setMenuId(sysMenu.getMenuId());
        adminMenuInfoDto.setName(sysMenu.getName());
        adminMenuInfoDto.setParentId(sysMenu.getParentId());
        adminMenuInfoDto.setCreateTime(sysMenu.getCreateTime());
        adminMenuInfoDto.setSystemSign(sysMenu.getSystemSign());
        adminMenuInfoDto.setType(sysMenu.getType());
        adminMenuInfoDto.setUrl(sysMenu.getUrl());
        adminMenuInfoDto.setOrderNum(sysMenu.getSort());
        adminMenuInfoDto.setIcon(sysMenu.getIcon());
        adminMenuInfoDto.setSysMenuList(sysMenuEntityList);
        return  adminMenuInfoDto;
    }

    private List<SysMenu> queryParentMenus(String parentId,List<SysMenu> sysMenuEntityList) {

        if(!"0".equals(parentId)){
            SysMenu menu = sysMenuMapper.selectById(parentId);
            sysMenuEntityList.add(menu);
            queryParentMenus(menu.getParentId(),sysMenuEntityList);
        }
        return  sysMenuEntityList;
    }



    public static String getCharAndNumr(int length) {

        Random random = new Random();

        StringBuffer valSb = new StringBuffer();

        String charStr = "0123456789abcdefghijklmnopqrstuvwxyz";

        int charLength = charStr.length();



        for (int i = 0; i < length; i++) {

            int index = random.nextInt(charLength);

            valSb.append(charStr.charAt(index));

        }

        return valSb.toString();

    }



    @Override
    public Map getMenuAndButton(List<SysMenu> menus) {
        Map map = new HashMap();

        Set<SysMenu> all = new HashSet<>();
        all.addAll(menus);
        List<MenuTree> menuTreeList = new ArrayList<>();
        List<String> buttons = new ArrayList<>();
        all.forEach(sysMenu -> {
            if("1".equals(sysMenu.getType())){
                buttons.add(sysMenu.getPermission());
            }else {
                menuTreeList.add(new MenuTree(sysMenu));
            }
        });
        CollUtil.sort(menuTreeList, Comparator.comparingInt(MenuTree::getSort));
        List<MenuTree> bulid = TreeUtil.bulid(menuTreeList, -1);
        map.put("buttons",buttons);
        map.put("menuTrees",bulid);
        return map;
    }

    @Override
    public Result queryMenusByDeptId(String userId,String deptId) {

        log.info("传入组织对应ID为："+deptId);
        if(StringUtils.isBlank(deptId)){
            log.error("传入组织对应ID为空,请确认！");
            return Result.error("传入组织对应ID为空,请确认！");
        }
        if(StringUtils.isBlank(userId)){
            log.error("传入用户对应ID为空,请确认！");
            return Result.error("传入用户对应ID为空,请确认！");
        }

        List<SysUserRole> roleByUsers = sysUserRoleMapper.getUserRoleByUserId(Long.valueOf(userId));

        Set<SysMenu> sysMenuEntityList = new HashSet<>();

        if(roleByUsers!=null&&roleByUsers.size()>0){
            //根据用户角色中间查询角色
            roleByUsers.forEach(roleByUser -> {
                SysRoleVo sysRole = sysRoleMapper.selectByRoleId(roleByUser.getRoleId());
                if(sysRole!=null&&deptId.equals(sysRole.getDeptId())){

                    //查询角色对应的菜单集合
                    List<String> menuIdList = sysRoleMapper.queryMenuIdListByRoleId(roleByUser.getRoleId());

                    Set<String> products = new HashSet<>();

                    for(String menid:menuIdList){
                        ProductMenuTree prov=sysMenuMapper.selectProductMenuByMenuId(menid);

                        if(prov!=null&& org.apache.commons.lang.StringUtils.isNotBlank(prov.getProductId())){
                            products.add(prov.getProductId());
                            SysMenu sysMenu= this.packageSysMenuByProductMenu(prov);
                            sysMenuEntityList.add(sysMenu);
                        }else{
                            products.add(menid);
                        }
                    }
                }
            });
        }

        return Result.ok().put("data",sysMenuEntityList);
    }

    /**
     *
     * @param productInfo
     * @return
     */
    public SysMenu packageSysMenuByProductInfo(ProductInfo productInfo){

        SysMenu sysMenu=new SysMenu();
        sysMenu.setMenuId(productInfo.getId());
        sysMenu.setName(productInfo.getName());
        sysMenu.setParentId("-1");
        sysMenu.setIcon(productInfo.getIcon());
        sysMenu.setProductId(productInfo.getId());
        return sysMenu;
    }


    @Override
    public List<SysMenu> getMenuListByRole(Long roleId){
        List<SysRoleMenu> roleMenus = sysRoleMenuService.getRoleMenuByRoleId(roleId);
        Set<SysMenu> sysMenus = new HashSet<>();
        roleMenus.forEach(roleMenu ->{
            SysMenu sysMenu = selectMenuByMenuId(roleMenu.getMenuId());
            if(sysMenu != null){
                sysMenus.add(sysMenu);
            }
        });
        return new ArrayList<>(sysMenus);
    }

    public void delRoleMenu(String distributorId,Long roleId){
        List<ProductInfo> productInfos = productService.getListByDistributorId(distributorId, "1");
        if(productInfos != null && !productInfos.isEmpty()){
            productInfos.forEach( productInfo -> {
                List<SysProductMenu> sysProductMenus = productService.selectByProductId(productInfo.getId());
                if(sysProductMenus != null && !sysProductMenus.isEmpty()){
                    sysProductMenus.forEach(sysProductMenu -> {
                        //查询菜单详情
                        SysRoleMenu roleMenu = new SysRoleMenu();
                        roleMenu.setRoleId(roleId);
                        roleMenu.setMenuId(sysProductMenu.getMenuId());
                        sysRoleMenuService.delRoleMenu(roleMenu);
                    });
                }
            });
        }
    }



    public SysMenu selectMenuByMenuId(String menuId){
        return sysMenuMapper.selectMenuByMenuId(menuId);
    }

    public static void main(String[] args){
          String json="{\n" +
                  "    \"code\": \"0000\",\n" +
                  "    \"message\": \"SUCCESSFULL!\",\n" +
                  "    \"data\": [\n" +
                  "        {\n" +
                  "            \"id\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"parentId\": \"-1\",\n" +
                  "            \"name\": \"风险管家\",\n" +
                  "            \"permission\": null,\n" +
                  "            \"path\": null,\n" +
                  "            \"url\": null,\n" +
                  "            \"icon\": null,\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"productName\": \"风险管家\",\n" +
                  "            \"sort\": 1,\n" +
                  "            \"type\": \"1\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"37GBWHJIaV\",\n" +
                  "            \"parentId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"name\": \"而亲仁\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"productName\": \"风险管家\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"R333sWkDjj\",\n" +
                  "            \"parentId\": \"37GBWHJIaV\",\n" +
                  "            \"name\": \"3232232\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"productName\": \"风险管家\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"8ffGEx5Ofx\",\n" +
                  "            \"parentId\": \"37GBWHJIaV\",\n" +
                  "            \"name\": \"阿达撒\",\n" +
                  "            \"permission\": \"取234\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"productName\": \"风险管家\",\n" +
                  "            \"sort\": 3,\n" +
                  "            \"type\": \"1\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"EjkbEvDrAV\",\n" +
                  "            \"parentId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"name\": \"啊飒飒\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"e5863fcf5a6a491b9cdc07352e852f6f\",\n" +
                  "            \"productName\": \"风险管家\",\n" +
                  "            \"sort\": 3,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"parentId\": \"-1\",\n" +
                  "            \"name\": \"自研产品\",\n" +
                  "            \"permission\": null,\n" +
                  "            \"path\": null,\n" +
                  "            \"url\": null,\n" +
                  "            \"icon\": null,\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"productName\": \"自研产品\",\n" +
                  "            \"sort\": 1,\n" +
                  "            \"type\": \"1\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"lfa2ENmqSD\",\n" +
                  "            \"parentId\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"name\": \"1111111\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"productName\": \"自研产品\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"QESa7pg3DM\",\n" +
                  "            \"parentId\": \"lfa2ENmqSD\",\n" +
                  "            \"name\": \"22222\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"productName\": \"自研产品\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"JuGauLc1aH\",\n" +
                  "            \"parentId\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"name\": \"3333333333\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"f8edc0b6d626497d9e7bf7f55ed2a6d3\",\n" +
                  "            \"productName\": \"自研产品\",\n" +
                  "            \"sort\": 3,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"parentId\": \"-1\",\n" +
                  "            \"name\": \"销项开票\",\n" +
                  "            \"permission\": null,\n" +
                  "            \"path\": null,\n" +
                  "            \"url\": null,\n" +
                  "            \"icon\": null,\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 1,\n" +
                  "            \"type\": \"1\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"3yfhg7HQZ1\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"用户引导\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/new/noviceGuide\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"http://10.1.5.48:8888/api-gateway/omp-common-service/omp-common-service/file/download/singlefile?id=7e5864629eca444888bfceb38059f6e7\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"gPURjpzVD7\",\n" +
                  "            \"parentId\": \"WgQpY2x30P\",\n" +
                  "            \"name\": \"原始订单\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/order/originalOrder\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"dHa9PAMdWD\",\n" +
                  "            \"parentId\": \"dJuop3JbOC\",\n" +
                  "            \"name\": \"二维码管理\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/scanInvoice/qrcodeManagement\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 2,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"WgQpY2x30P\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"订单开票\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 3,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"6D2EK1zlSN\",\n" +
                  "            \"parentId\": \"WgQpY2x30P\",\n" +
                  "            \"name\": \"订单处理\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/order/orderList\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 3,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"zwXFCzah6a\",\n" +
                  "            \"parentId\": \"dJuop3JbOC\",\n" +
                  "            \"name\": \"审核开票\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/scanInvoice/auditInvoice\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 3,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"qRdKDGYeAH\",\n" +
                  "            \"parentId\": \"WgQpY2x30P\",\n" +
                  "            \"name\": \"异常订单\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/order/abnormalList\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 4,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"dJuop3JbOC\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"扫码开票\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 4,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"lfgNYgX3M0\",\n" +
                  "            \"parentId\": \"dJuop3JbOC\",\n" +
                  "            \"name\": \"扫码开票\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/scanInvoice/scanInvoice\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 4,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"eNyVkLG7e1\",\n" +
                  "            \"parentId\": \"WgQpY2x30P\",\n" +
                  "            \"name\": \"订单查询\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/order/orderStatusStatistics\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 5,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"ubRDVUeTV0\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"发票填开\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 5,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"rePoCQ0bTG\",\n" +
                  "            \"parentId\": \"WgQpY2x30P\",\n" +
                  "            \"name\": \"订单处理（供应链）\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"http://10.1.1.221:8081/index.html#/order/orderListStatus\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 6,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"7UawaHbfvS\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"发票管理\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 6,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"15Hc35pJNM\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"预警监控\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 7,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"RCTCuMT8xr\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"数据统计\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 8,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"9OVuDM25QI\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"基础设置\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 9,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"98e2MI61bl\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"运维管理\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 10,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"89XLj3NKoY\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"税控管理\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 11,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"TqjRvt0Nml\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"系统设置\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 12,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        },\n" +
                  "        {\n" +
                  "            \"id\": \"T4QRTmzrI7\",\n" +
                  "            \"parentId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"name\": \"安全管理平台\",\n" +
                  "            \"permission\": \"\",\n" +
                  "            \"path\": \"\",\n" +
                  "            \"url\": \"\",\n" +
                  "            \"icon\": \"\",\n" +
                  "            \"method\": null,\n" +
                  "            \"productId\": \"1e58db09d3ac4e82989428cf76e7992e\",\n" +
                  "            \"productName\": \"销项开票\",\n" +
                  "            \"sort\": 13,\n" +
                  "            \"type\": \"0\",\n" +
                  "            \"children\": []\n" +
                  "        }\n" +
                  "    ]\n" +
                  "}";
        R r = JSON.parseObject(json, R.class);
        System.out.println(r.getData());
        JSONArray.parseArray(r.getData().toString());
        for(int i=0;i<JSONArray.parseArray(r.getData().toString()).size();i++){
            JSONArray.parseArray(r.getData().toString()).get(i);
            System.out.println(JSONArray.parseArray(r.getData().toString()).get(i));
        }
    }

}
