package com.dxhy.core.service;

import com.dxhy.core.pojo.entity.Distributor;
import com.dxhy.core.pojo.entity.ProductInfo;
import com.dxhy.core.pojo.entity.SysProductMenu;

import java.util.List;
import java.util.Map;


public interface ProductService {

    /**
     *
     * @param deptId
     * @return
     */
    public Map getProductMenuByDeptId(String deptId);


    List<ProductInfo> getListByDistributorId(String distributorId, String s);

    Distributor getDistributorByID(String distributorId);


    List<SysProductMenu> selectByProductId(String id);
}
