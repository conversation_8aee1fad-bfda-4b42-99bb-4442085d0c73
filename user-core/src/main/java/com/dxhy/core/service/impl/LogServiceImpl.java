/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.LogMapper;
import com.dxhy.core.pojo.entity.SysLog;
import com.dxhy.core.pojo.vo.SysLogVo;
import com.dxhy.core.service.ILogService;
import com.dxhy.core.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/30 17:05
 */
@Slf4j
@Service
public class LogServiceImpl extends ServiceImpl<LogMapper, SysLog> implements ILogService {


	@Override
	public Result listByPage(SysLogVo log) {
		int pageNo = log.getPageNo();
		int pageSize = log.getPageSize();
		Page page = new Page(pageNo, pageSize);
		page = this.selectPage(page, new EntityWrapper<SysLog>()
				.eq("tenant_id", log.getTenantId())
		);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data", pageUtils);
	}
}