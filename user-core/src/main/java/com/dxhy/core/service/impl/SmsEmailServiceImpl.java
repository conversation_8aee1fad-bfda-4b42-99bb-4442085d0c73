package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.pojo.DTO.EmailDto;
import com.dxhy.core.pojo.vo.EmailVo;
import com.dxhy.core.pojo.vo.SmsSessionContextVo;
import com.dxhy.core.pojo.vo.SmsSingleVo;
import com.dxhy.core.service.SmsEmailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Enumeration;
import java.util.UUID;

@Slf4j
@Service("SmsEmailService")
public class SmsEmailServiceImpl implements SmsEmailService {
    @Value("${sms.platformNo}")
    private String platformNo;

    @Value("${sms.platformKey}")
    private String platformKey;

    @Value("${sms.smsUrl}")
    private String smsUrl;

    @Value("${email.emailUrl}")
    private String emailUrl;

    @Value("${webhook.wxurl}")
    private String webhookUrl;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 发送单条短信验证码
     * @param desmobile  目标手机号
     * @param content 短信内容
     * @param type 短信类型 1：验证码 2：通知  5:语音验证码
     * @return
     * <AUTHOR>
     * @date 2022-08-09
     */
    @Override
    public Boolean sendSingleSms(String desmobile, String content, int type) {
        //封装上下文
        SmsSessionContextVo sscvo = new SmsSessionContextVo();
        SimpleDateFormat format = new SimpleDateFormat("YYYYMMDDHHmmss");
        String timestamp = format.format(new Date());
        sscvo.setPostingDateText(timestamp);
        sscvo.setValueDateText(timestamp);
        sscvo.setLocalDateTimeText(timestamp);
        String uuid= UUID.randomUUID().toString();
        sscvo.setExternalReferenceNo(uuid);
        sscvo.setOriginalReferenceNo(uuid);
        sscvo.setUserReferenceNumber(uuid);
        //封装短信主体
        SmsSingleVo smsvo = new SmsSingleVo();
        smsvo.setSessionContext(sscvo);
        smsvo.setPlatformNo(platformNo);
        smsvo.setPlatformKey(platformKey);
        smsvo.setPhone(desmobile);
        smsvo.setIpAddress(getHostIp());
        smsvo.setContent(content);
        smsvo.setMsgType(type);
        smsvo.setChannel("");
        JSONObject json = (JSONObject) JSONObject.toJSON(smsvo);

        //请求
        CommonRspVo result = sendPostSmsRequest(smsUrl,json);
        if (result.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            return true;
        }

        return false;
    }
    /**
     * 发送短信请求
     * @param smsUrl
     * @param json
     * @return
     * <AUTHOR>
     * @date 2022-08-09
     */
    private CommonRspVo sendPostSmsRequest(String smsUrl, JSONObject json) {
        log.info("【短信邮件】请求参数：{}",json);
        ResponseEntity<JSONObject> entity = restTemplate.postForEntity(smsUrl,json,JSONObject.class);
        JSONObject response = entity.getBody();
        log.info("【短信邮件】请求响应：{}",response);
        JSONObject result = response.getJSONObject("transactionStatus");
        Boolean issuccess = (Boolean) result.get("success");
        if (issuccess) {
            log.info("【短信邮件】请求发送，响应成功");
            return new CommonRspVo(ResponseCodeEnum.SUCCESS);
        }else{
            log.info("【短信邮件】请求发送，响应失败");
            String code = result.get("replyCode").toString();
            String msg = result.get("replyText").toString();
            return new CommonRspVo(code,msg);
        }
    }

    /** 获取主机地址 */
    public static String getHostIp(){
        String realIp = null;
        try {
            InetAddress address = InetAddress.getLocalHost();
            // 如果是回环网卡地址, 则获取ipv4 地址
            if (address.isLoopbackAddress()) {
                address = getInet4Address();
            }
            realIp = address.getHostAddress();
            log.info("获取主机ip地址成功, 主机ip地址:{}", address);
            return address.getHostAddress();
        } catch (Exception e) {
            log.error("获取主机ip地址异常", e);
        }
        return realIp;
    }
    /** 获取IPV4网络配置 */
    private static InetAddress getInet4Address() throws SocketException {
        // 获取所有网卡信息
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
        while (networkInterfaces.hasMoreElements()) {
            NetworkInterface netInterface = (NetworkInterface) networkInterfaces.nextElement();
            Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
            while (addresses.hasMoreElements()) {
                InetAddress ip = (InetAddress) addresses.nextElement();
                if (ip instanceof Inet4Address) {
                    return ip;
                }
            }
        }
        return null;
    }


    /**
     * 发邮件
     *
     * @return
     * <AUTHOR>
     * @date 2022-08-22
     */
    public CommonRspVo sendEmail(EmailDto emailDto) {
        log.info("邮件发送：接收参数{}",emailDto);
        //封装上下文
        SmsSessionContextVo sscvo = new SmsSessionContextVo();
        SimpleDateFormat format = new SimpleDateFormat("YYYYMMDDHHmmss");
        String timestamp = format.format(new Date());
        sscvo.setPostingDateText(timestamp);
        sscvo.setValueDateText(timestamp);
        sscvo.setLocalDateTimeText(timestamp);
        String uuid= UUID.randomUUID().toString();
        sscvo.setExternalReferenceNo(uuid);
        sscvo.setOriginalReferenceNo(uuid);
        sscvo.setUserReferenceNumber(uuid);

        //封装邮件主体
        emailDto.setIpAddress(getHostIp());
        EmailVo emailVo = new EmailVo();
        emailVo.setSessionContext(sscvo);
        emailVo.setPlatformNo(platformNo);
        emailVo.setPlatformKey(platformKey);
        emailVo.setChannel("");
        BeanUtils.copyProperties(emailDto,emailVo);
        JSONObject json = (JSONObject) JSONObject.toJSON(emailVo);

        //请求
        CommonRspVo result = sendPostSmsRequest(emailUrl,json);
        log.info("邮件发送：返回参数{}",result);
        return result;
    }

    /**
     * 企业微信机器人报警
     * @param content
     * @return
     */
    @Override
    public CommonRspVo webhookSend(String content) {
        JSONObject json = new JSONObject();
        json.put("msgtype","text");
        JSONObject text = new JSONObject();
        text.put("content",content);
        json.put("text",text);
        log.info("【企业微信机器人报警】请求参数：{}",json);
        ResponseEntity<JSONObject> entity = restTemplate.postForEntity(webhookUrl,json,JSONObject.class);
        JSONObject response = entity.getBody();
        log.info("【企业微信机器人报警】请求响应：{}",response);
        int errcode = response.getInteger("errcode");
        String errmsg =  response.getString("errmsg");
        if (errcode==0) {
            return new CommonRspVo(ResponseCodeEnum.SUCCESS);
        }else{
            return new CommonRspVo(errcode+"",errmsg);
        }
    }

}
