package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.dxhy.core.constants.ProductConstant;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.ProductMenuTree;
import com.dxhy.core.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ProductServiceImpl implements ProductService {
    @Autowired
    private IProductInfoService productService;
    @Autowired
    private IComboAccountService comboAccountService;
    @Autowired
    private IProductMenuService productMenuService;

    @Autowired
    private IComboAccountService.IComboDistributorService comboDistributorService;

    @Resource
    private WebProductMenuService webProductMenuService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IDistributorService distributorService;

    @Override
    public Map getProductMenuByDeptId(String deptId) {
        log.info("通过组织ID查询产品菜单，参数：{}", deptId);
        List list = new ArrayList();
        // 1、通过deptId查询企业信息，判断企业级别，主企业则需要使用createBy查询ComboAccount数，子企业则直接使用deptId查询ComboAccount，status=0为正常数据
        Map map = productService.selectSysDeptByDeptId(deptId);
        if(map != null) {
            int level = (int) map.get("level");
            int deptType = (int)map.get("deptType"); // 部门
            Map paramMap = new HashMap();
            if(level == 1 || deptType == 4) {
                // 主企业
                Long createUser = (Long) map.get("createUser"); // 主企业创建人ID
                paramMap.put("account_info_id", createUser +"");
            } else {
                // 子企业
                paramMap.put("customer_id", deptId);
            }
            List<ComboAccount> comboAccountList = comboAccountService.queryComboAccount(paramMap);
            if(comboAccountList != null && comboAccountList.size()!=0) {

                if(level ==1) {
                    // 剔除给子企业分配的数据
                    comboAccountList = comboAccountList.stream().filter(comboAccount -> StringUtils.isBlank(comboAccount.getCustomerId())).collect(Collectors.toList());
                }
                //2、 步骤1查询出List<ComboAccount>,遍历通过combo_distribute_id查询combo_distribute数据 ，statu=0为正常数据，通过template_id字段关联查询combo_template中id,status=0,获取到sell_babel_id
                comboAccountList.forEach(comboAccount -> {
                    ProductInfo productInfo = productService.selectById(comboAccount.getProductId());
                    if(productInfo != null && "1".equals(productInfo.getProductSource()) && !"3".equals(productInfo.getProductClass())) {
                        String sellLabelId = comboDistributorService.getSellLabelIdByComboDistributorId(comboAccount.getComboDistributorId());
                        Wrapper wrapper = new EntityWrapper();
                        wrapper.eq("product_id", comboAccount.getProductId());
                        wrapper.eq("status","0");
                        List<ProductMenu> productMenus = productMenuService.selectList(wrapper);
                        // 通过deptId查询税控类型，按照过滤规则进行过滤
                        productMenus = filterProudctMenus(comboAccount.getProductId(), productMenus, deptId);
                        log.info("{} 根据产品id获取产品所有菜单，返回值：{}", productMenus);
                        if (StringUtils.isEmpty(sellLabelId)){
                            //list.add(productMenus);
                            log.info("{} 根据comboDistributorId未获取到产品售卖类型,comboDistributorId：{}", comboAccount.getComboDistributorId());
                        } else {
                            List<ProductMenuTree> productMenuByProductId = webProductMenuService.getProductMenuBySellLabelId(sellLabelId,productMenus,comboAccount.getProductId());
                            list.addAll(productMenuByProductId);
                            log.info("{} 根据产品id和售卖类型id获取菜单，返回值：{}", productMenuByProductId);
                        }
                    } else {
                        // 第三方产品剔除
                    }

                });
            }
        }
        //3、通过sell_label_id，查询product_menu_sell中sell_label_id，通过product_menu_id关联查询product_menu中id并status=0
        return null;
    }

    @Override
    public List<ProductInfo> getListByDistributorId(String distributorId, String status) {
        List<ProductInfo> productInfoList = productService.selectListByDistributorId(distributorId,status,"","","");
        return productInfoList;
    }

    @Override
    public Distributor getDistributorByID(String id) {
        log.info("查询分销商, id:{}", id);
        Distributor distributor = distributorService.selectId(id);
        log.info("查询分销商, id:{}, 响应信息:{}", id, distributor);
        return distributor;
    }

    @Override
    public List<SysProductMenu> selectByProductId(String id) {
        return null;
    }

    /**
     *  过滤产品菜单
     *  @param productId
     *      *                  当前产品ID
     * @param productMenus
     *                         当前产品的全量菜单
     * @param deptId
     *                          组织ID
     */
    private List<ProductMenu> filterProudctMenus(String productId, List<ProductMenu> productMenus, String deptId) {

        /**
         * http://127.0.0.1:8088/aosp-product/productMenu/getProductMenuByDeptId?deptId=77bcbeb23063428687a09aba2509fcf4
         * 销项产品处理 - 数控设备菜单处理
         *  1、 根据组织ID查询对应税控设备类型
         *  2、 查询税控设备对应菜单列表
         *  3、 剔除全量菜单中不符合条件的菜单
         *  4、 返回过滤后的菜单列表
         */
        if(!Optional.ofNullable(productMenus).isPresent()) {
            return productMenus;
        }
        if(ProductConstant.PRODUCTID_XIAOXIANGKAIPIAO.equals(productId)) {
            String sksbbm = productService.selectTaxControlByDeptId(deptId);

            ProductMenu skglMenu = productMenus.stream().filter(productMenu -> {return "税控管理".equals(productMenu.getName());})
                    .findFirst().get();
            if(!Optional.ofNullable(skglMenu).isPresent()) {
                log.info("未获取到税控管理菜单，检查销项开票是否已配置税控管理菜单，deptId:{}", deptId);
                log.info("未获取到税控管理菜单，检查销项开票是否已配置税控管理菜单，deptId:{}", deptId);
                log.info("未获取到税控管理菜单，检查销项开票是否已配置税控管理菜单，deptId:{}", deptId);
                return productMenus;
            }

            if(StringUtils.isBlank(sksbbm)) {
                log.info("企业未获取到税控设备，不返回税控管理菜单，deptId:{}", deptId);
                log.info("企业未获取到税控设备，不返回税控管理菜单，deptId:{}", deptId);
                log.info("企业未获取到税控设备，不返回税控管理菜单，deptId:{}", deptId);
                //
                productMenus = productMenus.stream()
                        .filter(productMenu -> !skglMenu.getId().equals(productMenu.getId())) // 过滤数控管理菜单
                        .filter(productMenu -> !skglMenu.getId().equals(productMenu.getParentId())) // 过滤所有子菜单
                        .collect(Collectors.toList());

            } else {
                Set<String> menuSet = stringRedisTemplate.opsForSet().members(ProductConstant.OMP_SKGL_PERFIX + sksbbm);
                if(menuSet == null || menuSet.size() == 0) {
                    log.info("从缓存获取税控管理菜单配置为空，开始从数据库中获取，sksbbm:{}", sksbbm);
                    Dictionary dictionary = productService.getByFlag("sksbbm" + sksbbm);
                    if(Optional.ofNullable(dictionary).isPresent()) {
                        String desc = dictionary.getDesc();
                        log.info("从数据库中获取税控管理菜单配置为:{},sksbbm:{}", desc, sksbbm);
                        Long l = stringRedisTemplate.opsForSet().add(
                                ProductConstant.OMP_SKGL_PERFIX + sksbbm,desc.split("\\|"));
                        log.info("从数据库中获取税控管理菜单成功并插入缓存:{},sksbbm:{}", l, sksbbm);
                        menuSet = stringRedisTemplate.opsForSet().members(ProductConstant.OMP_SKGL_PERFIX + sksbbm);
                    } else {
                        log.info("从数据库中获取税控管理菜单配置为空，请检查是否已进行初始化配置，sksbbm:{}", sksbbm);
                        log.info("从数据库中获取税控管理菜单配置为空，请检查是否已进行初始化配置，sksbbm:{}", sksbbm);
                        log.info("从数据库中获取税控管理菜单配置为空，请检查是否已进行初始化配置，sksbbm:{}", sksbbm);
                        menuSet = new HashSet<>();
                    }

                }
                Set<String> lamdaMenuSet = menuSet;
                // 如果不存在集合里，删除该菜单
                productMenus.removeIf(productMenu -> (
                                !lamdaMenuSet.contains(productMenu.getName()) &&  // 删除不在范围内的菜单 ↓
                                        skglMenu.getId().equals(productMenu.getParentId()) && // 并且是属于税控管理下的子菜单 ↓
                                        !skglMenu.getId().equals(productMenu.getId()) // 并且不能删除税控管理菜单自身
                        )
                );
            }
        } else {
            // 其他产品菜单处理，暂未需求
        }

        return productMenus;
    }
}
