/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.AdminUserOperateDto;
import com.dxhy.core.pojo.DTO.CodeDTO;
import com.dxhy.core.pojo.DTO.EntUserDTO;
import com.dxhy.core.pojo.DTO.SaasTenantData;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.entity.SystemVersion;
import com.dxhy.core.pojo.vo.UserVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2017/10/31
 */
public interface SysUserService extends IService<SysUser> {
    /**
     * api 查询用户信息
     * @param userId
     * @return
     */
    Result queryUserInfo(Long userId);

    /**
     * 通过userid判断用户对应的企业是否激活状态
     * @param userId
     * @return
     */
    R<Boolean> getUserStatusByUserId(Long userId);

    UserVO findUserByUsername(String username);

    /**
     *  注册
     * @param entUserDTO
     * @return
     */
    R register(EntUserDTO entUserDTO) throws Exception;

    /**
     * 清除用户缓存
     * @param userId
     * @return
     */
    void redisDeleteByUserId(Long userId);

    /**
     * 判断用户是否开通当前产品权限
     * @param userId
     * @param productId
     * @param sourceId
     * @return
     */
    R getPermissionCheck(Long userId,String productId,String sourceId);

    /**
     * 通过手机号查询用户信息
     * @param mobile 手机号
     * @return 用户信息
     */
    UserVO findUserByMobile(String mobile);

    /**
     * 神策登录事件
     * @param entUserDTO
     * @param deviceId
     * @return
     */
    R<Boolean> sensorsLogin(EntUserDTO entUserDTO,String deviceId,String userType);

    R getSourceInfoByUri (String uri);

    R getProductInfoById (String productId);

    /**
     *  校验码验证
     * @param mobile
     * @param email
     * @Param uuid
     * @param code
     * @return
     */
    String validateCode(String mobile, String email,String uuid, String code);

    R<Boolean> validateCode(String mobile, String email, String code);
    void saveImageCode(String randomStr, String text);

    Result getDeptInfo(Long userId);

    /**
     *  设置密码
     * @param paramMap
     * @return
     */
    R<Boolean> settingPasswrod(Map<String, String> paramMap, Long userId);
    /**
     *  修改密码
     * @param paramMap
     * @return
     */
    R<Boolean> updatePassword(Map<String, String> paramMap, Long userId) throws Exception;

    UserVO selectUserVoById(Long userId);

    /**
     *  发送验证码，手机号 or 邮箱
     * @param codeDTO
     * @return
     */
    R<Boolean> sendCode(CodeDTO codeDTO);

    String getSimpeCodeByUnameAndTenid(String username, String tenantId);

    R<Boolean> sendWeChatCode(CodeDTO codeDTO);

    UserVO selectUserVoByContactPhone(String username);

    /**
     * 根据部门id 账号 邮箱搜索账号
     * @param params
     * @return
     */
    Result listUsersByDeptId(Map<String, Object> params);

    /**
     * 根据用户id 查询用户详情
     * @param userId
     * @return
     */
    Result userInfo(Long userId);

    /**
     * 用户新增
     * @param adminUserOperateDto
     * @return
     */
    Result addUser(AdminUserOperateDto adminUserOperateDto) throws Exception;

    /**
     * 用户信息修改
     * @param adminUserOperateDto
     * @return
     */
    Result updateUserByUserId(AdminUserOperateDto adminUserOperateDto)throws Exception;

    /**
     * 删除用户
     * @param sysUser 用户
     * @return boolean
     */
    Boolean deleteUserById(SysUser sysUser);

    /**
     * 忘记密码
     * @param paramMap
     * @return
     */
    R<Boolean> forgetPassword(Map<String, String> paramMap, String username) throws Exception;

    /**
     * 绑定手机号、邮箱
     * @param userDto
     * @param username
     * @return
     */
    R<Boolean> bind(EntUserDTO userDto, String username);

    /**
     *  重置密码
     * @param userId
     * @return
     */
    R resetPassword(Long userId, String username,String newPassword,String email) throws Exception;

    /**
     * 查询用户的销项税控菜单
     * @param userId
     * @return
     */
    Result getUserSKMenuList(Long userId);

    List<SystemVersion> versionStatus(Long userId);

    int updateVersionStatus(Long userId,Integer versionStatus);

    void dualSendEmail(String email, String content,String title);

    List<String> getAuthNameByTaxno(String taxNo, Boolean flag);

    Result syncSaasTenant(SaasTenantData saasTenantData, String dataId) throws Exception;

    CommonRspVo checkTenantInfo(SaasTenantData saasTenantData);

    Result syncUserInfo(Result result,String deptId,String userType) throws Exception;

    Result updateUserInfo(AdminUserOperateDto adminUserOperateDto) throws Exception;

    Result syncUserInfo2(Result res, String productId);

	List<Long> getUserIdList(String tenantId);
}
