package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.hw.TenantAuthInfoEntity;
import com.dxhy.core.pojo.hw.TenantInfoEntity;
import com.dxhy.core.pojo.hw.TenantRequestDto;

/**
 * 租户信息同步
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface TenantAuthInfoService extends IService<TenantAuthInfoEntity> {
    Result hwAuthSync(TenantRequestDto tenantRequestDto,int flag);

    Result InitRole(TenantInfoEntity tenantId);

    Result delUser(TenantAuthInfoEntity tenantAuthInfoEntity);

}
