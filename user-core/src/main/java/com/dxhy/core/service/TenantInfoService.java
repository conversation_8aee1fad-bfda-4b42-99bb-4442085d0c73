package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.hw.TenantInfoEntity;
import com.dxhy.core.pojo.hw.TenantTokenVo;

/**
 * 租户信息同步
 * <AUTHOR>
 * @date 2023-04-19
 */
public interface TenantInfoService extends IService<TenantInfoEntity> {
    Result transfromDisData(TenantInfoEntity tenantInfoEntity);

    TenantTokenVo getAccessTokenParam(String hwTenantId);

    TenantInfoEntity getAppKeyAndSecret(String taxNo);

    TenantInfoEntity getSecretKey(String secretId);
}
