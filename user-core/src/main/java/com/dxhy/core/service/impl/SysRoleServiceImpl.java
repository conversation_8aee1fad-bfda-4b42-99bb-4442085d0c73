/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.common.util.Query;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.DTO.*;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.SysDeptVo;
import com.dxhy.core.pojo.vo.SysRoleVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.dxhy.core.utils.GenerateRoleCodeUtil.generateNum;


/**
 * <p>
 * 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Service
@Slf4j
public class SysRoleServiceImpl extends ServiceImpl<SysRoleMapper, SysRole> implements SysRoleService {
    @Autowired
    private SysRoleDeptMapper sysRoleDeptMapper;
    @Autowired
    private SysRoleMapper sysRoleMapper;
	@Autowired
	private SysDeptMapper sysDeptMapper;
	@Autowired
	private SysUserMapper sysUserMapper;
	@Autowired
	private SysMenuMapper sysMenuMapper;
    @Autowired
	private SysUserRoleMapper sysUserRoleMapper;

	@Autowired
	private SysDeptService sysDeptService;

	@Autowired
	private SysMenuService sysMenuService;

	@Autowired
	private SysUserService sysUserService;

    @Autowired
    private BaseEntRoleService baseEntRoleService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;

	@Autowired
	private DistributorMapper distributorMapper;
	@Autowired
	private ProductMenuMapper productMenuMapper;

    /**
     * 添加角色
     *
     * @param roleDto 角色信息
     * @return 成功、失败
     */
    @Override
    public Boolean insertRole(RoleDTO roleDto) {
        SysRole sysRole = new SysRole();
        BeanUtils.copyProperties(roleDto, sysRole);
        sysRoleMapper.insert(sysRole);
        SysRoleDept roleDept = new SysRoleDept();
        roleDept.setRoleId(sysRole.getRoleId());
        roleDept.setDeptId(roleDto.getRoleDeptId());
        sysRoleDeptMapper.insert(roleDept);
        return true;
    }

    /**
     * 分页查角色列表
     *
     * @param query   查询条件
     * @param wrapper wapper
     * @return page
     */
    @Override
    public Page selectwithDeptPage(Query<Object> query, EntityWrapper<Object> wrapper) {
        query.setRecords(sysRoleMapper.selectRolePage(query, query.getCondition()));
        return query;
    }

    /**
     * 更新角色
     *
     * @param roleDto 含有部门信息
     * @return 成功、失败
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean updateRoleById(RoleDTO roleDto) {
        //删除原有的角色部门关系
        SysRoleDept condition = new SysRoleDept();
        condition.setRoleId(roleDto.getRoleId());
        sysRoleDeptMapper.delete(new EntityWrapper<>(condition));

        //更新角色信息
        SysRole sysRole = new SysRole();
        BeanUtils.copyProperties(roleDto, sysRole);
        sysRoleMapper.updateById(sysRole);

        //维护角色部门关系
        SysRoleDept roleDept = new SysRoleDept();
        roleDept.setRoleId(sysRole.getRoleId());
        roleDept.setDeptId(roleDto.getRoleDeptId());
        sysRoleDeptMapper.insert(roleDept);
        return true;
    }

    /**
     * 通过部门ID查询角色列表
     *
     * @param deptId 部门ID
     * @return 角色列表
     */
    @Override
    public List<SysRole> selectListByDeptId(Integer deptId) {
        return sysRoleMapper.selectListByDeptId(String.valueOf(deptId));
    }
    /**
     * ##############################################################################
     * 企业ID查询角色列表
     */
	@Override
	public List<SysRoleVo> getEntRoleList(String entId, String roleName) {
		return sysRoleMapper.getEntRoleList(entId,roleName);
	}

	/**
	 * ################
	 * 通过企业ID和用户id查询角色列表
	 */
	@Override
	public List<SysRoleVo> getUserAndEntRoleList(String entId, String userId) {
		return sysRoleMapper.getUserAndEntRoleList(entId,userId);
	}

	@Override
	public Result listRolesByDeptId(Map<String, Object> params) {
		//创建返回实体
		List<AdminRoleListDto> adminRoleListDtoList = new ArrayList<AdminRoleListDto>();
		String deptId = params.get("deptId").toString();
		String roleName = params.get("roleName").toString();
		Long loginUserId = (Long) params.get("loginUserId");

		if(StringUtils.isBlank(deptId)){
			return Result.error("根据企业查询组织下角色deptId不允许为空！");
		}

		//查询部门名称
		SysDept sysDept = selectDeptOrDistr(deptId);
		//分页操作
		int pageNo = Integer.parseInt(params.get("pageNo").toString());
		int pageSize = Integer.parseInt(params.get("pageSize").toString());
		Page page = new Page(pageNo, pageSize);


		List<SysRoleVo> sysRoles =sysRoleMapper.queryRoleListByDeptId(page,deptId,roleName,loginUserId);
		if (sysRoles.size()==0) {
			sysRoles =sysRoleMapper.queryRoleListByDeptId(page,sysDept.getTenantId(),roleName,loginUserId);
		}

		for (SysRoleVo sysRoleVo : sysRoles){
			//查询组织下角色对应的用户个数  roleId  -> sys_user_role
			int userCount = sysRoleMapper.queryRoleOfUserCountByRoleId(sysRoleVo.getRoleId());
			AdminRoleListDto adminRoleListDto = toAdminRoleListDto(sysRoleVo, sysDept,userCount);
			adminRoleListDtoList.add(adminRoleListDto);
		}

		//分页操作
		page.setRecords(adminRoleListDtoList);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data",pageUtils);
	}

	@Override
	public Result listRoles(Map<String, Object> params) {
		int pageNo = Integer.parseInt(params.get("pageNo").toString());
		int pageSize = Integer.parseInt(params.get("pageSize").toString());
		Map<String,Object> queryParams = new HashMap<>();
		queryParams.put("page",pageNo);
		queryParams.put("limit",pageSize);
		Page<SysRole> allRoles = this.selectPage(new Query<>(queryParams), new EntityWrapper<SysRole>().eq("del_flag", CommonConstant.STATUS_NORMAL));
		PageUtils pageUtils = new PageUtils(allRoles);
		return Result.ok().put("data",pageUtils);
	}

	@Override
	public Result listRoleListByDeptId(Map<String, Object> params) {
		//创建返回实体
		List<AdminRoleListDto> adminRoleListDtoList = new ArrayList<AdminRoleListDto>();
		String deptId = params.get("deptId").toString();

		if(StringUtils.isBlank(deptId)){
			return Result.error("根据企业查询组织下角色deptId不允许为空！");
		}

		//查询部门名称
		SysDept sysDept = selectDeptOrDistr(deptId);
		//分页操作
		int pageNo = Integer.parseInt(params.get("pageNo").toString());
		int pageSize = Integer.parseInt(params.get("pageSize").toString());
		Page page = new Page(pageNo, pageSize);


		List<SysRoleVo> sysRoles=new ArrayList<>();

		//根据deptId判断是中台还是企业侧
		if(sysDept.getLevel().equals("0")){
			/**
			 * 判断是平台侧还是渠道侧
			 */
			if(deptId.equals("0")){
				/**
				 * 平台侧查询所有可用角色
				 */
				sysRoles =sysRoleMapper.selectByDeptId(null);
			}else{
				sysRoles =sysRoleMapper.selectByDeptId(deptId);

				List<SysDept>  depts=sysDeptMapper.selectDeptBySourceId(deptId);
				for(SysDept sysDept1:depts){
					List<SysRoleVo>	sysRoleVoList =sysRoleMapper.selectByDeptId(sysDept1.getDeptId());
					sysRoles.addAll(sysRoleVoList);
				}
			}
		}else{
			//查询自己及以下组织
			Result  result=sysDeptService.listMyselfAll(deptId, true,null);
			List<SysDeptVo> deptList=new ArrayList<SysDeptVo>();
			if(result!=null&&result.get("code").toString().equals("0000")){
				deptList=(List<SysDeptVo>) result.get("data");
				for(SysDeptVo sysDeptVo:deptList){
					List<SysRoleVo> sysRoleVoList =sysRoleMapper.selectByDeptId(sysDeptVo.getDeptId());
					sysRoles.addAll(sysRoleVoList);
				}
			}else{
				return Result.error("根据deptId查询对应组织下组织树不存在");
			}

		}

		for (SysRoleVo sysRoleVo : sysRoles){
			//查询组织下角色对应的用户个数  roleId  -> sys_user_role
			int userCount = sysRoleMapper.queryRoleOfUserCountByRoleId(sysRoleVo.getRoleId());
			AdminRoleListDto adminRoleListDto = toAdminRoleListDto(sysRoleVo, sysDept,userCount);
			adminRoleListDtoList.add(adminRoleListDto);
		}

		//分页操作
		page.setRecords(adminRoleListDtoList);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data",pageUtils);
	}


	/**
	 * 无分页查询组织权限列表
	 * @param deptId
	 * @return
	 */
	@Override
	public Result roleListByDeptId(String deptId) {
		//创建返回实体
		List<AdminRoleListDto> adminRoleListDtoList = new ArrayList<AdminRoleListDto>();
		if(StringUtils.isBlank(deptId)){
			return Result.error("根据企业查询组织下角色deptId不允许为空！");
		}
		//查询部门名称
		SysDept sysDept = selectDeptOrDistr(deptId);
		List<SysRoleVo> sysRoles=new ArrayList<>();
		//根据deptId判断是中台还是企业侧
		boolean flag= false;
		if(sysDept.getLevel().equals("0")){
			/**
			 * 判断是平台侧还是渠道侧
			 */
			if(deptId.equals("0")){
				/**
				 * 平台侧查询所有可用角色
				 */
				sysRoles =sysRoleMapper.selectByDeptId(null);
			}else{
				sysRoles =sysRoleMapper.selectByDeptId(deptId);

				List<SysDept>  depts=sysDeptMapper.selectDeptBySourceId(deptId);
				for(SysDept sysDept1:depts){
					List<SysRoleVo>	sysRoleVoList =sysRoleMapper.selectByDeptId(sysDept1.getDeptId());
					sysRoles.addAll(sysRoleVoList);
				}
			}
		}else{
			//查询自己及以下组织
			Result  result=sysDeptService.listMyselfAll(deptId, true,null);
			List<SysDeptVo> deptList=new ArrayList<SysDeptVo>();
			Map<String,String> tenantIdMap =new HashMap();
			if(result!=null&&result.get("code").toString().equals("0000")){
				deptList=(List<SysDeptVo>) result.get("data");
				for(SysDeptVo sysDeptVo:deptList){
					List<SysRoleVo> sysRoleVoList =sysRoleMapper.selectByDeptId(sysDeptVo.getDeptId());
					sysRoles.addAll(sysRoleVoList);
					tenantIdMap.put(sysDeptVo.getTenantId(),sysDeptVo.getTenantId());
				}
				if (sysRoles.size()==0) {
					for (String key:tenantIdMap.keySet()){
						System.out.println("key= "+key+" and value= "+tenantIdMap.get(key));
						List<SysRoleVo> sysRoleVoList =sysRoleMapper.selectByDeptId(key);
						sysRoles.addAll(sysRoleVoList);
					}

				}
			}else{
				return Result.error("根据deptId查询对应组织下组织树不存在");
			}

		}


		for (SysRoleVo sysRoleVo : sysRoles){
			//查询组织下角色对应的用户个数  roleId  -> sys_user_role
			int userCount = sysRoleMapper.queryRoleOfUserCountByRoleId(sysRoleVo.getRoleId());
			AdminRoleListDto adminRoleListDto = toAdminRoleListDto(sysRoleVo, sysDept,userCount);
			adminRoleListDtoList.add(adminRoleListDto);
		}
		return Result.ok().put("data",adminRoleListDtoList);
	}

	/**
	 * 根据deptId查询对应组织信息
	 * @param deptId
	 * @return
	 */
	public SysDeptVo selectDeptOrDistr(String deptId){

		SysDept deptDao =sysDeptMapper.selectByDeptId(deptId);
		SysDeptVo sysdeptvo=new SysDeptVo();

		if(deptDao!=null) {
			BeanUtils.copyProperties(deptDao, sysdeptvo);
		}else{
			/**
			 * 如sysdept表没有则需要查对应
			 */
			Distributor distributor=distributorMapper.selectDistributorById(deptId);
			if(distributor!=null){
				sysdeptvo=this.packageSysDeptVoByDistributor(distributor);
			}
		}
		return sysdeptvo;
	}


	/**
	 * 渠道转组织信息
	 * @param distributor
	 * @return
	 */
	public SysDeptVo packageSysDeptVoByDistributor(Distributor distributor){
		SysDeptVo sysDeptVo=new SysDeptVo();
		sysDeptVo.setDeptId(distributor.getId());
		sysDeptVo.setParentId(distributor.getSuperior());
		sysDeptVo.setName(distributor.getCompanyName());
		sysDeptVo.setLevel(0);
		sysDeptVo.setTaxpayerCode(distributor.getTaxNo());
		sysDeptVo.setTaxpayerBank(distributor.getBankName());
		sysDeptVo.setTaxpayerAccount(distributor.getBankNo());
//       sysDeptVo.setTaxpayerType(StringUtils.isBlank(distributor.getTaxpayerType())?Integer.valueOf(distributor.getTaxpayerType()):0);
//       sysDeptVo.setTaxpayerIndustry(distributor.getTrade());
		sysDeptVo.setLocationCode(distributor.getLocation());
		sysDeptVo.setContactEmail(distributor.getContactEmail());
		sysDeptVo.setContactPhone(distributor.getContactPhone());
		return  sysDeptVo;
	}

	@Override
	public Result roleInfo(Long roleId,Long userId) {
		List<String> menuIds = new ArrayList<>();
		if(userId == 1L){
			//大象超级管理员查询所有有效的菜单数据，不受权限和授权产品的限制
			menuIds = productMenuMapper.getMenuIdListByProId(null);
		}else {
			//查询登录用户信息
			UserVO userVO = sysUserMapper.selectUserVoById(userId);
			//查询登录用户角色
			List<SysUserRole> roles=sysUserMapper.selectRoleIdsByUserId(userId);
			//获取角色id集合
			List<Long> loginUserRoleIdList = roles.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
			//根据角色id集合和产品id集合查询菜单id
			menuIds = sysUserMapper.queryMenuIdListByRoles(loginUserRoleIdList);
			if(CollectionUtils.isEmpty(menuIds)){
				return Result.error("当前用户无权限访问");
			}
		}
		//查询角色主体信息
		SysRoleVo sysRoleVo = sysRoleMapper.selectByRoleId(roleId);
		//查询部门名称
		SysDept sysDept = selectDeptOrDistr(sysRoleVo.getDeptId());
		//查询角色对应的菜单集合
		List<String> menuIdList = sysRoleMapper.queryMenuIdListByRoleId(roleId);
		//查询角色对应的菜单集合
		List <SysMenu> sysMenuEntityList = new ArrayList<SysMenu>();
		for(String menid:menuIds){
			ProductMenuTree prov=sysMenuMapper.selectProductMenuByMenuId(menid);
			if(prov != null){
				SysMenu sysMenu= this.packageSysMenuByProductMenu(prov);
				if(menuIdList.contains(sysMenu.getMenuId())){
					sysMenu.setSelected(1);
				}
				sysMenuEntityList.add(sysMenu);
			}
		}
		AdminRoleInfoDto adminRoleInfoDto = toAdminRoleInfoDto(sysRoleVo, userId.toString(),sysDept.getName(),sysMenuEntityList);
		return Result.ok().put("data",adminRoleInfoDto);
	}

	@Override
	public Result queryMenus(Long userId) {
		List<String> menuIds = new ArrayList<>();
		if(userId == 1L){
			//大象超级管理员查询所有有效的菜单数据，不受权限和授权产品的限制
			menuIds = productMenuMapper.getMenuIdListByProId(null);
		}else {
			//查询登录用户信息
			UserVO userVO = sysUserMapper.selectUserVoById(userId);
			//查询登录用户角色
			List<SysUserRole> roles=sysUserMapper.selectRoleIdsByUserId(userId);
			//获取角色id集合
			List<Long> loginUserRoleIdList = roles.stream().map(SysUserRole::getRoleId).collect(Collectors.toList());
			//根据角色id集合和产品id集合查询菜单id
			menuIds = sysUserMapper.queryMenuIdListByRoles(loginUserRoleIdList);
			if(CollectionUtils.isEmpty(menuIds)){
				return Result.error("当前用户无权限访问");
			}
		}
		//查询菜单id对应的菜单集合
		List <SysMenu> sysMenuEntityList = new ArrayList<SysMenu>();
		for(String menid:menuIds){
			ProductMenuTree prov=sysMenuMapper.selectProductMenuByMenuId(menid);
			if(prov != null){
				SysMenu sysMenu= this.packageSysMenuByProductMenu(prov);
				//新增时默认全是未选中
				sysMenu.setSelected(0);
				sysMenuEntityList.add(sysMenu);
			}
		}
		return Result.ok().put("data",sysMenuEntityList);
	}

	/**
	 *
	 * @param prov
	 * @return
	 */
	public SysMenu  packageSysMenuByProductMenu(ProductMenuTree prov){

		SysMenu sysMenu=new SysMenu();

		sysMenu.setMenuId(prov.getId());
		sysMenu.setName(prov.getName());
		sysMenu.setParentId(prov.getParentId());
		sysMenu.setPermission(prov.getPermission());
		sysMenu.setPath(prov.getPath());
		sysMenu.setUrl(prov.getUrl());
		sysMenu.setIcon(prov.getIcon());
		sysMenu.setMethod(prov.getMethod());
		sysMenu.setSort(prov.getSort());
		sysMenu.setType(prov.getType());
		sysMenu.setProductId(prov.getProductId());
		sysMenu.setSystemSign(prov.getSystemSign());

		return sysMenu;
	}

	/**
	 *
	 * @param sysProduct
	 * @return
	 */
	public SysMenu packageSysMenuByProductInfo(SysProduct sysProduct){

		SysMenu sysMenu=new SysMenu();
		sysMenu.setMenuId(sysProduct.getId().toString());
		sysMenu.setName(sysProduct.getName());
		sysMenu.setParentId("-1");
		sysMenu.setIcon(sysProduct.getIcon());
		sysMenu.setProductId(sysProduct.getId().toString());
		return sysMenu;
	}



	@Override
	@Transactional
	public Result updateRole(AdminRoleOperateDto adminRoleOperateDto) {

		if(adminRoleOperateDto.getRoleId()==null){
			return Result.error("更新角色，对应ID必填");
		}

		//查询角色主体信息
		SysRoleVo sysRoleVo = sysRoleMapper.selectByRoleId(adminRoleOperateDto.getRoleId());
		//比较角色名称是否变化，如有变查询新角色名称的总记录数
		if(!adminRoleOperateDto.getRoleName().equals(sysRoleVo.getRoleName())){
			int num = sysRoleMapper.queryRoleNameCountByDeptId(adminRoleOperateDto.getDeptId(), adminRoleOperateDto.getRoleName());
			if( num > 0){
				return Result.error("角色名称已存在");
			}
		}
		//更新用户主体  名称 描述 更新人id
		SysRole sysRole = new SysRole();
		sysRole.setRoleId(adminRoleOperateDto.getRoleId());
		sysRole.setRoleName(adminRoleOperateDto.getRoleName());
		sysRole.setRoleDesc(adminRoleOperateDto.getDescribe());
		sysRole.setUpdateBy(adminRoleOperateDto.getUserId());
		//sysRole.setDeptId(adminRoleOperateDto.getDeptId());
		SysDept sysDept=selectDeptOrDistr(adminRoleOperateDto.getDeptId());
		sysRole.setDeptName(sysDept.getName());
		sysRoleMapper.updateById(sysRole);
		//删除用户菜单关系
		sysRoleMapper.deleteRoleMenuByRoleId(sysRoleVo.getRoleId());
		//添加用户菜单关系
		List<String> menusList = adminRoleOperateDto.getMenusList();
		for(String menuId : menusList){
			SysRoleMenuEntity sysRoleMenuEntity = new SysRoleMenuEntity();
			sysRoleMenuEntity.setMenuId(menuId);
			sysRoleMenuEntity.setRoleId(Long.valueOf(sysRoleVo.getRoleId()));
			sysRoleMapper.addRoleMenuRelation(sysRoleMenuEntity);
		}

		//清除对应缓存
		List<SysUserRole> userRoleList = sysUserRoleMapper.getUserRoleByRoleId(adminRoleOperateDto.getRoleId());
		userRoleList.stream().forEach(sysUserRole -> {
			sysUserService.redisDeleteByUserId(sysUserRole.getUserId());
			log.info("更新角色后，清除对应用户缓存，用户Id：{}",sysUserRole.getUserId());
		});
		return Result.ok();
	}

	@Override
	@Transactional
	public Result addRole(AdminRoleOperateDto adminRoleOperateDto) {
		if (adminRoleOperateDto.getRoleProperty().equals("2")) {
			adminRoleOperateDto.setRoleType(12);
			SysDeptVo sysDeptVo=selectDeptOrDistr(adminRoleOperateDto.getDeptId());
			adminRoleOperateDto.setDeptName(sysDeptVo.getName());
		}

		//查询部门下角色名称的总记录数
		//新建角色不可以为管理员
		if("管理员".equals(adminRoleOperateDto.getRoleName())&&adminRoleOperateDto.getRoleProperty().equals("2")){
			return Result.error("新建角色名字不可以为管理员");
		}
		if("超级管理员".equals(adminRoleOperateDto.getRoleName())&&adminRoleOperateDto.getRoleProperty().equals("2")){
			return Result.error("新建角色名字不可以为超级管理员");
		}
		if(StringUtils.isBlank(adminRoleOperateDto.getRoleProperty())){
			return Result.error("角色类型必填");
		}

//		SysUser sysUser=sysUserMapper.selectById(adminRoleOperateDto.getUserId());
//		if(sysUser!=null){
//			adminRoleOperateDto.setDistributorId(sysUser.getDistributorId());
//		}
		int num = sysRoleMapper.queryRoleNameCountByDeptId(adminRoleOperateDto.getDeptId(), adminRoleOperateDto.getRoleName());
		if( num > 0){
			return Result.error("角色名称已存在");
		}

		//添加角色主体
		adminRoleOperateDto.setCreateTime(new Date());
		adminRoleOperateDto.setUpdateTime(new Date());
		//生成角色编码roleCode
		//查到数据库最后一个角色编码
		String roleCode = sysRoleMapper.selectLastRoleCode();
		if(StringUtils.isBlank(roleCode)){
			adminRoleOperateDto.setRoleCode(generateNum(4,""));
		}else{
			adminRoleOperateDto.setRoleCode(generateNum(4,roleCode));
		}
		sysRoleMapper.addRole(adminRoleOperateDto);

		//添加角色菜单关系
		List<String> menusList = adminRoleOperateDto.getMenusList();
		for(String menuId : menusList){
			SysRoleMenuEntity sysRoleMenuEntity = new SysRoleMenuEntity();
			sysRoleMenuEntity.setMenuId(menuId);
			sysRoleMenuEntity.setRoleId(adminRoleOperateDto.getRoleId());
			sysRoleMapper.addRoleMenuRelation(sysRoleMenuEntity);
		}

		return Result.ok();
	}

	@Override
	@Transactional
	public Result deleteRole(Long roleId) {
		List<AdminUserListDto>  aAdminUserListDtoList = new ArrayList<AdminUserListDto>();
		//查询角色绑定的用户及组织
		List <Long> userIdList = sysRoleMapper.queryUserIdListByRoleId(roleId);
		for (Long userId : userIdList){
			SysUser sysUser = sysUserMapper.selectById(userId);
			if(sysUser==null){
				return Result.error("数据有误，请联系管理员");
			}
			SysDept sysDept = selectDeptOrDistr(sysUser.getDeptId());
			AdminUserListDto adminUserListDto = new AdminUserListDto();
			adminUserListDto.setUserName(sysUser.getUsername());
			adminUserListDto.setDeptName(sysDept.getName());
			aAdminUserListDtoList.add(adminUserListDto);
		}
		if(aAdminUserListDtoList.size()>0){
			AdminRoleDeleteDto adminRoleDeleteDto = new AdminRoleDeleteDto();
			adminRoleDeleteDto.setAdminUserListDtoList(aAdminUserListDtoList);
			return Result.error().put("data",adminRoleDeleteDto);
		}

		//删除角色 sys_role
		sysRoleMapper.deleteById(roleId);
		//删除角色菜单关联  sys_role_menu
		sysRoleMapper.deleteRoleMenuByRoleId(roleId);
		return Result.ok();
	}

	@Override
	public Result queryUsersByRoleId(Map<String, Object> params) {
		//创建返回实体
		List<AdminUserListDto> adminUserListDtoList = new ArrayList<AdminUserListDto>();

		List<Long> userIdList = sysRoleMapper.queryUserIdListByRoleId(Long.valueOf(params.get("roleId").toString()));
		if(userIdList.size()==0){
			return Result.error("该角色暂无绑定用户");
		}
		log.info("查到的用户id为:{}",userIdList.toString());
		//分页操作
		int pageNo = Integer.parseInt(params.get("pageNo").toString());
		int pageSize = Integer.parseInt(params.get("pageSize").toString());
		Page page = new Page(pageNo, pageSize);

		List<SysUser> userList = sysUserMapper.queryPageUserByUserIdList(page, userIdList);

		/**
		 * 用户个数统计按照角色所属组织和当前用户所属组织一致
		 */
		SysRole role=sysRoleMapper.selectById(Long.valueOf(params.get("roleId").toString()));
        String roleDeptId=role.getDeptId();
		for (SysUser sysUser:userList){
			if(!roleDeptId.equals(sysUser.getTopLevel())){
				continue;
			}
			//查询用户的组织信息
			SysDept sysDept = selectDeptOrDistr(sysUser.getDeptId());
			//查询用户的角色信息
			List<SysRole> sysRoleList = sysUserMapper.queryRoleIdByUserId(sysUser.getUserId());
			List<String> roleNameList = new ArrayList<String>();
			for(SysRole sysRole : sysRoleList){
				roleNameList.add(sysRole.getRoleName());
			}
			AdminUserListDto adminUserListDto = toAdminUserListDto(sysUser, sysDept.getName(), roleNameList);
			adminUserListDtoList.add(adminUserListDto);
		}
		page.setRecords(adminUserListDtoList);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data",pageUtils);
	}

    @Override
    public Result roleInfoById(Long roleId) {
        return  Result.ok().put("data",sysRoleMapper.selectById(roleId));
    }

	@Override
	public List<SysRole> queryRoleByUserId(Long userId) {
		List<SysUserRole> userRoles = sysUserRoleMapper.getUserRoleByUserId(userId);
		Set<SysRole> sysRoles = new HashSet<>();
		userRoles.forEach(sysUserRole -> {
			SysRole sysRole = sysRoleMapper.selectByRoleId2(sysUserRole.getRoleId());
			if(sysRole != null){
				sysRoles.add(sysRole);
			}
		});
		return new ArrayList<>(sysRoles);
	}

	private AdminUserListDto toAdminUserListDto(SysUser sysUser, String name, List<String> roleNameList) {
		AdminUserListDto adminUserListDto = new AdminUserListDto();
		adminUserListDto.setUserId(sysUser.getUserId());
		adminUserListDto.setUserName(sysUser.getUsername());
		adminUserListDto.setName(sysUser.getNickname());
		adminUserListDto.setMobile(sysUser.getPhone());
		adminUserListDto.setDeptName(name);
		adminUserListDto.setDelFlag(sysUser.getDelFlag());
		adminUserListDto.setStatus(sysUser.getStatus());
		adminUserListDto.setCreateTime(sysUser.getCreateTime());
		adminUserListDto.setRoleName(roleNameList);
		return adminUserListDto;
	}

	private AdminRoleInfoDto toAdminRoleInfoDto(SysRoleVo sysRoleVo, String username, String deptName, List<SysMenu> sysMenuEntityList) {
		AdminRoleInfoDto adminRoleInfoDto = new AdminRoleInfoDto();
		adminRoleInfoDto.setRoleId(sysRoleVo.getRoleId());
		adminRoleInfoDto.setType(sysRoleVo.getType());
		adminRoleInfoDto.setRoleName(sysRoleVo.getRoleName());
		adminRoleInfoDto.setDescribe(sysRoleVo.getRoleDesc());
		adminRoleInfoDto.setDeptName(deptName);
		adminRoleInfoDto.setUpdateBy(username);
		adminRoleInfoDto.setUpdateTime(sysRoleVo.getUpdateTime());
		adminRoleInfoDto.setMenusList(sysMenuEntityList);
		adminRoleInfoDto.setDeptId(sysRoleVo.getDeptId());
		adminRoleInfoDto.setRoleProperty(sysRoleVo.getRoleProperty());
		adminRoleInfoDto.setRoleType(sysRoleVo.getRoleType());
		return adminRoleInfoDto;
	}

	private AdminRoleListDto toAdminRoleListDto(SysRoleVo sysRoleVo, SysDept sysDept, int userCount) {
		AdminRoleListDto adminRoleListDto = new AdminRoleListDto();
		adminRoleListDto.setRoleId(sysRoleVo.getRoleId());
		adminRoleListDto.setRoleCode(sysRoleVo.getRoleCode());
		adminRoleListDto.setRoleName(sysRoleVo.getRoleName());
		adminRoleListDto.setDescribe(sysRoleVo.getRoleDesc());
		adminRoleListDto.setType(sysRoleVo.getType());
		adminRoleListDto.setUserCount(userCount);
		adminRoleListDto.setDeptName(sysDept.getName());
		adminRoleListDto.setUpdateTime(sysRoleVo.getUpdateTime());
		adminRoleListDto.setRoleProperty(sysRoleVo.getRoleProperty());
		adminRoleListDto.setCreateTime(sysRoleVo.getCreateTime());
		adminRoleListDto.setRoleType(sysRoleVo.getRoleType());
		return  adminRoleListDto;
	}

	/**
	 * 添加角色
	 * @return 
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Object> addEntRole(RoleDTO roleDTO) {
		if(StringUtils.isEmpty(roleDTO.getEntId())){
			return new R<>(1,"企业id不能为空");
		}else if (StringUtils.isEmpty(roleDTO.getRoleName())) {
			return new R<>(2,"请输入角色名称");
		}else if (roleDTO.getMenuList() == null || roleDTO.getMenuList().size() == 0) {
			return new R<>(3,"请勾选权限列表");
		}
		List<SysRoleVo> roleList = sysRoleMapper.getRoleName(roleDTO.getEntId(),roleDTO.getRoleName());
		if(roleList.size() > 0){
			return new R<>(4,"该角色已存在");
		}
		//添加角色
		SysRole role = new SysRole();
		role.setRoleCode(System.currentTimeMillis()+"");
		role.setRoleName(roleDTO.getRoleName());
		role.setRoleDesc(roleDTO.getRoleDesc());
		role.setCreateTime(new Date());
		this.insert(role);
		//添加角色企业关系
		BaseEntRole entRole = new BaseEntRole();
		entRole.setRoleId(role.getRoleId().toString());
		entRole.setEntId(roleDTO.getEntId());
		entRole.setCreateTime(new Date());
		entRole.setCreateBy(roleDTO.getCreateBy().toString());
		baseEntRoleService.insert(entRole);
		
		//添加角色菜单关系
		SysRoleMenu roleMenu = null;
		List<SysRoleMenu> roleMenuList = new ArrayList<>();
		for (String menuId : roleDTO.getMenuList()) {
			roleMenu = new SysRoleMenu();
			roleMenu.setRoleId(role.getRoleId());
			roleMenu.setMenuId(menuId);
			roleMenuList.add(roleMenu);
		}
		sysRoleMenuService.insertBatch(roleMenuList);
		return new R<>();
		
	}
	/**
	 * 修改角色
	 * @Methods:editEntRole
	 * <AUTHOR>
	 * @date 2019年3月13日下午5:30:20
	 * @param roleDTO
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public R<Object> editEntRole(RoleDTO roleDTO) {
		//校验参数及合规
		if(StringUtils.isEmpty(roleDTO.getEntId())){
			return new R<>(1,"企业id不能为空");
		}else if (StringUtils.isEmpty(roleDTO.getRoleName())) {
			return new R<>(2,"请输入角色名称");
		}else if (roleDTO.getMenuList() == null || roleDTO.getMenuList().size() == 0) {
			return new R<>(3,"请勾选权限列表");
		}else if (roleDTO.getRoleId()== null) {
			return new R<>(4,"角色id不能为空");
		}
		List<SysRoleVo> roleList = sysRoleMapper.getRoleName(roleDTO.getEntId(),roleDTO.getRoleName());
		for (SysRoleVo sysRoleVo : roleList) {
			if(!sysRoleVo.getRoleId().equals(roleDTO.getRoleId())){
				return new R<>(5,"该角色已存在");
			}
		}
		//修改角色
		SysRole role = new SysRole();
		role.setRoleId(roleDTO.getRoleId());
		role.setRoleName(roleDTO.getRoleName());
		role.setRoleDesc(roleDTO.getRoleDesc());
		role.setUpdateTime(new Date());
		this.updateById(role);
		
		//删除角色菜单关系
		sysRoleMenuService.deleteById(roleDTO.getRoleId());
		//添加角色菜单关系
		SysRoleMenu roleMenu = null;
		List<SysRoleMenu> roleMenuList = new ArrayList<>();
		for (String menuId : roleDTO.getMenuList()) {
			roleMenu = new SysRoleMenu();
			roleMenu.setRoleId(role.getRoleId());
			roleMenu.setMenuId(menuId);
			roleMenuList.add(roleMenu);
		}
		sysRoleMenuService.insertBatch(roleMenuList);
		return new R<>();
		
	}
	/**
	 * 删除角色
	 * @Methods:deleteEntRole
	 * <AUTHOR>
	 * @date 2019年3月13日下午6:11:38
	 * @param roleId
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteEntRole(Long roleId) {
		
		//删除角色
		this.deleteById(roleId);
		//删除企业角色关系
		baseEntRoleService.deleteById(roleId.toString());
		//删除角色菜单关系
		sysRoleMenuService.deleteById(roleId);
	}




}
