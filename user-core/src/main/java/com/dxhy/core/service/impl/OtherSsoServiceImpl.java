package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.mapper.DictionaryMapper;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.service.OhtherSsoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
public class OtherSsoServiceImpl implements OhtherSsoService {
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private DictionaryMapper dictionaryMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${bigb}")
    private String bigb;//对应大B 的票税账号

//    @Value("${demouids}")
//    private String demouids;
    @Value("${demoauth}")
    private String demoauth;
    @Value("${exptime}")
    private Long exptime;

    @Autowired
    private NacosParam nacosParam;

    /**
     * 获取进项sessionId
     * @param userId
     * @return
     * <AUTHOR>
     * @date 2022-06-23
     */
    @Override
    public String getJxSsoId(String userId) {
        if (nacosParam.demouids.contains(userId)) {
            //Object result = redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY + userId);
            Object result = redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY + "#etaxpre");
            JSONObject jsons = (JSONObject) result;
            if (jsons != null) {
                List<String> cookies = (List<String>) jsons.get("Set-Cookie");
                String sso_session_id  = jsons.get("body").toString();
                return sso_session_id;
            }
            //ResponseEntity<String> entity = sendSessionRequest(userId);
            JSONObject json = sendSessionRequest2(userId);
            if (json == null || json.equals("")) {
                return null;
            }
            String sso_session_id = json.get("body").toString();
            //存储到redis 键：userId 值：entity
            redisTemplate.opsForValue().set(SystemConstants.SSO_COOKIE_CODE_KEY + "#etaxpre", json, 12, TimeUnit.HOURS);
            return sso_session_id;
        }
        return null;
    }

    /**
     * 发送请求 获取进行cookie
     * @param userId
     * @return
     */
    @Override
    public JSONObject sendSessionRequest(String userId){
        log.info("[sendSessionRequest]userId:{}",userId);
        //查询进项对应的userId
        List<Dictionary> userList = dictionaryMapper.selectDictionary("","",userId,"","-1","票税-大B");
        if (userList.size() <= 0) {
            return null;
        }
        String jxId = userList.get(0).getDesc();
        if (jxId.equals("-1")) {//线上获取固定user_id=100001 对应大B 16
            userId=bigb.split("-")[0];
            Object result = redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY + userId);
            if (result==null) {
                jxId=bigb.split("-")[1];
            }else{
                JSONObject jsons = (JSONObject) result;
                boolean flag = verifyJxSession((List<String>)jsons.get("Set-Cookie"));
                if (!flag) {
                    return jsons;
                }else{
                    jxId=bigb.split("-")[1];
                }
            }
        }
        restTemplate =new RestTemplate();
        String getSessionUrl = "http://etaxpre.biaopu.cloud:10000/itax/sso/getSessionId?userId="+jxId;
        ResponseEntity<String> entity = restTemplate.getForEntity(getSessionUrl,String.class);
        List<String> cookies = entity.getHeaders().get("Set-Cookie");
        String sso_session_id  = entity.getBody();
        log.info("进项获取到的最新sso_session_id：{},cookie:{}",sso_session_id,cookies);
        JSONObject json = new JSONObject();
        json.put("body",sso_session_id);
        json.put("Set-Cookie",cookies);

        return json;
    }
    /**
     * 发送请求 获取进行cookie
     * @param userId
     * @return
     */
    public JSONObject sendSessionRequest2(String userId){
        log.info("[sendSessionRequest]userId:{}",userId);
        String jxId=bigb.split("-")[1];
        restTemplate =new RestTemplate();
        String getSessionUrl = "http://etaxpre.biaopu.cloud:10000/itax/sso/getSessionId?userId="+jxId;
        ResponseEntity<String> entity = restTemplate.getForEntity(getSessionUrl,String.class);
        List<String> cookies = entity.getHeaders().get("Set-Cookie");
        String sso_session_id  = entity.getBody();
        log.info("进项获取到的最新sso_session_id：{},cookie:{}",sso_session_id,cookies);
        JSONObject json = new JSONObject();
        json.put("body",sso_session_id);
        json.put("Set-Cookie",cookies);
        return json;
    }
    /**
     * 验证进行cookies 是否有效
     * @param cookies
     * @return
     * <AUTHOR>
     * @date 2022-06-23
     */
    @Override
    public boolean verifyJxSession(List<String> cookies) {
        String checkUrl = "http://etaxpre.biaopu.cloud:10000/itax/sso/checkLogin?callback=";
        HttpHeaders headers = new HttpHeaders();
        headers.put(HttpHeaders.COOKIE,cookies);        //将cookie存入头部
        HttpEntity<String> requestEntity = new HttpEntity<String>(headers);

        String body = restTemplate.exchange(checkUrl, HttpMethod.GET, requestEntity, String.class).getBody();
//        log.info(body);
        String response = body.replace("(","").replace(")","");
        log.info("进项检查session repsonse:"+response);
        JSONObject json = JSON.parseObject(response);
        if (json.get("code").toString().equals("200")) {
//            log.info("进项session，没有失效，可继续使用");
            return false;
        }else {
            log.info("进项session已失效，重新获取session");
            return true;
        }
    }

    /**
     * demo.ele 大象系统 风控和报销
     * @return
     * <AUTHOR>
     * @date 2022-07-26
     */
    @Override
    public String getDbSsoId(String userId) {
        //查询进项对应的userId
//        List<Dictionary> userList = dictionaryMapper.selectDictionary("3420","3420","ssoid","",-1,"");
//        if (userList.size() <= 0) {
//            return null;
//        }
//        return userList.get(0).getDesc();
        //1.请求接口 定时请求此接口

        if (nacosParam.demouids.contains(userId)) {
            String ssoid = (String) redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY+"#demoele");
            if (StringUtils.isNotBlank(ssoid)) {
                return ssoid;
            }
            restTemplate =new RestTemplate();
//            String getSessionUrl = "https://demo.ele-cloud.com/itax-sso/doLogin";
            String getSessionUrl = "https://demo.ele-cloud.com/itax/doLogin";
            JSONObject param = new JSONObject();
            param.put("dxhyu",demoauth.split(",")[0]);
            param.put("dxhyp",demoauth.split(",")[1]);
            param.put("verifyCode","");

            ResponseEntity<JSONObject> entity = restTemplate.postForEntity(getSessionUrl,param,JSONObject.class);
            List<String> cookies = entity.getHeaders().get("Set-Cookie");
            log.info("demo环境的cookie存储格式是：{}",cookies);
            if (cookies == null) {
                return "error";
            }
//            String sso_session_id  = entity.getBody().get("data").toString();
            String sso_session_id=cookies.get(1).replace("dxhy_sso_sessionid=","").replace("; Path=/; HttpOnly","");
//            sso_session_id=sso_session_id.replace("https://demo.ele-cloud.com/itax-bench/?dxhy_sso_sessionid=","");
            log.info("进项获取到风控系统最新ssoid：{},cookie:{}",sso_session_id,cookies);
            //存储到redis 键：userId 值：entity
            redisTemplate.opsForValue().set(SystemConstants.SSO_COOKIE_CODE_KEY +"#demoele", sso_session_id, nacosParam.cookieExpire, TimeUnit.MINUTES);
            return sso_session_id;
        }
       return  null;
    }
}
