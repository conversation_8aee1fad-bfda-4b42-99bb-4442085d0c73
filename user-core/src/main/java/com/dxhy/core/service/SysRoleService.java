/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.common.util.Query;
import com.dxhy.core.pojo.DTO.AdminRoleOperateDto;
import com.dxhy.core.pojo.DTO.RoleDTO;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.vo.SysRoleVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysRoleService extends IService<SysRole> {

	/**
	 * 添加角色
	 *
	 * @param roleDto 角色信息
	 * @return 成功、失败
	 */
	Boolean insertRole(RoleDTO roleDto);

	/**
	 * 分页查角色列表
	 *
	 * @param objectQuery         查询条件
	 * @param objectEntityWrapper wapper
	 * @return page
	 */
	Page selectwithDeptPage(Query<Object> objectQuery, EntityWrapper<Object> objectEntityWrapper);

	/**
	 * 更新角色
	 *
	 * @param roleDto 含有部门信息
	 * @return 成功、失败
	 */
	Boolean updateRoleById(RoleDTO roleDto);

	/**
	 * 通过部门ID查询角色列表
	 *
	 * @param deptId 部门ID
	 * @return 角色列表
	 */
	List<SysRole> selectListByDeptId(Integer deptId);

	/**
	 * ########################################################################################
	 * 通过企业ID查询角色列表
	 *
	 * @param entId
	 * @param roleName
	 * @return
	 * @Methods:getEntRoleList
	 * <AUTHOR>
	 * @date 2019年3月13日下午1:41:34
	 */
	List<SysRoleVo> getEntRoleList(String entId, String roleName);

	/**
	 * 添加角色
	 *
	 * @param roleDTO
	 * @return
	 * @Methods:addEntRole
	 * <AUTHOR>
	 * @date 2019年3月13日下午4:08:17
	 */
	R addEntRole(RoleDTO roleDTO);

	/**
	 * 修改角色
	 *
	 * @param roleDTO
	 * @return
	 * @Methods:editEntRole
	 * <AUTHOR>
	 * @date 2019年3月13日下午5:29:56
	 */
	R editEntRole(RoleDTO roleDTO);

	/**
	 * 删除角色
	 *
	 * @param roleId
	 * @Methods:deleteEntRole
	 * <AUTHOR>
	 * @date 2019年3月13日下午6:11:14
	 */
	void deleteEntRole(Long roleId);

	/**
	 * ################
	 * 通过企业ID和用户id查询角色列表
	 */
	List<SysRoleVo> getUserAndEntRoleList(String entId, String userId);

	/**
	 * 查询部门对应角色列表
	 * @param params
	 * @return com.dxyun.heaven.admin.api.dto.Result
	 */
    Result listRolesByDeptId(Map<String, Object> params);

	/**
	 * 查询所有角色分页（管理员使用）
	 * @param params
	 * @return com.dxyun.heaven.admin.api.dto.Result
	 */
	Result listRoles(Map<String, Object> params);


	/**
	 * 查询部门下角色列表
	 * @param params
	 * @return
	 */
	Result listRoleListByDeptId(Map<String, Object> params);

    /**
     * 角色详情
     * @param roleId 角色id
	 * @param userId 用户id
     * @return com.dxyun.heaven.admin.api.dto.Result
     */
	Result roleInfo(Long roleId,Long userId);

	/**
	 * 查询当前登录用户所有权限菜单
	 * @return
	 */
	Result queryMenus(Long userId);

	/**
	 * 更新角色信息
	 * @param adminRoleOperateDto
	 * @return com.dxyun.heaven.admin.api.dto.Result
	 */
	Result updateRole(AdminRoleOperateDto adminRoleOperateDto);

	/**
	 * 添加角色
	 * @param adminRoleOperateDto
	 * @return com.dxyun.heaven.admin.api.dto.Result
	 */
	Result addRole(AdminRoleOperateDto adminRoleOperateDto);

	/**
	 * 角色删除   判断是否有用户关联
	 * @param roleId
	 * @return com.dxyun.heaven.admin.api.dto.Result
	 */
	Result deleteRole(Long roleId);

	/**
	 * 查询当前角色绑定的用户
	 * @param params
	 * @return com.dxyun.heaven.admin.api.dto.Result
	 */
	Result queryUsersByRoleId(Map<String, Object> params);

    Result roleInfoById(Long roleId);

	//···············································································
	//同步辅助运营侧

	/**
	 * 通过用户ID查询角色列表
	 */
	List<SysRole> queryRoleByUserId(Long userId);

    Result roleListByDeptId(String deptId);
}
