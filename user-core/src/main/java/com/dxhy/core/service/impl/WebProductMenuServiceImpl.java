package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.dxhy.core.constants.ProductConstant;
import com.dxhy.core.constants.ProductProperties;
import com.dxhy.core.pojo.entity.ProductInfo;
import com.dxhy.core.pojo.entity.ProductMenu;
import com.dxhy.core.pojo.entity.ProductMenuSell;
import com.dxhy.core.pojo.vo.ProductMenuTree;
import com.dxhy.core.service.IProductInfoService;
import com.dxhy.core.service.IProductMenuSellService;
import com.dxhy.core.service.WebProductMenuService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by IntelliJ IDEA.
 * <AUTHOR> User: jcz
 * Date: 2019/04/01
 * Description:
 */
@Service
@Slf4j
public class WebProductMenuServiceImpl implements WebProductMenuService {

    @Autowired
    private IProductInfoService productService;

    @Autowired
    private IProductMenuSellService productMenuSellService;

    // 基础配置
    @Autowired
    private ProductProperties productProperties;
    /**
     * 根据售卖类型ID获取产品菜单
     * <AUTHOR>
     * @return
     */
    @Override
    public List<ProductMenuTree> getProductMenuBySellLabelId(String sellLabelId, List<ProductMenu> productMenus, String productId) {
//        ProductInfo detail = productProviderClient.getDetail(productId);
        ProductInfo detail= productService.selectById(productId);
        ProductMenu menu = new ProductMenu();
        menu.setId(productId);
        menu.setParentId("-1");
        if (Optional.ofNullable(detail).isPresent()){
            menu.setName(detail.getName());
        }
        menu.setType("1");
        menu.setStatus("1");
        menu.setSort(0);
        menu.setProductId(productId);
        productMenus.add(menu);
        Map<String, ProductMenu> menuMap = productMenus.parallelStream().filter(productMenu -> {
            if (productMenus != null && !productMenus.isEmpty() && productMenu!=null){
                if (Optional.ofNullable(detail).isPresent()){
                    productMenu.setProductName(detail.getName());
                }
                return true;
            }
            return false;
        }).collect(Collectors.toMap(p -> p.getId(), p -> p));
//        List<ProductMenuSell> productMenuSellBySellId = productMenuClient.getProductMenuSellBySellId(sellLabelId);
        List<ProductMenuSell> productMenuSellBySellId = productMenuSellService.selectList(new EntityWrapper().eq("sell_label_id",sellLabelId));
        ProductMenuSell menuSell = new ProductMenuSell();
        menuSell.setProductMenuId(productId);
        menuSell.setSellLabelId(sellLabelId);
        productMenuSellBySellId.add(menuSell);
        List<ProductMenuTree> productMenuTree =
                Optional.ofNullable(productMenuSellBySellId).filter(p->Optional.ofNullable(menuMap).isPresent())
                        .map(p->p.parallelStream().filter(productMenuSell->Optional.ofNullable(menuMap.get(productMenuSell.getProductMenuId())).isPresent())
                                .map(productMenuSell->{
                                    ProductMenu productMenu = menuMap.get(productMenuSell.getProductMenuId());
                                    if (!productId.equals(productMenu.getId())&&"-1".equals(productMenu.getParentId())){
                                        productMenu.setParentId(productId);
                                    }
                                    productMenu.setIcon(StringUtils.isBlank(productMenu.getIcon())?
                                            productMenu.getIcon():String.format(productProperties.getFilePath(), productMenu.getIcon()));
                                    productMenu.setSort(productMenu.getSort()+ ProductConstant.ONE);
                                    return new ProductMenuTree(productMenu);
                                }).sorted(Comparator.comparingInt(ProductMenuTree::getSort)).collect(Collectors.toList())).get();
        return productMenuTree;
    }
}
