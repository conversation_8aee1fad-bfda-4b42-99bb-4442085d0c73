package com.dxhy.core.service;

import com.dxhy.core.common.response.R;
import com.dxhy.core.pojo.vo.UserVO;

import java.util.HashMap;
import java.util.Map;

public interface UserService {


    public R<Boolean> getUserStatusByUserId(Long userId);

    /**
     * 判断用户是否有已开通的产品并且未过期
     * @param userId
     * @return
     */
    public boolean getProductIsvalidByUserId(Long userId);

    public R getIsCompanyProductByUserId(Long userId, String productId, String sourceId);


    public UserVO findUserByUsername(String username);

    UserVO findUserByMobile(String mpbile);

    R<Map<String, String>> getSourceInfoByUri(String redirectURI);

    R<String> getProductInfoById(String productId);

    R sensorsLogin(HashMap<String, String> map);
}
