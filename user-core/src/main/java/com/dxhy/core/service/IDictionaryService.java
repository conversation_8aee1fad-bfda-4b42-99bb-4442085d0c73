package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.pojo.entity.Dictionary;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
public interface IDictionaryService extends IService<Dictionary> {

    /**
     * 根据父节点获取树结构
     * @param parent
     * @return
     */
    List<Dictionary> getTree(String parent);

    Dictionary getByFlag(String flag);

    List<Dictionary> selectDictionary(String id, String code, String name, String desc, String parent, String flag);
    //查询当前表中最大id
    Long selectMaxId();

}
