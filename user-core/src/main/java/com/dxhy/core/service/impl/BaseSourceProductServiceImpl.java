package com.dxhy.core.service.impl;

import com.dxhy.core.pojo.entity.BaseSourceProduct;
import com.dxhy.core.service.BaseSourceProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Service;

import java.sql.ResultSet;
import java.sql.SQLException;

@Slf4j
@Service
public class BaseSourceProductServiceImpl implements BaseSourceProductService {

    @Autowired
    private JdbcTemplate jdbcTemplate;
    private String SELECT_BY_DOMAIN_SQL = "SELECT id,domain,source_id,product_id,product_name,platform_type,version from base_source_product WHERE del_flag ='0' and domain = ?";

    @Override
    public BaseSourceProduct selectByDomain(String domain) {
        try {
            BaseSourceProduct baseSourceProduct = jdbcTemplate.queryForObject(SELECT_BY_DOMAIN_SQL, new RowMapper<BaseSourceProduct>() {
                @Override
                public BaseSourceProduct mapRow(ResultSet resultSet, int i) throws SQLException {
                    BaseSourceProduct result = new BaseSourceProduct();
                    result.setId(resultSet.getInt("id"));
                    result.setDomain(resultSet.getString("domain"));
                    result.setSourceId(resultSet.getString("source_id"));
                    result.setProductId(resultSet.getString("product_id"));
                    result.setProductName(resultSet.getString("product_name"));
                    result.setPlatformType(resultSet.getString("platform_type"));
                    result.setVersion(resultSet.getString("version"));
                    return result;
                }
            }, domain);
            return baseSourceProduct;
        } catch(EmptyResultDataAccessException e) {
            log.error("【BaseSourceProductService.selectByDomain】查询结果为空，domain:{}",domain);
        } catch(Exception e) {
            log.error("【BaseSourceProductService.selectByDomain】查询异常:{}, {}",domain , e);
        }
        return null ;
    }
}
