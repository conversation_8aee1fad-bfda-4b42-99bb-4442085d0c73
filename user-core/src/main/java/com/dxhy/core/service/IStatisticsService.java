/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.service;

import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.vo.StatisticsRzVo;
import com.dxhy.core.pojo.vo.StatisticsVo;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/30 17:03
 */
public interface IStatisticsService {

	Result cylist(StatisticsVo vo);

	Result rzlist(StatisticsRzVo vo);

	Result kplist(StatisticsVo vo);

}
