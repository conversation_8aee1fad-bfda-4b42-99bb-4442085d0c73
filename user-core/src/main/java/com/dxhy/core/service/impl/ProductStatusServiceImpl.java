package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.ProductStatusMapper;
import com.dxhy.core.pojo.DTO.SaasProductInfo;
import com.dxhy.core.pojo.entity.ProductStatus;
import com.dxhy.core.service.ProductStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;

/**
 * @description product_status
 * <AUTHOR>
 * @date 2022-10-13
 */
@Slf4j
@Service
public class ProductStatusServiceImpl extends ServiceImpl<ProductStatusMapper, ProductStatus>  implements ProductStatusService {

    @Resource
    private ProductStatusMapper productStatusMapper;


    @Override
    public Result addUserProductBatch(List<SaasProductInfo> productList, String distributorId) {
        //先删除用户对应的产品信息

        List<ProductStatus> list = productStatusMapper.selectList(new EntityWrapper<ProductStatus>().eq("distributor_id",distributorId));
        if (list.size()>0) {
            productStatusMapper.delete(new EntityWrapper<ProductStatus>().eq("distributor_id",distributorId));
        }


        int num = 0;
        for (int i = 0; i < productList.size(); i++) {
            ProductStatus productStatus = new ProductStatus();
            productStatus.setProductId(productList.get(i).getProductId());
            productStatus.setDistributorId(distributorId);
            productStatus.setAuthStime(productList.get(i).getBeginTime());
            productStatus.setAuthEtime(productList.get(i).getEndTime());
            productStatus.setUid("0");
            productStatus.setStatus("2");
            int n = productStatusMapper.addUserProduct(productStatus);
            num = num + n;
        }

        //找出新增的产品，需要调用异步线程同步数据
        HashMap<String,Boolean> map = new HashMap<>();
        for (int i = 0; i < productList.size(); i++) {
            map.put(productList.get(i).getProductId(),false);
            for (int j = 0; j < list.size(); j++) {
                if ( productList.get(i).equals(list.get(j).getProductId())) {
                    map.put(productList.get(i).getProductId(),true);
                    break;
                }
            }
        }
        log.info("");
        return Result.ok().put("num",num).put("map",map);
    }


}