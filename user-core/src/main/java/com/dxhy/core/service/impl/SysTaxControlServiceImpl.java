package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.SysTaxControlMapper;
import com.dxhy.core.pojo.entity.SysTaxControl;
import com.dxhy.core.pojo.vo.SysDeptResqVo;
import com.dxhy.core.service.ISysTaxControlService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-07
 */
@Service
@Slf4j
public class SysTaxControlServiceImpl extends ServiceImpl<SysTaxControlMapper, SysTaxControl> implements ISysTaxControlService {
    @Resource
    private SysTaxControlMapper sysTaxControlMapper;
    @Override
    public boolean syncTaxControl(String deptId, SysDeptResqVo sysDept,int type) {
        SysTaxControl control = new SysTaxControl();
        control.setDeptId(deptId);
        control.setType(type);
        SysTaxControl sysTaxControl = sysTaxControlMapper.selectOne(control);
        if (type == 0) {
            control.setSksbbm(sysDept.getSksbbm());
            control.setSksbmc(sysDept.getSksbmc());
        }else{
            control.setSksbbm(sysDept.getAceId());
            control.setSksbmc(sysDept.getAceKey());
        }
        if (StringUtils.isNotBlank(deptId) && sysTaxControl==null) {
            boolean insert = this.insert(control);
            log.info("insert sys_tax_control ={}", insert);
            return insert;
        }else if(sysTaxControl!=null){
            boolean update = this.update(control,new EntityWrapper<SysTaxControl>().eq("dept_id",deptId).eq("type",type));
            log.info("update sys_tax_control ={}", update);
            return update;
        }
        return false;

    }
}
