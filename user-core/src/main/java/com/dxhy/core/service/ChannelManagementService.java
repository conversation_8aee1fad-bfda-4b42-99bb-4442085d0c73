package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.DTO.ChannelManagementListDTO;
import com.dxhy.core.pojo.DTO.ChannelManagementSaveDTO;
import com.dxhy.core.pojo.entity.ChannelManagement;

/**
 * 通道管理表 service
 * <AUTHOR>
 * @Date 2025/01/10
 * @Version 1.0
 **/
public interface ChannelManagementService extends IService<ChannelManagement> {

    /**
     * 查询通道管理列表（分页）
     * @param channelManagementListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    Result queryPage(ChannelManagementListDTO channelManagementListDTO);
    /**
     * 获取匹配到的通道名称（开票服务调用）
     * @param channelManagementListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    Result getChannelName(ChannelManagementListDTO channelManagementListDTO);

    /**
     * 获取匹配到的所有产品的所有业务类型
     * @param channelManagementListDTO
     * @return
     */
    Result getBusinessTypes(ChannelManagementListDTO channelManagementListDTO);
    /**
     * 获取匹配到的所有产品的所有业务类型
     * @param channelManagementListDTO
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    Result getBusinessTypeList(ChannelManagementListDTO channelManagementListDTO);
    /**
     * 新增&修改 通道管理
     * @param channelManagementSaveDTO
     * @param userName
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    Result saveData(ChannelManagementSaveDTO channelManagementSaveDTO,String userName);

    /**
     * 删除通道管理
     * @param id
     * @param userName
     * @return com.dxhy.order.utils.R
     * <AUTHOR>
     **/
    Result deleteData(String id,String userName);

    /**
     * 根据id查询通道管理信息
     * @param id
     * @return
     */
    Result getById(String id);
}