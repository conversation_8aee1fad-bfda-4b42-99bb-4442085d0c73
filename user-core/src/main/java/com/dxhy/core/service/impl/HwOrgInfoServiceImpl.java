package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.SysDeptMapper;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysTaxbureauInfo;
import com.dxhy.core.pojo.entity.SysUserDept;
import com.dxhy.core.pojo.hw.OrgRequestDto;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.Generator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Slf4j
@Service
public class HwOrgInfoServiceImpl implements HwOrgInfoService {
    @Resource
    private SysDeptService sysDeptService;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysUserService sysUserService;
    @Resource
    private SysUserDeptService sysUserDeptService;
    @Resource
    private BSystemLogicService bSystemLogicService;
    @Resource
    private SysTaxbureauInfoService sysTaxbureauInfoService;
    @Override
    public Boolean addOrgBySingle(OrgRequestDto orgRequestDto) {
        SysDept deptInfo = new SysDept();
        deptInfo.setDeptSname(orgRequestDto.getOrgName());
        deptInfo.setName(orgRequestDto.getOrgName());
        deptInfo.setDelFlag("0");
        deptInfo.setAuthorizationCode(this.authCode());
        deptInfo.setCode("ID-" + "A");
        deptInfo.setTaxpayerType("0");
        deptInfo.setAccountingPrincipleCode("1");
        deptInfo.setAccountingPrinciple("企业会计准则-一般企业");
        if (StringUtils.isBlank(orgRequestDto.getParentCode())) {
            deptInfo.setEnterpriseNumbers(this.getEnterpriseNumbers());
            deptInfo.setLevel(1);
            deptInfo.setDeptType(1);
            deptInfo.setParentId(null);
        }else{
            //根据子code 查询 父code 查询到 说明是子级
            String parentCode = orgRequestDto.getParentCode();
            SysDept sysDept = sysDeptMapper.selectByDeptIdAndTenantId(StringUtils.isBlank(parentCode)?"notnull":parentCode,orgRequestDto.getTenantId(),null);
            if (sysDept != null) {
                deptInfo.setCode(sysDept.getCode() + "ID-" + "A");
                deptInfo.setEnterpriseNumbers(this.getEnterpriseNumbers(sysDept.getEnterpriseNumbers()));
                deptInfo.setLevel(sysDept.getLevel()+1);
                deptInfo.setDeptType(2);
                deptInfo.setParentId(orgRequestDto.getParentCode());
            }else{
                deptInfo.setEnterpriseNumbers(this.getEnterpriseNumbers());
                deptInfo.setLevel(1);
                deptInfo.setDeptType(1);
            }

        }
        if(orgRequestDto.getFlag() == 1) {//新增
            deptInfo.setDeptId(orgRequestDto.getOrgCode());
            deptInfo.setSourceId(orgRequestDto.getTenantId());
            deptInfo.setDataSource("6");//华为云
            deptInfo.setCreateTime(new Date());
            deptInfo.setUpdateTime(new Date());
            deptInfo.setCreateUser(1l);
            deptInfo.setTenantId(orgRequestDto.getTenantId());
            deptInfo.setStatus(1);
            sysDeptService.insert(deptInfo);
            //给该租户下所有用户 添加数据权限
            Boolean userDept = addUserOrgData(orgRequestDto.getOrgCode(),orgRequestDto.getTenantId());
        }else if(orgRequestDto.getFlag() == 2) {//修改
            SysDept sysDept = sysDeptMapper.selectByDeptIdAndTenantId(orgRequestDto.getOrgCode(),orgRequestDto.getTenantId(),null);
            if (sysDept != null) {
                sysDept.setDeptSname(deptInfo.getDeptSname());
                sysDept.setName(deptInfo.getName());
                sysDept.setCode(deptInfo.getCode());
                sysDept.setLevel(deptInfo.getLevel());
                sysDept.setParentId(deptInfo.getParentId());
                sysDept.setDeptType(deptInfo.getDeptType());
                sysDept.setUpdateTime(new Date());
                sysDeptService.updateById(sysDept);
            }
        }
        int num = sysDeptMapper.updateCodeByTenantIdAndDeptId(orgRequestDto.getOrgCode(),orgRequestDto.getTenantId());
        if (num >0) {
            log.info("更新code数量:{} ",num);
        }
        //清除租户下所有用户信息缓存
        List<Long> userIds = sysUserMapper.getUserIdList(orgRequestDto.getTenantId());
        for (int i = 0; i < userIds.size(); i++) {
            sysUserService.redisDeleteByUserId(userIds.get(0));
        }
        return Boolean.TRUE;
    }

    public Boolean addUserOrgData(String orgCode, String tenantId) {
        List<SysUserDept> userDeptList = new ArrayList<>();
        List<Long> userIds = sysUserMapper.getUserIdList(tenantId);
        for (int i = 0; i < userIds.size(); i++) {
            SysUserDept sysUserDept = new SysUserDept();
            sysUserDept.setDeptId(orgCode);
            sysUserDept.setUserId(userIds.get(i));
            userDeptList.add(sysUserDept);
        }
        return sysUserDeptService.insertBatch(userDeptList);
    }

    @Override
    public Boolean addOrgByBatch(OrgRequestDto orgRequestDto) {
        List<SysDept> deptAddList = new ArrayList<>();
        List<SysDept> deptUpdateList = new ArrayList<>();
        JSONArray jsonArray = JSON.parseArray(orgRequestDto.getOrgInfo());
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            log.info(jsonObject.toString());
            String orgCode = jsonObject.getString("orgCode");
            if (StringUtils.isBlank(orgCode)) {
                continue;
            }
            String parentCode = jsonObject.getString("parentCode");
            String name = jsonObject.getString("name");
            OrgRequestDto orgRequestDto1  = new OrgRequestDto();
            orgRequestDto1.setOrgCode(orgCode);
            orgRequestDto1.setOrgName(name);
            orgRequestDto1.setParentCode(parentCode);
            orgRequestDto1.setTenantId(orgRequestDto.getTenantId());
            this.synInvoiceData(orgRequestDto1);
            SysDept sysDept = sysDeptMapper.selectByDeptIdAndTenantId(orgCode,orgRequestDto.getTenantId(),null);
            //根据子code 查询 父code 查询到 说明是子级
            SysDept sysDept1 = sysDeptMapper.selectByDeptIdAndTenantId(StringUtils.isBlank(parentCode)?"notnull":parentCode,orgRequestDto.getTenantId(),null);
            if (sysDept != null) {
                //批量修改
                sysDept.setDeptSname(name);
                sysDept.setName(name);
                sysDept.setDelFlag("0");
                sysDept.setAuthorizationCode(this.authCode());
                sysDept.setUpdateTime(new Date());
                if (StringUtils.isBlank(parentCode)) {
                    if (StringUtils.isNotBlank(sysDept.getParentId())) {
                        sysDept.setCode(sysDept.getId() + "A");
                        sysDept.setLevel(1);
                        sysDept.setDeptType(1);
                        sysDept.setParentId(null);
                    }
                }else{
                    if (sysDept1 != null) {
                        if (StringUtils.isBlank(sysDept.getParentId()) || !sysDept.getParentId().equals(parentCode)) {
                            sysDept.setCode(sysDept1.getCode() + sysDept.getId() + "A");
                            sysDept.setLevel(sysDept1.getLevel()+1);
                            sysDept.setDeptType(2);
                            sysDept.setParentId(parentCode);
                        }
                    }
                }

                //deptUpdateList.add(sysDept);
                Boolean up = sysDeptService.updateById(sysDept);
                log.info("修改结果 up：{} sysDept{}",up,sysDept);
            }else{
                //批量新增
                SysDept deptInfo = new SysDept();
                deptInfo.setDeptSname(name);
                deptInfo.setName(name);
                deptInfo.setDeptId(orgCode);
                deptInfo.setDelFlag("0");
                deptInfo.setAuthorizationCode(this.authCode());
                deptInfo.setCode("ID-" + "A");
                deptInfo.setTaxpayerType("0");
                deptInfo.setAccountingPrincipleCode("1");
                deptInfo.setAccountingPrinciple("企业会计准则-一般企业");
                if (StringUtils.isBlank(parentCode)) {
                    deptInfo.setEnterpriseNumbers(this.getEnterpriseNumbers());
                    deptInfo.setLevel(1);
                    deptInfo.setDeptType(1);
                    deptInfo.setParentId(null);
                }else{
                    //根据子code 查询 父code 查询到 说明是子级
                    if (sysDept1 != null) {
                        deptInfo.setCode(sysDept1.getCode() + "ID-" + "A");
                        deptInfo.setEnterpriseNumbers(this.getEnterpriseNumbers(sysDept1.getEnterpriseNumbers()));
                        deptInfo.setLevel(sysDept1.getLevel()+1);
                        deptInfo.setDeptType(2);
                        deptInfo.setParentId(parentCode);
                    }else{
                        deptInfo.setEnterpriseNumbers(this.getEnterpriseNumbers());
                        deptInfo.setLevel(1);
                        deptInfo.setDeptType(1);
                    }
                }
                deptInfo.setDeptId(orgCode);
                deptInfo.setSourceId(orgRequestDto.getTenantId());
                deptInfo.setDataSource("6");//华为云
                deptInfo.setCreateTime(new Date());
                deptInfo.setUpdateTime(new Date());
                deptInfo.setCreateUser(1l);
                deptInfo.setTenantId(orgRequestDto.getTenantId());
                deptInfo.setStatus(1);
                //deptAddList.add(deptInfo);
                //给该租户下所有用户 添加数据权限
                Boolean userDept = addUserOrgData(orgCode,orgRequestDto.getTenantId());
                sysDeptService.insert(deptInfo);
                log.info("修改：sysDept{}",sysDept);
            }
        }
        int num = sysDeptMapper.updateCodeByTenantIdAndDeptId(null,orgRequestDto.getTenantId());
        if (num >0) {
            log.info("更新code数量:{} ",num);
        }
//        log.debug("全量同步集合报文 deptAddList：{} deptUpdateList：{}",deptAddList,deptUpdateList);
//        if (deptAddList.size() >0) {
//            Boolean isaddOk = sysDeptService.insertBatch(deptAddList);
//        }
//        if (deptUpdateList.size() >0) {
//            Boolean isupdateOk = sysDeptService.updateBatchById(deptUpdateList);
//        }
        //清除租户下所有用户信息缓存
        List<Long> userIds = sysUserMapper.getUserIdList(orgRequestDto.getTenantId());
        for (int i = 0; i < userIds.size(); i++) {
            sysUserService.redisDeleteByUserId(userIds.get(0));
        }
        return Boolean.TRUE;
    }

    @Override
    public Result delDept(OrgRequestDto orgRequestDto) {
        //删除组织
        //如果有下级组织不能删除
        SysDept sysDept = sysDeptMapper.selectByDeptIdAndTenantId(null,orgRequestDto.getTenantId(),orgRequestDto.getOrgCode());
        if(sysDept!=null) {
            return Result.error(sysDept.getName()+"下还有级联组织，请删除下级组织后再删除");
        }else{
            //删除该组织应用的数据权限
            SysDept dept = sysDeptMapper.selectByDeptIdAndTenantId(orgRequestDto.getOrgCode(),orgRequestDto.getTenantId(),null);
            if (dept != null) {
                dept.setDelFlag("1");
                sysDeptMapper.update(dept,new EntityWrapper<SysDept>().eq("dept_id",dept.getDeptId()).eq("tenant_id",dept.getTenantId()));
                //删除用户组织关联关系
                //查询租户下所有用户ID
                List<Long> ulist = sysUserMapper.getUserIdList(orgRequestDto.getTenantId());
                for (int i = 0; i < ulist.size(); i++) {
                    sysUserDeptService.delete(new EntityWrapper<SysUserDept>().eq("user_id",ulist.get(i)).eq("dept_id",dept.getDeptId()));
                }
            }
        }
        //清除租户下所有用户信息缓存
        List<Long> userIds = sysUserMapper.getUserIdList(orgRequestDto.getTenantId());
        for (int i = 0; i < userIds.size(); i++) {
            sysUserService.redisDeleteByUserId(userIds.get(0));
        }
        return Result.ok();
    }

    @Override
    public Result synInvoiceData(OrgRequestDto orgRequestDto) {
        String taxNo = this.getUUID16();
        //全电：电子税务局账号密码入库
            SysTaxbureauInfo sysTaxbureauInfo = new SysTaxbureauInfo();
            sysTaxbureauInfo.setDeptId(orgRequestDto.getOrgCode());
            sysTaxbureauInfo.setTaxNo(taxNo);
            sysTaxbureauInfo.setUserName("taxCloud");
            sysTaxbureauInfo.setUserPass("taxCloud123");
            CommonRspVo commonRspVo = sysTaxbureauInfoService.updateTaxBureauInfo(sysTaxbureauInfo);


        JSONObject json = new JSONObject();
//        json.put("name","大3456789象01");
//        json.put("taxpayerCode","45678987654567876567876");
//        json.put("tenantId","34567898765");
        json.put("name",orgRequestDto.getOrgName());
        json.put("taxpayerCode",taxNo );//写死
        json.put("taxpayerType","0");
        json.put("taxBureaName","taxCloud");
        json.put("taxBureaPass","taxCloud123");
        json.put("tenantId",orgRequestDto.getTenantId());
        CommonRspVo response = bSystemLogicService.einvoiceAddDept(json);
        log.info("Saas系统同步租户信息到票税系统,全电数据交互结果：{}",response);
        if (!response.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("Saas系统同步租户信息到票税系统：全电数据交互失败");
            return Result.error(response.getCode(),response.getMessage());
        }
        return Result.ok();
    }

    public String getUUID16() {
        String uuid= UUID.randomUUID().toString().replace("-", "").toUpperCase();
        return uuid.substring(0,15);
    }

    public String authCode(){
         /**
          * 生成企业授权码
          */
         Generator generator = new Generator();
         String stringRandom = "";
         for (int i = 1; i > 0; i++) {
             //生成位数为20位参数
             stringRandom = generator.getStringRandom(20);
             //判断生成的参数是否唯一若非唯一再次生成
             SysDept deptByQybm = sysDeptMapper.queryDeptByQybm(stringRandom);
             if (deptByQybm == null) {
                 break;
             }
         }
         return stringRandom;
     }

    public String getEnterpriseNumbers(){
        /**
         * 生成企业编码
         */
        String enterpriseNumbers="";
        for(int i = 1; i > 0; i++){
            enterpriseNumbers = Generator.getRandomChar();
            SysDept system = sysDeptMapper.queryDeptByQybm(enterpriseNumbers);
            if(system==null){
                break;
            }
        }
        return Generator.getRandomChar();
    }

    public String getEnterpriseNumbers(String parentEnterNumbers){
        /**
         * 生成子企业编码
         */
        String enterpriseNumbers="";
        for(int i = 1; i > 0; i++){
            enterpriseNumbers =parentEnterNumbers;
            SysDept system = sysDeptMapper.queryDeptByQybm(enterpriseNumbers+"0"+i);
            if(system==null){
                break;
            }
        }
        return Generator.getRandomChar();
    }



}