/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.StatisticsMapper;
import com.dxhy.core.pojo.DTO.StatisticsDto;
import com.dxhy.core.pojo.vo.StatisticsRzVo;
import com.dxhy.core.pojo.vo.StatisticsVo;
import com.dxhy.core.service.IStatisticsService;
import com.dxhy.core.service.ITenantService;
import com.dxhy.core.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/30 17:05
 */
@Slf4j
@Service
public class StatisticsServiceImpl implements IStatisticsService {

	@Autowired
	private StatisticsMapper statisticsMapper;

	@Autowired
	private ITenantService tenantService;

	@Override
	public Result cylist(StatisticsVo vo) {
		int pageNo = vo.getPageNo();
		int pageSize = vo.getPageSize();
		Page<StatisticsDto> page = new Page(pageNo, pageSize);
		String tenantId = vo.getTenantId();
		List<String> tenantIdList = tenantService.getTenantIdList(tenantId);
		vo.setTenantIdList(tenantIdList);
		List<StatisticsDto> list = statisticsMapper.cylist(page, vo);
		page.setRecords(list);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data", pageUtils);
	}

	@Override
	public Result rzlist(StatisticsRzVo vo) {
		int pageNo = vo.getPageNo();
		int pageSize = vo.getPageSize();
		Page<StatisticsDto> page = new Page(pageNo, pageSize);
		String tenantId = vo.getTenantId();
		List<String> tenantIdList = tenantService.getTenantIdList(tenantId);
		vo.setTenantIdList(tenantIdList);
		List<StatisticsDto> list = statisticsMapper.rzlist(page, vo);
		page.setRecords(list);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data", pageUtils);
	}

	@Override
	public Result kplist(StatisticsVo vo) {
		int pageNo = vo.getPageNo();
		int pageSize = vo.getPageSize();
		Page<StatisticsDto> page = new Page(pageNo, pageSize);
		String tenantId = vo.getTenantId();
		List<String> tenantIdList = tenantService.getTenantIdList(tenantId);
		vo.setTenantIdList(tenantIdList);
		List<StatisticsDto> list = statisticsMapper.kplist(page, vo);
		page.setRecords(list);
		PageUtils pageUtils = new PageUtils(page);
		return Result.ok().put("data", pageUtils);
	}

}