package com.dxhy.core.service.impl;

import com.dxhy.core.mapper.SysRegionMapper;
import com.dxhy.core.pojo.vo.RegionParent;
import com.dxhy.core.service.SysRegionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 地区维护业务接口实现类
 * @date 2019-02-23 18:22
 */
@Service
public class SysRegionServiceImpl implements SysRegionService {

    /**
     * 地区信息
     */
    @Autowired
    private SysRegionMapper sysRegionMapper;

    /**
     * 获取所有的地区信息集合
     *
     * @return 所属地区集合
     */
    @Override
    public List<RegionParent> selectAllArea() {
        //未删除的
        List<RegionParent> regionList = sysRegionMapper.selectAllArea(1);
        return regionList;
    }


}
