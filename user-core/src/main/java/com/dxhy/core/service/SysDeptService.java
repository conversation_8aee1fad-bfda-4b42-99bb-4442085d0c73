/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.vo.SysDeptResqVo;

/**
 * <p>
 * 部门管理 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-01-20
 */
public interface SysDeptService extends IService<SysDept> {


    /**
     * 添加组织
     * @param sysDept
     * @return
     */
    Result addDept(SysDeptResqVo sysDept, String token);

    /**
     * 更新部门
     * @param sysDept 部门信息
     * @return 成功、失败
     */
    Result updateDept(SysDeptResqVo sysDept,boolean bookmark);

    /**
     * 查询自己及以下组织
     * @param deptId
     * @param isContainBm
     * @param userId 当前登录用户ID
     * @return
     */
    Result listMyselfAll(String deptId, boolean isContainBm,Long userId);


    /**
     * 查询当前级以下组织
     * @param deptId
     * @param isContainBm
     * @return
     */
    Result listMyselfAllByTier(String deptId, boolean isContainBm);

    /**
     * 查询自己及以下组织及每个组织的总人数
     * @param deptId
     * @param isContainBm
     * @return
     */
    Result listMyselfAllAndUserCount(String deptId, boolean isContainBm);

    /**
     * 查询自己下一级组织
     * @param pageSize
     * @param currPage
     * @param deptId
     * @param isContainBm
     * @return
     */
    Result listMyselfOneLevel(Integer pageSize, Integer currPage, String deptId, boolean isContainBm,String entName,String nsrsbh);

    /**
     * 通过ID查询
     * @param deptId
     * @return
     */
    Result selectById(String deptId);

    /**
     * 删除部门
     * @param deptId 部门 ID
     * @return 成功、失败
     */
    Result deleteDeptByDeptId(String deptId);
    /**
     * 删除部门批量
     * @param deptIds 部门ID逗号分隔字符串
     * @return 成功、失败
     */
    Result deleteDeptBatch(String deptIds);

    Result queryDeptByTaxpayerNameAndCode(String name, String taxpayerCode);

    String getDeptId();

    Result confSksbAndAce(String deptId,SysDeptResqVo sysDeptResqVo);

    Result queryAllTaxListByTax(String taxNo,String tenantId);
}
