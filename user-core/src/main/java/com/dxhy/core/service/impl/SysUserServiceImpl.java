package com.dxhy.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.common.assist.UserVo;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.R;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.common.util.RegistrationIssue;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.constants.SecurityConstants;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.enums.CodeTypeEnum;
import com.dxhy.core.enums.LoginUserTypeEnum;
import com.dxhy.core.enums.ProductNameEnum;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.DTO.*;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.ProductMenuTree;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.*;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.AESUtils;
import com.dxhy.core.utils.PageUtils;
import com.xiaoleilu.hutool.collection.CollectionUtil;
import com.xiaoleilu.hutool.util.RandomUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.math.BigInteger;
import java.net.URLDecoder;
import java.security.MessageDigest;
import java.text.MessageFormat;
import java.time.*;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.dxhy.core.utils.GenerateRoleCodeUtil.generateNum;


/**
 * <AUTHOR> @date
 */
@Slf4j
@Service
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {


    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;

    @Resource
    private SysMenuMapper sysMenuMapper;

    @Autowired
    private SysRoleMenuMapper sysRoleMenuMapper;

    @Resource
    private SysDeptMapper sysDeptMapper;

    @Autowired
    private DistributorMapper distributorMapper;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Resource
    private SysRoleMapper sysRoleDao;


    @Autowired
    private ProductService productService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private BaseSourceUserService baseSourceUserService;

    @Value("${sourceId.haokuaiji}")
    private String haokuaijiSourceId;//好会计渠道id

    @Value("${sourceId.yidaizhang}")
    private String yidaizhang;//易代账渠道id

    @Value("${sensors.project:default}")
    private String project; //神策服务器项目环境

    @Value("${defalut.parent}")
    private String defalutParent;

    @Value("${security.encode.key}")
    private String key;//用于解密的Key

    @Autowired
    private NacosParam nacosParam;

    @Autowired
    private SmsEmailService smsEmailService;

    @Autowired
    private SystemVersionMapper sysVersionMapper;

    @Resource
    private SysDeptService sysDeptService;

    @Resource
    private SysRoleService sysRoleService;

    @Resource
    private SysTenantProductMapper tenantProductMapper;

    @Autowired
    private SysUserService sysUserService;

    @Resource
    private BSystemLogicService bSystemLogicService;

    @Resource
    private ProductStatusService productStatusService;

    @Resource
    private ProductMenuMapper productMenuMapper;
    @Resource
    private IDictionaryService dictionaryService;
    @Resource
    private EmailLogMapper emailLogMapper;
    @Resource
    private SysTaxbureauInfoService sysTaxbureauInfoService;
    @Resource
    private SysTaxbureauInfoMapper sysTaxbureauInfoMapper;
    @Resource
    private ProductStatusMapper productStatusMapper;
    @Resource
    private SysIndustryMapper sysIndustryMapper;

    @Resource
    private ISysProductService sysProductService;

    @Resource
    private RestTemplate restTemplate;
    private ThreadFactory threadFactory = Executors.defaultThreadFactory();

    @Value("${sms.expire}")
    private Long smsExpire;
    @Value("${sms.smsContentLogin}")
    private String smsContentLogin;
    @Value("${sms.smsContentBind}")
    private String smsContentBind;
    @Value("${sms.smsContentUpass}")
    private String smsContentUpass;
    @Value("${sms.smsContentRegister}")
    private String smsContentRegister;

    @Value("${email.emailContent}")
    private String emailContent;

    @Value("${email.emailContentReset}")
    private String emailContentReset;

    @Override
    public Result queryUserInfo(Long userId) {
        SysUser sysUser =  sysUserMapper.selectById(userId);
        if (sysUser == null || "".equals(sysUser)) {
            return Result.error("未查到该用户信息");
        }
        log.info("api查询用户主体信息为{}",sysUser);

        //查询该用户所属部门信息
        String deptId = sysUser.getDeptId();
        SysDeptVo sysDeptVo=selectDeptOrDistr(deptId);
        if(sysDeptVo==null || "".equals(sysDeptVo)){
            return Result.error("该用户暂无部门信息");
        }
        log.info("api查询用户部门信息为:{}",sysDeptVo);

        SysDept sysDept = new  SysDept();
        BeanUtils.copyProperties(sysDeptVo,sysDept);

        //用户关联的所有机构的集合
        List<SysUserDept> depts = sysUserMapper.selectDeptIdsByUserId(userId);
        final Instant now = Instant.now();

        //定义taxplayercodeDeptList
        List<SysDeptVo> taxplayercodeDeptList = new ArrayList<>();

        if(depts!=null&&depts.size()>0){
            for(SysUserDept sysUserDept:depts){
                SysDeptVo sysDeptVo1= selectDeptOrDistr(sysUserDept.getDeptId());
                if (sysDeptVo1 == null || sysDeptVo1.getId() == null) {
                    continue;
                }
                //查询该机构有哪些角色
                List<SysRole> roleList= sysRoleMapper.selectRolesByDeptId(sysUserDept.getDeptId());
                sysDeptVo1.setRoleList(roleList);
                taxplayercodeDeptList.add(sysDeptVo1);
            }
        }
        List<SysUserRole> roles=sysUserMapper.selectRoleIdsByUserId(userId);
        List<SysRole> roleList=new ArrayList<>();
        List<Long> roleIdList=new ArrayList<>();
        List<SysMenu> menus=new ArrayList<> ();
        if(roles!=null&&roles.size()>0){
            for(SysUserRole sysUserRole:roles) {
                SysRole sysRole =sysRoleDao.selectById(sysUserRole.getRoleId());
                roleList.add(sysRole);
                roleIdList.add(sysUserRole.getRoleId());
            }
            //查询用户机构关联的有效产品id
            //List<Long> productIds=sysDeptMapper.selectProductIdByDeptId(deptId);
            List<String> menuIds= null;
            if (userId == 1L) {
                //大象超级管理员查询所有有效的菜单数据，不受权限和授权产品的限制
                menuIds = productMenuMapper.getMenuIdListByProId(null);
            } else {
                //根据角色id集合和产品id集合查询菜单id
                menuIds = sysUserMapper.queryMenuIdListByRoles(roleIdList);
            }
            //组织菜单数据
            if(menuIds!=null && menuIds.size()>0){
                Set set = new HashSet();
                List<String> listNew=new ArrayList<>();
                set.addAll(menuIds);
                listNew.addAll(set);
                for(String men:listNew){
                    SysMenu sysMenu=  sysMenuMapper.selectMenuByMenuId(men);
                    /**
                     * 去除标记删除的菜单
                     */
                    if(sysMenu!=null){
                        menus.add(sysMenu);
                    }else{
                        ProductMenuTree productMenuTree = sysMenuMapper.selectProductMenuByMenuId(men);
                        if(productMenuTree != null && StringUtils.equals(productMenuTree.getStatus(),"0")){
                            if(StringUtils.equals("3",sysDept.getEinType()) || StringUtils.isBlank(sysDept.getTaxpayerCode())){
                                // 无税号时将非系统管理的菜单过滤调
                                if(!StringUtils.equals("7",productMenuTree.getSystemSign())){
                                    continue;
                                }
                            }
                            log.info("通过菜单id:{}查询到的菜单详情结果为:{}",men,JSONObject.toJSONString(productMenuTree));
                            SysMenu sysMenu1= this.packageSysMenuByProductMenu(productMenuTree);
                            menus.add(sysMenu1);
                        }
                    }
                }
            }
        }

        log.info("[queryUserInfo接口]查询用户权限耗时"+ Duration.between(now,Instant.now()).toMillis());

        //taxplayercodeDeptList 排序 主机构放在最上层 按照level排序
        Collections.sort(taxplayercodeDeptList, Comparator.comparingInt(SysDept::getLevel));

        //查询试用期
        boolean tipPass = false; //是否显示提示框
        //处理时间
        long remainDay  = 0;
        if(sysUser.getUserSource().equals("1")) {//官网注册 PLG
            SysProductVo product = sysProductService.selectByTenantId(sysUser.getTenantId());
            if (BeanUtil.isNotEmpty(product)) {
                remainDay = DateUtil.between(new Date(), product.getAuthEtime(), DateUnit.DAY);
                if (remainDay <0) {
                    //试用期到期 无法登录
                    return new Result(ResponseCodeEnum.PLG_EXPIRE_REMIND);
                }else{
                    //显示提示框  剩余天数
                    tipPass = true;
                }
            }else{
                //非标准全电产品 无法登录
                return new Result(ResponseCodeEnum.PLG_ERROR);
            }
        }
        log.info("[queryUserInfo接口]试用期倒计时；是否显示提示框：{},剩余天数：{}",tipPass,remainDay);

        //创建UserInfoResponseDto并赋值主体信息
        UserInfoResponseDto userInfoResponseDto = toUserInfoResponseDto(sysUser);
        userInfoResponseDto.setTipPass(tipPass);
        userInfoResponseDto.setRemainDay((int) remainDay);
        userInfoResponseDto.setDept(sysDept);
        userInfoResponseDto.setTaxplayercodeDeptList(taxplayercodeDeptList);
        userInfoResponseDto.setRoles(roleList);
        userInfoResponseDto.setMenus(menus);
        return Result.ok().put("data",userInfoResponseDto);
    }

    /**
     * 根据deptId查询对应组织信息
     * @param deptId
     * @return
     */
    public SysDeptVo selectDeptOrDistr(String deptId){

        SysDept deptDao =sysDeptMapper.selectByDeptId(deptId);
        SysDeptVo sysdeptvo=new SysDeptVo();

        if(deptDao!=null) {
            BeanUtils.copyProperties(deptDao, sysdeptvo);
        }else{
            /**
             * 如sysdept表没有则需要查对应
             */
            Distributor distributor=distributorMapper.selectDistributorById(deptId);
            if(distributor!=null){
                sysdeptvo=this.packageSysDeptVoByDistributor(distributor);
            }
        }
        return sysdeptvo;
    }

    /**
     * 根据deptId查询对应组织信息
     * 不包含父级部门
     * @param deptId
     * @return
     */
    public SysDeptVo selectDeptOrDistr2(String deptId){
        SysDept deptDao =sysDeptMapper.selectByDeptId2(deptId);
        SysDeptVo sysdeptvo=new SysDeptVo();
        if(deptDao!=null) {
            BeanUtils.copyProperties(deptDao, sysdeptvo);
        }else{
            return null;
        }
        return sysdeptvo;
    }

    /**
     * 渠道转组织信息
     * @param distributor
     * @return
     */
    public SysDeptVo packageSysDeptVoByDistributor(Distributor distributor){
        SysDeptVo sysDeptVo=new SysDeptVo();
        sysDeptVo.setDeptId(distributor.getId());
        sysDeptVo.setParentId(distributor.getSuperior());
        sysDeptVo.setName(distributor.getCompanyName());
        sysDeptVo.setLevel(0);
        sysDeptVo.setTaxpayerCode(distributor.getTaxNo());
        sysDeptVo.setTaxpayerBank(distributor.getBankName());
        sysDeptVo.setTaxpayerAccount(distributor.getBankNo());
//       sysDeptVo.setTaxpayerType(StringUtils.isBlank(distributor.getTaxpayerType())?Integer.valueOf(distributor.getTaxpayerType()):0);
//       sysDeptVo.setTaxpayerIndustry(distributor.getTrade());
        sysDeptVo.setLocationCode(distributor.getLocation());
        sysDeptVo.setContactEmail(distributor.getContactEmail());
        sysDeptVo.setContactPhone(distributor.getContactPhone());
        return  sysDeptVo;
    }





    /**
     *
     * @param prov
     * @return
     */
    public SysMenu  packageSysMenuByProductMenu(ProductMenuTree prov){

        SysMenu sysMenu=new SysMenu();

        sysMenu.setMenuId(prov.getId());
        sysMenu.setName(prov.getName());
        sysMenu.setParentId(prov.getParentId());
        sysMenu.setPermission(prov.getPermission());
        sysMenu.setPath(prov.getPath());
        sysMenu.setUrl(prov.getUrl());
        sysMenu.setIcon(prov.getIcon());
        sysMenu.setMethod(prov.getMethod());
        sysMenu.setSystemSign(prov.getSystemSign());
        sysMenu.setSort(prov.getSort());
        sysMenu.setType(prov.getType());
        sysMenu.setProductId(prov.getProductId());
        SysProduct sysProduct= sysMenuMapper.selectSysProductByMenuId(prov.getProductId());
        if(sysProduct!=null){
            sysMenu.setProductName(sysProduct.getName());
        }

        return sysMenu;
    }

    private UserInfoResponseDto toUserInfoResponseDto(SysUser sysUserEntity){
        UserInfoResponseDto userInfoResponseDto = new UserInfoResponseDto();
        userInfoResponseDto.setName(sysUserEntity.getNickname());
        userInfoResponseDto.setTenantId(sysUserEntity.getTenantId());
        userInfoResponseDto.setDeptId(sysUserEntity.getDeptId());
        userInfoResponseDto.setUserId(Long.valueOf(sysUserEntity.getUserId()));
        userInfoResponseDto.setUsername(sysUserEntity.getUsername());
        userInfoResponseDto.setPassword(sysUserEntity.getPassword());
        userInfoResponseDto.setEmail(sysUserEntity.getEmail());
        userInfoResponseDto.setMobile(sysUserEntity.getPhone());
        userInfoResponseDto.setStatus(sysUserEntity.getStatus());
        userInfoResponseDto.setCreateTime(sysUserEntity.getCreateTime());
        userInfoResponseDto.setSalt(sysUserEntity.getSalt());
        userInfoResponseDto.setUpdateTime(sysUserEntity.getUpdateTime());
        userInfoResponseDto.setDelFlag(sysUserEntity.getDelFlag());
        userInfoResponseDto.setAvatar(sysUserEntity.getAvatar());
        userInfoResponseDto.setCreateBy(sysUserEntity.getCreateBy());
        userInfoResponseDto.setUserType(sysUserEntity.getUserType());
        userInfoResponseDto.setUserSource(sysUserEntity.getUserSource());
        userInfoResponseDto.setUpdateBy(sysUserEntity.getUpdateBy());
        userInfoResponseDto.setDistributorId(sysUserEntity.getDistributorId());
        userInfoResponseDto.setLastLoginTime(sysUserEntity.getLastLoginTime());
        return userInfoResponseDto;
    }

    /**
     * 通过userid判断用户对应的企业是否激活状态
     * @param userId
     * @return
     */
    @Override
    public R<Boolean> getUserStatusByUserId(Long userId) {

        SysUser sysUser = this.selectById(userId);
        if(sysUser.getDelFlag().equals("0")){
            return new R<>(Boolean.TRUE);
        }else{
            return new R<>(Boolean.FALSE);
        }
    }

    @Override
//    @Cacheable(value = "user_details", key = "#username")
    public UserVO findUserByUsername(String username) {
        return sysUserMapper.selectUserVoByUsername(username);
    }

    @Transactional(rollbackFor = Exception.class)
    public R register(EntUserDTO entUserDTO) throws Exception {
        return null;
    }

    @Override
    public void redisDeleteByUserId(Long userId){
        if(userId != null){
            Set keySet = redisTemplate.keys(SystemConstants.USER_INFO_CODE_KEY + userId + "*");
            Iterator<String> iterator = keySet.iterator();
            while (iterator.hasNext()) {
                String nextKey = iterator.next();
                redisTemplate.delete(nextKey);
            }
            /**
             * 删除权限缓存
             * 20220812 新增
             */
            Set keySet1 = redisTemplate.keys(SystemConstants.DEPT_INFO_CODE_KEY + userId + "*");
            Iterator<String> iterator1 = keySet1.iterator();
            while (iterator1.hasNext()) {
                String nextKey = iterator1.next();
                redisTemplate.delete(nextKey);
            }
        }
    }

    /**
     * 判断用户是否开通当前产品权限
     * @param userId
     * @param productId
     * @param sourceId
     * @return
     */
    @Override
    public R getPermissionCheck(Long userId,String productId,String sourceId){

        String status = "7"; //默认未开通
        Map<String, String> data = new HashMap<>();
        try {
            List<BaseSourceUser> baseSourceUsers = baseSourceUserService.selectList(new EntityWrapper<BaseSourceUser>().eq("user_id", userId).eq("type", "1"));
            if (baseSourceUsers != null && baseSourceUsers.size() > 0) {
                //用户非客户端跨渠道登录
                if (!baseSourceUsers.get(0).getSourceId().equals(sourceId) && !productId.equals(ProductNameEnum.CEPING.getId())) {
                    String url = "";
                    status = "6";
                    Distributor distributor = distributorMapper.selectDistributorBySimpleCode(baseSourceUsers.get(0).getSourceId());
                    if (distributor != null) {
                        String distributorId = distributor.getId();
                        List<String> uris = sysUserMapper.selectUriByDistributorIdProId(productId, distributorId);
                        log.info("根据productId {}和sourceId {}获取产品链接返回的参数为:{}", productId, sourceId, JSONObject.toJSONString(uris));
                        if (uris != null && uris.size() > 0) {
                            url = URLDecoder.decode(uris.get(0), "UTF-8");
                        }
                    }
                    data.put("status", status);
                    data.put("url", url);
                    return new R(data);
                }
            }

            //获取产品权限
            SysUser sysUser = sysUserMapper.selectById(userId);
            Map productOpen = this.getProductOpen(sysUser, userId, productId, "1");
            if (productOpen != null) {
                status = productOpen.get("status").toString();
            }
            data.put("status", status);

            return new R(data);
        }catch (Exception e){
            return new R(R.FAIL,"开通当前产品权限异常："+e.getMessage());
        }
    }

    @Override
    public UserVO findUserByMobile(String mobile) {
        return sysUserMapper.selectUserVoByMobile(mobile);
    }

    /**
     * 神策登录事件
     * @param entUserDTO
     * @param deviceId
     * @return
     */
    @Override
    public R<Boolean> sensorsLogin(EntUserDTO entUserDTO,String deviceId,String userType){

        String productName = "";
        if(StringUtils.isBlank(entUserDTO.getProductName())){
            //获取产品名称
            productName=sysUserMapper.selectProductInfoById(entUserDTO.getProductId());
            if(productName == null ) {
                log.warn("获取产品详情失败！", entUserDTO.getProductId());
                log.warn("根据产品id:{},获取获取产品详情返回结果：{}",entUserDTO.getProductId(), productName);
            }
        }else{
            productName = entUserDTO.getProductName();
        }
        HashMap<String, Object> properties = new HashMap<String, Object>();
        UserVO vo = null;
        String username = entUserDTO.getUsername();
        String newUserName = username.substring(0,username.length()-2);
        if(userType.equals(LoginUserTypeEnum.COMPANY.getCode())){
            vo = this.findUserByUsername(newUserName);
            properties.put("userId",vo.getUserId());
        }else if(userType.equals(LoginUserTypeEnum.PERSONAL.getCode())){
        }else{
            vo = this.findUserByUsername(username);
            properties.put("userId",vo.getUserId());
        }
        properties.put("account",entUserDTO.getUsername());
        if(entUserDTO.getUsername().contains("@")){
            properties.put("login_method","邮箱");
        }else{
            properties.put("login_method","手机号");
        }
        properties.put("device_id",deviceId);
        properties.put("productName",productName);
        properties.put("platformType",entUserDTO.getPlatformType());
        properties.put("platform",entUserDTO.getPlatform());
        try{
            RegistrationIssue registrationIssue = new RegistrationIssue();
            //神策登录事件日志记录
            registrationIssue.loginSuccessLog(properties);
            registrationIssue.loginSuccessHttp(properties,project);
        }catch (Exception e){
            log.error("sensorsLogin-神策登录事件异常",e.toString());
        }
        R r = new R<>(true);
        return r;
    }


    /**
     * 根据产品url获取渠道信息
     * @param uri
     * @return
     */
    @Override
    public R getSourceInfoByUri(String uri){
        log.debug("【getSourceInfoByUri】根据产品url,获取渠道信息请求参数uri:{}", uri);
        long startTime = System.currentTimeMillis();
        Map sourceMap = (Map)redisTemplate.opsForValue().get(SystemConstants.LOGIN_PRODUCT_CODE_KEY + uri);
        Map<String, String> map=new HashMap<String, String>();
        if(sourceMap == null || !"0000".equals(sourceMap.get("code"))){
            log.debug("【getSourceInfoByUri】根据产品url获取渠道信息缓存为空或者异常");
            try {
                map = sysUserMapper.selectSourceIdProIdByUri(URLDecoder.decode(uri, "UTF-8"));
                if(sourceMap==null){
                    sourceMap =new HashMap<String,Object>();
                    sourceMap.put("code","0000");
                    sourceMap.put("message","SUCCESSFULL");
                    sourceMap.put("data",map);
                }else{
                    sourceMap.put("code","0000");
                    sourceMap.put("message","SUCCESSFULL");
                    sourceMap.put("data",map);
                }
            }catch (Exception e){
                log.error("根据产品url,获取渠道信息返回异常："+e.getMessage());
                return new R();
            }
            redisTemplate.opsForValue().set(SystemConstants.LOGIN_PRODUCT_CODE_KEY+uri, sourceMap,600, TimeUnit.SECONDS);
        }
        long endTime = System.currentTimeMillis();
        log.debug("【getSourceInfoByUri】根据产品url,获取渠道信息返回结果: {},{},请求耗时:{}",uri, JSON.toJSONString(sourceMap == null ? "" : sourceMap),(endTime - startTime) + "ms");
        if(sourceMap == null || !"0000".equals(sourceMap.get("code"))) {
            log.warn("根据产品url获取渠道信息失败！", uri);
            return new R();
        }
        Map<String, String> data = (Map) sourceMap.get("data");
        return new R(data);
    }

    /**
     * 获取产品详情
     * @param productId
     * @return
     */
    @Override
    public R<String> getProductInfoById (String productId){
        log.debug("【getProductInfoById】根据产品ID获取产品详情,请求参数productId:{}", productId);
        long startTime = System.currentTimeMillis();
        Map<String, Object> productMap = null;
        //aospProductService.getProductInfoById(productId);
        long endTime = System.currentTimeMillis();
        log.debug("【getProductInfoById】根据产品ID获取产品详情，返回结果：{}, {},请求耗时:{}",productId, JSON.toJSONString(productMap == null ? "" : productMap),(endTime - startTime) + "ms");
        if(productMap == null ) {
            log.warn("获取产品详情失败！", productId);
            return new R();
        }
        return new R(productMap.get("name"));
    }

    /**
     * 保存用户验证码，和randomStr绑定
     *
     * @param randomStr 客户端生成
     * @param imageCode 验证码信息
     */
    @Override
    public void saveImageCode(String randomStr, String imageCode) {
        redisTemplate.opsForValue().set(SecurityConstants.DEFAULT_CODE_KEY + randomStr, imageCode, SecurityConstants.DEFAULT_IMAGE_EXPIRE, TimeUnit.SECONDS);
    }

    /**
     * 产品开通
     * @param sysUser
     * @param userId
     * @param productId
     * @param type
     * @return
     */
    public HashMap<String, String> getProductOpen(SysUser sysUser,Long userId,String productId,String type){
        HashMap<String, String> map = new HashMap<>();
        try {
            Integer status=sysUserMapper.selectByUserAndProductId(userId,productId);
            if(status==null){
                log.info("用户没有开通该产品,默认开通试用，对应用户id:"+userId);
                /**
                 * 查询字典配置产品项
                 */
                List<String> dictionaryList = sysUserMapper.getDictionaryByParent(Integer.valueOf(defalutParent));
                if (StringUtils.isNotBlank(productId)) {
                    dictionaryList.add(productId);
                }
                log.info("字典配置的默认开通产品数据:{}", JSONObject.toJSONString(dictionaryList));
                List<ProductInfo> productInfo = productService.getListByDistributorId(sysUser.getDistributorId(), "2");
                if (dictionaryList != null && dictionaryList.size() > 0) {
                    if (productInfo != null && productInfo.size() > 0) {
                        for (String desc : dictionaryList) {
                            for (ProductInfo pro : productInfo) {
                                if (desc.equals(pro.getId())) {
                                    Map<String, Object> insertMap = new HashMap<String, Object>();
                                    insertMap.put("accountInfoId", userId);
                                    insertMap.put("createTime", new Date());
                                    insertMap.put("modifyTime", new Date());
                                    if ("3".equals(sysUser.getUserType())) {
                                        insertMap.put("customerType", "2");
                                    } else {
                                        insertMap.put("customerType", "1");
                                    }
                                    insertMap.put("customerId", String.valueOf(userId));
                                    insertMap.put("productId", pro.getId());
                                    insertMap.put("beginTime", new Date());
                                    insertMap.put("used", 0);
                                    insertMap.put("productType", 1);
                                    String total = new DateTime().toString(CommonConstant.YYYYMMDD) + "|" + new DateTime().plusYears(1).toString(CommonConstant.YYYYMMDD);
                                    insertMap.put("total", total);
                                    insertMap.put("id", UUID.randomUUID().toString().replaceAll("\\-", ""));
                                    insertMap.put("unit", 2);
                                    insertMap.put("delFlag", 2);
                                    insertMap.put("status", 1);
                                    sysUserMapper.insertCustomerProduct(insertMap);
                                }
                            }
                        }
                    }
                }
            }else{
                /**
                 * 更新对应用户产品开通状态
                 */
                Map<String, Object> updateMap = new HashMap<String, Object>();
                updateMap.put("accountInfoId", userId);
                updateMap.put("modifyTime", new Date());
                String total = new DateTime().toString(CommonConstant.YYYYMMDD) + "|" + new DateTime().plusYears(1).toString(CommonConstant.YYYYMMDD);
                updateMap.put("total", total);
                updateMap.put("status", 1);
                sysUserMapper.updateCustomerProduct(updateMap);
            }

            String productName= sysUserMapper.selectProductInfoById(productId);
            map.put("status","1");
            map.put("productName",productName);
            map.put("productId",productId);
        }catch (Exception e){
            log.error("默认开通产品异常，错误日志:{}", e.getMessage());
            return null;
        }

        return map;
    }
    /**
     * 暂时没用到
     * @param mobile
     * @param email
     * @param code
     * @return
     * <AUTHOR>
     * @date 2022-05-19
     */
    @Override
    public String validateCode(String mobile, String email,String uuid, String code) {

        String username = null;
        String redisCodeKey = null;
        if (StringUtils.isNotBlank(mobile)) {
            username = mobile;
            redisCodeKey = SystemConstants.DEFAULT_SMS_CODE_KEY;
        } else if (StringUtils.isNotBlank(email)) {
            username = email;
            redisCodeKey = SystemConstants.DEFAULT_EMAIL_CODE_KEY;
        } else if (StringUtils.isNotBlank(uuid)) {
            username = uuid;
            redisCodeKey = SystemConstants.DEFAULT_CODE_KEY;
        }
        String tempCode = (String) redisTemplate.opsForValue().get(redisCodeKey + username);

        if (tempCode != null && tempCode.equals(code)) {
            log.info("清楚验证码缓存");
            redisTemplate.delete(redisCodeKey + username);
        }
        return tempCode;

    }

    @Override
    public R<Boolean> validateCode(String mobile, String email, String code) {

        String username = null;
        String redisCodeKey = null;
        if(org.apache.commons.lang.StringUtils.isNotBlank(mobile)) {
            username = mobile;
            redisCodeKey = SystemConstants.DEFAULT_SMS_CODE_KEY;
        } else if(org.apache.commons.lang.StringUtils.isNotBlank(email)){
            username = email;
            redisCodeKey = SystemConstants.DEFAULT_EMAIL_CODE_KEY;
        }
        String tempCode = (String) redisTemplate.opsForValue().get(redisCodeKey + username);
        if (org.apache.commons.lang.StringUtils.isBlank(tempCode)) {
            log.error("用户:{}验证码已失效{}", username, tempCode);
            return new R<>(1, "验证码已失效，请再次申请");
        }
        if (!tempCode.equals(code)) {
            log.error("用户:{}验证码错误{}", username, tempCode);
            return new R<>(2, "验证码错误，请重新输入");
        }
        redisTemplate.delete(redisCodeKey + username);
        return new R<>(0, "验证成功");
    }

    /**
     * 查询用户组织权限（子企业）
     * @param userId
     * @return
     * <AUTHOR>
     * @date 2022-06-22
     */
    @Override
    public Result getDeptInfo(Long userId) {
        //1.查询userInfo主体信息
        SysUser sysUser =  sysUserMapper.selectById(userId);
        if (sysUser == null || "".equals(sysUser)) {
            return Result.error("未查到该用户信息");
        }
        log.info("[getDeptInfo接口]api查询用户主体信息为{}",sysUser);

        /**
         * 查询对应渠道简码
         */
//        Distributor distributor=distributorMapper.selectDistributorById(sysUser.getDistributorId());
//        if(distributor==null){
//            return Result.error("未查到该用户所属渠道信息");
//        }
//        String simpleCode=distributor.getSimpleCode();

        //2.查询该用户所属部门信息 deptInfo
        String deptId = sysUser.getDeptId();
        SysDeptVo sysDeptVo=selectDeptOrDistr(deptId);
        if(sysDeptVo==null || "".equals(sysDeptVo)){

            return Result.error("该用户暂无部门信息");
        }
        log.info("[getDeptInfo接口]api查询用户部门信息为:{}",sysDeptVo);


        SysDept sysDept=new  SysDept();
        BeanUtils.copyProperties(sysDeptVo,sysDept);
        UserInfoDTO userInfoDTO = new UserInfoDTO();
        BeanUtils.copyProperties(sysUser,userInfoDTO);



        final Instant now = Instant.now();
        userInfoDTO.setMobile(sysUser.getPhone());
        userInfoDTO.setName(sysUser.getNickname());

        //3.定义taxplayercodeDeptList
        List<SysDept> taxplayercodeDeptList = sysDeptMapper.queryUserDeptList(sysUser.getUserId());

        //4.查询开通的产品列表
        List<SysTenantProduct> tenantProductList = tenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("tenant_id",sysUser.getTenantId()));

        log.info("[getDeptInfo接口]查询用户权限耗时"+ Duration.between(now,Instant.now()).toMillis());
        //创建DeptInfoResponseDto并赋值3个对象信息
        DeptInfoResponseDto deptInfoResponseDto = new DeptInfoResponseDto();
        deptInfoResponseDto.setUserInfo(userInfoDTO);
        deptInfoResponseDto.setDeptInfo(sysDept);
        deptInfoResponseDto.setTenantProductList(tenantProductList);
        deptInfoResponseDto.setTaxplayercodeDeptList(taxplayercodeDeptList);
        return Result.ok().put("data",deptInfoResponseDto);
    }

    @Override
//    @CacheEvict(value = "user_details", key = "#username")
    public R<Boolean> settingPasswrod(Map<String, String> paramMap, Long userId) {
        SysUser sysUser  = this.selectById(userId);
        String newPassword = paramMap.get("newPassword");
        if (sysUser == null) {
            log.error("修改密码，未获取到用户信息,用户:{}", userId);
            return new R<>(3, "未获取到用户信息");
        }
        sysUser.setPassword(ENCODER.encode(newPassword));
        sysUser.setUpdateTime(new Date());
        this.updateById(sysUser);
        this.redisDeleteByUserId(sysUser.getUserId());
        return new R(true);
    }


    @Override
//    @CacheEvict(value = "user_details", key = "#username")
    public R<Boolean> updatePassword(Map<String, String> paramMap, Long userId) throws Exception {
        SysUser sysUser  = this.selectById(userId);
        String password = paramMap.get("password");
        String newPassword = paramMap.get("newPassword");
        if (sysUser == null) {
            log.error("修改密码，未获取到用户信息,用户:{}", userId);
            return new R<>(3, "未获取到用户信息");
        }
        if (password.equals(newPassword)) {
            log.error("修改密码，新密码不能与原密码相同,用户:{}", sysUser.getUsername());
            return new R<>(2, "新密码不能与原密码相同");
        }
        boolean bool = ENCODER.matches(password, sysUser.getPassword());
        if (!bool) {
            try{
                MessageDigest md = MessageDigest.getInstance("MD5");
                // 计算md5函数
                md.update(password.getBytes());
                String passwordMd5 = new BigInteger(1, md.digest()).toString(16);
                if(!passwordMd5.equals(sysUser.getPassword())){
                    log.error("修改密码，用户:{}原密码错误{}", sysUser.getUsername(), password);
                    return new R<>(4, "原密码错误，请重新输入");
                }
            }catch (Exception e){
                log.error("修改密码，用户:{}，异常{}", sysUser.getUsername(), e.toString());
            }
        }
        sysUser.setPassword(ENCODER.encode(newPassword));
        sysUser.setUpdateTime(new Date());
        this.updateById(sysUser);
        String content = "【忘记密码】账号："+sysUser.getUsername()+"，新密码："+AESUtils.decryptAES(newPassword, key);
        emailLogMapper.insert(new EmailLog(content));

//        // 异步同步报销系统
//        threadFactory.newThread(new Runnable() {
//            @Override
//            public void run() {
//                reimbursementService.resetPassword(sysUser.getPhone(),newPassword);
//            }
//        }).start();

        this.redisDeleteByUserId(userId);

        return new R(true);
    }

    /**
     * 根据用户查询用户信息
     * @param userId
     * @return
     */
    @Override
    public UserVO selectUserVoById(Long userId) {
        return sysUserMapper.selectUserVoById(userId);
    }


    /**
     * 发送验证码
     * @param codeDTO
     * @return
     */
    @Override
    public R<Boolean> sendCode(CodeDTO codeDTO) {
        log.info("【sendCode】发送短信接口入参:{}",JSON.toJSONString(codeDTO));
        R r = new R();

        if(codeDTO.getMobile() == null && codeDTO.getEmail() == null){
            r.setCode(R.FAIL);
            r.setMsg("无手机号/邮箱设置,无法发送短信");
            return r;
        }

        if (codeDTO.getSmsType() == null) {
            r.setCode(R.FAIL);
            r.setMsg("短信类型不能为空");
            return r;
        }

        if(StringUtils.isNotBlank(codeDTO.getMobile())) {
            r = this.sendSmsCode(codeDTO.getMobile(), codeDTO.getType(),codeDTO.getContent(),codeDTO.getSmsType());
        }else if(StringUtils.isNotBlank(codeDTO.getEmail())) {
//            r = this.sendEmailCode(codeDTO.getEmail(), codeDTO.getType());
        }

        return r;
    }

    @Override
    public String getSimpeCodeByUnameAndTenid(String username, String tenantId) {
        String simpeCode =  sysUserMapper.getSimpeCodeByUnameAndTenid(username,tenantId);
        return simpeCode;
    }

    /**
     * 发送验证码
     * <p>
     * 1. 先去redis 查询是否 60S内已经发送
     * 2. 未发送： 判断手机号是否存 ? false :产生4位数字  手机号-验证码
     * 3. 发往消息中心-》发送信息
     * 4. 保存redis
     *
     * @param mobile 手机号
     * @param type 类型；1登录;2=注册;3=忘记密码;4-绑定
     * @return true、false
     */
    public R<Boolean> sendSmsCode(String mobile, String type,String content,String smsType) {

        Object tempNum = redisTemplate.opsForValue().get(SystemConstants.DEFAULT_SMS_CODE_NUM_KEY + mobile);
        if (tempNum == null) {
            tempNum = 0;
        }
        if ((int) tempNum >= 50) {
            log.error("用户:{}当天最多获取验证码50次", mobile);
            return new R<>(4, "当天最多获取验证码50次");
        }
        SysUser params = new SysUser();
        params.setPhone(mobile);
        List<SysUser> userList = this.selectList(new EntityWrapper<>(params));
        String code = RandomUtil.randomNumbers(6);
        String smsContent = "";
        if (CodeTypeEnum.LOGIN.getCode().equals(type)) {
            if (CollectionUtil.isEmpty(userList)) {
                log.error("手机登录，根据用户手机号{}查询用户为空", mobile);
                return new R<>(3, "该手机号未注册");
            }
//            smsContent = smsContentLogin;
            smsContent = MessageFormat.format(smsContentLogin, code);
        } else if (CodeTypeEnum.REGISTER.getCode().equals(type)) {
            if (!CollectionUtil.isEmpty(userList)) {
                log.error("手机注册，根据用户手机号{}查询用户已注册", mobile);
                return new R<>(2, "手机号已注册，请进行登录");
            }
            //smsContent = smsContentRegister;
            smsContent =  MessageFormat.format(smsContentRegister, code);
        } else if (CodeTypeEnum.FORGOT_PASSWORD.getCode().equals(type)) {
            if (CollectionUtil.isEmpty(userList)) {
                log.error("手机忘记/修改密码，根据用户手机号{}查询用户未注册", mobile);
                return new R<>(3, "手机号未注册");
            }
            //smsContent = smsContentUpass;
            smsContent =  MessageFormat.format(smsContentUpass, code);
        } else if(CodeTypeEnum.BIND.getCode().equals(type)){

            if (!CollectionUtil.isEmpty(userList)) {
                log.error("手机绑定，根据用户手机号{}查询用户已注册", mobile);
                return new R<>(5, "该手机号已注册，请绑定未注册手机号");
            }
            smsContent =  MessageFormat.format(smsContentBind, code);
        } else if(CodeTypeEnum.BINDWECHAT.getCode().equals(type)){

        } else if(CodeTypeEnum.UNTIEDWECHAT.getCode().equals(type)){
            if (CollectionUtil.isEmpty(userList)) {
                log.error("微信解绑手机验证，根据用户手机号{}查询用户为空", mobile);
                return new R<>(7, "手机号未注册");
            }
        } else if(CodeTypeEnum.XIAOXIANG.getCode().equals(type)){
            if (CollectionUtil.isEmpty(userList)) {
                log.error("销项注册，根据用户手机号{}查询用户为空", mobile);
                return new R<>(8, "手机号未注册");
            }
        }else if(CodeTypeEnum.XIAOXIANG_REGISTER.getCode().equals(type)){
            if (!CollectionUtil.isEmpty(userList)) {
                log.error("销项注册，根据用户手机号{}查询用户已注册", mobile);
                return new R<>(9, "手机号已注册，请进行登录");
            }
        }else if(CodeTypeEnum.TEXT_MESSAGE.getCode().equals(type)){

        }else {
            log.error("未知短信类型,{}", type);
            return new R<>(6, "未知短信类型");
        }
        if (StringUtils.isNotBlank(content)) {
            callSms(mobile,tempNum,content,smsType);
        }else{

            callSmsCode(mobile,tempNum,smsContent,code);
        }
        return new R<>(true);
    }

    @Override
    public R<Boolean> sendWeChatCode(CodeDTO codeDTO) {
        log.info("【sendCode】发送短信接口入参:{}",JSON.toJSONString(codeDTO));
        R r = new R();

        if(codeDTO.getMobile() == null && codeDTO.getEmail() == null){
            r.setCode(R.FAIL);
            r.setMsg("无手机号/邮箱设置,无法发送验证码");
            return r;
        }

        if(StringUtils.isNotBlank(codeDTO.getMobile())) {
            r = this.sendWeChatSmsCode(codeDTO.getMobile(), codeDTO.getType());
        }
        return r;
    }

    @Override
    public UserVO selectUserVoByContactPhone(String phone) {
        return sysUserMapper.selectUserVoByContactPhone(phone);
    }

    private R sendWeChatSmsCode(String mobile, String type) {
        Object tempNum = redisTemplate.opsForValue().get(SystemConstants.DEFAULT_SMS_CODE_NUM_KEY + mobile);
        if (tempNum == null) {
            tempNum = 0;
        }
        if ((int) tempNum >= 50) {
            log.error("用户:{}当天最多获取验证码50次", mobile);
            return new R<>(4, "当天最多获取验证码50次");
        }
        Map<String, Object> params = new HashMap<>();
        params.put("contactPhone",mobile);
        List<SysDept> deptlist= sysDeptMapper.queryDeptList(params);

        if (CodeTypeEnum.LOGIN.getCode().equals(type)) {
            if (CollectionUtil.isEmpty(deptlist)) {
                log.error("手机登录，根据手机号{}查询企业为空", mobile);
                return new R<>(3, "当前手机号未注册");
            }

        }
        String code = RandomUtil.randomNumbers(6);
        String smsContent = "验证码："+code+"，有效期10分钟。如非本人操作，请忽略。";
        //调用短信
        callSmsCode(mobile,tempNum,smsContent,code);
        return new R<>(true);
    }

    private void callSmsCode(String mobile,Object tempNum,String content,String code) {

        log.info("################短信发送请求消息中心 -> 手机号:{} -> 验证码：{} ->短信内容:{}", mobile, code,content);
        // 优化：异步发送短信
        threadFactory.newThread(new Runnable() {
            @Override
            public void run() {
                /***
                 *发送短信 封装方法
                 * @param
                 * @return
                 * <AUTHOR>
                 * @date 2022-08-08
                 */
                //1：验证码 2：通知  5:语音验证码
                int type = 1;

                smsEmailService.sendSingleSms(mobile,content,type);
            }
        }).start();
        redisTemplate.opsForValue().set(SystemConstants.DEFAULT_SMS_CODE_KEY + mobile, code, smsExpire, TimeUnit.MINUTES);
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        Duration duration = Duration.between(LocalDateTime.now(), todayEnd);
        redisTemplate.opsForValue().set(SystemConstants.DEFAULT_SMS_CODE_NUM_KEY + mobile,
                (int) tempNum + 1,
                duration.toMillis(),
                TimeUnit.MILLISECONDS);
    }


    private void callSms(String mobile,Object tempNum,String content,String smsType) {
        log.info("################短信发送请求消息中心 -> 手机号:{} -> 内容：{}", mobile, content);
        // 优化：异步发送短信
        threadFactory.newThread(new Runnable() {
            @Override
            public void run() {
                /***
                 *发送短信 封装方法
                 * @param
                 * @return
                 * <AUTHOR>
                 * @date 2022-08-08
                 */
                //1：验证码 2：通知  5:语音验证码
                int type = Integer.parseInt(smsType);
                smsEmailService.sendSingleSms(mobile,content,type);
            }
        }).start();
        LocalDateTime todayEnd = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        Duration duration = Duration.between(LocalDateTime.now(), todayEnd);
        redisTemplate.opsForValue().set(SystemConstants.DEFAULT_SMS_CODE_NUM_KEY + mobile,
                (int) tempNum + 1,
                duration.toMillis(),
                TimeUnit.MILLISECONDS);
    }
    @Override
    public Result listUsersByDeptId(Map<String, Object> params) {
        //创建返回实体
        List<AdminUserListDto> adminUserListDtoList = new ArrayList<AdminUserListDto>();
        //前端传回的肯定是顶级机构企业ID
        String deptId = params.get("deptId").toString();
        String deptName = null;
        if(params.get("deptName") != null || !"".equals(params.get("deptName"))){
            deptName = params.get("deptName").toString();
        }
        //查出主部门Id对应的管理员userId  20220826 注释 sys_user加了字段可根据主机构ID直接查出所有deptId
//        SysUser sysuser = sysUserMapper.selectSmrByDeptId(deptId);
//        List <String> deptIdList = sysUserMapper.selectDeptIds(sysuser.getUserId(),deptName);
        List <String> deptIdList = sysUserMapper.selectDeptIds(null,deptName,deptId);
        if (deptIdList.size()==0) {
            deptIdList = sysUserMapper.selectDeptIdsHw(deptName,deptId);
        }

        int pageNo = Integer.parseInt(params.get("pageNo").toString());
        int pageSize = Integer.parseInt(params.get("pageSize").toString());
        String email = null;
        if(params.get("email") != null || !"".equals(params.get("email"))){
            email = params.get("email").toString();
        }
        String phone = null;
        if(params.get("phone") != null || !"".equals(params.get("phone"))){
            phone = params.get("phone").toString();
        }
        String username = null;
        if(params.get("userName") != null || !"".equals(params.get("userName"))){
            username = params.get("userName").toString();
        }


        Page page = new Page(pageNo, pageSize);
        if (deptIdList.size()>0) {
//            deptIdList.add(deptId);
            params.put("deptIdList",deptIdList);
            //查询部门下的用户列表  sys_user
            List<SysUser> sysUsers = sysUserMapper.listUsersByDeptIdList(page,deptIdList,email,phone,username);
            for (SysUser sysUser:sysUsers){
                //查询用户的组织信息
                SysDept sysDept = selectDeptOrDistr(sysUser.getDeptId());
                //查询用户的角色信息
                List<SysRole> sysRoleEntityList = sysUserMapper.queryRoleIdByUserId(sysUser.getUserId());
                List<String> roleNameList = new ArrayList<String>();
                for(SysRole sysRoleEntity : sysRoleEntityList){
                    roleNameList.add(sysRoleEntity.getRoleName());
                }
                AdminUserListDto adminUserListDto = toAdminUserListDto(sysUser, sysDept.getName(), roleNameList);
                adminUserListDtoList.add(adminUserListDto);
            }
        }

        //分页操作
        page.setRecords(adminUserListDtoList);
        PageUtils pageUtils = new PageUtils(page);
        return Result.ok().put("data",pageUtils);
    }

    private AdminUserListDto toAdminUserListDto(SysUser sysUser, SysDept sysDept, List<SysDeptVo> deptRoleList, List<SysRoleVo> roleList, SysUser updateUser, List<String> roleNameList) {
        AdminUserListDto adminUserListDto = new AdminUserListDto();
        adminUserListDto.setUserId(sysUser.getUserId());
        adminUserListDto.setUserType(sysUser.getUserType());
        adminUserListDto.setMobile(sysUser.getPhone());
        adminUserListDto.setUserName(sysUser.getUsername());
        adminUserListDto.setName(sysUser.getNickname());
        adminUserListDto.setEmail(sysUser.getEmail());
        adminUserListDto.setDeptName(sysDept ==null?"":sysDept.getDeptSname());
        adminUserListDto.setLastUpdateTime(sysUser.getUpdateTime());
        adminUserListDto.setUpdateBy(updateUser == null ? "":updateUser.getUsername());
        adminUserListDto.setDeptRoleList(deptRoleList);
        adminUserListDto.setLastLoginTime(sysUser.getLastLoginTime());
        adminUserListDto.setSysDept(sysDept);
        adminUserListDto.setRoleName(roleNameList);
        adminUserListDto.setRoleList(roleList);
        adminUserListDto.setDeptRoleList(deptRoleList);
        return adminUserListDto;

    }

    private AdminUserListDto toAdminUserListDto(SysUser sysUser, String deptName, List<String> roleNameList) {
        AdminUserListDto adminUserListDto = new AdminUserListDto();
        adminUserListDto.setUserId(sysUser.getUserId());
        adminUserListDto.setUserName(sysUser.getUsername());
        adminUserListDto.setName(sysUser.getNickname());
        adminUserListDto.setMobile(sysUser.getPhone());
        adminUserListDto.setEmail(sysUser.getEmail());
        adminUserListDto.setDeptName(deptName);
        adminUserListDto.setUserType(sysUser.getUserType());
        adminUserListDto.setDelFlag(sysUser.getDelFlag());
        adminUserListDto.setCreateTime(sysUser.getCreateTime());
        adminUserListDto.setRoleName(roleNameList);
        adminUserListDto.setDeptId(sysUser.getDeptId());
        return adminUserListDto;
    }

    @Override
    public Result userInfo(Long userId) {
        //查询用户主体信息
        SysUser sysUser = sysUserMapper.selectById(userId);
        //查询更新人信息
        SysUser updateUser = sysUserMapper.selectById(sysUser.getUpdateBy());
        //查询用户所属部门信息
        SysDept sysDept = selectDeptOrDistr(sysUser.getDeptId());
        //查询用户在当前组织下拥有的角色
        List<String> roleNameList = new ArrayList<String>();

        /**
         *查询用户对应数据权限
         */
        List<SysDeptVo> deptRoleList=new ArrayList<>();
        List<SysUserDept>  sysUserDepts= sysUserMapper.selectDeptIdsByUserId(userId);
        for(SysUserDept sysUserDept:sysUserDepts){
            SysDept sysDept1=  selectDeptOrDistr(sysUserDept.getDeptId());
            SysDeptVo sysdo=new SysDeptVo();
            BeanUtils.copyProperties(sysDept1,sysdo);
            deptRoleList.add(sysdo);
        }

        AdminUserListDto adminUserListDto = new AdminUserListDto();
        if(sysDept != null){
            List<SysRole>  list = sysRoleMapper.selectRolesByUserId(userId);
            if(list!=null&&list.size()>0){
                List<SysRole> roles=new ArrayList<>();
                for(SysRole sysRole:list){
                    roleNameList.add(sysRole.getRoleId().toString());
                }
            }else{
                log.info("根据用户ID查询对应角色列表为空");
            }
            adminUserListDto = toAdminUserListDto(sysUser,sysDept,deptRoleList,null,updateUser,roleNameList);

        }else{
            adminUserListDto = toAdminUserListDto(sysUser,null,null,null,updateUser,null);

        }
        return Result.ok().put("data",adminUserListDto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addUser(AdminUserOperateDto adminUserOperateDto) throws Exception{
        this.redisDeleteByUserId(adminUserOperateDto.getPuserId());
        // 新增手机号字段 不能为空 且唯一
        if(!StringUtils.isBlank(adminUserOperateDto.getMobile())) {
            UserVO userByUsername = findUserByUsername(adminUserOperateDto.getMobile());
            if(userByUsername != null) {
                return new Result(ResponseCodeEnum.PHONE_IS_EXITES);
            }
        }else{
            return Result.error(ResponseCodeEnum.PHONE_NULL.getCode(),"手机号不能为空");
        }

        // 新增邮箱字段 唯一
        if(!StringUtils.isBlank(adminUserOperateDto.getEmail())) {
            UserVO userByUsername = findUserByUsername(adminUserOperateDto.getEmail());
            if(userByUsername != null) {
                return new Result(ResponseCodeEnum.EAMIL_IS_EXITES);
            }
        }

        /**
         * 判断目前授权的产品中是否有进项 ，进项邮箱需必传
         * 2022/11/04
         */
        List<SysRoleMenu> menuList =  sysRoleMenuMapper.getJxeMenuByRoleId(adminUserOperateDto.getRoleIdList(),"DXJX100000");
        if (menuList.size()>0 && StringUtils.isBlank(adminUserOperateDto.getEmail())) {
            return Result.error(ResponseCodeEnum.EAMIL_NULL.getCode(),"此账号包含进项产品，邮箱不能为空");
        }

//        if(org.apache.commons.lang.StringUtils.isBlank(adminUserOperateDto.getEmail())){
//            return new Result(ResponseCodeEnum.EAMIL_NULL);
//        }
        //查看用户是否绑定组织 已经绑定组织的 提示已经有所属组织不允许添加
//        SysUser sysUser = sysUserMapper.selectByUserNameAndEmail("",adminUserOperateDto.getEmail());
        //根据手机号查看用户是否绑定组织
        SysUser sysUser = sysUserMapper.selectByUserNameAndMobile("",adminUserOperateDto.getMobile());
        if(sysUser  == null){

            if(StringUtils.isBlank(adminUserOperateDto.getMobile())) { // 如果手机号没有，就检查用户名
                //如果用户名输入的手机号,那么会把手机号添加  20200616
                if(isPhone(adminUserOperateDto.getUserName())){
                    adminUserOperateDto.setMobile(adminUserOperateDto.getUserName());
                }
            }
            //如果无此用户信息再次根据用户名判断
            SysUser sysUserByUserName = sysUserMapper.selectByUserNameAndEmail(adminUserOperateDto.getUserName(),"");
            //用户名存在 提示用户名已存在,请修改用户名   用户名不存在 直接添加
            if(sysUserByUserName == null){
                //用户名不存在直接添加
                return addUser(adminUserOperateDto,true);
            }else{
                if(org.apache.commons.lang.StringUtils.isNotBlank(sysUserByUserName.getDeptId())){
                    return Result.error(6001,"用户名已存在且已绑定组织,请修改用户名再试");
                }else{
                    //用户存在无绑定组织不更改密码添加
                    adminUserOperateDto.setUserId(sysUserByUserName.getUserId());
                    adminUserOperateDto.setPassword("");
                    return addUser(adminUserOperateDto,false);
                }
            }


        }else{
            if("".equals(sysUser.getDeptId()) || null == sysUser.getDeptId()){
                //账号已存在无绑定组织  可以添加  不修原密码
                //更新账号   不修改密码   添加修改人
                // 无绑定组织在判断用户名是否和当前的邮箱对应的用户名一致
                if(!adminUserOperateDto.getUserName().equals(sysUser.getUsername())) {
                    //判断用户名是否重复
                    SysUser sysUserByUserName = sysUserMapper.selectByUserNameAndEmail(adminUserOperateDto.getUserName(), "");
                    //用户名存在 提示用户名已存在,请修改用户名   用户名不存在 直接添加
                    if (sysUserByUserName != null ) {
                        return Result.error(6005, "用户名已存在,请修改用户名再试");
                    }
                    //用户名存在 无绑定组织 允许添加子账号  数据库就会保留两条数据
                    //如果用户名输入的手机号,那么会把手机号添加  20200616
                    if(isPhone(adminUserOperateDto.getUserName())){
                        adminUserOperateDto.setMobile(adminUserOperateDto.getUserName());
                    }
                }
                adminUserOperateDto.setUserId(sysUser.getUserId());
                adminUserOperateDto.setPassword("");
                return addUser(adminUserOperateDto,false);
            }else{
                //账号存在且已绑定组织 提示不能添加
                return Result.error("该账号邮箱已经绑定组织,不能添加，请更换邮箱");
            }
        }


    }


    /**
     * 判断用户名是否手机号
     * @param username
     * @return
     */
    private Boolean isPhone (String username){
        //手机号正则
        String regExp  = "^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\\d{8}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(username);
        if(m.find()){
            return true;
        }
        return false;
    }

    private Result addUser(AdminUserOperateDto adminUserOperateDto,Boolean isUpdatePassWord) throws Exception {


        SysUser sysUser = toSysUser(adminUserOperateDto);
        sysUser.setDelFlag("0");
        /**
         * 这里代码合并，判断是否根据所属组织查询渠道号
         */
        String sourceId=null;
        String distributorName=null;
        String distributorId=null;
        /**
         * 是否渠道来源
         */
        boolean distFrom=false;

        if(adminUserOperateDto.getType().equals("4")){
            sysUser.setUserType("4");
            sourceId="0";
            distributorId="0";
        }else if(StringUtils.isNotBlank(adminUserOperateDto.getSourceId())){
            Distributor distributor=distributorMapper.selectDistributorBySimpleCode(adminUserOperateDto.getSourceId());
            sourceId=distributor.getSimpleCode();
            distributorId=distributor.getId();
        }else{
            SysDept sysDept=sysDeptMapper.selectByDeptId(adminUserOperateDto.getDeptId());
            if(sysDept==null){
                Distributor distributor=distributorMapper.selectDistributorById(adminUserOperateDto.getDeptId());
                if(distributor==null){
                    return Result.error("用户添加失败，所属渠道不存在");
                }else{
                    if("0".equals(adminUserOperateDto.getType())){
                        sysUser.setUserType("5");
                        adminUserOperateDto.setType("5");
                        /**
                         * 渠道侧默认账户和用户管理渠道账户类型区分
                         */
                        sysUser.setRemark("MR");
                        distFrom=true;
                    }
                    sourceId=distributor.getSimpleCode();
                    distributorName=distributor.getCompanyName();
                    distributorId=distributor.getId();
                }
            }else{
                if(StringUtils.isNotBlank(sysDept.getSourceId())){
                    Distributor distributor=distributorMapper.selectDistributorById(sysDept.getSourceId());
                    sourceId=distributor.getSimpleCode();
                    distributorId=distributor.getId();
                }else{
                    if(adminUserOperateDto.getPuserId()!=null){
                        SysUser sysUser1=sysUserMapper.selectById(adminUserOperateDto.getPuserId());
                        if(sysUser1!=null){
                            distributorId=sysUser1.getDistributorId();
                            Distributor distributor=distributorMapper.selectDistributorById(sysUser1.getDistributorId());
                            sourceId= distributor == null ? "0" : distributor.getSimpleCode();
                        }
                    }
                }
            }
        }

        sysUser.setDistributorId(distributorId);
        log.info("[添加用户]根据所属组织:{} 查到的渠道id是:{}",adminUserOperateDto.getDeptId(),sourceId);


        String  passWord=null;
        if(isUpdatePassWord){
            /**
             * 中台客户中心中页添加新客户默认密码88888888
             */
            if(distFrom){
                passWord ="88888888";
            }else{
                passWord = generatePassWord();
            }


            log.info("[添加用户]对应生成的密码为:{}"+passWord);

            sysUser.setPassword(ENCODER.encode(passWord));

            /**
             * 添加该用户所属顶级机构 20220826
             */
            SysDept sysDept = sysDeptMapper.selectTopLevelByDeptId(sysUser.getDeptId());

            if (sysDept == null && adminUserOperateDto.getType().equals("1")) {//超管用户
                sysUser.setTopLevel(sysUser.getDeptId());
                sysUser.setTenantId(adminUserOperateDto.getTenantId());
            }else{
                sysUser.setTopLevel(sysDept.getDeptId());
                sysUser.setTenantId(sysDept.getTenantId());
            }
            if (StringUtils.isBlank(sysUser.getTenantId()) && StringUtils.isNotBlank(adminUserOperateDto.getTenantId())) {
                sysUser.setTenantId(adminUserOperateDto.getTenantId());
            }

            sysUserMapper.addUser(sysUser);


        }else{
            sysUserMapper.updateUserByUserId(adminUserOperateDto);
        }

        //用户渠道关联  base_source_user
        BaseSourceUser baseSourceUser1 = sysUserMapper.selectBaseSourceUserByUserIdAnd(sysUser.getUserId(),sourceId);
        if(baseSourceUser1 == null){
            BaseSourceUser baseSourceUser = new BaseSourceUser();
            baseSourceUser.setUserId(sysUser.getUserId().toString());
            baseSourceUser.setSourceId(sourceId);
            baseSourceUser.setType(1);
            sysUserMapper.addBaseSourceUser(baseSourceUser);
        }

        /**
         * 添加成功返回对应用户信息
         */
        UserVo userInfo=toUserInfoBySysUser(sysUser);
        /**
         * 新增用户返回密码为明文
         */
        if(isUpdatePassWord) {
            userInfo.setPassword(passWord);
        }
        List<com.dxhy.core.pojo.vo.SysRole> sysRoles=new ArrayList<>();


        createDistAdminRole(sysUser,distributorName,sysRoles,distFrom);

        List<String> deptList= adminUserOperateDto.getDeptList();
        if(deptList!=null&&deptList.size()>0){
            for(String deptId:deptList){
                sysUserMapper.addUserDeptRelation(sysUser.getUserId(),deptId);
            }
        }


        List<Long> roleIdList = adminUserOperateDto.getRoleIdList();
        log.info("*************{}",roleIdList);
        if(roleIdList!=null&&roleIdList.size()>0){
            SysUserRoleVo sysUserDeptRoleVo =  new SysUserRoleVo();
            sysUserDeptRoleVo.setUserId(sysUser.getUserId());
            roleIdList.forEach(x->{
                sysUserDeptRoleVo.setRoleId(x);
                sysUserMapper.addUserRoleRelation(sysUserDeptRoleVo);
                com.dxhy.core.pojo.vo.SysRole sysRo=new com.dxhy.core.pojo.vo.SysRole();
                SysRole sysRole  =sysRoleDao.selectById(x);
                BeanUtils.copyProperties(sysRole,sysRo);
                sysRoles.add(sysRo);
            });
            userInfo.setSysRoles(sysRoles);
        }


        /** 发送邮件操作*/
        String content = MessageFormat.format(emailContent,adminUserOperateDto.getUserName(),passWord);
//            String content="尊敬的用户您好，现已为您开通票税系统账号，用户名："+adminUserOperateDto.getUserName()+" 密码："+passWord+" 祝您生活愉快~";
        log.info(content);
        try {
            emailLogMapper.insert(new EmailLog(content));
        }catch (Exception e){
            e.printStackTrace();
        }
//        dualSendEmail(adminUserOperateDto.getEmail(),content,CommonConstant.EMAIL_TITLE);

        // 异步发送短信开户通知
//        threadFactory.newThread(new Runnable() {
//            @Override
//            public void run() {
//                smsEmailService.sendSingleSms(adminUserOperateDto.getMobile(),content,2);
//            }
//        }).start();

        return Result.ok("用户添加成功").put("data",userInfo);
    }

    private Result addUser2(AdminUserOperateDto adminUserOperateDto,Boolean isUpdatePassWord) throws Exception {


        SysUser sysUser = toSysUser(adminUserOperateDto);
        sysUser.setDelFlag("0");
        /**
         * 这里代码合并，判断是否根据所属组织查询渠道号
         */
        String sourceId=null;
        String distributorName=null;
        String distributorId=null;
        /**
         * 是否渠道来源
         */
        boolean distFrom=false;

        if(adminUserOperateDto.getType().equals("4")){
            sysUser.setUserType("4");
            sourceId="0";
            distributorId="0";
        }else if(StringUtils.isNotBlank(adminUserOperateDto.getSourceId())){
            Distributor distributor=distributorMapper.selectDistributorBySimpleCode(adminUserOperateDto.getSourceId());
            sourceId=distributor.getSimpleCode();
            distributorId=distributor.getId();
        }else{
            SysDept sysDept=sysDeptMapper.selectByDeptId(adminUserOperateDto.getDeptId());
            if(sysDept==null){
                Distributor distributor=distributorMapper.selectDistributorById(adminUserOperateDto.getDeptId());
                if(distributor==null){
                    return Result.error("用户添加失败，所属渠道不存在");
                }else{
                    if("0".equals(adminUserOperateDto.getType())){
                        sysUser.setUserType("5");
                        adminUserOperateDto.setType("5");
                        /**
                         * 渠道侧默认账户和用户管理渠道账户类型区分
                         */
                        sysUser.setRemark("MR");
                        distFrom=true;
                    }
                    sourceId=distributor.getSimpleCode();
                    distributorName=distributor.getCompanyName();
                    distributorId=distributor.getId();
                }
            }else{
                if(StringUtils.isNotBlank(sysDept.getSourceId())){
                    Distributor distributor=distributorMapper.selectDistributorById(sysDept.getSourceId());
                    sourceId=distributor.getSimpleCode();
                    distributorId=distributor.getId();
                }else{
                    if(adminUserOperateDto.getPuserId()!=null){
                        SysUser sysUser1=sysUserMapper.selectById(adminUserOperateDto.getPuserId());
                        if(sysUser1!=null){
                            distributorId=sysUser1.getDistributorId();
                            Distributor distributor=distributorMapper.selectDistributorById(sysUser1.getDistributorId());
                            sourceId=distributor.getSimpleCode();
                        }
                    }
                }
            }
        }

        sysUser.setDistributorId(distributorId);
        log.info("[添加用户]根据所属组织:{} 查到的渠道id是:{}",adminUserOperateDto.getDeptId(),sourceId);


        String  passWord=null;
        if(isUpdatePassWord){
            /**
             * 中台客户中心中页添加新客户默认密码88888888
             */
            if(StringUtils.isNotBlank(adminUserOperateDto.getPassword())){
                passWord = adminUserOperateDto.getPassword();
            }else{
                passWord = generatePassWord();
            }


            log.info("[添加用户]对应生成的密码为:{}"+passWord);

            sysUser.setPassword(ENCODER.encode(passWord));

            /**
             * 添加该用户所属顶级机构 20220826
             */
            SysDept sysDept = sysDeptMapper.selectTopLevelByDeptId(sysUser.getDeptId());

            if (sysDept == null && adminUserOperateDto.getType().equals("1")) {//超管用户
                sysUser.setTopLevel(sysUser.getDeptId());
                sysUser.setTenantId(adminUserOperateDto.getTenantId());
            }else{
                sysUser.setTopLevel(sysDept.getDeptId());
                sysUser.setTenantId(sysDept.getTenantId());
            }
            if (StringUtils.isBlank(sysUser.getTenantId()) && StringUtils.isNotBlank(adminUserOperateDto.getTenantId())) {
                sysUser.setTenantId(adminUserOperateDto.getTenantId());
            }

            sysUserMapper.addUser(sysUser);


        }else{
            sysUserMapper.updateUserByUserId(adminUserOperateDto);
        }

        //用户渠道关联  base_source_user
        BaseSourceUser baseSourceUser1 = sysUserMapper.selectBaseSourceUserByUserIdAnd(sysUser.getUserId(),sourceId);
        if(baseSourceUser1 == null){
            BaseSourceUser baseSourceUser = new BaseSourceUser();
            baseSourceUser.setUserId(sysUser.getUserId().toString());
            baseSourceUser.setSourceId(sourceId);
            baseSourceUser.setType(1);
            sysUserMapper.addBaseSourceUser(baseSourceUser);
        }

        /**
         * 添加成功返回对应用户信息
         */
        UserVo userInfo=toUserInfoBySysUser(sysUser);
        /**
         * 新增用户返回密码为明文
         */
        if(isUpdatePassWord) {
            userInfo.setPassword(passWord);
        }
        List<com.dxhy.core.pojo.vo.SysRole> sysRoles=new ArrayList<>();


        createDistAdminRole(sysUser,distributorName,sysRoles,distFrom);

        List<String> deptList= adminUserOperateDto.getDeptList();
        if(deptList!=null&&deptList.size()>0){
            for(String deptId:deptList){
                sysUserMapper.addUserDeptRelation(sysUser.getUserId(),deptId);
            }
        }


        List<Long> roleIdList = adminUserOperateDto.getRoleIdList();
        log.info("*************{}",roleIdList);
        if(roleIdList!=null&&roleIdList.size()>0){
            SysUserRoleVo sysUserDeptRoleVo =  new SysUserRoleVo();
            sysUserDeptRoleVo.setUserId(sysUser.getUserId());
            roleIdList.forEach(x->{
                sysUserDeptRoleVo.setRoleId(x);
                sysUserMapper.addUserRoleRelation(sysUserDeptRoleVo);
                com.dxhy.core.pojo.vo.SysRole sysRo=new com.dxhy.core.pojo.vo.SysRole();
                SysRole sysRole  =sysRoleDao.selectById(x);
                BeanUtils.copyProperties(sysRole,sysRo);
                sysRoles.add(sysRo);
            });
            userInfo.setSysRoles(sysRoles);
        }


        /** 发送邮件操作*/
        String content = MessageFormat.format(emailContent,adminUserOperateDto.getUserName(),passWord);
//            String content="尊敬的用户您好，现已为您开通票税系统账号，用户名："+adminUserOperateDto.getUserName()+" 密码："+passWord+" 祝您生活愉快~";
        log.info(content);


        return Result.ok("用户添加成功").put("data",userInfo).put("smsContent",content);
    }
    @Override
    public void dualSendEmail(String email, String content,String title) {
        EmailDto emailDto = new EmailDto();
        emailDto.setContent(content);
        List<String> relist = new ArrayList<>();
        relist.add(email);
        emailDto.setReceiverList(relist);
        emailDto.setTitle(title);
        // 异步发送邮箱
        threadFactory.newThread(new Runnable() {
            @Override
            public void run() {
                smsEmailService.sendEmail(emailDto);
            }
        }).start();
    }

    @Override
    public List<String> getAuthNameByTaxno(String taxNo,Boolean flag) {
        //查询企业Id
        List<String> list = new ArrayList<>();
        SysDept sysDept =  sysDeptMapper.queryDeptByTaxpayerCode(taxNo).get(0);
        if (StringUtils.isNotBlank(sysDept.getDeptId())) {
            String topLevel = "";
            if (flag) {
                SysDept dept = sysDeptMapper.selectTopLevelByDeptId(sysDept.getDeptId());
                topLevel = dept.getDeptId();
            }
            list  = sysUserMapper.getAuthNameByDeptId(sysDept.getDeptId(), topLevel);
        }
        return list;
    }

    private SysUser toSysUser(AdminUserOperateDto adminUserOperateDto) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(adminUserOperateDto.getUserId());
        sysUser.setUsername(adminUserOperateDto.getUserName());
        sysUser.setEmail(adminUserOperateDto.getEmail());
        sysUser.setNickname(adminUserOperateDto.getName());
        sysUser.setCreateBy(adminUserOperateDto.getPuserId());
        sysUser.setUpdateBy(adminUserOperateDto.getPuserId());
        sysUser.setPhone(adminUserOperateDto.getMobile());
        sysUser.setStatus(1);//启用
        sysUser.setDeptId(adminUserOperateDto.getDeptId());
        sysUser.setCreateTime(new Date());
        sysUser.setUserType(adminUserOperateDto.getType());
        sysUser.setUserSource(adminUserOperateDto.getUserSource());//0 管理后台 1官网注册   2022/11/2前默认 4 新用户中心
        return sysUser;
    }
    public static String generatePassWord() {

        Random random = new Random();

        StringBuffer valSb = new StringBuffer();

        String charStr = "abcdefghijklmnopqrstuvwxyz";

        String numStr = "0123456789";

        int charLength = charStr.length();

        int numStrLength = numStr.length();

        for (int i = 0; i < 3; i++) {

            int index = random.nextInt(numStrLength);

            valSb.append(numStr.charAt(index));

        }

        for (int i = 0; i < 3; i++) {

            int index = random.nextInt(charLength);

            valSb.append(charStr.charAt(index));

        }

        return valSb.toString();

    }

    /**
     * 组装用户添加成功返回data
     * @param sysUser
     * @return
     */
    public UserVo toUserInfoBySysUser(SysUser sysUser){
        UserVo userInfo=new UserVo();
        userInfo.setId(sysUser.getUserId());
        userInfo.setUsername(sysUser.getUsername());
        userInfo.setPassword(sysUser.getPassword());
        userInfo.setUserType(sysUser.getUserType());
        userInfo.setPhone(sysUser.getPhone());
        userInfo.setEmail(sysUser.getEmail());
        userInfo.setStatus(sysUser.getStatus().toString());
        userInfo.setRemark(sysUser.getRemark());
        userInfo.setDistributorId(sysUser.getDistributorId());
        userInfo.setCreateTime(sysUser.getCreateTime());
        userInfo.setModifyTime(sysUser.getUpdateTime());

        return userInfo;
    }

    /**
     * 创建渠道管理员
     * @param sysUser
     * @param distributorName
     * @param sysRoles
     */
    private void createDistAdminRole(SysUser sysUser, String distributorName, List<com.dxhy.core.pojo.vo.SysRole> sysRoles, boolean distFrom){
        /**
         * 如果是渠道添加，则默认创建渠道管理员角色，关联渠道对应菜单
         */
        if(distFrom){

            SysRole sysRole=new SysRole();

            //生成角色编码roleCode
            //查到数据库最后一个角色编码
            String roleCode = sysRoleDao.selectLastRoleCode();
            if(org.apache.commons.lang.StringUtils.isBlank(roleCode)){
                sysRole.setRoleCode(generateNum(4,""));
            }else{
                sysRole.setRoleCode(generateNum(4,roleCode));
            }
            sysRole.setRoleName(distributorName+"渠道管理员");
            sysRole.setRoleProperty("1");
            sysRole.setType(0);
            sysRole.setDeptId(sysUser.getDistributorId());
            sysRole.setDeptName(distributorName);
            sysRole.setRoleDesc(distributorName+"管理员");
            sysRole.setCreateTime(new Date());
            sysRole.setCreateBy(sysUser.getUserId());
            sysRole.setUpdateTime(new Date());
            sysRole.setUpdateBy(sysUser.getUserId());
            sysRole.setDistributorId(sysUser.getDistributorId());
            sysRole.setDelFlag("0");
            sysRoleDao.insert(sysRole);

            List<SysMenu>  menuItems=sysMenuMapper.queryMenusBySystemSign("4");

            List<SysMenu>  menuList=sysMenuMapper.queryMenusBySystemSign("2");

            List<SysMenu> mentions=new ArrayList<SysMenu>();

            /**
             * 根据渠道ID查询对应开通的产品ID
             */
            List<String> products=sysMenuMapper.queryProductsByDistributorId(sysUser.getDistributorId());

            /**
             * 以下产品ID为特殊判断处理
             */
            List<String> productIds=new ArrayList<String>();
            productIds.add("a2cc30f6552542ad8d22155f96adba30");
            productIds.add("27d8f87724924b3089cc6fbd7d3684c7");
            productIds.add("036a01086f1342d48f9af733a4d521d6");
            productIds.add("ac5463eec58f4fca80d1a00802581ddc");



            if (menuList != null && menuList.size() > 0) {
                for (SysMenu sysMenu : menuList) {
                    if (org.apache.commons.lang.StringUtils.isNotBlank(sysMenu.getProductId())) {
                        /**
                         * 判断是否开通以上4种产品，如开通才能展示对应菜单，如未开通，则不展示
                         */
                        if(products!=null&&products.size()>0){
                            for(String productId:productIds) {
                                for (String produc:products) {
                                    if (produc.equals(productId)) {
                                        if (sysMenu.getProductId().equals(productId)) {
                                            mentions.add(sysMenu);
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        mentions.add(sysMenu);
                    }
                }
            }

            if(mentions!=null&&mentions.size()>0){
                menuItems.addAll(mentions);
            }

            for(SysMenu sysMenu:menuItems){
                SysRoleMenu sysRoleMenu=new  SysRoleMenu();
                sysRoleMenu.setMenuId(sysMenu.getMenuId());
                sysRoleMenu.setRoleId(sysRole.getRoleId());
                sysRoleMenuMapper.addRoleMenuRelation(sysRoleMenu);
            }

            SysUserRoleVo sysUserDeptRoleVo =  new SysUserRoleVo();
            sysUserDeptRoleVo.setUserId(sysUser.getUserId());
            sysUserDeptRoleVo.setRoleId(sysRole.getRoleId());
            sysUserMapper.addUserRoleRelation(sysUserDeptRoleVo);


            com.dxhy.core.pojo.vo.SysRole sysRo=new com.dxhy.core.pojo.vo.SysRole();
            BeanUtils.copyProperties(sysRole,sysRo);
            sysRoles.add(sysRo);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateUserByUserId(AdminUserOperateDto adminUserOperateDto) throws Exception {

        // 首先查询当前用户信息
        SysUser currentUserinfo = this.selectById(adminUserOperateDto.getUserId());
        if(currentUserinfo == null) {
            throw new Exception("未获取到用户信息，修改用户参数为：" + adminUserOperateDto.toString());
        }
        // 如果填写了手机号，则校验
        if(org.apache.commons.lang.StringUtils.isNotBlank(adminUserOperateDto.getMobile())) {
            UserVO userByUsername = findUserByUsername(adminUserOperateDto.getMobile());
            if(userByUsername != null&&!userByUsername.getUserId().equals(adminUserOperateDto.getUserId())) {
                return new Result(ResponseCodeEnum.PHONE_IS_EXITES);
            }
        }else{
            return Result.error(ResponseCodeEnum.PHONE_NULL.getCode(),"手机号不能为空");
        }
        // 如果填写了邮箱，则校验
        if(org.apache.commons.lang.StringUtils.isNotBlank(adminUserOperateDto.getEmail())) {
            UserVO userByUsername = findUserByUsername(adminUserOperateDto.getEmail());
            if(userByUsername != null&&!userByUsername.getUserId().equals(adminUserOperateDto.getUserId())) {
                return new Result(ResponseCodeEnum.EAMIL_IS_EXITES);
            }
        }
        /**
         * 判断目前授权的产品中是否有进项 ，进项邮箱需必传
         * 2022/11/04
         */
        List<SysRoleMenu> menuList =  sysRoleMenuMapper.getJxeMenuByRoleId(adminUserOperateDto.getRoleIdList(),"DXJX100000");
        if (menuList.size()>0 && StringUtils.isBlank(adminUserOperateDto.getEmail())) {
            return Result.error(ResponseCodeEnum.EAMIL_NULL.getCode(),"此账号包含进项产品，邮箱不能为空");
        }

        //账号唯一性检查   账号 手机号不能重复 全局唯一
        //SysUser sysUser = sysUserMapper.selectByUserNameAndEmail("",adminUserOperateDto.getEmail());
        SysUser sysUser = sysUserMapper.selectByUserNameAndEmail(adminUserOperateDto.getUserName(),"");
        if( sysUser == null){//前端账号是写死的 不会变 实际上不会出现此情况
            //账号不存在 根据id更新账户邮箱姓名   更新角色关联
            sysUserMapper.updateUserByUserId(adminUserOperateDto);

            //查询主账号对应的渠道
//            String sourceId = sysUserMapper.getSourceIdByUserId(adminUserOperateDto.getPuserId());
//            SysUser sysUserById = this.selectById(adminUserOperateDto.getPuserId());

            this.redisDeleteByUserId(adminUserOperateDto.getUserId());
            //更新用户关联关系  把原有的关联关系先删除
            return updateUserAndRoleRelation(adminUserOperateDto);

        }else{
            this.redisDeleteByUserId(sysUser.getUserId());

            //查询主账号对应的渠道
//            String sourceId = sysUserMapper.getSourceIdByUserId(adminUserOperateDto.getPuserId());

            if(org.apache.commons.lang3.StringUtils.isBlank(sysUser.getDeptId())){
                //账号存在无组织  把账号邮箱姓名密码更新到A  删除此账号 更新角色关联
                adminUserOperateDto.setName(sysUser.getNickname());
                adminUserOperateDto.setPassword(sysUser.getPassword());
                sysUserMapper.updateUserByUserId(adminUserOperateDto);
                //删除无组织的账号
                sysUserMapper.deleteById(sysUser);

                //更新用户关联关系  把原有的关联关系先删除
                return updateUserAndRoleRelation(adminUserOperateDto);

            }else{
                //账号存在有组织(相当于修改本身)   更新角色关联
                sysUserMapper.updateUserByUserId(adminUserOperateDto);
                return updateUserAndRoleRelation(adminUserOperateDto);
            }
        }

    }

    private Result updateUserAndRoleRelation(AdminUserOperateDto adminUserOperateDto){

        SysUser sysUser = toSysUser(adminUserOperateDto);
        /**
         * 添加成功返回对应用户信息
         */
        UserVo userInfo=toUserInfoBySysUser(sysUser);

        sysUserMapper.deleteUserDeptRelation(adminUserOperateDto.getUserId());


        //更新用户关联关系  把原有的关联关系先删除
        sysUserMapper.deleteUserRoleRelation(adminUserOperateDto.getUserId());

        //增加用户和组织关联
        List<String> deptList= adminUserOperateDto.getDeptList();
        if(deptList!=null&&deptList.size()>0){
            for(String deptId:deptList){
                sysUserMapper.addUserDeptRelation(sysUser.getUserId(),deptId);
            }
        }

        //增加关联关系
        List<Long> roleIdList = adminUserOperateDto.getRoleIdList();
        List<com.dxhy.core.pojo.vo.SysRole> sysRoles=new ArrayList<>();
        if(roleIdList!=null&&roleIdList.size()>0){
            SysUserRoleVo sysUserRoleVo =  new SysUserRoleVo();
            sysUserRoleVo.setUserId(adminUserOperateDto.getUserId());

            roleIdList.forEach(x->{
                sysUserRoleVo.setRoleId(x);
                sysUserMapper.addUserRoleRelation(sysUserRoleVo);
                com.dxhy.core.pojo.vo.SysRole sysRo=new com.dxhy.core.pojo.vo.SysRole();
                SysRole sysRole  =sysRoleDao.selectById(x);
                BeanUtils.copyProperties(sysRole,sysRo);
                sysRoles.add(sysRo);
            });
            userInfo.setSysRoles(sysRoles);
        }

        return Result.ok().put("data",userInfo);

    }

    /**
     * 删除用户
     *
     * @param sysUser 用户
     * @return Boolean
     */
    @Override
//    @CacheEvict(value = "user_details", key = "#sysUser.username")
    public Boolean deleteUserById(SysUser sysUser) {
        sysUserMapper.deleteUserDeptRelation(sysUser.getUserId());
        sysUserRoleService.deleteByUserId(sysUser.getUserId());
        this.deleteById(sysUser.getUserId());
        return Boolean.TRUE;
    }

    @Override
//    @CacheEvict(value = "user_details", key = "#username")
    public R<Boolean> forgetPassword(Map<String, String> paramMap, String username) throws Exception {
        String code = paramMap.get("code");
        String newPassword = paramMap.get("newPassword");
        UserVO userVO = this.findUserByUsername(username);
        if (userVO == null) {
            log.error("修改密码，未获取到用户信息,用户:{}", username);
            return new R<>(3, "未获取到用户信息");
        }
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userVO.getUserId());
        sysUser.setPassword(ENCODER.encode(AESUtils.decryptAES(newPassword, key)));
        sysUser.setUpdateTime(new Date());
        this.updateById(sysUser);
        String content = "【忘记密码】账号："+username+"，新密码："+AESUtils.decryptAES(newPassword, key);
        emailLogMapper.insert(new EmailLog(content));

        this.redisDeleteByUserId(userVO.getUserId());

        redisTemplate.delete(SystemConstants.USER_SIGN_CODE_KEY + username);

        return new R(true);
    }

    @Override
//    @CacheEvict(value = "user_details", key = "#username")
    public R resetPassword(Long userId, String username,String newPassword,String email) throws Exception {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(userId);
        sysUser.setPassword(ENCODER.encode(newPassword));
        sysUser.setUpdateTime(new Date());
        boolean bool = this.updateById(sysUser);
        this.redisDeleteByUserId(userId);
        if (bool) {
            log.info("重置密码成功，发送信息到短信或邮箱通知客户，{}",email);
            String content = MessageFormat.format(emailContentReset,username,newPassword);
            log.info(content);
            emailLogMapper.insert(new EmailLog("【重置密码】"+content));
            //this.dualSendEmail(email,content,CommonConstant.EMAIL_TITLE);
            bool = smsEmailService.sendSingleSms(email,content,2);
        }
        return new R(bool);
    }

    /**
     * 查询用户对应的税控菜单列表
     * @param userId
     * @return
     */
    @Override
    public Result getUserSKMenuList(Long userId) {
        //1.用户对应的税控编码类型
        return null;
    }

    @Override
    public List<SystemVersion> versionStatus(Long userId) {
        //查询版本状态
        List<SystemVersion> svlist = new ArrayList<>();
        int versionStatus = sysUserMapper.selectVersionStatus(userId);
        if (versionStatus == 1) {
            //查询更新日志 含历史日志
            svlist = sysVersionMapper.queryHistoryVersionList(0);
        }else{
            //查询当前日志
            svlist = sysVersionMapper.queryHistoryVersionList(1);
        }
        return svlist;
    }

    @Override
    public int updateVersionStatus(Long userId,Integer versionStatus) {
        return  sysUserMapper.updateVersionStatus(userId,versionStatus);
    }

    @Override
//    @CacheEvict(value = "user_details", key = "#username")
    public R<Boolean> bind(EntUserDTO userDto, String username) {

        if( (!org.apache.commons.lang.StringUtils.isBlank(userDto.getMobile()))&&this.findUserByUsername(userDto.getMobile().trim())!=null){
            return  new R<Boolean>(1,"该手机号已注册，请绑定未注册手机号");
        }

        if( (!org.apache.commons.lang.StringUtils.isBlank(userDto.getEmail()))&&this.findUserByUsername(userDto.getEmail().trim())!=null){
            return  new R<Boolean>(1,"该邮箱已注册，请绑定未注册邮箱");
        }
        UserVO userVo = this.findUserByUsername(username);
        log.info("绑定操作,username:{};userDto:{};userId:{}",username,userDto.getMobile()+","+userDto.getEmail(),userVo.getUserId());
        SysUser sysUser = new SysUser();
        sysUser.setPhone(userVo.getPhone());
        sysUser.setEmail(userVo.getEmail());
        //判断username是否为税号，是的话不修改
//        Boolean IsTaxNo = isTaxNo(userVo.getUsername());
//        //如果手机号不为空,则username以手机号为主
//        if(!org.apache.commons.lang.StringUtils.isBlank(userDto.getMobile())){
//            sysUser.setPhone(userDto.getMobile());
//            if(!IsTaxNo){
//                sysUser.setUsername(userDto.getMobile());
//            }
//        }
//        // 邮箱绑定/换绑时，邮箱存在
//        if(org.apache.commons.lang.StringUtils.isNotBlank(userDto.getEmail())) {
//            sysUser.setEmail(userDto.getEmail());
//            if(org.apache.commons.lang.StringUtils.isBlank(userVo.getPhone())&&!IsTaxNo){ // 邮箱账号，换绑时可能不存在手机号
//                sysUser.setUsername(userDto.getEmail());
//            }
//        }


//        if(org.apache.commons.lang.StringUtils.isNotBlank(userDto.getMobile())){
//            // 异步同步报销
//            threadFactory.newThread(new Runnable() {
//                @Override
//                public void run() {
//                    reimbursementService.updatePhonenNumber(userVo.getPhone(),userDto.getMobile());
//                }
//            }).start();
//        }

        sysUser.setUserId(userVo.getUserId());
        this.redisDeleteByUserId(userVo.getUserId());
        return new R<>(this.updateById(sysUser));
    }

    /**
     * 判断用户名是否为税号（既不是手机号也不是邮箱就默认为税号）
     * @param username
     * @return
     */
    private Boolean isTaxNo (String username){
        //手机号正则
        String regExp  = "^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\\d{8}$";
        Pattern p = Pattern.compile(regExp);
        Matcher m = p.matcher(username);
        //邮箱正则
        String check = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(username);
        if(!m.find()&&!matcher.matches()){
            return true;
        }else{
            return false;
        }
    }

    @Override
    @Transactional(isolation = Isolation.READ_UNCOMMITTED ,rollbackFor = Exception.class)
    public Result syncSaasTenant(SaasTenantData saasTenantData, String dataId) throws Exception {
        //判断产品信息
        TopOrg topOrg = saasTenantData.getTopOrg();
        SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameOrCode(topOrg.getName(),topOrg.getTaxpayerCode());
        SysUser sysUser = sysUserMapper.selectByUserNameAndEmail(saasTenantData.getUsername(),null);
        if (sysUser!=null && !sysUser.getTenantId().equals(saasTenantData.getTenantId())) {
            return  Result.error(ResponseCodeEnum.UORG_IS_EXITS.getCode(),"该超管账号已被其他租户绑定");
        }
        log.info("机构是否存在:{} ,dataId:{}",sysDept,dataId);
        //顶级机构ID
        String deptId ="";
        if (sysDept != null) {
            if (sysDept.getTenantId() !=null && sysDept.getTenantId().equals(saasTenantData.getTenantId())) {
                deptId=sysDept.getDeptId();
            }else{
                return Result.error(ResponseCodeEnum.UORG_IS_EXITS.getCode(),"该租户的顶级机构已被其他租户绑定");
            }
            /**
             * 若超管账号不存在 机构信息存在  则提示 超管账号绑定的机构已存在
             */
            if (sysUser == null) {
                return new Result(ResponseCodeEnum.UORG_IS_EXITS);
            }else{
                /**
                 * 若超管账号存在 机构信息存在 但租户Id不一致  则提示 超管账号已有其他租户绑定
                 */
                if (!sysUser.getTenantId().equals(saasTenantData.getTenantId())) {
                    return  Result.error(ResponseCodeEnum.UORG_IS_EXITS.getCode(),"该超管账号已被其他租户绑定");
                }
            }
        } else{
            deptId = sysDeptService.getDeptId();
        }

        //1.同步渠道信息
        Distributor distributor = transfromDisData(topOrg,saasTenantData.getEmail(),dataId);

        //2.同步超管和管理员角色
        List<Long> roleList = this.syncSuperRole(deptId,saasTenantData,distributor.getId(),dataId);

        //3.同步产品信息
        Result result = productStatusService.addUserProductBatch(saasTenantData.getProductList(),distributor.getId());
        log.info("同步产品信息结果：{},dataId:{}",result,dataId);
        if ((Integer)result.get("num")>0) {
            log.info("Saas系统同步租户信息到票税系统：产品信息同步成功,dataId:{}",dataId);
        }
        //4.同步超管账号信息 sys_user(此时不同步到大象，等第5步 即税号 同步后在进行)
        Result res = syncSuperManageUserInfo(saasTenantData,deptId,roleList,distributor.getSimpleCode(),sysUser);
        Long userId;
        if (res!=null && res.get("code").equals(ResponseCodeEnum.SUCCESS.getCode())) {
            log.info("Saas系统同步租户信息到票税系统,超管用户信息同步成功{},dataId:{}",result,dataId);
            JSONObject json = (JSONObject) JSON.toJSON(res);
            JSONObject data = (JSONObject) json.get("data");
            userId = data.getLong("id");
        }else{
            log.info("Saas系统同步租户信息到票税系统：超管账号同步失败,dataId:{}",dataId);
            return res;
        }


        HashMap<String,Boolean> map = (HashMap<String, Boolean>) result.get("map");

        //5.同步顶级机构信息 sys_dept  包括税盘信息、此采集鉴权Id和Key
        Result s = this.syncTopOrgInfo(saasTenantData,deptId,userId,distributor.getId(),map,dataId,res);
        log.info("###################################：{}",s);
        if (!s.get("code").equals("0000")) {
            log.info("Saas系统同步租户信息到票税系统：顶级机构同步失败,dataId:{}",dataId);
//            return Result.error();
            return Result.error(s.get("code").toString(),s.get("msg").toString());
        }

        /**
         * 循环map根据产品Id调用各个同步接口（批量形式  机构和组织）
         */
        Set set = map.keySet();
        Iterator iterator = set.iterator();
        while (iterator.hasNext()){
            Object next = iterator.next();
            System.out.println("key为："+next+",value为："+map.get(next));
            if (!map.get(next)) {
                threadFactory.newThread(new Runnable() {
                    @SneakyThrows
                    @Override
                    public void run() {
                        Thread.sleep(10000);
                        log.info("休眠10秒，同步新产品的全量数据");
                        CommonRspVo commonRspVo = bSystemLogicService.tbThirdsysInfoBatch(s.get("deptId").toString(),userId,next.toString(),saasTenantData.getTenantId());
                    }
                }).start();
            }
        }
        //全电交互  租户数据
        boolean isqd = false;
        for (int i = 0; i < saasTenantData.getProductList().size(); i++) {
            if (saasTenantData.getProductList().get(i).getProductId().equals(nacosParam.QdYPProId) || saasTenantData.getProductList().get(i).getProductId().equals(nacosParam.QdKpProId)){
                isqd=true;
                break;
            }
        }

        if (isqd) {
            JSONObject json = new JSONObject();
//        json.put("name","大3456789象01");
//        json.put("taxpayerCode","45678987654567876567876");
//        json.put("tenantId","34567898765");
            json.put("name",topOrg.getName());
            json.put("taxpayerCode",topOrg.getTaxpayerCode());
            json.put("taxpayerType",topOrg.getTaxpayerType());
            json.put("taxBureaName",topOrg.getTaxBureaName());
            json.put("taxBureaPass",topOrg.getTaxBureaPass());
            json.put("tenantId",saasTenantData.getTenantId());
            CommonRspVo response = bSystemLogicService.einvoiceAddDept(json);
            log.info("Saas系统同步租户信息到票税系统,全电数据交互结果：{}",response);
            if (!response.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                log.info("Saas系统同步租户信息到票税系统：全电数据交互失败,dataId:{}",dataId);
                return Result.error(response.getCode(),response.getMessage());
            }
        }

        //发送开通短息
        if (res.get("smsContent") != null) {
            String content = res.get("smsContent").toString();
            try {
                emailLogMapper.insert(new EmailLog(content));
            }catch (Exception e){
                e.printStackTrace();
            }
            // 异步发送短信开户通知
            threadFactory.newThread(new Runnable() {
                @Override
                public void run() {
                    smsEmailService.sendSingleSms(saasTenantData.getPhone(),content,2);
                }
            }).start();
        }


        return result;
    }

    @Transactional
    public List<Long> syncSuperRole(String deptId, SaasTenantData saasTenantData, String id,String dataId) {
        //查询该机构下的超管和管理员角色是否已存在，存在-》修改 不存在-》新增
        List<SysRole> sysRole = sysRoleMapper.getSuperRoleListByCompanyName(saasTenantData.getTopOrg().getName());

        String roleName="";
        String desc="";
        for (int i = 0; i < 2; i++) {
            if (i==0) {
                roleName="超级管理员";
                desc="系统最高配置所有权限";
            }else{
                roleName="管理员";
                desc="系统设置功能权限";
            }
            AdminRoleOperateDto adminRoleOperateDto = transfromRoleData(deptId,roleName,desc,saasTenantData.getMenuList(),id,saasTenantData.getProductList());
            adminRoleOperateDto.setDeptName(saasTenantData.getTopOrg().getName());
            if (sysRole.size()==0) {
                Result s = sysRoleService.addRole(adminRoleOperateDto);
                log.info("Saas系统同步租户信息到票税系统：角色,建立结果：{},dataId:{}",s,dataId);
            }else{
                for (int j = 0; j < sysRole.size();j++) {//默认查询的列表肯定包含超管和管理员 若数量<2 异常情况
                    if (sysRole.get(j).getRoleName().equals(roleName)) {
                        adminRoleOperateDto.setRoleId(sysRole.get(j).getRoleId());
                        Result s = sysRoleService.updateRole(adminRoleOperateDto);
                        log.info("Saas系统同步租户信息到票税系统：角色,建立结果：{},dataId:{}",s,dataId);
                        break;
                    }
                }
            }

        }
        List<SysRole> list = sysRoleMapper.getSuperRoleListByCompanyName(saasTenantData.getTopOrg().getName());
        List<Long> roleList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            roleList.add(list.get(i).getRoleId());
        }
        return roleList;
    }

    @Transactional
    public Result syncSuperManageUserInfo(SaasTenantData saasTenantData, String qyid, List<Long> roleList, String simpleCode, SysUser sysUser) throws Exception {
        //先查询超管账号是否存在，如果存在不操作
//        SysUser sysUser = sysUserMapper.selectByUserNameAndEmail(saasTenantData.getUsername(),saasTenantData.getEmail());
        AdminUserOperateDto adminUserOperateDto = new AdminUserOperateDto();
        Result result = null;
        adminUserOperateDto.setType("1");
        adminUserOperateDto.setMobile(saasTenantData.getPhone());
        adminUserOperateDto.setRoleIdList(roleList);
        adminUserOperateDto.setUserSource(saasTenantData.getRegChannel());
        adminUserOperateDto.setPassword(saasTenantData.getPassword());
        if (sysUser == null) {
            Long uid = sysUserMapper.getMinUserId();
            adminUserOperateDto.setPuserId(uid);
            adminUserOperateDto.setUserName(saasTenantData.getUsername());
            adminUserOperateDto.setEmail(saasTenantData.getEmail());
            adminUserOperateDto.setDeptId(qyid);
            adminUserOperateDto.setTenantId(saasTenantData.getTenantId());
            adminUserOperateDto.setSourceId(simpleCode);
            List<String> deptList = new ArrayList<>();
            deptList.add(qyid);
            adminUserOperateDto.setDeptList(deptList);
//            result = this.addUserInfo(adminUserOperateDto);
            result = this.addUser2(adminUserOperateDto);
        }else{
            adminUserOperateDto.setUserName(sysUser.getUsername());
            adminUserOperateDto.setEmail(saasTenantData.getEmail());
            adminUserOperateDto.setDeptId(sysUser.getDeptId());
            adminUserOperateDto.setUserId(sysUser.getUserId());
            adminUserOperateDto.setTenantId(sysUser.getTenantId());
            List<String> deptList = new ArrayList<>();
            deptList.add(sysUser.getDeptId());
            adminUserOperateDto.setDeptList(deptList);
//            result = this.updateUserInfo(adminUserOperateDto);
            result = this.updateUserByUserId(adminUserOperateDto);
        }
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    public Result addUser2(AdminUserOperateDto adminUserOperateDto) throws Exception{
        this.redisDeleteByUserId(adminUserOperateDto.getPuserId());
        // 新增手机号字段 不能为空 且唯一
        if(!StringUtils.isBlank(adminUserOperateDto.getMobile())) {
            UserVO userByUsername = findUserByUsername(adminUserOperateDto.getMobile());
            if(userByUsername != null) {
                return new Result(ResponseCodeEnum.PHONE_IS_EXITES);
            }
        }else{
            return Result.error(ResponseCodeEnum.PHONE_NULL.getCode(),"手机号不能为空");
        }

        // 新增邮箱字段 唯一
        if(!StringUtils.isBlank(adminUserOperateDto.getEmail())) {
            UserVO userByUsername = findUserByUsername(adminUserOperateDto.getEmail());
            if(userByUsername != null) {
                return new Result(ResponseCodeEnum.EAMIL_IS_EXITES);
            }
        }

        /**
         * 判断目前授权的产品中是否有进项 ，进项邮箱需必传
         * 2022/11/04
         */
        List<SysRoleMenu> menuList =  sysRoleMenuMapper.getJxeMenuByRoleId(adminUserOperateDto.getRoleIdList(),"DXJX100000");
        if (menuList.size()>0 && StringUtils.isBlank(adminUserOperateDto.getEmail())) {
            return Result.error(ResponseCodeEnum.EAMIL_NULL.getCode(),"此账号包含进项产品，邮箱不能为空");
        }

//        if(org.apache.commons.lang.StringUtils.isBlank(adminUserOperateDto.getEmail())){
//            return new Result(ResponseCodeEnum.EAMIL_NULL);
//        }
        //查看用户是否绑定组织 已经绑定组织的 提示已经有所属组织不允许添加
//        SysUser sysUser = sysUserMapper.selectByUserNameAndEmail("",adminUserOperateDto.getEmail());
        //根据手机号查看用户是否绑定组织
        SysUser sysUser = sysUserMapper.selectByUserNameAndMobile("",adminUserOperateDto.getMobile());
        if(sysUser  == null){

            if(StringUtils.isBlank(adminUserOperateDto.getMobile())) { // 如果手机号没有，就检查用户名
                //如果用户名输入的手机号,那么会把手机号添加  20200616
                if(isPhone(adminUserOperateDto.getUserName())){
                    adminUserOperateDto.setMobile(adminUserOperateDto.getUserName());
                }
            }
            //如果无此用户信息再次根据用户名判断
            SysUser sysUserByUserName = sysUserMapper.selectByUserNameAndEmail(adminUserOperateDto.getUserName(),"");
            //用户名存在 提示用户名已存在,请修改用户名   用户名不存在 直接添加
            if(sysUserByUserName == null){
                //用户名不存在直接添加
                return addUser2(adminUserOperateDto,true);
            }else{
                if(org.apache.commons.lang.StringUtils.isNotBlank(sysUserByUserName.getDeptId())){
                    return Result.error(6001,"用户名已存在且已绑定组织,请修改用户名再试");
                }else{
                    //用户存在无绑定组织不更改密码添加
                    adminUserOperateDto.setUserId(sysUserByUserName.getUserId());
                    adminUserOperateDto.setPassword("");
                    return addUser2(adminUserOperateDto,false);
                }
            }


        }else{
            if("".equals(sysUser.getDeptId()) || null == sysUser.getDeptId()){
                //账号已存在无绑定组织  可以添加  不修原密码
                //更新账号   不修改密码   添加修改人
                // 无绑定组织在判断用户名是否和当前的邮箱对应的用户名一致
                if(!adminUserOperateDto.getUserName().equals(sysUser.getUsername())) {
                    //判断用户名是否重复
                    SysUser sysUserByUserName = sysUserMapper.selectByUserNameAndEmail(adminUserOperateDto.getUserName(), "");
                    //用户名存在 提示用户名已存在,请修改用户名   用户名不存在 直接添加
                    if (sysUserByUserName != null ) {
                        return Result.error(6005, "用户名已存在,请修改用户名再试");
                    }
                    //用户名存在 无绑定组织 允许添加子账号  数据库就会保留两条数据
                    //如果用户名输入的手机号,那么会把手机号添加  20200616
                    if(isPhone(adminUserOperateDto.getUserName())){
                        adminUserOperateDto.setMobile(adminUserOperateDto.getUserName());
                    }
                }
                adminUserOperateDto.setUserId(sysUser.getUserId());
                adminUserOperateDto.setPassword("");
                return addUser2(adminUserOperateDto,false);
            }else{
                //账号存在且已绑定组织 提示不能添加
                return Result.error("该账号邮箱已经绑定组织,不能添加，请更换邮箱");
            }
        }


    }

    public Result syncTopOrgInfo(SaasTenantData saasTenantData, String deptId, Long userId, String id, HashMap<String, Boolean> map, String dataId, Result res) {
        //先查询顶级机构是否存在，如果存在：修改  不存在：新增
        TopOrg topOrg = saasTenantData.getTopOrg();
        SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameAndCode(topOrg.getName(),topOrg.getTaxpayerCode());
        Result result = null;
        SysDeptResqVo sysDeptResqVo = new SysDeptResqVo();
        sysDeptResqVo.setSksbbm(topOrg.getSksbbm());
//        Dictionary taxControl = dictionaryService.getByFlag("sksbbm-"+topOrg.getSksbbm());
        Dictionary taxControl = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("flag","sksbbm-"+topOrg.getSksbbm()));
        if (StringUtils.isNotBlank(topOrg.getSksbbm())) {
            if (taxControl == null ) {
                return new Result(ResponseCodeEnum.SKSBBM_NO_EXITS);
            }else{
                if (!topOrg.getSksbbm().equals("014")) sysDeptResqVo.setSksbmc(taxControl.getDesc());
                else sysDeptResqVo.setSksbmc("托管-税务UKEY");
            }
        }
        sysDeptResqVo.setAceId(topOrg.getAceId());
        sysDeptResqVo.setAceKey(topOrg.getAceKey());

        String taxNo = topOrg.getTaxpayerCode();
        int updateType=0;
        if (sysDept == null) {

            SysDept deptInfo = new SysDept();
            BeanUtils.copyProperties(topOrg,deptInfo);
            if (StringUtils.isNotBlank(topOrg.getTaxpayerIndustry())) {
                Matcher m1 = Pattern.compile("^[0-9]*$").matcher(topOrg.getTaxpayerIndustry());
                if (!m1.matches()) {
                    SysIndustry sysIndustry = sysIndustryMapper.selectIndustryByName(topOrg.getTaxpayerIndustry());
                    deptInfo.setTaxpayerIndustryCode(sysIndustry==null?null:sysIndustry.getIndustryCode());
                    deptInfo.setTaxpayerIndustry(sysIndustry==null?null:sysIndustry.getIndustryName());
                }else{
                    SysIndustry sysIndustry = sysIndustryMapper.selectIndustryByCode(topOrg.getTaxpayerIndustry());
                    deptInfo.setTaxpayerIndustry(sysIndustry==null?null:sysIndustry.getIndustryName());
                    deptInfo.setTaxpayerIndustryCode(sysIndustry==null?null:sysIndustry.getIndustryCode());
                }
            }
            deptInfo.setSourceId(id);
            deptInfo.setLevel(1);
            deptInfo.setDeptType(1);
            deptInfo.setCreateUser(userId);
            deptInfo.setDeptId(deptId);
            deptInfo.setDeptSname(topOrg.getName());
            if (StringUtils.isNotBlank(topOrg.getAccountingPrincipleCode())) {
                Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code",topOrg.getAccountingPrincipleCode()).eq("parent",14));
                deptInfo.setAccountingPrinciple(dictionary.getName());
            }
            deptInfo.setTaxpayerType(topOrg.getTaxpayerType());
            deptInfo.setDataSource("4");
            deptInfo.setCreateTime(new Date());
            deptInfo.setUpdateTime(new Date());
            deptInfo.setTenantId(saasTenantData.getTenantId());
            sysDeptResqVo.setApiDeptEntity(deptInfo);
            result = sysDeptService.addDept(sysDeptResqVo,userId+"");
            updateType=1;
            //新增
            result.put("deptId",deptId);
        }else{
            taxNo=sysDept.getTaxpayerCode();
//            sysDept.setName(topOrg.getName());
//            sysDept.setDeptSname(topOrg.getName());
            sysDept.setContactEmail(topOrg.getContactEmail());
            sysDept.setContactPhone(topOrg.getContactPhone());
            sysDept.setContactName(topOrg.getContactName());
            if (StringUtils.isNotBlank(topOrg.getTaxpayerIndustry())) {
                Matcher m1 = Pattern.compile("^[0-9]*$").matcher(topOrg.getTaxpayerIndustry());
                if (!m1.matches()) {
                    SysIndustry sysIndustry = sysIndustryMapper.selectIndustryByName(topOrg.getTaxpayerIndustry());
                    sysDept.setTaxpayerIndustryCode(sysIndustry==null?null:sysIndustry.getIndustryCode());
                    sysDept.setTaxpayerIndustry(sysIndustry==null?null:sysIndustry.getIndustryName());
                }else{
                    SysIndustry sysIndustry = sysIndustryMapper.selectIndustryByCode(topOrg.getTaxpayerIndustry());
                    sysDept.setTaxpayerIndustry(sysIndustry==null?null:sysIndustry.getIndustryName());
                    sysDept.setTaxpayerIndustryCode(sysIndustry==null?null:sysIndustry.getIndustryCode());
                }
            }
            sysDept.setTaxpayerAddress(topOrg.getTaxpayerAddress());
            sysDept.setTaxpayerPhone(topOrg.getTaxpayerPhone());
            sysDept.setTaxpayerBank(topOrg.getTaxpayerBank());
            sysDept.setTaxpayerAccount(topOrg.getTaxpayerAccount());
            sysDept.setTaxpayerType(topOrg.getTaxpayerType());
            sysDept.setAccountingPrincipleCode(topOrg.getAccountingPrincipleCode());
            if (StringUtils.isNotBlank(topOrg.getAccountingPrincipleCode())) {
                Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code",topOrg.getAccountingPrincipleCode()).eq("parent",14));
                sysDept.setAccountingPrinciple(dictionary.getName());
            }
            sysDept.setUpdateUser(userId);
            sysDept.setCreateTime(new Date());
            sysDept.setUpdateTime(new Date());
            sysDept.setTenantId(saasTenantData.getTenantId());
            sysDeptResqVo.setApiDeptEntity(sysDept);
            result = sysDeptService.updateDept(sysDeptResqVo,true);
            updateType=2;
            //修改
            result.put("deptId",sysDept.getDeptId());


        }

        //全电：电子税务局账号密码入库
        if (StringUtils.isNotBlank(topOrg.getTaxBureaName()) && StringUtils.isNotBlank(topOrg.getTaxBureaPass())) {
            SysTaxbureauInfo sysTaxbureauInfo = new SysTaxbureauInfo();
            sysTaxbureauInfo.setDeptId(result.get("deptId").toString());
            sysTaxbureauInfo.setTaxNo(taxNo);
            sysTaxbureauInfo.setUserName(topOrg.getTaxBureaName());
            sysTaxbureauInfo.setUserPass(topOrg.getTaxBureaPass());
            CommonRspVo commonRspVo = sysTaxbureauInfoService.updateTaxBureauInfo(sysTaxbureauInfo);
        }

        boolean flag = false;
        Set set = map.keySet();
        Iterator iterator = set.iterator();
        while (iterator.hasNext()){
            Object next = iterator.next();
            System.out.println("key为："+next+",value为："+map.get(next));
            if (!map.get(next) && next.toString().equals(nacosParam.XxProId)) {
                flag = true;
                break;
            }
        }
        if (flag && sysDept!=null) {
            updateType=1;
            log.info("销项产品是新增的：{}",updateType);
        }
        if (Integer.parseInt(result.get("code").toString())==0||result.get("msg").toString().equals("success")) {
            log.info("Saas系统同步租户信息到票税系统,顶级机构信息同步成功,{},dataId:{},updateType:{}",result,dataId,updateType);
            bSystemLogicService.tbThirdsysInfo(result,userId,sysDeptResqVo,updateType,res);
        }

        return result;
    }


    private AdminRoleOperateDto transfromRoleData(String deptId, String roleName, String des, List<SaasMenu> menuList, String distributorId, List<SaasProductInfo> productList) {
        AdminRoleOperateDto  adminRoleOperateDto = new AdminRoleOperateDto();
        adminRoleOperateDto.setDeptId(deptId);
        adminRoleOperateDto.setDescribe(des);
        adminRoleOperateDto.setRoleName(roleName);
        Long userId = sysUserMapper.getMinUserId();
        adminRoleOperateDto.setUserId(userId);
        adminRoleOperateDto.setRoleProperty("1");
        adminRoleOperateDto.setDistributorId(distributorId);
        if (roleName.equals("超级管理员")) {
            adminRoleOperateDto.setRoleType(10);
        }else if(roleName.equals("管理员")){
            adminRoleOperateDto.setRoleType(11);
        }
        List<String> mlist = new ArrayList<>();
        //查询开通产品对应的全量菜单Id
        List<Long> proIdlist = new ArrayList<>();
        productList.stream().forEach(saasProductInfo -> proIdlist.add(Long.valueOf(saasProductInfo.getProductId())));
        mlist = productMenuMapper.getMenuIdListByProId(proIdlist);
//        for (int i = 0; i < menuList.size(); i++) {
//            List<String> midlist = menuList.get(i).getMenuIds();
//            for (int j = 0; j < midlist.size(); j++) {
//                mlist.add(midlist.get(j));
//            }
//        }
        adminRoleOperateDto.setMenusList(mlist);
        log.info("封装结果{}",adminRoleOperateDto);
        return adminRoleOperateDto;
    }

    private Distributor transfromDisData(TopOrg topOrg,String email,String dataId) {
        Distributor distr = distributorMapper.selectDistributorByCompanyName(topOrg.getName());
        if (distr == null) {
            //查询渠道信息是否存在 存在-》修改 不存在-》新增
            String simpleCode = distributorMapper.getNextSimpleCode();
            Distributor distributor = new Distributor();
            distributor.setId(sysDeptService.getDeptId());
            distributor.setCompanyName(topOrg.getName());
            distributor.setIsDistributor(1);
            distributor.setTaxNo(topOrg.getTaxpayerCode());
            distributor.setBankName(topOrg.getTaxpayerBank());
            distributor.setBankNo(topOrg.getTaxpayerAccount());
            distributor.setContactEmail(email);
            distributor.setSuperior("0");
            distributor.setLevel("1");
            distributor.setDelFlag(0);
            distributor.setSimpleCode(simpleCode);
            distributor.setCreateTime(new Date());
            distributor.setModifyTime(new Date());
            distributor.setType("0");
            distributor.setTrialDays("1");
            int num = distributorMapper.insert(distributor);
            if (num>0) log.info("Saas系统同步租户信息到票税系统：渠道信息建立成功,,dataId:{}",dataId);
            return distributor;
        }else{
            distr.setBankName(topOrg.getTaxpayerBank());
            distr.setBankNo(topOrg.getTaxpayerAccount());
            distr.setContactEmail(email);
            int num = distributorMapper.update(distr, new EntityWrapper<Distributor>().eq("id",distr.getId()));
            if (num>0) log.info("Saas系统同步租户信息到票税系统：渠道信息修改成功,dataId:{}",dataId);
            return distr;
        }
    }

    @Override
    public CommonRspVo checkTenantInfo(SaasTenantData saasTenantData) {
        if (saasTenantData.getProductList() == null || saasTenantData.getProductList().size()==0) return new CommonRspVo(ResponseCodeEnum.PRODUCT_NULL);

        boolean checkflag = true;
        if (saasTenantData.getProductList().size()==1) {
            if (saasTenantData.getProductList().get(0).getProductId().equals(nacosParam.QdKpProId)) checkflag=false;
            if (saasTenantData.getProductList().get(0).getProductId().equals(nacosParam.QdYPProId)) checkflag=false;
        }else if(saasTenantData.getProductList().size()==2){
            if (saasTenantData.getProductList().get(0).getProductId().equals(nacosParam.QdKpProId) && saasTenantData.getProductList().get(1).getProductId().equals(nacosParam.QdYPProId)) checkflag=false;
            if (saasTenantData.getProductList().get(0).getProductId().equals(nacosParam.QdYPProId) && saasTenantData.getProductList().get(1).getProductId().equals(nacosParam.QdKpProId)) checkflag=false;
        }

        //检查超管账号 是否不为空
        if (StringUtils.isBlank(saasTenantData.getUsername())) return new CommonRspVo(ResponseCodeEnum.ACCOUT_NULL);
        //检查租户Id 是否不为空
        if (StringUtils.isBlank(saasTenantData.getTenantId())) return new CommonRspVo(ResponseCodeEnum.TENANTID_NULL);
        //检查邮箱 是否不为空
        //if (StringUtils.isBlank(saasTenantData.getEmail())) return new CommonRspVo(ResponseCodeEnum.EAMIL_NULL);

        //检查手机号是否不为空
        if (StringUtils.isBlank(saasTenantData.getPhone())) return new CommonRspVo(ResponseCodeEnum.MOBILE_NULL);
        //检查租户来源
        if (StringUtils.isBlank(saasTenantData.getRegChannel())) return new CommonRspVo(ResponseCodeEnum.REG_CHANNEL_NULL);
        //租户来源规则校验
        Matcher matches = Pattern.compile("^[0-9]*$").matcher(saasTenantData.getRegChannel());
        if (!matches.matches()) return new CommonRspVo(ResponseCodeEnum.REG_CHANNEL_NUMBER);


        //检查顶级机构信息
        TopOrg topOrg = saasTenantData.getTopOrg();
        if (StringUtils.isBlank(topOrg.getName())) return new CommonRspVo(ResponseCodeEnum.EAMIL_NULL);
        if (StringUtils.isBlank(topOrg.getTaxpayerCode())) return new CommonRspVo(ResponseCodeEnum.TAXNO_NULL);
        //税号长度和规则校验
        Matcher ma = Pattern.compile("^[A-Z|\\d]{15}$|^[A-Z|\\d]{17}$|^[A-Z|\\d]{18}$|^[A-Z|\\d]{20}$").matcher(topOrg.getTaxpayerCode());
        if (!ma.matches()) return new CommonRspVo(ResponseCodeEnum.TAXNO_RULES);

        if (StringUtils.isBlank(topOrg.getTaxpayerType())) return new CommonRspVo(ResponseCodeEnum.NSRLX_NULL);
        Matcher m = Pattern.compile("^[01]$").matcher(topOrg.getTaxpayerType());
        if (!m.matches()) return new CommonRspVo(ResponseCodeEnum.NSRLX_NUMBER);

        if (checkflag) {
            if (StringUtils.isBlank(topOrg.getTaxpayerAddress())) return new CommonRspVo(ResponseCodeEnum.ADDRESS_NULL);
            if (StringUtils.isBlank(topOrg.getTaxpayerPhone())) return new CommonRspVo(ResponseCodeEnum.PHONE_NULL);
            if (StringUtils.isBlank(topOrg.getTaxpayerBank())) return new CommonRspVo(ResponseCodeEnum.BNAK_NULL);
            if (StringUtils.isBlank(topOrg.getTaxpayerAccount())) return new CommonRspVo(ResponseCodeEnum.YHZH_NULL);

            if (StringUtils.isBlank(topOrg.getAccountingPrincipleCode())) return new CommonRspVo(ResponseCodeEnum.KJZZ_NULL);
            log.info("纳税人类型和会计准则校验:{},{}",topOrg.getTaxpayerType(),topOrg.getAccountingPrincipleCode());

            Matcher m1 = Pattern.compile("^[0-9]*$").matcher(topOrg.getAccountingPrincipleCode());
            if (!m1.matches()) return new CommonRspVo(ResponseCodeEnum.KJZZ_NUMBER);
            //检查税控设备类型、采集鉴权Id和Key
            for (int i = 0; i < saasTenantData.getProductList().size(); i++) {
                if (saasTenantData.getProductList().get(i).getProductId().equals(nacosParam.XxProId) && StringUtils.isBlank(topOrg.getSksbbm())) {
                    return new CommonRspVo(ResponseCodeEnum.SKSBBM_IS_NULL);
                }
                if (saasTenantData.getProductList().get(i).getProductId().equals(nacosParam.JxProId) && (StringUtils.isBlank(topOrg.getAceId()) || StringUtils.isBlank(topOrg.getAceKey())) ) {
                    return new CommonRspVo(ResponseCodeEnum.ACE_IS_NULL);
                }
            }

        }

        return CommonRspVo.success(ResponseCodeEnum.SUCCESS);
    }

    @Override
    public Result syncUserInfo(Result result,String deptId,String userType) throws Exception {

        if (result.get("code").equals(ResponseCodeEnum.SUCCESS.getCode())) {
            JSONObject json = (JSONObject) JSON.toJSON(result);
            JSONObject data = (JSONObject) json.get("data");
            Long userId = data.getLong("id");
//            String deptId = adminUserOperateDto.getDeptId();
            //查询购买的产品 同步用户权限信息
            if (nacosParam.dxSwitch == 1) {//1开 0 关
                //List<CustomerProduct> cproList = iCustomerProductService.getCustomerProductByUserId(userId);
                List<String> cproList=sysMenuMapper.queryProductsByDistributorId(deptId);
                for (int i = 0; i < cproList.size(); i++) {
                    String productId = cproList.get(i);
                    /**
                     * 1.用户数据权限分配至 到DX
                     * 2.支持新增账号
                     */
                    // 异步同步DX销项
                    threadFactory.newThread(new Runnable() {
                        @SneakyThrows
                        @Override
                        public void run() {
                            if (productId.equals(nacosParam.XxProId)){//"6beb5a5237f611edbb4b52540079e9e2"
//                                if(adminUserOperateDto.getType().equals("1")) {//主账号（超管）：顶级机构同步
//                                    bSystemLogicService.synchronizationDXData(deptId, userId, 1);
//                                }
                                bSystemLogicService.synchronUserAuth(userId);//销项产品
                            }
                            if (productId.equals(nacosParam.JxProId))//主机构的建立要在税号建立后
                                bSystemLogicService.synchronDXjxUserData(userId);//进项产品
                        }
                    }).start();
                }
            }

        }
        return result;
    }

    @Override
    public Result syncUserInfo2(Result result,String productId) {

        if (result.get("code").equals(ResponseCodeEnum.SUCCESS.getCode())) {
            JSONObject json = (JSONObject) JSON.toJSON(result);
            JSONObject data = (JSONObject) json.get("data");
            Long userId = data.getLong("id");
            //查询购买的产品 同步用户权限信息
            if (nacosParam.dxSwitch == 1) {//1开 0 关
                    /**
                     * 1.用户数据权限分配至 到DX
                     * 2.支持新增账号
                     */
                    // 异步同步DX销项
                    threadFactory.newThread(new Runnable() {
                        @SneakyThrows
                        @Override
                        public void run() {
                            if (productId.equals(nacosParam.XxProId)){//"6beb5a5237f611edbb4b52540079e9e2"
                                bSystemLogicService.synchronUserAuth(userId);//销项产品
                            }
                            if (productId.equals(nacosParam.JxProId))//主机构的建立要在税号建立后
                                bSystemLogicService.synchronDXjxUserData(userId);//进项产品
                        }
                    }).start();
            }

        }
        return result;
    }

    @Override
    public List<Long> getUserIdList(String tenantId) {
        return baseMapper.getUserIdList(tenantId);
    }

    @Override
    public Result updateUserInfo(AdminUserOperateDto adminUserOperateDto) throws Exception {
        Result result = this.updateUserByUserId(adminUserOperateDto);
        if (result.get("code").equals(ResponseCodeEnum.SUCCESS.getCode())) {
            JSONObject json = (JSONObject) JSON.toJSON(result);
            JSONObject data = (JSONObject) json.get("data");
            Long userId = data.getLong("id");
            String deptId = adminUserOperateDto.getDeptId();
            //查询超级管理员购买的产品 同步用户权限信息
            if (nacosParam.dxSwitch == 1) {//1开 0 关
                //List<CustomerProduct> cproList = iCustomerProductService.getCustomerProductByUserId(userId);
                List<String> cproList=sysMenuMapper.queryProductsByDistributorId(deptId);
                for (int i = 0; i < cproList.size(); i++) {
                    String productId = cproList.get(i);
                    /**
                     * 1.用户数据权限分配至 到DX
                     * 2.支持新增账号
                     */
                    // 异步同步DX销项
                    threadFactory.newThread(new Runnable() {
                        @SneakyThrows
                        @Override
                        public void run() {
                            if (productId.equals(nacosParam.XxProId))//"6beb5a5237f611edbb4b52540079e9e2"
                                bSystemLogicService.synchronUserAuth(userId);//销项产品
                            if (productId.equals(nacosParam.JxProId))//"e4a4607c37f811edbb4b52540079e9e2"
                                bSystemLogicService.synchronDXjxUserData(userId);//进项产品
                        }
                    }).start();
                }
            }
        }
        return result;
    }

}
