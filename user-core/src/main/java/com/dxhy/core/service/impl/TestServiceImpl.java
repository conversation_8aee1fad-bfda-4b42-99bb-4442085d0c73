package com.dxhy.core.service.impl;

import com.dxhy.core.mapper.TestMapper;
import com.dxhy.core.pojo.PO.TempTablePO;
import com.dxhy.core.service.TestService;
import com.dxhy.core.utils.PageUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: zhang<PERSON>jing
 * @Date: 2022/5/5 17:38
 * @Version 1.0
 */
@Service
public class TestServiceImpl implements TestService {

    @Autowired
    private TestMapper testMapper;

    @Override
    public TempTablePO getById(Integer id) {
        return testMapper.getById(id);
    }

    @Override
    public TempTablePO selectById(Integer id) {
        return testMapper.selectById(id);
    }

    @Override
    public PageUtils testPage() {
//        Page<TempTablePO> tempTablePOPage = new Page<>(2, 5);
//        List<TempTablePO> tempTablePOList = testMapper.selectAll(tempTablePOPage);
//        tempTablePOPage.setRecords(tempTablePOList);
//        PageUtils pageUtils = new PageUtils(tempTablePOPage);
        return null;
    }

}
