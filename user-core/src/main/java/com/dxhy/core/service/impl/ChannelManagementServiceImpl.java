package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.DictParentConstants;
import com.dxhy.core.mapper.ChannelManagementDao;
import com.dxhy.core.mapper.SysDeptMapper;
import com.dxhy.core.mapper.SysTenantProductMapper;
import com.dxhy.core.pojo.DTO.ChannelManagementListDTO;
import com.dxhy.core.pojo.DTO.ChannelManagementSaveDTO;
import com.dxhy.core.pojo.entity.ChannelManagement;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import com.dxhy.core.service.ChannelManagementService;
import com.dxhy.core.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 通道管理表 serviceImpl
 * <AUTHOR>
 * @Date 2025/01/10
 * @Version 1.0
 **/
@Service("channelManagementService")
@Slf4j
public class ChannelManagementServiceImpl extends ServiceImpl<ChannelManagementDao, ChannelManagement> implements ChannelManagementService {

    @Autowired
    private ChannelManagementDao channelManagementDao;
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysTenantProductMapper sysTenantProductMapper;
    @Autowired
    private DictionaryServiceImpl dictionaryService;

    @Override
    public Result queryPage(ChannelManagementListDTO channelManagementListDTO) {
        /*if (channelManagementListDTO == null || StringUtils.isEmpty(channelManagementListDTO.getBaseNsrsbh())) {
            return Result.error("请选择公司主体");
        }*/
        
        EntityWrapper<ChannelManagement> queryWrapper = new EntityWrapper<>();
        queryWrapper.eq("is_delete", "0");
        //queryWrapper.eq("base_nsrsbh", channelManagementListDTO.getBaseNsrsbh());
        
        if (StringUtils.isNotEmpty(channelManagementListDTO.getChannelType())) {
            queryWrapper.like("channel_type", channelManagementListDTO.getChannelType());
        }
        if (StringUtils.isNotEmpty(channelManagementListDTO.getChannelName())) {
            queryWrapper.like("channel_name", channelManagementListDTO.getChannelName());
        }
        if (StringUtils.isNotEmpty(channelManagementListDTO.getProductName())) {
            queryWrapper.like("product_name", channelManagementListDTO.getProductName());
        }
        if (StringUtils.isNotEmpty(channelManagementListDTO.getBusinessType())) {
            queryWrapper.like("business_type", channelManagementListDTO.getBusinessType());
        }
        if (StringUtils.isNotEmpty(channelManagementListDTO.getStatus())) {
            queryWrapper.eq("status", channelManagementListDTO.getStatus());
        }

        queryWrapper.orderDesc(Collections.singleton("create_time"));
        Page<ChannelManagement> page = new Page(channelManagementListDTO.getPageNo(), channelManagementListDTO.getSize());
        page = this.selectPage(page, queryWrapper);
        if(CollectionUtils.isNotEmpty(page.getRecords())) {
            page.getRecords().forEach(e->{
                //字典值转换,业务类型的格式为 ["00","01","02","03"] ，需要对其中每一项进行转换，然后将转换后的再拼成json格式
                if(StringUtils.isNotEmpty(e.getBusinessType())){
                    List<String> businessTypeList = JSON.parseArray(e.getBusinessType(), String.class);
                    if(CollectionUtils.isNotEmpty(businessTypeList)) {
                        List<String> businessTypeNameList = new ArrayList<>();
                        for(String businessType : businessTypeList) {
                            List<Dictionary> dictionaryList = dictionaryService.selectDictionary("",businessType,"","", DictParentConstants.TD_YWLX,"");
                            if(CollectionUtils.isNotEmpty(dictionaryList)) {
                                businessTypeNameList.add(dictionaryList.get(0).getName());
                            }
                        }
                        e.setBusinessType(JSON.toJSONString(businessTypeNameList));
                    }
                }
                if(StringUtils.isNotEmpty(e.getChannelType())){
                    List<Dictionary> dictionaryList = dictionaryService.selectDictionary("",e.getChannelType(),"","", DictParentConstants.TD_TDLX,"");
                    if(CollectionUtils.isNotEmpty(dictionaryList)) {
                        e.setChannelType(dictionaryList.get(0).getName());
                    }
                }
                if(StringUtils.isNotEmpty(e.getProductName())){
                    List<Dictionary> dictionaryList = dictionaryService.selectDictionary("",e.getProductName(),"","", DictParentConstants.TD_CPLX,"");
                    if(CollectionUtils.isNotEmpty(dictionaryList)) {
                        e.setProductName(dictionaryList.get(0).getName());
                    }
                }
            });
        }
        PageUtils pageUtils = new PageUtils(page);
        return Result.ok().put("data", pageUtils);
    }

    @Override
    public Result getChannelName(ChannelManagementListDTO channelManagementListDTO) {
        if (channelManagementListDTO == null || StringUtils.isEmpty(channelManagementListDTO.getBaseNsrsbh())) {
            return Result.error("请选择公司主体");
        }
        //根据纳税人识别号获取机构信息
        SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameAndCode("", channelManagementListDTO.getBaseNsrsbh());
        if(sysDept != null){
            //查询租户通道关联表获取租户配置的通道信息
            EntityWrapper<SysTenantProduct> tenantProductWrapper = new EntityWrapper<>();
            tenantProductWrapper.eq("tenant_id", sysDept.getTenantId());
            tenantProductWrapper.eq("dept_id", sysDept.getDeptId());
            tenantProductWrapper.le("auth_stime", new Date());
            tenantProductWrapper.ge("auth_etime", new Date());
            List<SysTenantProduct> tenantProducts = sysTenantProductMapper.selectList(tenantProductWrapper);
            if(CollectionUtils.isNotEmpty(tenantProducts)){
                EntityWrapper<ChannelManagement> queryWrapper = new EntityWrapper<>();
                queryWrapper.eq("is_delete", "0");
                queryWrapper.in("id", tenantProducts.stream().map(SysTenantProduct::getChannelId).collect(Collectors.toSet()));
                if (StringUtils.isNotEmpty(channelManagementListDTO.getChannelType())) {
                    queryWrapper.like("channel_type", channelManagementListDTO.getChannelType());
                }
                if (StringUtils.isNotEmpty(channelManagementListDTO.getChannelName())) {
                    queryWrapper.like("channel_name", channelManagementListDTO.getChannelName());
                }
                if (StringUtils.isNotEmpty(channelManagementListDTO.getProductName())) {
                    queryWrapper.eq("product_name", channelManagementListDTO.getProductName());
                }
                if (StringUtils.isNotEmpty(channelManagementListDTO.getBusinessType())) {
                    queryWrapper.like("business_type", channelManagementListDTO.getBusinessType());
                }
                if (StringUtils.isNotEmpty(channelManagementListDTO.getStatus())) {
                    queryWrapper.eq("status", channelManagementListDTO.getStatus());
                }
                queryWrapper.orderDesc(Collections.singleton("create_time"));
                List<ChannelManagement> list = this.selectList(queryWrapper);
                if (CollectionUtils.isNotEmpty(list)) {
                    if(list.size()>1){
                        return Result.error("匹配到多个通道数据,请联系管理员");
                    }
                    return Result.ok().put("data", list.get(0).getChannelName());
                }else {
                    return Result.error("未匹配到通道数据");
                }
            }
        }
        return Result.error("未根据纳税人识别号查询到机构信息，无法匹配通道");
    }

    @Override
    public Result getBusinessTypes(ChannelManagementListDTO channelManagementListDTO) {
        if (channelManagementListDTO == null || StringUtils.isEmpty(channelManagementListDTO.getBaseNsrsbh())) {
            return Result.error("请选择公司主体");
        }
        //根据纳税人识别号获取机构信息
        SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameAndCode("", channelManagementListDTO.getBaseNsrsbh());
        if(sysDept != null){
            //查询租户通道关联表获取租户配置的通道信息
            EntityWrapper<SysTenantProduct> tenantProductWrapper = new EntityWrapper<>();
            tenantProductWrapper.eq("tenant_id", sysDept.getTenantId());
            tenantProductWrapper.eq("dept_id", sysDept.getDeptId());
            tenantProductWrapper.le("auth_stime", new Date());
            tenantProductWrapper.ge("auth_etime", new Date());
            List<SysTenantProduct> tenantProducts = sysTenantProductMapper.selectList(tenantProductWrapper);
            if(CollectionUtils.isNotEmpty(tenantProducts)){
                EntityWrapper<ChannelManagement> queryWrapper = new EntityWrapper<>();
                queryWrapper.eq("is_delete", "0");
                queryWrapper.in("id", tenantProducts.stream().map(SysTenantProduct::getChannelId).collect(Collectors.toSet()));
                List<ChannelManagement> list = this.selectList(queryWrapper);
                //将获取到的所有通道的业务类型放到一个list集合中
                Set<String> businessTypeSet = new HashSet<>();
                for (ChannelManagement channelManagement : list) {
                    if (StringUtils.isNotEmpty(channelManagement.getBusinessType())) {
                        List<String> businessTypes = JSON.parseArray(channelManagement.getBusinessType(), String.class);
                        businessTypeSet.addAll(businessTypes);
                    }
                    if (CollectionUtils.isNotEmpty(businessTypeSet)) {
                        return Result.ok().put("data", businessTypeSet);
                    }else {
                        return Result.error("未匹配到任何业务类型，无权限访问！");
                    }
                }
            }
        }
        return Result.error("未根据纳税人识别号查询到机构信息，无法匹配通道");
    }

    @Override
    public Result getBusinessTypeList(ChannelManagementListDTO channelManagementListDTO) {
        if (channelManagementListDTO == null || StringUtils.isEmpty(channelManagementListDTO.getBaseNsrsbh())) {
            return Result.error("请选择公司主体");
        }
        //根据纳税人识别号获取机构信息
        SysDept sysDept = sysDeptMapper.queryDeptByTaxpayerNameAndCode("", channelManagementListDTO.getBaseNsrsbh());
        if(sysDept != null){
            //查询租户通道关联表获取租户配置的通道信息
            EntityWrapper<SysTenantProduct> tenantProductWrapper = new EntityWrapper<>();
            tenantProductWrapper.eq("tenant_id", sysDept.getTenantId());
            tenantProductWrapper.eq("dept_id", sysDept.getDeptId());
            tenantProductWrapper.le("auth_stime", new Date());
            tenantProductWrapper.ge("auth_etime", new Date());
            List<SysTenantProduct> tenantProducts = sysTenantProductMapper.selectList(tenantProductWrapper);
            if(CollectionUtils.isNotEmpty(tenantProducts)){
                EntityWrapper<ChannelManagement> queryWrapper = new EntityWrapper<>();
                queryWrapper.eq("is_delete", "0");
                queryWrapper.in("id", tenantProducts.stream().map(SysTenantProduct::getChannelId).collect(Collectors.toSet()));
                List<ChannelManagement> list = this.selectList(queryWrapper);
                //将获取到的所有通道的业务类型放到一个list集合中
                Set<String> businessTypeSet = new HashSet<>();
                for (ChannelManagement channelManagement : list) {
                    if (StringUtils.isNotEmpty(channelManagement.getBusinessType())) {
                        List<String> businessTypes = JSON.parseArray(channelManagement.getBusinessType(), String.class);
                        businessTypeSet.addAll(businessTypes);
                    }
                }
                if (CollectionUtils.isNotEmpty(businessTypeSet)) {
                    return Result.ok().put("data", businessTypeSet);
                }else {
                    return Result.error("未匹配到任何业务类型，无权限访问！");
                }
            }
        }
        return Result.error("未根据纳税人识别号查询到机构信息，无法匹配通道");
    }

    @Override
    public Result saveData(ChannelManagementSaveDTO channelManagementSaveDTO,String userName) {
        String checkStr = this.checkChannelManagementSaveDTO(channelManagementSaveDTO);
        if (StringUtils.isNotEmpty(checkStr)) {
            return Result.error(checkStr);
        }
        if(StringUtils.isBlank(userName)){
            userName = "admin";
        }

        // 将业务类别列表转换为JSON字符串
        String businessType = "";
        if (CollectionUtils.isNotEmpty(channelManagementSaveDTO.getBusinessTypeList())) {
            businessType = JSON.toJSONString(channelManagementSaveDTO.getBusinessTypeList());
        }
        
        if (StringUtils.isNotBlank(channelManagementSaveDTO.getId())) {
            // 修改
            ChannelManagement existEntity = channelManagementDao.selectById(channelManagementSaveDTO.getId());
            existEntity.setProductName(channelManagementSaveDTO.getProductName());
            existEntity.setChannelType(channelManagementSaveDTO.getChannelType());
            existEntity.setChannelName(channelManagementSaveDTO.getChannelName());
            existEntity.setBusinessType(businessType);
            existEntity.setStatus(channelManagementSaveDTO.getStatus());
            existEntity.setUpdateTime(new Date());
            existEntity.setUpdateBy(userName);
            channelManagementDao.updateById(existEntity);
            return Result.ok();
        } else {
            // 新增
            ChannelManagement saveData = new ChannelManagement();
            saveData.setProductName(channelManagementSaveDTO.getProductName());
            saveData.setChannelType(channelManagementSaveDTO.getChannelType());
            saveData.setChannelName(channelManagementSaveDTO.getChannelName());
            saveData.setBusinessType(businessType);
            saveData.setBaseNsrsbh(channelManagementSaveDTO.getBaseNsrsbh());
            saveData.setStatus(channelManagementSaveDTO.getStatus());
            saveData.setIsDelete("0");
            saveData.setCreateTime(new Date());
            saveData.setCreateBy(userName);
            saveData.setUpdateTime(new Date());
            saveData.setUpdateBy(userName);
            channelManagementDao.insert(saveData);
            return Result.ok().put("data", saveData.getId());
        }
    }

    private String checkChannelManagementSaveDTO(ChannelManagementSaveDTO channelManagementSaveDTO) {
        if (channelManagementSaveDTO == null) {
            return "入参为空";
        }
        if (StringUtils.isEmpty(channelManagementSaveDTO.getProductName())) {
            return "产品名称不能为空";
        }
        if (StringUtils.isEmpty(channelManagementSaveDTO.getChannelType())) {
            return "通道类别不能为空";
        }
        if (StringUtils.isEmpty(channelManagementSaveDTO.getChannelName())) {
            return "通道名称不能为空";
        }
        /*if (StringUtils.isEmpty(channelManagementSaveDTO.getBaseNsrsbh())) {
            return "纳税人识别号不能为空";
        }*/
        //通道名称不能重复
        EntityWrapper<ChannelManagement> queryWrapper = new EntityWrapper<>();
        queryWrapper.eq("is_delete", "0");
        //queryWrapper.eq("base_nsrsbh", channelManagementSaveDTO.getBaseNsrsbh());
        queryWrapper.eq("channel_name", channelManagementSaveDTO.getChannelName());
        if (StringUtils.isNotEmpty(channelManagementSaveDTO.getId())) {
            queryWrapper.ne("id", channelManagementSaveDTO.getId());
        }
        int count = channelManagementDao.selectCount(queryWrapper);
        if (count > 0) {
            return "通道名称不能重复";
        }
        return "";
    }

    @Override
    public Result deleteData(String id,String userName) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("请选择要删除的数据");
        }
        ChannelManagement baseData = channelManagementDao.selectById(id);
        if (baseData == null) {
            return Result.error("数据不存在");
        }
        
        // 逻辑删除
        baseData.setIsDelete("1");
        baseData.setUpdateTime(new Date());
        baseData.setUpdateBy(userName);
        channelManagementDao.updateById(baseData);
        
        return Result.ok();
    }

    @Override
    public Result getById(String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("id不能为空");
        }
        ChannelManagement baseData = channelManagementDao.selectById(id);
        if (baseData == null) {
            return Result.error("数据不存在");
        }
        return Result.ok().put("data", baseData);
    }
} 