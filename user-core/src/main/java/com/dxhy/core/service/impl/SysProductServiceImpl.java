/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.SysProductMapper;
import com.dxhy.core.pojo.entity.SysProduct;
import com.dxhy.core.pojo.vo.SysProductVo;
import com.dxhy.core.service.ISysProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 11:46
 */
@Service
@Slf4j
public class SysProductServiceImpl extends ServiceImpl<SysProductMapper, SysProduct> implements ISysProductService {

	@Override
	public SysProductVo selectByTenantId(String tenantId) {
		return baseMapper.selectByTenantId(tenantId);
	}

}