/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service;


import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysMenu;
import com.dxhy.core.pojo.vo.MenuTreeVO;
import com.dxhy.core.pojo.vo.MenuVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 菜单权限表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysMenuService extends IService<SysMenu> {
    /**
     * 通过角色名称查询URL 权限
     *
     * @param role 角色名称
     * @return 菜单列表
     */
    List<SysMenu> findMenuByRoleName(String role);

    /**
     * 级联删除菜单
     *
     * @param id 菜单ID
     * @return 成功、失败
     */
    Boolean deleteMenu(Integer id);

    /**
     * 更新菜单信息
     *
     * @param sysMenu 菜单信息
     * @return 成功、失败
     */
    Boolean updateMenuById(SysMenu sysMenu);

    /**
     * 通过用户ID+企业ID获取 权限
     *
     * @param userId
     * @param entId
     * @return 菜单集合，防止重复-多角色时可能重复
     */
    Set<MenuVO> findMenuByEntUser(Long userId, String entId);

    /**
     * 查询角色已勾线菜单树
     *
     * @param entId
     * @param roleId
     * @return
     * @Methods:getMenuList
     * <AUTHOR>
     * @date 2019年3月14日下午2:32:23
     */
    List<MenuTreeVO> getMenuList(String entId, String roleId);

    /**
     * 通过用户ID+企业ID获取 开通的产品
     *
     * @param userId
     * @param entId
     * @return
     */
    Set<MenuVO> findProductByEntUser(Long userId, String entId);

    /**
     * 通过角色id获取菜单列表
     *
     * @param roleId
     * @return
     */
    List<MenuTreeVO> getMenuListByRoleId(Long roleId);


    /**
     * 通过角色id获取菜单列表
     *
     * @param roleId
     * @return
     */
    List<MenuTreeVO> getProductByRoleId(Long roleId);

    /**
     * 查询用户在渠道下的门户菜单列表
     * @param roleId
     * @param sourceId
     * @return
     */
    Set<MenuVO> findMenuByUserIdAndSourceId(Long roleId, String sourceId);

    /**
     * 查询用户在渠道下的门户菜单列表
     * @param sourceId
     * @return
     */
    Set<MenuVO> findMenuBySourceId(String sourceId);

    /**
     * 查询主键最大值
     * @return
     */
    int findMaxMenuId();

    /**
     * 通过菜单id获取菜单详情
     * @param menuId
     * @return
     */
    MenuVO getMenuVoById(Integer menuId);

    /**
     * 通过角色名称以及版本号，渠道查询URL 权限
     *
     * @param role 角色名称
     * @return 菜单列表
     */
    List<SysMenu> findMenuByRoleNameAndVersion(String role,String version,String sourceId);

    /**
     * 通过角色名称以及版本号，渠道查询URL 权限
     *
     * @param userId
     * @param entId
     * @return 菜单集合，防止重复-多角色时可能重复
     */
    Set<SysMenu> findMenuByEntUserAndVersion(Long userId, String entId,String version,String sourceId);

    /**
     * 根据userid 查询菜单
     * @param userId profductId
     * @return java.util.List<com.dxyun.heaven.admin.api.entity.SysMenu>
     */
    Result queryMenusByUserIdAndDeptId(String userId, String deptId);

    /**
     * 查询所有的一级菜单
     * @param
     * @return com.dxyun.heaven.admin.api.dto.Result
     */
    Result querySysMenus();

    /**
     * 查询对应菜单
     * @param roleProperty
     * @param deptId
     * @return
     */
    Result queryMenusByPropertyAndDeptId(String roleProperty, String deptId);
    /**
     * 根据菜单type查询
     * @return
     */
    Result queryMenusByType();


    Result allMenus();

    Result listMenus(Map<String, Object> params);

    Result addMenu(SysMenu sysMenu);

    Result updateMenu(SysMenu sysMenu);

    Result deleteMenuByMenuId(String menuId);

    Result menuInfo(String menuId);



    /**
     * 根据角色id查询菜单信息
     * @param roleId
     * @return
     */
    List<SysMenu> getMenuListByRole(Long roleId);







    Map getMenuAndButton(List<SysMenu> menus);

    /**
     * 门户查询组织对应菜单信息
     * @param userId
     * @param deptId
     * @return
     */
    Result queryMenusByDeptId(String userId,String deptId);
}
