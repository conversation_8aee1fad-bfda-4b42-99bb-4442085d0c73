package com.dxhy.core.service.impl;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.constants.DictParentConstants;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.*;
import com.dxhy.core.pojo.DTO.AdminUserOperateDto;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.IndustryVo;
import com.dxhy.core.pojo.vo.SysDeptResqVo;
import com.dxhy.core.pojo.vo.SysDeptVo;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.*;
import com.dxhy.core.utils.Generator;
import com.dxhy.core.utils.GetUuidUtils;
import com.dxhy.core.utils.MycstUtil;
import com.dxhy.core.utils.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.dxhy.core.utils.GenerateRoleCodeUtil.generateNum;


/**
 * 部门管理 服务实现类
 * @return
 * <AUTHOR>
 * @date 2022-08-10
 */
@Service
@Slf4j
public class SysDeptServiceImpl extends ServiceImpl<SysDeptMapper, SysDept> implements SysDeptService {
    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private DistributorMapper distributorMapper;

    @Autowired
    private SysUserMapper sysUserDao;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private SysUserRoleService sysUserRoleService;

    @Autowired
    private SysRoleMapper sysRoleDao;

    @Autowired
    private SysUserDeptService sysUserDeptService;

    @Resource
    private SysTaxControlMapper sysTaxControlMapper;
    @Resource
    private IDictionaryService dictionaryService;
    @Resource
    private ISysTaxControlService sysTaxControlService;
    @Resource
    private CustomerProductMapper customerProductMapper;
    @Resource
    private SysTaxbureauInfoMapper sysTaxbureauInfoMapper;
    @Resource
    private ChannelManagementDao channelManagementDao;
    @Resource
    private SysTenantProductMapper sysTenantProductMapper;
    @Autowired
    private MycstUtil mycstUtil;
    @Resource
    private BSystemLogicService bSystemLogicService;
    @Autowired
    private SysRoleMenuService sysRoleMenuService;
    @Autowired
    private ProductMenuMapper productMenuMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result addDept(SysDeptResqVo sysdeptresqvo, String token) {
        /**
         * 组织bean
         */
        SysDept dept=sysdeptresqvo.getApiDeptEntity();
        //删除用户缓存
        sysUserService.redisDeleteByUserId(dept.getCreateUser());
        /**
         * 第一步：校验传入数据  只有分支机构可调用创建企业
         * 第二步：只有分支机构需要校验企业名称+税号唯一，企业类型企业校验税号唯一，根据组织名称和税号查询录入企业是否已存在，如已存在则返回提示信息，该企业已存在，请录入企业授权码
         * 第三步：造层级code
         * 第四步：生成企业编码和授权码
         * 第五步：默认是否删除 否,如果是顶级企业添加，需要创建对应的管理员角色
         * 第六步：
         */

        /**
         * 判断是否需要生成组织ID，如同步则直接用传入的
         */
        String deptId=null;
        if(StringUtils.isNotBlank(dept.getDeptId())){
            deptId=dept.getDeptId();
        }else{
            deptId =getDeptId();
            dept.setDeptId(deptId);
        }

        /**
         * 部门数据和授权数据校验
         */
        List<SysTenantProduct> sysTenantProductList = sysdeptresqvo.getSysTenantProductList();
        String bool=this.checkData(dept,sysTenantProductList);
        log.info("数据校验对应返回："+bool);
        if(StringUtils.isBlank(bool)){
            try {
                /**
                 * 企业层级默认赋值
                 */
                if (StringUtils.isBlank(dept.getParentId())||dept.getDeptType().toString().equals("1")) {
                    //总公司上级组织为空，其他完善企业信息对应创建的组织为顶级组织
                    if(dept.getDeptType()!=1){
                        log.info("默认是完善企业信息对应添加的公司是顶级组织");
                    }
                    dept.setParentId(null);
                    dept.setLevel(1);
                } else {
                    SysDept parentDept =this.getSysDeptByDeptId(dept.getParentId());
                    if (parentDept.getDeptType() == 4) {
                        log.error("部门下不能再建组织!");
                        return Result.error("组织类型部门下不能再建组织");
                    }
                    dept.setLevel(parentDept.getLevel().intValue() + 1);
                }
                /**
                 * 判断企业是否已存在
                 */
                String separator=this.checkDeptSfcz(dept);
                if (separator != null) {
                    log.error("添加企业已存在，请录入该企业授权码");
                    return Result.error(separator);
                }
                /**
                 * 根据用户ID查询对应的渠道id
                 */
                if(StringUtils.isBlank(dept.getSourceId())){
                    SysUser sysUser=sysUserMapper.selectById(dept.getCreateUser());
                    if (sysUser.getUserType().equals("4") && dept.getDeptType()==1) {
                        dept.setSourceId(dept.getParentId());//这里如果是平台账号建立 渠道id没传，parentId是渠道id
                    }
                    if(StringUtils.isNotBlank(sysUser.getDistributorId())){
                        dept.setSourceId(sysUser.getDistributorId());
                    }
                }else{
                    if(dept.getSourceId().length()>9){
                        dept.setSourceId(dept.getSourceId());
                    }else{
                        Distributor distributor =distributorMapper.selectDistributorBySimpleCode(dept.getSourceId());
                        if(distributor==null){
                            log.error("对应渠道不存在，请确认", dept.getSourceId());
                            return Result.error("对应渠道不存在，请确认"+ dept.getSourceId());
                        }
                        dept.setSourceId(distributor.getId());
                    }
                }
                /**
                 * 组织添加
                 */
                String resulttStr= this.insertDept(dept);
                if(StringUtils.isNotBlank(resulttStr)){
                    return Result.error(resulttStr);
                }
                //添加机构产品授权信息
                if (CollectionUtils.isNotEmpty(sysTenantProductList)) {
                    for (SysTenantProduct deptProduct : sysTenantProductList) {
                        SysTenantProduct tenantProduct = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("tenant_id", dept.getTenantId()).eq("product_id",deptProduct.getProductId()).andNew("dept_id is null OR dept_id = ''")).get(0);
                        deptProduct.setTenantId(StringUtils.isNotBlank(dept.getTenantId()) ? dept.getTenantId() : tenantProduct.getTenantId());
                        deptProduct.setDeptId(deptId);
                        deptProduct.setCreateBy(String.valueOf(dept.getCreateUser()));
                        deptProduct.setUpdateBy(String.valueOf(dept.getCreateUser()));
                        deptProduct.setUpdateTime(new Date());
                        deptProduct.setUpdateTime(new Date());
                        deptProduct.setChannelId(tenantProduct.getChannelId());
                        deptProduct.setChannelType(tenantProduct.getChannelType());
                        sysTenantProductMapper.insert(deptProduct);
                    }
                }
                /**
                 * 新增子机构时，默认给超级管理员添加机构数据权限
                 */
                Long smUserId = Long.parseLong(token);
                SysRole sysRole = sysRoleDao.selectSmRolesByUserId(smUserId);
                if (sysRole == null) {
                    return Result.error("当前用户不是超级管理员账号");
                }else{
                    if (sysRole.getRoleType() == 10 && dept.getDeptType() == 2) {
                        SysUserDept sysUserDept = new SysUserDept();
                        sysUserDept.setDeptId(dept.getDeptId());
                        sysUserDept.setUserId(smUserId);
                        boolean b = sysUserDeptService.insert(sysUserDept);
                        if (b) {
                            log.info("超级管理员数据权限添加成功 账户ID：{}",smUserId);
                        }
                    }
                }

                //配置税控
                Result result = this.confSksbAndAce(deptId,sysdeptresqvo);
                log.info("新增税控和采集鉴权配置：{}",result);
                // 销项数据同步
                if (Integer.parseInt(result.get("code").toString())==0 || result.get("msg").toString().equals("success")) {
                    result.put("data",deptId);
//                    UserVO userVO = sysUserService.selectUserVoById(smUserId);
                    bSystemLogicService.tbThirdsysInfo(result,smUserId,sysdeptresqvo,1, null);
                    List<SysTenantProduct> productList = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>()
                            .eq("tenant_id", dept.getTenantId()).last("limit 1"));
                    if (CollectionUtils.isNotEmpty(productList) && StringUtils.isNotBlank(dept.getTaxpayerCode())) {
                        SysTenantProduct tenantProduct = productList.get(0);
                        JSONObject json = new JSONObject();
                        SysTaxbureauInfo info = sysTaxbureauInfoMapper.selectByTaxNo(dept.getTaxpayerCode());
                        json.put("name", dept.getName());
                        json.put("taxpayerCode", dept.getTaxpayerCode());
                        json.put("taxpayerType", dept.getTaxpayerType());
                        json.put("taxBureaName", info == null ? "" : info.getUserName());
                        json.put("taxBureaPass", info == null ? "" : info.getUserPass());
                        json.put("tenantId", dept.getTenantId());
                        json.put("channel", tenantProduct.getChannelType());
                        //开票方式默认是SHRPA，目前已经没用了，需要动态匹配通道
                        json.put("kpfs", "SHRPA");
                        json.put("spid", dept.getSpId());
                        log.info("全电数据同步开始:{}",json);
                        CommonRspVo response = bSystemLogicService.einvoiceAddDept(json);
                        log.info("全电数据交互结果：{}", response);
                        if (!response.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                            log.info("全电数据交互失败,taxpayerCode:{}", dept.getTaxpayerCode());
                            return Result.error(response.getCode(), response.getMessage());
                        }
                        //TODO 进项交互暂时先注释掉
                        /*JSONObject jxjson = new JSONObject();
                        jxjson.put("nsrmc", sysDept.getApiDeptEntity().getName());
                        jxjson.put("nsrsbh", sysDept.getApiDeptEntity().getTaxpayerCode());
                        jxjson.put("tenantId", sysDept.getApiDeptEntity().getTenantId());
                        json.put("channel", tenantProduct.getChannelType());
                        CommonRspVo jxResponse = bSystemLogicService.jxInvoiceAddDept(jxjson);
                        log.info("进项数据交互结果：{}", jxResponse);
                        if (!jxResponse.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                            log.info("进项数据交互结果,taxpayerCode:{}", sysDept.getApiDeptEntity().getTaxpayerCode());
                            return Result.error(jxResponse.getCode(), jxResponse.getMessage());
                        }*/
                    }
                }
            }catch (Exception e) {
                log.error("组织信息保存异常：{}"+e.getMessage());
                throw new RuntimeException(e);
            }
        }else{
            log.error("数据校验失败，组织信息保存失败");
            return Result.error(bool);
        }

        return Result.ok().put("data",deptId);
    }

   /**
    * 初始化顶级机构的管理员菜单
    * @param dept
    * @param roleName
    * @param rolelDesc
    * @param roleType
    * @return
    * <AUTHOR>
    * @date 2022-09-21
    */
    private void initTopOrgRole(SysDept dept,String roleName,String rolelDesc,int roleType) {
        SysRole srbean = new SysRole();
        String roleCode = sysRoleDao.selectLastRoleCode();
        if(StringUtils.isBlank(roleCode)){
            srbean.setRoleCode(generateNum(4,""));
        }else{
            srbean.setRoleCode(generateNum(4,roleCode));
        }
        srbean.setRoleName(roleName);
        srbean.setRoleProperty("2");
        srbean.setType(0);
        srbean.setDeptId(dept.getDeptId());
        srbean.setDeptName(dept.getName());
        srbean.setRoleDesc(rolelDesc);
        srbean.setCreateTime(new Date());
        srbean.setCreateBy(dept.getCreateUser());
        srbean.setUpdateTime(new Date());
        srbean.setUpdateBy(dept.getCreateUser());
        srbean.setDistributorId(dept.getSourceId());
        srbean.setRoleType(roleType);
        srbean.setDelFlag("0");
        sysRoleDao.insert(srbean);
    }


    /**
     * 生成DeptId
     * @return
     */
    public String getDeptId(){
        String deptId=null;
        for(int i = 1; i > 0; i++){
            deptId= GetUuidUtils.getUUID();
            SysDept ssodep =this.getSysDeptByDeptId(deptId);
            if(ssodep==null){
                break;
            }
        }
        return deptId;
    }
    /**
     * 判断企业是否已存在
     * @param dept
     * @return
     */
    public String checkDeptSfcz(SysDept dept){
        /**
         * 企业唯一性校验规则调整如下：
         2、企业唯一性校验规则调整：1）如果顶级父级组织为分支机构类型时校验名称+税号唯一性校验；如果是顶级组织是总公司类型则校验企业名称不允许重复、税号不允许重复
         2）向下添加的组织为分支机构类型时校验名称+税号唯一性校验；如果添加的组织类型为虚拟机构时，校验同一个组织下虚拟机构名称唯一；如果添加的组织类型为子公司时则校验企业名称不允许重复、税号不允许重复；如果添加的组织类型为部门时，校验同一个组织内名称唯一
         */
        if(dept.getDeptType().equals(3)){
            log.error("数据校验，组织信息保存失败");
            Result  result = this.queryDeptByTaxpayerNameAndCode(dept.getName(), dept.getTaxpayerCode());
            if(result!=null&&result.get("code").toString().equals("0000")){
                log.error("添加分支机构，对应企业已存在");
                return "组织已存在";
            }
        }else{
            /**
             * 总公司名称全局唯一，部门及虚拟机构组织下唯一
             */
            if(dept.getDeptType().equals(1)){
                log.info("添加顶级企业");
                String stackTrace=checkDeptData(dept);
                if(StringUtils.isNotBlank(stackTrace)){
                    log.error("添加顶级企业"+stackTrace);
                    return "组织已存在";
                }
            }else{
                if(dept.getDeptType().equals(5)&&StringUtils.isNotBlank(dept.getParentId())){
                    log.info("添加的组织类型是：虚拟机构");
                    return this.checkNameByCode(dept);
                }
                if(dept.getDeptType().equals(6)){
                    log.info("添加的组织类型是：独立企业");
                    return this.checkDeptData(dept);
                }
                if(dept.getDeptType().equals(2)&&StringUtils.isNotBlank(dept.getParentId())){
                    log.info("添加的组织类型是：子公司");
                    return this.checkNameByCode(dept);
                }
                if(dept.getDeptType().equals(4)&&StringUtils.isNotBlank(dept.getParentId())){
                    log.info("添加的组织类型是：部门");
                    return this.checkNameByCode(dept);
                }
            }
        }
        return null;
    }
    /**
     * 校验同一组织下名称是否唯一
     * @param dept
     * @return
     */
    public String checkNameByCode(SysDept dept){

        /**
         * 查询code
         */
        if(StringUtils.isNotBlank(dept.getParentId())){
            SysDept sysdept =this.getSysDeptByDeptId(dept.getParentId());
            String code = sysdept.getCode();

            /**
             * 查询所有组织
             */
            List<SysDept> list = sysDeptMapper.listMyselfAll(code, true,false,false);
            if(list!=null&&list.size()>0){
                log.info("查询所有组织返回结果:"+ JSON.toJSONString(list));
                for(SysDept sysDept:list){
                    if(sysDept.getName().equals(dept.getName())){
                        log.info("添加组织或虚拟机构，同组织下名称必须唯一");
                        return "组织名称已存在";
                    }
                    if(dept.getDeptType().equals(2) && !Objects.equals(dept.getEinType(), "3")){
                        if(dept.getTaxpayerCode().equals(sysDept.getTaxpayerCode())){
                            log.info("添加子公司，同组织下税号必须唯一");
                            return "税号已存在";
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 校验名称、税号不允许重复
     * @param dept
     * @return
     */
    public String checkDeptData(SysDept dept){
        if(StringUtils.isBlank(dept.getTaxpayerCode())){
            List<SysDept>   system = sysDeptMapper.queryDeptByName(dept.getName());
            if(system!=null&&system.size()>0){
                log.error("无税号时名称必须全局唯一");
                return "名称必须全局唯一";
            }
        }else{
            List<SysDept>   system = sysDeptMapper.queryDeptByTaxpayerCode(dept.getTaxpayerCode());
            if(system!=null&&system.size()>0){
                log.error("税号必须全局唯一");
                return "税号必须全局唯一";
            }
            if(StringUtils.isNotBlank(dept.getName())){
                List<SysDept>   systema = sysDeptMapper.queryDeptByName(dept.getName());
                if(systema!=null&&systema.size()>0){
                    log.error("有税号时名称必须全局唯一");
                    return "名称必须全局唯一";
                }
            }
        }
        return null;
    }


    public Result queryDeptByTaxpayerNameAndCode(String name, String taxpayerCode) {
        SysDept sysdept=sysDeptMapper.queryDeptByTaxpayerNameAndCode(name,taxpayerCode);
        SysDeptVo sysdeptvo=new SysDeptVo();
        if(sysdept!=null){
            log.info("根据名称和税号联合查询返回结果:"+ JSON.toJSONString(sysdept));
            BeanUtils.copyProperties(sysdept, sysdeptvo);
            if(StringUtils.isNotBlank(sysdept.getParentId())){
                sysdeptvo.setEpName(this.getSysDeptByDeptId(sysdept.getParentId()).getName());
            }
            return Result.ok().put("data",sysdeptvo);
        }
        return Result.error("根据名称和税号联合查询返回结果为空");
    }

    /**
     * 组织添加、修改数据校验
     * @param dept
     * @param sysTenantProductList
     * @return
     */
    public  String checkData(SysDept dept,List<SysTenantProduct> sysTenantProductList){
        if(dept.getDeptType()==null){
            return "组织类型不允许为空";
        }else{
            if(dept.getDeptType()!=1&&dept.getDeptType()!=2&&dept.getDeptType()!=3&&dept.getDeptType()!=4&&dept.getDeptType()!=5&&dept.getDeptType()!=6){
                return "组织类型只允许传1/2/3/4/5/6";
            }
        }
        if(StringUtils.isBlank(dept.getDeptSname())||StringUtils.isBlank(dept.getName())){
            return "组织名称不允许为空";
        }
        if(dept.getDeptType()!=1&&dept.getDeptType()!=4&&dept.getDeptType()!=5){//总公司可以无税号、部门、虚拟机构税号可为空
            if(!Objects.equals(dept.getEinType(), "3")){
                if(StringUtils.isBlank(dept.getTaxpayerCode())){
                    log.info("除总公司及部门、虚拟机构外纳税人识别号必填");
                    return "税号不允许为空";
                }
            }
        }
        if(dept.getTaxpayerType()!=null){
            if(!"0".equals(dept.getTaxpayerType()) && !"1".equals(dept.getTaxpayerType())){
                return "纳税人类型只允许传0/1";
            }
        }

        if(dept.getEinType()!=null){
            if("1".equals(dept.getEinType()) && "2".equals(dept.getEinType()) && "3".equals(dept.getEinType())){
                return "税号类型只允许传1/2/3";
            }
        }
        if(dept.getCreateUser()==null){
            return "创建用户不允许为空";
        }
        /*if(StringUtils.isNotBlank(dept.getTaxpayerProvinceCode())){
            AddressVo addressvo=sysDeptMapper.getAddressByCode(dept.getTaxpayerProvinceCode());
            if(addressvo!=null){
                dept.setTaxpayerProvince(addressvo.getAddressName());
            }else{
                return "对应地区code不存在，请确认";
            }
        }
        if(StringUtils.isNotBlank(dept.getTaxpayerCityCode())){
            AddressVo addressvo=sysDeptMapper.getAddressByCode(dept.getTaxpayerCityCode());
            if(addressvo!=null){
                dept.setTaxpayerCity(addressvo.getAddressName());
            }else{
                return "对应地区code不存在，请确认";
            }
        }
        if(StringUtils.isNotBlank(dept.getTaxpayerCountyCode())){
            AddressVo addressvo=sysDeptMapper.getAddressByCode(dept.getTaxpayerCountyCode());
            if(addressvo!=null){
                dept.setTaxpayerCounty(addressvo.getAddressName());
            }else{
                return "对应地区code不存在，请确认";
            }
        }*/
        if (CollectionUtils.isNotEmpty(sysTenantProductList)){
            //机构不能选通道，只能选产品，校验机构产品的授权时间必须在租户的授权时间之间
            for (SysTenantProduct deptProduct : sysTenantProductList) {
                List<SysTenantProduct> tenantProducts = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("tenant_id", dept.getTenantId()).eq("product_id",deptProduct.getProductId()).andNew("dept_id is null OR dept_id = ''"));
                if(tenantProducts == null || tenantProducts.size() == 0){
                    return "未获取到租户授权信息！";
                }else {
                    SysTenantProduct tenantProduct = tenantProducts.get(0);
                    if (deptProduct.getAuthEtime().before(tenantProduct.getAuthStime()) || deptProduct.getAuthStime().after(tenantProduct.getAuthEtime())) {
                        return "机构产品的授权时间必须在租户产品的授权时间之间！";
                    }
                }
            }
        }
        return null;
    }


    /**
     * 信息添加业务层处理封装
     * @param dept
     * @return
     */
    public String insertDept(SysDept dept){
        /**
         * 只有渠道账号才能添加组织信息
         */
        SysUser sysUser = sysUserMapper.selectById(dept.getCreateUser());
        /**
         * 是否完善主企业
         */
        boolean wsqy=false;
        /**
         * 是否添加子企业
         */
        boolean zqytj=false;
        Long roleId=null;
        if(sysUser!=null){
            log.info("添加企业对应的用户类型："+sysUser.getUserType());
            /**
             * 完善企业信息
             */
            if(sysUser.getUserType().equals("3")){
                wsqy=true;
                /**
                 * 完善企业信息对应用户改为主账号
                 */
                sysUserDao.updateUserType(dept.getCreateUser(),1);
            }else{
                if("4".equals(dept.getDataSource())||"5".equals(dept.getDataSource())){
                    if(1!=dept.getLevel()&&!"1".equals(sysUser.getUserType())){
                        log.error("添加子企业必须是主账号");
                        return "添加子企业必须是主账号";
                    }
                    if(1!=dept.getLevel()&&"5".equals(dept.getDataSource())){
                        log.info("客户中心添加子企业");
                        zqytj=true;
                    }
                }else if(!"7".equals(dept.getDataSource())&&!("5").equals(sysUser.getUserType())){
                    log.error("添加企业必须是渠道账号");
                    return "添加企业必须是渠道账号";
                }
            }
        }else{
            log.error("添加企业对应用户账户已删除");
            return "添加企业对应用户账户已删除";
        }

        /**
         * 生成企业授权码
         */
        Generator generator = new Generator();
        String stringRandom = "";
        for (int i = 1; i > 0; i++) {
            //生成位数为20位参数
            stringRandom = generator.getStringRandom(20);
            //判断生成的参数是否唯一若非唯一再次生成
            SysDept deptByQybm = sysDeptMapper.queryDeptByQybm(stringRandom);
            if (deptByQybm == null) {
                break;
            }
        }
        dept.setAuthorizationCode(stringRandom);
        /**
         * 新增企业禁用启用状态
         */
        dept.setStatus(1);
        /**
         * 补充纳税人省份编码，用于税航机构注册，默认填北京编码
         */
        if(StringUtils.isBlank(dept.getTaxpayerProvinceCode())){
            dept.setTaxpayerProvinceCode("1100");
        }
        /**
         * 生成对应企业层级code
         */
        if (StringUtils.isBlank(dept.getParentId())||dept.getDeptType().toString().equals("1")) {
            //存储
            sysDeptMapper.insert(dept);
            //修改code
            dept.setCode(dept.getId() + "A");
            /**
             * 生成企业编码
             */
            String enterpriseNumbers="";
            for(int i = 1; i > 0; i++){
                enterpriseNumbers = Generator.getRandomChar();
                SysDept system = sysDeptMapper.queryDeptByQybm(enterpriseNumbers);
                if(system==null){
                    break;
                }
            }
            dept.setEnterpriseNumbers(Generator.getRandomChar());
            dept.setDelFlag("0");
            //无税号不注册终端
            if(StringUtils.isNotBlank(dept.getTaxpayerCode())){
                dept.setSpId(mycstUtil.terminalRegist(dept));
            }
            sysDeptMapper.updateAllColumnById(dept);
        } else {
            SysDept parentDept =this.getSysDeptByDeptId(dept.getParentId());
            /**
             * 生成企业授权码
             */
            Generator genera = new Generator();
            String stringRan = "";
            for (int i = 1; i > 0; i++) {
                //生成位数为20位参数
                stringRan = genera.getStringRandom(20);
                //判断生成的参数是否唯一若非唯一再次生成
                SysDept deptByQybm = sysDeptMapper.queryDeptByQybm(stringRan);
                if (deptByQybm == null) {
                    break;
                }
            }
            dept.setAuthorizationCode(stringRan);
            //存储
            sysDeptMapper.insert(dept);
            //修改code
            dept.setCode(parentDept.getCode() + dept.getId() + "A");
            /**
             * 生成企业编码
             */
            String enterpriseNumbers="";
            for(int i = 1; i > 0; i++){
                enterpriseNumbers =parentDept.getEnterpriseNumbers();
                SysDept system = sysDeptMapper.queryDeptByQybm(enterpriseNumbers+"0"+i);
                if(system==null){
                    break;
                }
            }
            dept.setEnterpriseNumbers(Generator.getRandomChar());
            dept.setDelFlag("0");
            //无税号不注册终端
            if(StringUtils.isNotBlank(dept.getTaxpayerCode())){
                dept.setSpId(mycstUtil.terminalRegist(dept));
            }
            sysDeptMapper.updateAllColumnById(dept);
        }
        /**
         * 如果是完善企业信息用户表存对应deptId和用户组织关联关系表
         */
        if(wsqy){
            AdminUserOperateDto dto=new AdminUserOperateDto();
            dto.setUserId(dept.getCreateUser());
            dto.setDeptId(dept.getDeptId());
            dto.setSourceId(dept.getSourceId());
            dto.setPuserId(dept.getUpdateUser());
            sysUserDao.updateUserByUserId(dto);

            SysUserDept sysUserDept = new SysUserDept();
            sysUserDept.setDeptId(dept.getDeptId());
            sysUserDept.setUserId(sysUser.getUserId());
            sysUserDeptService.insert(sysUserDept);

            /**
             * 添加管理员角色
             */
            roleId = this.addRole(dept);

            // 添加新角色用户关系
            List<SysUserRole> roleList = new ArrayList<>();
            SysUserRole  ur = new SysUserRole();
            ur.setRoleId(roleId);
            ur.setUserId(dept.getCreateUser());
            roleList.add(ur);
            sysUserRoleService.insertBatch(roleList);
        }
        /**
         * 子企业添加
         */
        if(zqytj){
            SysUserDept sysUserDept = new SysUserDept();
            sysUserDept.setDeptId(dept.getDeptId());
            sysUserDept.setUserId(sysUser.getUserId());
            sysUserDeptService.insert(sysUserDept);
            /**
             * 添加管理员角色
             */
            roleId = this.addRole(dept);
            // 添加新角色用户关系
            List<SysUserRole> roleList = new ArrayList<>();
            SysUserRole  ur = new SysUserRole();
            ur.setRoleId(roleId);
            ur.setUserId(dept.getCreateUser());
            roleList.add(ur);
            sysUserRoleService.insertBatch(roleList);
        }

        return null;
    }

    /**
     * 创建角色 返回角色ID
     * @param dept
     * @return
     */
    public Long  addRole(SysDept dept){
        SysRole sysRole=new SysRole();
        //生成角色编码roleCode
        //查到数据库最后一个角色编码
        String roleCode = sysRoleDao.selectLastRoleCode();
        if(StringUtils.isBlank(roleCode)){
            sysRole.setRoleCode(generateNum(4,""));
        }else{
            sysRole.setRoleCode(generateNum(4,roleCode));
        }
        sysRole.setRoleName("管理员");
        sysRole.setType(0);
        sysRole.setDeptId(dept.getDeptId());
        sysRole.setDeptName(dept.getName());
        sysRole.setCreateTime(new Date());
        sysRole.setCreateBy(dept.getCreateUser());
        sysRole.setUpdateTime(new Date());
        sysRole.setUpdateBy(dept.getUpdateUser());
        /**
         * 管理员角色默认是门户侧企业角色
         */
        sysRole.setRoleProperty("2");
        sysRole.setDistributorId(dept.getSourceId());
        sysRole.setDelFlag("0");
        sysRoleDao.insert(sysRole);
        Long roleId=sysRole.getRoleId();
        /**
         * 角色关联对应企业菜单信息    待执行（和兴蒙沟通过了，创建企业管理员角色对应权限为空，兴蒙开通产品后调更新管理员权限接口）
         */
        log.info("管理员角色创建完成");

        return roleId;
    }

    /**
     * 更新部门
     *
     * @param sysdeptresqvo 部门信息
     * @return 成功、失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result updateDept(SysDeptResqVo sysdeptresqvo, boolean bookmark) {
        //机构信息
        SysDept dept=sysdeptresqvo.getApiDeptEntity();
        //机构产品授权信息
        List<SysTenantProduct> sysTenantProductList = sysdeptresqvo.getSysTenantProductList();
        /**
         * 判断是否需要生成组织ID，如同步则直接用传入的
         */
        String deptId=dept.getDeptId();
        //删除缓存
        sysUserService.redisDeleteByUserId(dept.getCreateUser());
        /**
         * 第一步：数据校验
         * 第二步：根据组织名称和税号查询录入企业是否已存在
         * 第三步：组织信息修改
         */
        if(StringUtils.isBlank(dept.getDeptId())){
            return Result.error("修改企业信息需传入对应的deptId");
        }
        String bool=this.checkData(dept, sysTenantProductList);
        SysUser sysUser = sysUserMapper.selectById(dept.getCreateUser());
        if(sysUser==null){
            log.error("根据创建用户查询对应用户信息为空，请确认");
            return Result.error("根据创建用户查询对应用户信息为空");
        }
        if(StringUtils.isBlank(bool)){
            try {
                SysDept oldDept =this.getSysDeptByDeptId(dept.getDeptId());
                /**
                 * 添加校验逻辑
                 */
                if(oldDept==null){
                    log.error("根据企业ID查询企业信息返回为空,该企业不存在");
                    return Result.error("该企业不存在");
                }


                //更新保留原先的code
                dept.setCode(oldDept.getCode());
                dept.setEnterpriseNumbers(oldDept.getEnterpriseNumbers());
                dept.setAuthorizationCode(oldDept.getAuthorizationCode());
                dept.setSpId(oldDept.getSpId());

                /**
                 * 票税管家更新企业信息对应用户信息可不为主账户
                 */
                if(StringUtils.isNotBlank(dept.getDataSource())&&"6".equals(dept.getDataSource())){
                    log.info("票税管家修改企业可以为主账户，也可以是子账户");
                    dept.setCreateUser(oldDept.getCreateUser());
                    dept.setUpdateUser(dept.getCreateUser());
                }else {
                    /**
                     * 门户进来更新默认新用户中心
                     */
                    SysUser sysUser1 = sysUserMapper.selectById(dept.getUpdateUser());
                    if (sysUser1 == null) {
                        log.error("根据修改用户查询对应用户信息为空，请确认");
                        return Result.error("根据修改用户查询对应用户信息为空");
                    }

                    dept.setDataSource("4");
                }

                if(StringUtils.isNotBlank(dept.getTaxpayerIndustryCode())&&!dept.getTaxpayerIndustryCode().equals(oldDept.getTaxpayerIndustryCode())){
                    log.error("修改企业所属行业编码");
                    IndustryVo industryvo=sysDeptMapper.selectIndustryByCode(dept.getTaxpayerIndustryCode());
                    dept.setTaxpayerIndustry(industryvo.getIndustryName());
                }

                /**
                 * 上级组织和类型不能修改
                 */
                if (StringUtils.isNotBlank(oldDept.getParentId()) && !oldDept.getParentId().equals(dept.getParentId())) {
                    return Result.error("上级组织不能修改!");
                } else if (!oldDept.getDeptType().equals(dept.getDeptType())) {
                    return Result.error("组织类型不能修改!");
                } else if (StringUtils.isBlank(oldDept.getTaxpayerCode()) || StringUtils.isBlank(dept.getTaxpayerCode()) || !oldDept.getName().equals(dept.getName()) || !oldDept.getTaxpayerCode().equals(dept.getTaxpayerCode())) {
                    /**
                     * 判断企业是否已存在
                     */
                    String str = this.checkUpdate(dept, oldDept);
                    if (StringUtils.isNotBlank(str)) {
                        return Result.error(str);
                    }
                }
                /**
                 * 查询对应的渠道id
                 */
                UserVO userVO = sysUserMapper.selectUserVoById(dept.getCreateUser());
                if (StringUtils.isBlank(dept.getSourceId())) {
                    dept.setSourceId(userVO.getDistributorId());
                }
                /**
                 * 更新时判断spid是否有值
                 */
                if(StringUtils.isBlank(dept.getSpId())){
                    if(StringUtils.isBlank(dept.getTaxpayerProvinceCode()) && StringUtils.isBlank(oldDept.getTaxpayerProvinceCode())){
                        dept.setTaxpayerProvinceCode("1100");
                    } else if (StringUtils.isBlank(dept.getTaxpayerProvinceCode()) && StringUtils.isNotBlank(oldDept.getTaxpayerProvinceCode())) {
                        dept.setTaxpayerProvinceCode(oldDept.getTaxpayerProvinceCode());
                    }
                    //无税号不注册税航终端
                    if(StringUtils.isNotBlank(dept.getTaxpayerCode())){
                        dept.setSpId(mycstUtil.terminalRegist(dept));
                    }
                }
                sysDeptMapper.update(dept, new EntityWrapper<SysDept>().eq("dept_id", dept.getDeptId()));
                //配置税控
                Result result = this.confSksbAndAce(deptId,sysdeptresqvo);
                log.info("新增税控和采集鉴权配置：{}",result);
                //机构产品授权关联先删后插
                sysTenantProductMapper.delete(new EntityWrapper<SysTenantProduct>().eq("dept_id",dept.getDeptId()));
                if(CollectionUtils.isNotEmpty(sysTenantProductList)){
                    sysTenantProductList.forEach(deptProduct->{
                        SysTenantProduct tenantProduct = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("tenant_id", dept.getTenantId()).eq("product_id",deptProduct.getProductId()).andNew("dept_id is null OR dept_id = ''")).get(0);
                        deptProduct.setTenantId(StringUtils.isNotBlank(dept.getTenantId()) ? dept.getTenantId() : tenantProduct.getTenantId());
                        deptProduct.setDeptId(deptId);
                        deptProduct.setCreateBy(String.valueOf(dept.getCreateUser()));
                        deptProduct.setUpdateBy(String.valueOf(dept.getCreateUser()));
                        deptProduct.setCreateTime(new Date());
                        deptProduct.setUpdateTime(new Date());
                        deptProduct.setChannelId(tenantProduct.getChannelId());
                        deptProduct.setChannelType(tenantProduct.getChannelType());
                        sysTenantProductMapper.insert(deptProduct);
                    });
                    //为管理员重新分配菜单权限
                    SysRole sysRole = new SysRole();
                    sysRole.setDeptId(dept.getDeptId());
                    sysRole.setRoleType(11);
                    SysRole admin = sysRoleDao.selectOne(sysRole);
                    //删除角色菜单关系
                    SysRoleMenu sysRoleMenu = new SysRoleMenu();
                    sysRoleMenu.setRoleId(admin.getRoleId());
                    sysRoleMenuService.delRoleMenu(sysRoleMenu);
                    // 3. 重新分配权限
                    // 3.1 获取租户已授权产品的所有菜单权限
                    List<String> menuIds = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(sysTenantProductList)) {
                        List<Long> productIdList = sysTenantProductList.stream().map(SysTenantProduct::getProductId).collect(Collectors.toList());
                        menuIds = productMenuMapper.getMenuIdListByProId(productIdList);
                    }
                    // 3.2 为角色分配菜单权限
                    for (String menuId : menuIds) {
                        SysRoleMenu roleMenu = new SysRoleMenu();
                        roleMenu.setRoleId(admin.getRoleId());
                        roleMenu.setMenuId(menuId);
                        sysRoleMenuService.insert(roleMenu);
                    }
                }
                //销项数据同步
                if (Integer.parseInt(result.get("code").toString())==0||result.get("msg").toString().equals("success")) {
                    result.put("data",deptId);
                    bSystemLogicService.tbThirdsysInfo(result, userVO.getUserId(), sysdeptresqvo,2, null);
                    List<SysTenantProduct> productList = sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>()
                            .eq("tenant_id", userVO.getTenantId())
                            .last("limit 1"));
                    if (productList.size() > 0) {
                        SysTenantProduct tenantProduct = productList.get(0);
                        JSONObject json = new JSONObject();
                        SysTaxbureauInfo info = sysTaxbureauInfoMapper.selectByTaxNo(dept.getTaxpayerCode());
                        json.put("name", dept.getName());
                        json.put("taxpayerCode", dept.getTaxpayerCode());
                        json.put("taxpayerType", dept.getTaxpayerType());
                        json.put("taxBureaName",info==null?"":info.getUserName());
                        json.put("taxBureaPass",info==null?"":info.getUserPass());
                        json.put("tenantId", dept.getTenantId());
                        json.put("channel", tenantProduct.getChannelType());
                        //开票方式默认是SHRPA，目前已经没用了，需要动态匹配通道
                        json.put("kpfs", "SHRPA");
                        json.put("spid", dept.getSpId());
                        log.info("全电数据同步开始:{}",json);
                        CommonRspVo response = bSystemLogicService.einvoiceAddDept(json);
                        log.info("全电数据交互结果：{}",response);
                        if (!response.getCode().equals(ResponseCodeEnum.SUCCESS.getCode())) {
                            log.info("全电数据交互失败,taxpayerCode:{}", dept.getTaxpayerCode());
                            return Result.error(response.getCode(), response.getMessage());
                        }
                    }
                }
            }catch (Exception e){
                log.error("组织信息修改异常：{}"+e.getMessage());
                throw new RuntimeException(e);
            }
        }else{
            log.error("数据校验失败，组织信息修改失败");
            return Result.error(bool);
        }

        return Result.ok().put("data",deptId);
    }

    public Result confSksbAndAce(String deptId, SysDeptResqVo sysDept) {
        //税控设备类型
        if (StringUtils.isNotBlank(sysDept.getSksbbm())) {
            boolean isadd =  sysTaxControlService.syncTaxControl(deptId,sysDept,0);
            if (!isadd) return Result.error("税控类型配置失败，请到机构管理中重新配置税控类型");
        }
        //采集鉴权Id
        if (StringUtils.isNotBlank(sysDept.getAceId())) {
            boolean isadd =  sysTaxControlService.syncTaxControl(deptId,sysDept,1);
            if (!isadd) return Result.error("采集鉴权Id和Key配置失败，请到机构管理中重新配置");
        }
        return Result.ok();
    }

    /**
     * 查询税号所属集团的所有企业税号
     * @param taxNo
     * @return
     */
    @Override
    public Result queryAllTaxListByTax(String taxNo,String tenantId) {
        //获取顶级机构code
        SysDept sysDept = sysDeptMapper.selectTopOrgByTaxNo(taxNo,tenantId);
        List<String> taxList = new ArrayList<>();
        if (sysDept != null) {
          List<SysDept> list = sysDeptMapper.listMyselfAll(sysDept.getCode(),true,false,false);
          list.stream().forEach(sysDept1 -> taxList.add(sysDept1.getTaxpayerCode()));
        }
        return Result.ok().put("data",taxList);
    }


    /**
     * 修改企业唯一性校验
     * @param dept
     * @return
     */
    public String checkUpdate(SysDept dept,SysDept oldDept){

        if(dept.getDeptType().equals(3)){
            log.error("数据校验，组织信息保存失败");
            Result  result = this.queryDeptByTaxpayerNameAndCode(dept.getName(), dept.getTaxpayerCode());
            if(result!=null&&result.get("code").toString().equals("0000")){
                log.error("修改分支机构，对应企业已存在");
                return "对应名称和税号关联的企业已存在";
            }
        }else{
            if(StringUtils.isBlank(dept.getTaxpayerCode())||StringUtils.isBlank(oldDept.getTaxpayerCode())){
                /**
                 * 总公司名称全局唯一，部门及虚拟机构组织下唯一
                 */
                if(dept.getDeptType().equals(1)&&!oldDept.getName().equals(dept.getName())){
                    List<SysDept>   system = sysDeptMapper.queryDeptByName(dept.getName());
                    if(system!=null&&system.size()>0){
                        log.error("修改顶级总公司，无税号时名称必须全局唯一");
                        return "修改顶级总公司，无税号时名称必须全局唯一";
                    }
                }else{
                    /**
                     * 查询code
                     */
                    if(StringUtils.isNotBlank(dept.getParentId())&&!oldDept.getName().equals(dept.getName())){
                        SysDept sysdept =this.getSysDeptByDeptId(dept.getParentId());
                        String code = sysdept.getCode();

                        /**
                         * 查询所有组织
                         */
                        List<SysDept> list = sysDeptMapper.listMyselfAll(code, true,false,false);
                        if(list!=null&&list.size()>0){
                            log.info("查询所有组织返回结果:"+ JSON.toJSONString(list));
                            for(SysDept sysDept:list){
                                if(sysDept.getName().equals(dept.getName())){
                                    return "修改组织或虚拟机构，同组织下名称必须唯一";
                                }
                            }
                        }
                    }
                }

            }else{
                if(!oldDept.getTaxpayerCode().equals(dept.getTaxpayerCode())){
                    List<SysDept>   system = sysDeptMapper.queryDeptByTaxpayerCode(dept.getTaxpayerCode());
                    if(system!=null&&system.size()>0){
                        return "对应名称和税号关联的企业已存在";
                    }
                }
            }
        }
        return null;
    }


    @Override
    public Result listMyselfAll(String deptId, boolean isContainBm,Long userId) {


        /**
         * 数据校验
         */
        if(StringUtils.isBlank(deptId)){
            log.error("请求参数deptId为空:"+deptId);
            return Result.error("查询自己及以下组织deptId不可以为空");
        }

        /**
         * 查询code
         * 20220826 只能查询到当前组织下所属组织
         */
//        SysDept sysdept =this.getSysDeptByDeptId(deptId);
        //查询顶级组织部门表
        SysDept sysdept =sysDeptMapper.selectTopLevelByDeptId(deptId);
        List<SysDeptVo> sysDeptList = new ArrayList<SysDeptVo>();
        if(sysdept==null){
            /**
             * 如sysdept表没有则需要查对应
             */
            Distributor distributor=distributorMapper.selectDistributorById(deptId);
            if(distributor==null){
                return Result.error("查询自己及以下组织deptId对应组织机构不存在");
            }else{
                log.info("查询组织树类型为企业级以上:"+ deptId);
                /**
                 * 产品设计：渠道去掉了是否分销，默认平台下只有一级渠道
                 */
                if(distributor.getId().equals("0")){
                    List<Distributor> distributorList=distributorMapper.selectAllDistributor();
                    /**
                     * 把所有平台及渠道都加到List里面
                     */
                    for(Distributor distr:distributorList){
                        SysDeptVo  sysDeptVo=this.packageSysDeptVoByDistributor(distr);
                        if("0".equals(distr.getId())){
                            sysDeptVo.setLevel(-1);
                        }
                        sysDeptList.add(sysDeptVo);
                    }
                    List<SysDeptVo> sysDeptVos = sysDeptMapper.queryAllDepts();
                    List<SysDeptVo> sysDeptVoList=new ArrayList<>();
                    for(SysDeptVo sysDeptVo:sysDeptVos){
                        /**
                         * 1级组织且传入渠道ID不为空时需添加parent_id为source_id
                         */
                        if(sysDeptVo.getLevel()==1&&StringUtils.isNotBlank(sysDeptVo.getSourceId())){
                            sysDeptVo.setParentId(sysDeptVo.getSourceId());
                        }
                        sysDeptVoList.add(sysDeptVo);
                    }
                    sysDeptList.addAll(sysDeptVoList);

                }else{
                    sysDeptList=getSysDeptListByDistributor(distributor,isContainBm);
                }
            }
        }else {
            boolean isDxAdmin = false;
            boolean isAdmin = false;
            if(userId != null){
                if(userId == 1L){
                    isDxAdmin=true;
                }else {
                    //查询用户角色
                    List<SysRole> sysRoles = sysRoleDao.selectRolesByUserId(userId);
                    if(sysRoles != null && sysRoles.size() > 0){
                        for (SysRole sysRole : sysRoles) {
                            if(sysRole.getRoleType() == 10){
                                isAdmin = true;
                                break;
                            }
                        }
                    }
                }
            }
            sysDeptList=getSysDeptListByCode(sysdept.getCode(), isContainBm,isDxAdmin,isAdmin);
        }

        return Result.ok().put("data", sysDeptList);
    }

    @Override
    public Result listMyselfAllByTier(String deptId, boolean isContainBm) {

        /**
         * 数据校验
         */
        if(StringUtils.isBlank(deptId)){
            log.error("请求参数deptId为空:"+deptId);
            return Result.error("查询当前级以下组织deptId不可以为空");
        }

        /**
         * 查询code
         */
        SysDept sysdept =this.getSysDeptByDeptId(deptId);

        List<SysDeptVo> sysDeptList = new ArrayList<SysDeptVo>();
        if(sysdept==null){
            /**
             * 如sysdept表没有则需要查对应
             */
            Distributor distributor=distributorMapper.selectDistributorById(deptId);
            if(distributor==null){
                return Result.error("查询当前级以下组织deptId对应组织机构不存在");
            }else{
                log.info("查询组织树类型为企业级以上:"+ deptId);
                /**
                 * 产品设计：渠道去掉了是否分销，默认平台下只有一级渠道
                 */
                if(distributor.getId().equals("0")){
                    List<Distributor> distributorList=distributorMapper.selectNextDistributor(deptId);
                    /**
                     * 把所有下一级渠道都加到List里面
                     */
                    List<String> sources=sysDeptMapper.selectDeptCountCondion();
                    List<String> sourceIds=new ArrayList <String>();
                    for(Distributor distr:distributorList){
                        sourceIds.add(distr.getId());
                        SysDeptVo  sysDeptVo=this.packageSysDeptVoByDistributor(distr);
                        /**
                         * 添加是否存在下级标志
                         */
                        for(String sourceId:sources){
                            if(distr.getId().equals(sourceId)){
                                sysDeptVo.setIsNextCheck("1");
                            }else{
                                if(StringUtils.isNotBlank(sysDeptVo.getIsNextCheck())&&"1".equals(sysDeptVo.getIsNextCheck())){
                                    sysDeptVo.setIsNextCheck("1");
                                }else {
                                    sysDeptVo.setIsNextCheck("0");
                                }
                            }
                        }
                        sysDeptList.add(sysDeptVo);
                    }
                }else{
                    sysDeptList=getNextSysDeptListByDistributor(distributor,isContainBm);
                }
            }
        }else {
            sysDeptList=getNextSysDeptListByCode(sysdept.getCode(),sysdept.getLevel(), true,null);
        }
        return Result.ok().put("data", sysDeptList);
    }


    @Override
    public Result listMyselfAllAndUserCount(String deptId, boolean isContainBm) {
        return null;
    }

    @Override
    public Result listMyselfOneLevel(Integer pageSize, Integer currPage, String deptId, boolean isContainBm,String entName,String nsrsbh) {

        Page<SysDeptVo> page = new Page(currPage, pageSize);

        List<SysDeptVo> list = sysDeptMapper.listMyselfOneLevel(page, deptId, isContainBm,entName,nsrsbh);
        log.info("查询自己下一级组织返回结果:"+ JSON.toJSONString(deptId));
        list.stream().forEach(sysDeptVo -> {
            //字典转换
            if (sysDeptVo.getTaxpayerType() != null) {
                Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code",sysDeptVo.getTaxpayerType()).eq("parent", DictParentConstants.NSRLX));
                if (dictionary != null) {
                    sysDeptVo.setTaxpayerTypeName(dictionary.getName());
                }
            }
            //查询机构产品授权（列表不用查，单条查即可）
            //sysDeptVo.setSysTenantProductList(sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("dept_id",sysDeptVo.getDeptId())));
        });
        page.setRecords(list);
        PageUtils pageUtils = new PageUtils(page);
        return Result.ok().put("data",pageUtils);
    }

    public SysDept getSysDeptByDeptId(String deptId){
        return sysDeptMapper.selectByDeptId(deptId);
    }

    /**
     * 渠道转组织信息
     * @param distributor
     * @return
     */
    public SysDeptVo packageSysDeptVoByDistributor(Distributor distributor){
        SysDeptVo sysDeptVo=new SysDeptVo();
        sysDeptVo.setDeptId(distributor.getId());
        sysDeptVo.setParentId(distributor.getSuperior());
        sysDeptVo.setName(distributor.getCompanyName());
        sysDeptVo.setLevel(0);
        sysDeptVo.setTaxpayerCode(distributor.getTaxNo());
        sysDeptVo.setTaxpayerBank(distributor.getBankName());
        sysDeptVo.setTaxpayerAccount(distributor.getBankNo());
        sysDeptVo.setLocationCode(distributor.getLocation());
        sysDeptVo.setContactEmail(distributor.getContactEmail());
        sysDeptVo.setContactPhone(distributor.getContactPhone());
        return  sysDeptVo;
    }
    /**
     * 根据渠道信息查询自身渠道下的组织树
     * @param distributor
     * @param isContainBm
     * @return
     */
    public List<SysDeptVo> getSysDeptListByDistributor(Distributor distributor,boolean isContainBm){

        List<SysDeptVo> sysdeptlist = new ArrayList<SysDeptVo>();
        /**
         * 把对应渠道加到第0条
         */
        SysDeptVo  sysDeptVo=this.packageSysDeptVoByDistributor(distributor);
        sysdeptlist.add(0,sysDeptVo);
        List<SysDept>   depts=sysDeptMapper.selectDeptBySourceId(distributor.getId());
        for(SysDept dep:depts){
            if(dep.getLevel()==1){
                dep.setParentId(distributor.getId());
            }
            SysDeptVo sysdeptvo=new SysDeptVo();
            BeanUtils.copyProperties(dep, sysdeptvo);
            sysdeptlist.add(sysdeptvo);
        }

        return  sysdeptlist;

    }


    /**
     *
     * @param code
     * @return
     */
    public List<SysDeptVo>  getSysDeptListByCode(String code,boolean isContainBm,boolean isDxAdmin,boolean isAdmin){

        List<SysDeptVo> SysDeptList = new ArrayList<SysDeptVo>();

        /**
         * 查询所有组织
         */
        List<SysDept> list = sysDeptMapper.listMyselfAll(code, isContainBm,isDxAdmin,isAdmin);

        /**
         * 查询所属组织名称
         */

        for (SysDept dept : list) {
            SysDeptVo sysdeptvo = new SysDeptVo();
            if (dept != null) {
                BeanUtils.copyProperties(dept, sysdeptvo);

                if (StringUtils.isNotBlank(dept.getParentId())) {
                    SysDept sysDeptEntity = this.getSysDeptByDeptId(dept.getParentId());
                    if (sysDeptEntity != null && !"".equals(sysDeptEntity)) {
                        sysdeptvo.setEpName(sysDeptEntity.getName());
                    }
                }
                SysDeptList.add(sysdeptvo);
            }
        }

        return SysDeptList;
    }

    /**
     * 根据渠道信息查询自身渠道下的组织树
     * @param distributor
     * @param isContainBm
     * @return
     */
    public List<SysDeptVo> getNextSysDeptListByDistributor(Distributor distributor,boolean isContainBm){

        List<SysDeptVo> sysdeptlist = new ArrayList<SysDeptVo>();
        List<SysDept>   depts=sysDeptMapper.selectOneLevelDeptBySourceId(distributor.getId());

        List<String>   nextDepts=sysDeptMapper.selectNextDeptCountCondion();

        for(SysDept dep:depts){
            if(dep.getLevel()==1){
                dep.setParentId(distributor.getId());
            }
            SysDeptVo sysdeptvo=new SysDeptVo();
            BeanUtils.copyProperties(dep, sysdeptvo);
            for(String nextId:nextDepts){
                if(dep.getDeptId().equals(nextId)){
                    sysdeptvo.setIsNextCheck("1");
                }else{
                    if(StringUtils.isNotBlank(sysdeptvo.getIsNextCheck())&&"1".equals(sysdeptvo.getIsNextCheck())) {
                        sysdeptvo.setIsNextCheck("1");
                    }else{
                        sysdeptvo.setIsNextCheck("0");
                    }
                }
            }
            sysdeptlist.add(sysdeptvo);
        }
        return  sysdeptlist;
    }

    /**
     * 查询除自己外的子级
     * @param code
     * @return
     */
    public List<SysDeptVo>  getNextSysDeptListByCode(String code,Integer level,boolean isContainBm,String sourceId){

        List<SysDeptVo> SysDeptList = new ArrayList<SysDeptVo>();
        /**
         * 查询自己以下组织
         */
        List<SysDept> list = sysDeptMapper.listUnlessMyselfAll(code, isContainBm,level+1);
        /**
         * 查询所属组织名称
         */
        for (SysDept dept : list) {
            SysDeptVo sysdeptvo = new SysDeptVo();
            if (dept != null) {
                BeanUtils.copyProperties(dept, sysdeptvo);
                if (StringUtils.isNotBlank(dept.getParentId())) {
                    SysDept sysDeptEntity = this.getSysDeptByDeptId(dept.getParentId());
                    if (sysDeptEntity != null && !"".equals(sysDeptEntity)) {
                        sysdeptvo.setEpName(sysDeptEntity.getName());
                    }
                }
                /**
                 * 添加是否存在下级标志
                 */
                List<SysDept> depts = sysDeptMapper.listUnlessMyselfAll(dept.getCode(), isContainBm,dept.getLevel()+1);
                if(depts!=null&&depts.size()>0){
                    sysdeptvo.setIsNextCheck("1");
                }else{
                    sysdeptvo.setIsNextCheck("0");
                }
                SysDeptList.add(sysdeptvo);
            }
        }



        return SysDeptList;
    }

    @Override
    public Result selectById(String deptId) {

        SysDept sysdept=selectDeptOrDistr(deptId);
        SysDeptVo sysdeptvo=new SysDeptVo();
        if(sysdept!=null){
            BeanUtils.copyProperties(sysdept, sysdeptvo);
            log.info("根据ID获取组织信息返回结果:"+ sysdeptvo);
            if(StringUtils.isNotBlank(sysdept.getParentId())) {
                sysdeptvo.setEpName(selectDeptOrDistr(sysdept.getParentId()).getName());
            }
            if(sysdeptvo.getCreateUser()!=null){
                UserVO userVO=sysUserDao.selectUserVoById(sysdeptvo.getCreateUser());
                if(userVO!=null){
                    sysdeptvo.setCreateUserName(userVO.getUsername());
                }
            }
            if(sysdeptvo.getUpdateUser()!=null){
                UserVO userVO=sysUserDao.selectUserVoById(sysdeptvo.getUpdateUser());
                if(userVO!=null){
                    sysdeptvo.setUpdateUserName(userVO.getUsername());
                }
            }
            if(sysdeptvo.getTaxpayerType() != null){
                //字典转换
                Dictionary dictionary = dictionaryService.selectOne(new EntityWrapper<Dictionary>().eq("code",sysdeptvo.getTaxpayerType()).eq("parent", DictParentConstants.NSRLX));
                if(dictionary != null){
                    sysdeptvo.setTaxpayerTypeName(dictionary.getName());
                }
            }
            //税控类型和采集鉴权Id
            SysTaxControl sksb = sysTaxControlMapper.selectAceByDeptId(deptId,0);
            if (sksb != null) {
                String strs [] =sksb.getSksbmc().split("-");
                if (strs.length>1) {
                    sysdeptvo.setSksbmc(strs[0].equals("本地")?"1":"2");
                    sysdeptvo.setSksbbm(strs[1]);
                }else{
                    Dictionary dictionary = dictionaryService.getByFlag("sksbbm-"+sksb.getSksbbm());
                    if (dictionary != null) {
                        String sk[] =  dictionary.getDesc().split("-");
                        sysdeptvo.setSksbmc(sk[0].equals("本地")?"1":"2");
                        if (!sksb.getSksbbm().equals("014")) {
                            sysdeptvo.setSksbbm(sk[1]);
                        }else{
                            sysdeptvo.setSksbbm("税务UKEY");
                        }
                    }

                }


            }
            SysTaxControl ace = sysTaxControlMapper.selectAceByDeptId(deptId,1);
            if (ace != null) {
                sysdeptvo.setAceId(ace.getSksbbm());
                sysdeptvo.setAceKey(ace.getSksbmc());
            }

            /**
             * 2022/11/3
             * 全电：电子税局局账号密码信息
             */
            SysTaxbureauInfo info = sysTaxbureauInfoMapper.selectByTaxNo(sysdept.getTaxpayerCode());
            if (info != null) {
                sysdeptvo.setTaxBureaName(info.getUserName());
                sysdeptvo.setTaxBureaPass(info.getUserPass());
            }
            //查询授权产品信息
            sysdeptvo.setSysTenantProductList(sysTenantProductMapper.selectList(new EntityWrapper<SysTenantProduct>().eq("dept_id",deptId)));
        }else{
            return Result.error("根据ID获取组织信息返回结果为空");
        }
        return Result.ok().put("data",sysdeptvo);
    }

    /**
     * 根据deptId查询对应组织信息
     * @param deptId
     * @return
     */
    public SysDeptVo selectDeptOrDistr(String deptId){

        SysDept deptDao =this.getSysDeptByDeptId(deptId);
        SysDeptVo sysdeptvo=new SysDeptVo();

        if(deptDao!=null) {
            BeanUtils.copyProperties(deptDao, sysdeptvo);
        }else{
            /**
             * 如sysdept表没有则需要查对应
             */
            Distributor distributor=distributorMapper.selectDistributorById(deptId);
            if(distributor!=null){
                sysdeptvo=this.packageSysDeptVoByDistributor(distributor);
            }
        }
        return sysdeptvo;
    }

    /**
     * 删除部门
     *
     * @param deptId 部门 ID
     * @return 成功、失败
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result deleteDeptByDeptId(String deptId) {

        /**
         * 第一步：判断企业是否已删除
         * 第二步：判断是否满足企业删除逻辑
         * 第三步：逻辑删除
         */
        SysDept dept = selectDeptOrDistr(deptId);

        //删除用户缓存
        sysUserService.redisDeleteByUserId(dept.getCreateUser());
        if(dept!=null){
            String canDelete = this.isCanDelete(deptId);
            if(canDelete==null){
                //删除对应组织---改为逻辑删除
//                sysDeptMapper.delete(new EntityWrapper<SysDept>().eq("dept_id",dept.getDeptId()));
                if(dept.getLevel().equals("0")){
                    sysDeptMapper.updateDistributor(1,deptId);
                }else{
                    dept.setDelFlag("1");
                    sysDeptMapper.update(dept,new EntityWrapper<SysDept>().eq("dept_id",dept.getDeptId()));
                }

                //查询部门下禁用用户
                List<SysUser> userList = sysUserDao.querySysUserStatus(deptId, 0);
                //删除组织下禁用用户
                sysUserDao.deleteByDeptIdAndStatus(deptId, 0);
                //删除用户角色绑定关系
                for (SysUser user : userList) {
                    sysUserRoleService.deleteByUserId(user.getUserId());
                }
                //删除该组织应用的数据权限
                sysDeptMapper.deleteUserDeptByDeptId(deptId);
                //删除该机构的角色数据,逻辑删除
                SysDept sysDept = new SysDept();
                sysDept.setDelFlag("1");
                baseMapper.update(sysDept,new EntityWrapper<SysDept>().eq("dept_id",deptId));
                //删除该机构的产品授权数据
                sysTenantProductMapper.delete(new EntityWrapper<SysTenantProduct>().eq("dept_id",deptId));
            }else{
                return Result.error(canDelete);
            }
        }

        return Result.ok();
    }

    @Override
    public Result deleteDeptBatch(String deptIds) {
        Result result = null;
        String[] split = deptIds.split(",");
        for (int i = 0; i < split.length; i++) {
            result = this.deleteDeptByDeptId(split[i]);
        }
        return result;
    }


    /**
     * 判断企业是否可以删除
     * @param deptId
     * @return
     */
    public String isCanDelete(String  deptId) {

        //查询code值
        SysDept sysDeptEntity =selectDeptOrDistr(deptId);
        String msg = null;
        if(sysDeptEntity==null || StringUtils.isBlank(sysDeptEntity.getDeptId())){
            return "未获取到有效的机构信息！";
        }
        if(sysDeptEntity.getLevel().equals("0")){
            if(sysDeptEntity.getName().contains("平台")){
                msg = sysDeptEntity.getName() + "平台不允许删除！";
                return msg;
            }else{
                msg = sysDeptEntity.getName() + "下还存在级联的公司、部门、用户和角色，请将级联信息删除后再删除分销商！";
            }

            List<SysDept>  sysDeptList= sysDeptMapper.selectDeptBySourceId(deptId);

            if(sysDeptList!=null&&sysDeptList.size()>0){
                return msg;
            }

        }else{
            String code = sysDeptEntity.getCode();
            Integer deptType = sysDeptEntity.getDeptType();

            if (deptType.equals(1) || deptType.equals(2)) {
                msg = sysDeptEntity.getName() + "下还存在级联的公司、部门、用户和角色，请将级联信息删除后再删除公司！";
            } else if (deptType.equals(3)) {
                msg = sysDeptEntity.getName() + "下还存在级联的部门、用户和角色，请将级联信息删除后再删除公司！";
            } else if (deptType.equals(4)) {
                msg = sysDeptEntity.getName() + "下还存在级联的用户和角色，请将级联信息删除后再删除公司！";
            } else if (deptType.equals(5)) {
                msg = sysDeptEntity.getName() + "下还存在级联的公司、部门、用户和角色，请将级联信息删除后再删除公司！";
            }

            //查询以下有没有组织
            Integer deptNum = sysDeptMapper.queryDeptCount(deptId, null, code, null, null, null);
            if (deptNum != 0) {
                return msg;
            }

        }

        //查询以下有没有角色
        Integer roleNum = sysRoleDao.queryRoleNameCountByDeptId(deptId, null);
        if (roleNum != 0) {
            return msg;
        }
        //查询以下有没有人员
        List<SysUser> userNum = sysUserDao.querySysUserStatus(deptId, 1);
        if (userNum!=null&&userNum.size() >0) {
            return msg;
        }

        return null;
    }

}
