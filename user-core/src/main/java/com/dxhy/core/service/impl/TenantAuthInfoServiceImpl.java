package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.mapper.ProductMenuMapper;
import com.dxhy.core.mapper.SysRoleMapper;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.mapper.TenantAuthInfoMapper;
import com.dxhy.core.pojo.DTO.AdminRoleOperateDto;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.entity.SysUserRole;
import com.dxhy.core.pojo.hw.TenantAuthInfoEntity;
import com.dxhy.core.pojo.hw.TenantInfoEntity;
import com.dxhy.core.pojo.hw.TenantRequestDto;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class TenantAuthInfoServiceImpl extends ServiceImpl<TenantAuthInfoMapper, TenantAuthInfoEntity> implements TenantAuthInfoService {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private TenantInfoService tenantInfoService;
    @Resource
    private SysRoleService sysRoleService;
    @Resource
    private ProductMenuMapper productMenuMapper;
    @Resource
    private SysUserRoleService sysUserRoleService;
    @Resource
    private SysUserService sysUserService;
    @Override
    public Result hwAuthSync(TenantRequestDto tenantRequestDto,int flag) {

        /*
         * usernmae：账号名称 有
         * email： 邮箱 有 非必填
         * phone：手机号 有 非必填
         * deptId：组织ID  部门ID orgCode
         * distrid：渠道ID
         * tenantId： 租户ID
         * topLevel：顶级组织ID
         * status： enable:账号状态（true-启用，false-禁用
         *
         * 姓名必填
         * 角色必填
         * */
        //1.同步超管账号信息 sys_user
        //解析前反转义该json字符串
        String userList = StringEscapeUtils.unescapeJavaScript(tenantRequestDto.getUserList());
        JSONArray jsonArray = JSON.parseArray(userList);
        log.info("******************* {} *****:{}",userList,jsonArray.size());
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject json =  jsonArray.getJSONObject(i);
            log.info(json.toString());
            SysUser sysUser = new SysUser();
            sysUser.setUsername(json.getString("userName"));
            sysUser.setNickname(json.getString("name"));
            sysUser.setDeptId(json.getString("orgCode"));
            sysUser.setStatus(json.getString("enable").equals("true")? 1 : 0);
            sysUser.setTenantId(tenantRequestDto.getTenantId());
            sysUser.setPhone(json.getString("mobile"));
            sysUser.setEmail(json.getString("email"));
            sysUser.setDistributorId(tenantRequestDto.getTenantId());
            sysUser.setUserType(json.getString("role").equals("admin")?"1":"2");
            sysUser.setUserSource("5");
            sysUser.setTopLevel(tenantRequestDto.getTenantId());
            sysUser.setPassword(ENCODER.encode("88888888"));
            UserVO userVO = sysUserMapper.selectUserVoByUsername(sysUser.getUsername());
            if (userVO == null && flag==1) {
                sysUserMapper.addUser(sysUser);
            }else if(flag==2 || (userVO != null && flag==1)){
                sysUserMapper.update(sysUser,new EntityWrapper<SysUser>().eq("user_id",userVO.getUserId()));
            }
            //2.用户角色关联
            UserVO userVO1 = sysUserMapper.selectUserVoByUsername(sysUser.getUsername());
            if (userVO1 != null) {
                Result result2 = this.syncUserRoleRelation(tenantRequestDto.getTenantId(), userVO1.getUserId(),json.getString("role"));
                if (!result2.get("code").equals("0000")) {
//                    log.info("又进这里了？");
                    return result2;
                }
            }
        }
        //清除租户下所有用户信息缓存
        List<Long> userIds = sysUserMapper.getUserIdList(tenantRequestDto.getTenantId());
        for (int i = 0; i < userIds.size(); i++) {
            sysUserService.redisDeleteByUserId(userIds.get(0));
        }
        return Result.ok();
    }

    private Result syncUserRoleRelation(String tenantId,Long userId,String role) {
        List<SysRole> roles = new ArrayList<>();
//        if (role.equals("admin")) {
//            roles = sysRoleMapper.queryRoleList(null,null,tenantId);
//        }else{
//            roles  = sysRoleMapper.queryRoleList("12",null,tenantId);
//        }
        roles = sysRoleMapper.queryRoleList(role.equals("admin")?"11":"12",null,tenantId);
        List<SysUserRole> userRoleList = new ArrayList<>();
        for (int j = 0; j < roles.size(); j++) {
            Boolean d = sysUserRoleService.deleteByUserId(userId);
            SysUserRole  ur = new SysUserRole();
            ur.setRoleId(roles.get(j).getRoleId());
            ur.setUserId(userId);
            userRoleList.add(ur);
        }
        sysUserRoleService.insertBatch(userRoleList);
        return Result.ok();
    }

    private Result syncUserRole(String tenantId,String roleType) {
        //查询该机构(租户)下的角色是否已存在，存在-》修改 不存在-》新增 新增超管
        TenantInfoEntity tenantInfoEntity = tenantInfoService.selectOne(new EntityWrapper<TenantInfoEntity>().eq("tenant_id",tenantId));
        if (tenantInfoEntity != null) {
            List<SysRole> roleList = sysRoleMapper.queryRoleList("10",tenantInfoEntity.getName(),tenantId);
            if (roleList.size()==0) {
                AdminRoleOperateDto adminRoleOperateDto = transfromRoleData(tenantId,"超级管理员","系统最高配置所有权限",tenantId);
                adminRoleOperateDto.setDeptName(tenantInfoEntity.getName());
                Result s = sysRoleService.addRole(adminRoleOperateDto);
            }
            List<SysRole> list = sysRoleMapper.queryRoleList(roleType.equals("admin")?"11":"12",tenantInfoEntity.getName(),tenantId);
            AdminRoleOperateDto adminRoleOperateDto = transfromRoleData(tenantId,roleType.equals("admin")?"管理用":"普通用户","",tenantId);
            if (roleList.size()==0) {
                adminRoleOperateDto.setDeptName(tenantInfoEntity.getName());
                Result s = sysRoleService.addRole(adminRoleOperateDto);
                return s;
            }else{
                adminRoleOperateDto.setRoleId(list.get(0).getRoleId());
                Result s = sysRoleService.updateRole(adminRoleOperateDto);
                return s;
            }
        }

        return Result.error();

    }
    private AdminRoleOperateDto transfromRoleData(String deptId, String roleName, String des, String distributorId) {
        AdminRoleOperateDto  adminRoleOperateDto = new AdminRoleOperateDto();
        adminRoleOperateDto.setDeptId(deptId);
        adminRoleOperateDto.setDescribe(des);
        adminRoleOperateDto.setRoleName(roleName);
        Long userId = sysUserMapper.getMinUserId();
        adminRoleOperateDto.setUserId(userId);
        adminRoleOperateDto.setRoleProperty("3");
        adminRoleOperateDto.setDistributorId(distributorId);
        if (roleName.equals("超级管理员")) {
            adminRoleOperateDto.setRoleType(10);
        }else if(roleName.equals("管理员")){
            adminRoleOperateDto.setRoleType(11);
        }else if(roleName.equals("普通用户")){
            adminRoleOperateDto.setRoleType(12);
        }
        List<String> mlist = new ArrayList<>();
        //查询开通产品对应的全量菜单Id
        List<Long> proIdlist = new ArrayList<>();
        proIdlist.add(1580039484260196352L);
        mlist = productMenuMapper.getMenuIdListByProId(proIdlist);
        adminRoleOperateDto.setMenusList(mlist);
        log.info("封装结果{}",adminRoleOperateDto);
        return adminRoleOperateDto;
    }
    /**
     * 角色初始化  超管 管理员 普通用户
     * @param tenantInfo
     * @return
     * <AUTHOR>
     * @date 2023-04-23
     */
    @Override
    public Result InitRole(TenantInfoEntity tenantInfo) {
        //1.需要同步角色（sys_role 只有角色类型 角色对应菜单权限无，这里先固定死非管理员类菜单）
        for (int i = 0; i < 3; i++) {
            String roleName = "";
            String desc = "";
            if (i == 0) {
                roleName="超级管理员";
                desc = "系统最高配置所有权限";
            }
            if(i == 1){
                roleName="管理员";
                desc = "";
            }
            if(i == 2){
                roleName="普通用户";
                desc = "";
            }
            AdminRoleOperateDto adminRoleOperateDto = transfromRoleData(tenantInfo.getTenantId(),roleName,desc,tenantInfo.getTenantId());
            adminRoleOperateDto.setDeptName(tenantInfo.getName());
            Result s = sysRoleService.addRole(adminRoleOperateDto);
        }
        return Result.ok();
    }

    @Override
    public Result delUser(TenantAuthInfoEntity tenantAuthInfoEntity) {
        //清除该租户下所有用户缓存  待做
        String userList = StringEscapeUtils.unescapeJavaScript(tenantAuthInfoEntity.getUserList());
        log.info(userList);
        JSONArray jsonArray = JSON.parseArray(userList);
        List<String> namelist  = new ArrayList<>();
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject json =  jsonArray.getJSONObject(i);
            namelist.add(json.getString("userName"));
        }
        if (namelist.size()>0) {
            List<Long> list = sysUserMapper.queryUserIdByNameList(namelist,tenantAuthInfoEntity.getTenantId());

            //删除用户角色关联
            ObjectMapper mapper = new ObjectMapper();
            List<Long> list2 = mapper.convertValue(list, new TypeReference<List<Long>>() { });
            list2.stream().forEach(userId -> {
                sysUserRoleService.deleteByUserId(userId);
                sysUserMapper.deleteUserDeptRelation(userId);
            });
            //删除用户数据权限关联
            return Result.ok().put("data",sysUserService.deleteBatchIds(list));
        }
        return Result.ok();
    }


}
