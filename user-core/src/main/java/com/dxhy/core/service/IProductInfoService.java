package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.pojo.entity.ProductInfo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-08
 */
public interface IProductInfoService extends IService<ProductInfo> {

    Map selectSysDeptByDeptId(String deptId);
    String selectTaxControlByDeptId(String deptId);

    Dictionary getByFlag(String flag);

    /**
     * 根据分销商id查询当前分销商对应状态的产品列表
     * @param distributorId
     * @param status 未传查询未删除的产品
     * @return
     */
    List<ProductInfo> selectListByDistributorId(String distributorId, String status,String currentDistributorId,String productName,String productClass);
}
