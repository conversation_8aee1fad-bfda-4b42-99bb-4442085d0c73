package com.dxhy.core.service;

import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.serializer.JdkSerializationRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.security.oauth2.provider.OAuth2Authentication;
import org.springframework.security.oauth2.provider.code.RandomValueAuthorizationCodeServices;

/**
 * Implementation of authorization code services that stores the codes and authentication in redis.
 * 
 * <AUTHOR>
 */
public class RedisAuthorizationCodeServices extends RandomValueAuthorizationCodeServices {

	private static final byte[] EMPTY_ARRAY = new byte[0];
	private static final Long EXPIRE_SECODES = 600L;

	private String prefix = "";

	private static final StringRedisSerializer STRING_SERIALIZER = new StringRedisSerializer();
	private static final JdkSerializationRedisSerializer OBJECT_SERIALIZER = new JdkSerializationRedisSerializer();

	private RedisConnectionFactory redisConnectionFactory;

	public RedisAuthorizationCodeServices(RedisConnectionFactory redisConnectionFactory) {
		this.redisConnectionFactory = redisConnectionFactory;
	}

	private RedisConnection getConnection() {
		return redisConnectionFactory.getConnection();
	}
	@Override
	protected void store(String code, OAuth2Authentication authentication) {
		RedisConnection conn = getConnection();
		try {
			//conn.openPipeline();
			conn.set(serialize(code), serialize(authentication));
			conn.expire(serialize(code), EXPIRE_SECODES);
		} finally {
			conn.close();
		}
	}

	@Override
	public OAuth2Authentication remove(String code) {
		RedisConnection conn = getConnection();
		byte[] bytes = null;
		try {
			bytes = conn.get(serialize(code));
			conn.del(serialize(code));
		} finally {
			conn.close();
		}
		OAuth2Authentication oAuth2Authentication = (OAuth2Authentication) deserialize(bytes);
		return oAuth2Authentication;
	}

	private byte[] serialize(String key) {
		byte[] keyCode = STRING_SERIALIZER.serialize(key);
		if(keyCode == null) {
			return EMPTY_ARRAY;
		}
		return keyCode;
	}

	private byte[] serialize(Object object) {
		return OBJECT_SERIALIZER.serialize(object);
	}

	private Object deserialize(byte[] bytes) {
		return OBJECT_SERIALIZER.deserialize(bytes);
	}

	public void setPrefix(String prefix) {
		this.prefix = prefix;
	}

}
