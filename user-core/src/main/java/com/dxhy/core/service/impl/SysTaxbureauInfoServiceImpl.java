package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.mapper.SysTaxbureauInfoMapper;
import com.dxhy.core.pojo.entity.SysTaxbureauInfo;
import com.dxhy.core.service.SysTaxbureauInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @description sys_taxbureau_info
 * <AUTHOR>
 * @date 2022-11-03
 */
@Service
@Slf4j
public class SysTaxbureauInfoServiceImpl extends ServiceImpl<SysTaxbureauInfoMapper, SysTaxbureauInfo>  implements SysTaxbureauInfoService {

    @Resource
    private SysTaxbureauInfoMapper sysTaxbureauInfoMapper;

    @Override
    public CommonRspVo updateTaxBureauInfo(SysTaxbureauInfo sysTaxbureauInfo) {
        SysTaxbureauInfo info = sysTaxbureauInfoMapper.selectByTaxNo(sysTaxbureauInfo.getTaxNo());
        if (info==null) {
            boolean insert = this.insert(sysTaxbureauInfo);
            log.info("新增税局账号密码信息：sys_taxbureau_info ={}", insert);
            return CommonRspVo.success(insert);
        }else {
            boolean update = this.update(sysTaxbureauInfo,new EntityWrapper<SysTaxbureauInfo>().eq("tax_no",sysTaxbureauInfo.getTaxNo()));
            log.info("修改税局账号密码信息：sys_taxbureau_info ={}", update);
            return CommonRspVo.success(update);
        }
    }
}