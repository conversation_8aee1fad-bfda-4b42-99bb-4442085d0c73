package com.dxhy.core.service;

import com.dxhy.core.pojo.PO.TempTablePO;
import com.dxhy.core.utils.PageUtils;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/5 17:38
 * @Version 1.0
 */
public interface TestService {

    /**
     * 测试
     * @param id
     * @return java.lang.String
     * <AUTHOR>
     **/
    TempTablePO getById(Integer id);

    /**
     * 测试
     * @param id
     * @return java.lang.String
     * <AUTHOR>
     **/
    TempTablePO selectById(Integer id);

    /**
     * 测试分页
     * @param
     * @return java.util.List<com.dxhy.core.pojo.PO.TempTablePO>
     * <AUTHOR>
     **/
    PageUtils testPage();

}
