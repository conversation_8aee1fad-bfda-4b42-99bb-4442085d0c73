package com.dxhy.core.service.impl;/**
 * @Auther: 李永强
 * @Date: 2019/7/8 15:13
 * @Description:
 */

import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.R;
import com.dxhy.core.component.mobile.MobileAuthenticationToken;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.enums.StateTypeEnum;
import com.dxhy.core.pojo.DTO.EntUserDTO;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.SigninService;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.service.UserService;
import com.dxhy.core.utils.AESUtils;
import com.dxhy.core.utils.AuthUtils;
import com.xiaoleilu.hutool.map.MapUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.util.URLEncoder;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationDetailsSource;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.oauth2.common.OAuth2AccessToken;
import org.springframework.security.oauth2.common.exceptions.InvalidClientException;
import org.springframework.security.oauth2.common.exceptions.UnapprovedClientAuthenticationException;
import org.springframework.security.oauth2.provider.*;
import org.springframework.security.oauth2.provider.request.DefaultOAuth2RequestValidator;
import org.springframework.security.oauth2.provider.token.AuthorizationServerTokenServices;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 *@program: heaven
 *@description:
 *@author: liu yan
 *@create: 2019-07-08 15:13
 */
@Slf4j
@Service("SigninService")
//@Service
public class SigninServiceImpl implements SigninService {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    @Autowired
    private UserService userService;

    @Autowired
    private SysUserService sysUserService;

    @Autowired
    private ClientDetailsService clientDetailsService;

    @Autowired
    private UserDetailsService userDetailsService;

    protected AuthenticationDetailsSource<HttpServletRequest, ?> authenticationDetailsSource = new WebAuthenticationDetailsSource();

    @Resource
    @Qualifier("defaultAuthorizationServerTokenServices")
    private AuthorizationServerTokenServices authorizationServerTokenServices;

    @Value("${security.encode.key}")
    private String key;//用于解密的Key

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${login.isOnce:1}")
    private String loginIsOnce;

    @Value("${sso.login.permissions:1}")
    private String permissions;

    @Resource
    private RestTemplate restTemplate;


    /**
     * signin登录接口参数校验
     * @param entUserDTO
     * @return
     */
    @Override
    public R parameterCheck(EntUserDTO entUserDTO) throws Exception {

        String username = entUserDTO.getUsername();
        String password = entUserDTO.getPassword();
        String code = entUserDTO.getCode();//验证码
        String uuid = entUserDTO.getUuid();//key 获取redis中存储的验证码文本

        if (username.length()<3) {
            return new R(7, "登录/注册账号长度不能小于3");
        }

        if(StringUtils.isBlank(username)){
            log.info("parameterCheck-用户名为空:username={}",username);
            return new R(5, "用户名为空");
        }

        if(StringUtils.isBlank(password)&&StringUtils.isBlank(code)){
            //密码和验证码都不存在的情况默认为免密登录
            username  = AESUtils.decryptAES(username, key);
            entUserDTO.setUsername(username);
        }
        URLEncoder urlEncoder = new URLEncoder();
        username = urlEncoder.encode(username.trim(), StandardCharsets.UTF_8);
        UserVO vo = null;
        if (entUserDTO.getClientType() == 2) {//手机号绑定税号
            vo = sysUserService.selectUserVoByContactPhone(username);
        }else{
            vo = userService.findUserByUsername(username);
        }


        if (vo == null && StringUtils.isNotBlank(password)) {
            log.info("parameterCheck-该账号未注册:username={}", username);
            return new R(1, "该账号未注册");
        }else if(vo == null && StringUtils.isBlank(password) && StringUtils.isNotBlank(code)){
            log.info("parameterCheck-该手机号未注册:username={}", username);
            return new R(1, "该手机号未注册");
        }else if (vo == null){
            //注册流程
            if(username.contains("@")){
                entUserDTO.setEmail(username);
            }else{
                entUserDTO.setMobile(username);
            }
            if (vo == null) {
                try{
//                    userService.autoRegister(entUserDTO);
                    vo = userService.findUserByUsername(username);
                    return  new R(vo);
                }catch (Exception e){
                    return new R(1,"服务器繁忙");
                }
            }

        }

        if(StateTypeEnum.ACTIVATION.getCode().equals(vo.getStatus().toString())){
            log.info("parameterCheck-用户已被禁用:username={}",username);
            return new R(4, "用户已被禁用");
        }

        if(StateTypeEnum.FROZEN.getCode().equals(vo.getDelFlag())){
            log.info("parameterCheck-用户已被删除:username={}",username);
            return new R(4, "用户已被删除");
        }
        if(StringUtils.isNotBlank(password)){
            password = AESUtils.decryptAES(password, key);
            boolean bool = ENCODER.matches(password, vo.getPassword());
            if (!bool) {
                // 密码不正确
                log.info("parameterCheck-密码错误:username={},password={}",username,password);
                return new R(2, "密码错误");
            }
        }
        //图片验证码
        if(StringUtils.isNotBlank(code)){
            String tempCode = "";
            if (StringUtils.isNotBlank(uuid)) {
                tempCode = sysUserService.validateCode(null,null,uuid,code);

            }else{
                tempCode = sysUserService.validateCode(username,null,null,code);
            }

            log.info("parameterCheck-验证码：{}",tempCode);
            if (StringUtils.isBlank(tempCode)) {
                log.error("用户:{}验证码已失效{}", username, tempCode);
                return new R(3, "验证码已失效，请再次申请");
            }
            if (!tempCode.equals(code)) {
                log.error("用户:{}验证码错误{}", username, tempCode);
                return new R(3, "验证码错误，请重新输入");
            }
        }


        if("0".equals(permissions)){
            Map<String, String> statusAndPermission = this.getStatusAndPermission(vo.getUserId(), entUserDTO.getProductId(), entUserDTO.getSourceId());
            Integer status = Integer.valueOf(statusAndPermission.get("status"));

            if(entUserDTO.getCheck() == 1){
                return  new R(vo);
            }
            if(status != 1 && status != 2){
                return new R((int)status, statusAndPermission.get("msg"));
            }
        }else {
            if(vo.getUserId() != 1){
                //校验用户所属机构是否有开通对应产品权限并且未过期(大象超级管理员不用校验)
                boolean productIsvalidByUserId = userService.getProductIsvalidByUserId(vo.getUserId());
                if(!productIsvalidByUserId){
                    return new R(10, "该企业未开通此产品");
                }
            }
        }
        return  new R(vo);
    }


    /**
     * 接口登录获取token
     * @param request
     * @param entUserDTO
     * @return
     * @throws Exception
     */
    @Override
    public String getToken(HttpServletRequest request, EntUserDTO entUserDTO) throws Exception {

        String clientId = entUserDTO.getClientId();
        String secret = entUserDTO.getClientSecret();
        // 2、 获取token
        String token = null;
        if(StringUtils.isBlank(entUserDTO.getPassword())) {
            // 免密
            token = this.getTokenByPasswordFree(request, entUserDTO.getUsername(),clientId, secret);
        } else {
            // 密码
            String password = AESUtils.decryptAES(entUserDTO.getPassword(), key);
            token = this.getTokenByPassword(entUserDTO.getUsername(), password, clientId, secret);
        }
        return token;
    }

    @Override
    public void updateUserLastLoginTime(Long userId) {
        log.info("修改用户最后登录时间:{}",userId);
        try {
            SysUser sysUser = sysUserService.selectById(userId);
            sysUser.setLastLoginTime(new Date());
            sysUserService.updateById(sysUser);
            sysUserService.redisDeleteByUserId(userId);

        } catch (Exception e) {
            log.error("修改用户最后登录时间异常",e);
        }
    }

    @Override
    public CommonRspVo encoderParamCheck(String username, String taxNo, String entName, String redirectURI) {
        if (StringUtils.isBlank(username)) {
            return new CommonRspVo(ResponseCodeEnum.ACCOUT_NULL);
        }
        if (StringUtils.isBlank(taxNo)) {
            return new CommonRspVo(ResponseCodeEnum.TAXNO_NULL);
        }
        if (StringUtils.isBlank(entName)) {
            return new CommonRspVo(ResponseCodeEnum.ENT_NAME_NULL);
        }
        if (StringUtils.isBlank(redirectURI)) {
            return new CommonRspVo(ResponseCodeEnum.MENU_URI_NULL);
        }
        return new CommonRspVo(ResponseCodeEnum.SUCCESS);
    }

    /**
     *  从请求头获取clientId,secret信息
     * @param request
     * @return
     * @throws IOException
     */
    private String[] getClientIdSecretFromHeader(HttpServletRequest request) throws IOException {
        String header = request.getHeader("Authorization");
        if (header == null || !header.startsWith("Basic")) {
            throw new UnapprovedClientAuthenticationException("请求头中client信息为空");
        }
        String[] tokens = AuthUtils.extractAndDecodeHeader(header);
        assert tokens.length == 2;
        return tokens;
    }



    /**
     *   无密码获取token - 用户免密登录及手机号验证码登录
     * @param request
     * @param mobile
     * @param clientId
     * @param secret
     * @return
     * @throws IOException
     */
    private String getTokenByPasswordFree(HttpServletRequest request, String mobile, String clientId, String secret) throws IOException {

        ClientDetails clientDetails = clientDetailsService.loadClientByClientId(clientId);
        //校验secret
        if (!clientDetails.getClientSecret().equals(secret)) {
            throw new InvalidClientException("Given client ID does not match authenticated client");
        }
        TokenRequest tokenRequest = new TokenRequest(MapUtil.newHashMap(), clientId, clientDetails.getScope(), "mobile");
        //校验scope
        new DefaultOAuth2RequestValidator().validateScope(tokenRequest, clientDetails);
        OAuth2Request oAuth2Request = tokenRequest.createOAuth2Request(clientDetails);
        UserDetails userDetails = userDetailsService.loadUserByUsername(mobile);
        MobileAuthenticationToken mobileAuthenticationToken = new MobileAuthenticationToken(userDetails, userDetails.getAuthorities());
        mobileAuthenticationToken.setDetails(authenticationDetailsSource.buildDetails(request));
        OAuth2Authentication oAuth2Authentication = new OAuth2Authentication(oAuth2Request, mobileAuthenticationToken);
        OAuth2AccessToken oAuth2AccessToken = authorizationServerTokenServices.createAccessToken(oAuth2Authentication);
        String accessToken = oAuth2AccessToken.getValue();
        return accessToken;
    }

    /**
     *  通过密码认证方式获取token，用户密码登录
     * @return
     */
    private String getTokenByPassword(String username, String password, String clientId, String secret) throws IOException {
        String url = "http://localhost:8501/user-base/oauth/token" ;
        MultiValueMap<String, String> paramsMap = new LinkedMultiValueMap<>();
        paramsMap.put("grant_type", Collections.singletonList("password"));
        paramsMap.put("scope", Collections.singletonList("server"));
        paramsMap.put("client_id", Collections.singletonList(clientId));
        paramsMap.put("client_secret", Collections.singletonList(secret));
        paramsMap.put("username", Collections.singletonList(username)); // 邮箱时候有问题，因此编码一下);
        paramsMap.put("password", Collections.singletonList(password));
        JSONObject response = restTemplate.postForObject(url.toString(), paramsMap, JSONObject.class);
        System.out.println(response.get("access_token"));
        String accessToken = response.get("access_token").toString();
        if (response != null) {
            return accessToken;
        }
        return null;
    }


    /**
     * 判断用户是否被禁用以及是否开通当前产品权限
     * @param userId
     * @param productId
     * @param sourceId
     * @return
     */
    public Map<String,String> getStatusAndPermission(Long userId, String productId, String sourceId){
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("status","0");
        R<Boolean> userTypeByUserId = userService.getUserStatusByUserId(userId);
        //查看用户是否被禁用
        if(userTypeByUserId!=null&&!userTypeByUserId.getData()){
            log.error("用户已被禁用:用户id:{}",userId);
            dataMap.put("status","5");
            dataMap.put("msg","用户已被禁用");
            return dataMap;
        }
        //查看企业用户是否开通产品权限
        log.info("查看用户是否开通产品权限:用户id:{},产品id:{},渠道id:{}",userId,productId,sourceId);
        R r = userService.getIsCompanyProductByUserId(userId,productId,sourceId);

        if(r != null && r.getCode() == 0){
            Map<String,String> data = (Map)r.getData();
            dataMap.put("status",data.get("status"));
            if(data.get("status").equals("3")){
                dataMap.put("msg","已停用");
            }
            if(data.get("status").equals("4")){
                //过期状态可以登录
                dataMap.put("status","1");
            }
            if(data.get("status").equals("6")){
                dataMap.put("msg","请在正确地址登录:"+data.get("url"));
            }
            if(data.get("status").equals("7")){
                dataMap.put("msg","该企业未开通此产品");
            }
        }else{
            dataMap.put("status","7");
            dataMap.put("msg","该企业未开通此产品");
        }
        return dataMap;
    }



}