package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.common.NacosParam;
import com.dxhy.core.mapper.HwBuyProductInfoMapper;
import com.dxhy.core.mapper.HwRenewProductInfoMapper;
import com.dxhy.core.mapper.HwUpgradeProductInfoMapper;
import com.dxhy.core.pojo.DTO.AppInfo;
import com.dxhy.core.pojo.DTO.AssociatedMarketDto;
import com.dxhy.core.pojo.PO.InstanceInfo;
import com.dxhy.core.pojo.entity.HwBuyProductInfoEntity;
import com.dxhy.core.pojo.entity.HwRenewProductInfoEntity;
import com.dxhy.core.pojo.entity.HwUpgradeProductInfoEntity;
import com.dxhy.core.service.HwBuyProductInfoService;
import com.dxhy.core.utils.HwUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;


@Service("hwBuyProductInfoService")
@Slf4j
public class HwBuyProductInfoServiceImpl extends ServiceImpl<HwBuyProductInfoMapper, HwBuyProductInfoEntity> implements HwBuyProductInfoService {

    @Resource
    private HwUtil hwUtil;

    @Autowired
    private NacosParam nacosParam;


    @Resource
    private HwBuyProductInfoMapper hwBuyProductInfoMapper;

    @Resource
    private HwRenewProductInfoMapper hwRenewProductInfoMapper;

    @Resource
    private HwUpgradeProductInfoMapper hwUpgradeProductInfoMapper;

    @Override
    public String newInstance(AssociatedMarketDto associatedMarketDto,HttpServletResponse response) throws Exception{
        insertBuyProductInfo(associatedMarketDto);
        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");
        map.put("encryptType","1");
        map.put("instanceId",associatedMarketDto.getInstanceId());


//
//        map.put("appInfo", appInfo);
        String json = JSONObject.toJSONString(map);
        log.info("新购商品返回参数：{}",json);
        hwUtil.signForResp(response,json);

        return json;
    }

    @Override
    public String refreshInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response) throws Exception{
        insertReNewProductInfo(associatedMarketDto);

        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");

        String json = JSONObject.toJSONString(map);
        hwUtil.signForResp(response,json);
        return json;
    }

    /**
     *  商品过期
     * @param associatedMarketDto
     * @param response
     * @return
     */
    @Override
    public String expireInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response) {
        // status   是否过期 1未过期 0过期
        HwBuyProductInfoEntity hwBuyProductInfoEntity = new HwBuyProductInfoEntity();
        hwBuyProductInfoEntity.setStatus("0");
        Wrapper<HwBuyProductInfoEntity> var2 = new EntityWrapper<>();
        var2.eq("instance_id",associatedMarketDto.getInstanceId());
        hwBuyProductInfoMapper.update(hwBuyProductInfoEntity,var2);

        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");

        String json = JSONObject.toJSONString(map);
        hwUtil.signForResp(response,json);
        return json;
    }

    /**
     * 资源释放
     * @param associatedMarketDto
     * @param response
     * @return
     */
    @Override
    public String releaseInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response) {
        //resource_release   是否商品资源释放 1未释放 0释放
        HwBuyProductInfoEntity hwBuyProductInfoEntity = new HwBuyProductInfoEntity();
        hwBuyProductInfoEntity.setResourceRelease("0");
        Wrapper<HwBuyProductInfoEntity> var2 = new EntityWrapper<>();
        var2.eq("instance_id",associatedMarketDto.getInstanceId());
        hwBuyProductInfoMapper.update(hwBuyProductInfoEntity,var2);
        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");

        String json = JSONObject.toJSONString(map);
        hwUtil.signForResp(response,json);
        return json;
    }

    /**
     * 商品升级
     * @param associatedMarketDto
     * @param response
     * @return
     */
    @Override
    public String upgrade(AssociatedMarketDto associatedMarketDto, HttpServletResponse response) throws Exception{
        insertUpgradeProductInfo(associatedMarketDto);

        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");

        String json = JSONObject.toJSONString(map);
        hwUtil.signForResp(response,json);
        return json;
    }



    /**
     * 资源状态变更
     * @param associatedMarketDto
     * @param response
     * @return
     */
    @Override
    public String instanceStatus(AssociatedMarketDto associatedMarketDto, HttpServletResponse response) {
        HwBuyProductInfoEntity hwBuyProductInfoEntity = new HwBuyProductInfoEntity();
        hwBuyProductInfoEntity.setFreezeStatus(associatedMarketDto.getInstanceStatus());
        Wrapper<HwBuyProductInfoEntity> var2 = new EntityWrapper<>();
        var2.eq("instance_id",associatedMarketDto.getInstanceId());
        hwBuyProductInfoMapper.update(hwBuyProductInfoEntity,var2);

        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");

        String json = JSONObject.toJSONString(map);
        hwUtil.signForResp(response,json);
        return json;
    }

    @Override
    public String queryInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response) {
        //instanceId 可能有多个  逗号分隔
        final String[] split = associatedMarketDto.getInstanceId().split(",");
        List infos = new ArrayList();
        for (String s : split) {
            InstanceInfo instanceInfo = new InstanceInfo();
            instanceInfo.setInstanceId(s);
            AppInfo appInfo = new AppInfo();
            appInfo.setAdminUrl(nacosParam.adminUrl);
            appInfo.setFrontEndUrl(nacosParam.frontEndUrl);
            appInfo.setUserName("");
            appInfo.setPassword("");
            instanceInfo.setAppInfo(appInfo);
            infos.add(instanceInfo);

        }
        Map<String,Object> map = new HashMap<>();
        map.put("resultCode","000000");
        map.put("resultMsg","success");
        map.put("encryptType","1");
        map.put("info",infos);
        String json = JSONObject.toJSONString(map);
        hwUtil.signForResp(response,json);
        return json;
    }


    private void insertReNewProductInfo(AssociatedMarketDto associatedMarketDto) throws Exception{
        //续费 添加操作记录  并更新buy_product_info  表
        Map map = new HashMap();
        map.put("instance_id",associatedMarketDto.getInstanceId());
        map.put("order_id",associatedMarketDto.getOrderId());
        final List list = hwRenewProductInfoMapper.selectByMap(map);
        if(list.size() == 0 ){
            HwRenewProductInfoEntity hwRenewProductInfoEntity = new HwRenewProductInfoEntity();
            BeanUtils.copyProperties(hwRenewProductInfoEntity,associatedMarketDto);
            hwRenewProductInfoEntity.setCreateTime(new Date());
            hwRenewProductInfoMapper.insert(hwRenewProductInfoEntity);
        }
        HwBuyProductInfoEntity hwBuyProductInfoEntity = new HwBuyProductInfoEntity();
        hwBuyProductInfoEntity.setProductId(associatedMarketDto.getProductId());
        hwBuyProductInfoEntity.setExpireTime(associatedMarketDto.getExpireTime());
        hwBuyProductInfoEntity.setTrialFlag(associatedMarketDto.getTrialFlag());
        hwBuyProductInfoEntity.setPeriodType(associatedMarketDto.getPeriodType());
        hwBuyProductInfoEntity.setPeriodNumber(associatedMarketDto.getPeriodNumber());
        hwBuyProductInfoEntity.setOrderAmount(associatedMarketDto.getOrderAmount());
        Wrapper<HwBuyProductInfoEntity> var2 = new EntityWrapper<>();
        var2.eq("instance_id",associatedMarketDto.getInstanceId());
        hwBuyProductInfoMapper.update(hwBuyProductInfoEntity,var2);

    }

    private void insertBuyProductInfo(AssociatedMarketDto associatedMarketDto) throws Exception{
        if(StringUtils.isBlank(associatedMarketDto.getInstanceId())){
            associatedMarketDto.setInstanceId(associatedMarketDto.getBusinessId());
        }
        Map map = new HashMap();
        map.put("instance_id",associatedMarketDto.getInstanceId());
        List<HwBuyProductInfoEntity> hwBuyProductInfoEntities = hwBuyProductInfoMapper.selectByMap(map);
        if(hwBuyProductInfoEntities.size() ==0){

            HwBuyProductInfoEntity hwBuyProductInfoEntity = new HwBuyProductInfoEntity();

            BeanUtils.copyProperties(hwBuyProductInfoEntity,associatedMarketDto);

            hwBuyProductInfoEntity.setCreateTime(new Date());
            hwBuyProductInfoMapper.insert(hwBuyProductInfoEntity);
        }

    }

    private void insertUpgradeProductInfo(AssociatedMarketDto associatedMarketDto) throws Exception{

        //商品升级  添加操作记录  并更新buy_product_info  表
        Map map = new HashMap();
        map.put("instance_id",associatedMarketDto.getInstanceId());
        map.put("order_id",associatedMarketDto.getOrderId());
        final List list = hwUpgradeProductInfoMapper.selectByMap(map);
        if(list.size() == 0 ){
            HwUpgradeProductInfoEntity hwUpgradeProductInfoEntity = new HwUpgradeProductInfoEntity();
            BeanUtils.copyProperties(hwUpgradeProductInfoEntity,associatedMarketDto);
            hwUpgradeProductInfoEntity.setCreateTime(new Date());
            hwUpgradeProductInfoMapper.insert(hwUpgradeProductInfoEntity);
        }
        HwBuyProductInfoEntity hwBuyProductInfoEntity = new HwBuyProductInfoEntity();
        hwBuyProductInfoEntity.setProductId(associatedMarketDto.getProductId());
        hwBuyProductInfoEntity.setSkuCode(associatedMarketDto.getSkuCode());
        hwBuyProductInfoEntity.setAmount(associatedMarketDto.getAmount());
        hwBuyProductInfoEntity.setDiskSize(associatedMarketDto.getDiskSize());
        hwBuyProductInfoEntity.setBandWidth(associatedMarketDto.getBandWidth());
        Wrapper<HwBuyProductInfoEntity> var2 = new EntityWrapper<>();
        var2.eq("instance_id",associatedMarketDto.getInstanceId());
        hwBuyProductInfoMapper.update(hwBuyProductInfoEntity,var2);
    }


}
