package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.constants.DbcacheConstants;
import com.dxhy.core.mapper.ProductMenuMapper;
import com.dxhy.core.pojo.entity.ProductMenu;
import com.dxhy.core.service.IProductMenuService;
import com.elephant.dbcache.annotation.CacheKey;
import com.elephant.dbcache.annotation.Cacheable;
import com.elephant.dbcache.annotation.collection.CacheParam;
import com.elephant.dbcache.annotation.collection.CollectionCacheable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * sys_permission 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Service
public class ProductMenuServiceImpl extends ServiceImpl<ProductMenuMapper, ProductMenu> implements IProductMenuService {

    @Autowired
    private ProductMenuMapper productMenuMapper;

    @Override
    @Cacheable(key= DbcacheConstants.DB,table = DbcacheConstants.TABLE_NAME_PRODUCT_MENU,operMode = Cacheable.OperMode.DELETE)
    public void deleteCache(@CacheKey String id){

    }


    @Override
    @CollectionCacheable(key= DbcacheConstants.DB,table = DbcacheConstants.TABLE_NAME_PRODUCT_MENU,operMode = CollectionCacheable.OperateMode.DELETE)
    public void delCacheByProductId(@CacheParam String productId) {

    }

    @Override
    public List<ProductMenu> getProductMenuByProductIdAndSellLabelId(String productId, String sellLabelId) {

        return productMenuMapper.getProductMenuByProductIdAndSellLabelId(productId,sellLabelId);
    }

    @Override
    public List<ProductMenu> getProMenuListByDeptId(String deptId,Integer roleType) {
        return productMenuMapper.getProMenuListByDeptId(deptId,roleType);
    }

    @Override
    public ProductMenu selectMenuId(String name, String systemSign) {
        return productMenuMapper.selectMenuId(name,systemSign);
    }

    @Override
    public List<ProductMenu> getProductMenu(String parentId) {
        Wrapper<ProductMenu> wrapper = new EntityWrapper();
        wrapper.eq("status","0");
        if (StringUtils.isNotEmpty(parentId)) {
            wrapper.eq("parent_id", parentId);
        }
        return baseMapper.selectList(wrapper);
    }

}
