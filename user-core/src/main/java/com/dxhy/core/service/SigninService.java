package com.dxhy.core.service;/**
 * @Auther: 李永强
 * @Date: 2019/7/8 15:13
 * @Description:
 */


import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.R;
import com.dxhy.core.pojo.DTO.EntUserDTO;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 *
 * @return 
 * <AUTHOR>
 * @date 2022-05-10
 */
public interface SigninService {

    /**
     * 接口登录校验入参
     * @param entUserDTO
     * @return
     */
    R parameterCheck(EntUserDTO entUserDTO) throws Exception;

    /**
     * 获取tokenUserVO
     * @param request
     * @param entUserDTO
     * @return
     * @throws IOException
     */
    String getToken(HttpServletRequest request, EntUserDTO entUserDTO) throws Exception;

    void updateUserLastLoginTime(Long userId);

    CommonRspVo encoderParamCheck(String username, String taxNo, String entName, String redirectURI);
}