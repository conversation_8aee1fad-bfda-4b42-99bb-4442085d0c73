package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.pojo.DTO.AssociatedMarketDto;
import com.dxhy.core.pojo.entity.HwBuyProductInfoEntity;

import javax.servlet.http.HttpServletResponse;

/**
 * 商品新购  过期  资源释放 资源状态变更 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 10:58:16
 */
public interface HwBuyProductInfoService extends IService<HwBuyProductInfoEntity> {


    String newInstance(AssociatedMarketDto associatedMarketDto,HttpServletResponse response)throws Exception;

    String refreshInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response)throws Exception;

    String expireInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response);

    String releaseInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response);

    String upgrade(AssociatedMarketDto associatedMarketDto, HttpServletResponse response)throws Exception;

    String instanceStatus(AssociatedMarketDto associatedMarketDto, HttpServletResponse response);

    String queryInstance(AssociatedMarketDto associatedMarketDto, HttpServletResponse response);
}

