package com.dxhy.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.mapper.Wrapper;
import com.dxhy.core.common.response.R;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.enums.LoginGrantTypeEnum;
import com.dxhy.core.mapper.SysRoleMapper;
import com.dxhy.core.mapper.SysTenantProductMapper;
import com.dxhy.core.pojo.DTO.EntUserDTO;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import com.dxhy.core.pojo.entity.SysUser;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.SysUserService;
import com.dxhy.core.service.UserService;
import com.dxhy.core.utils.UserUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;


@Slf4j
@Service("UserService")
public class UserServiceImpl implements UserService {
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();
    @Autowired
    private HttpServletRequest request;
    @Autowired
    private SysUserService sysUserService;
    @Autowired
    private SysTenantProductMapper sysTenantProductMapper;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SysRoleMapper sysRoleMapper;

    @Override
    public R<Boolean> getUserStatusByUserId(Long userId) {
        if (userId == null) {
            userId = getUserId();
        }
        R r = sysUserService.getUserStatusByUserId(userId);
        return r;
    }

    @Override
    public boolean getProductIsvalidByUserId(Long userId) {
        //获取用户信息
        SysUser sysUser = sysUserService.selectById(userId);
        //根据用户id获取角色信息
        List<SysRole> sysRoles = sysRoleMapper.selectRolesByUserId(userId);
        if(!Objects.isNull(sysUser)){
            //查询该用户所属机构是否有已开通产品并且未失效
            Wrapper<SysTenantProduct> wrapper = new EntityWrapper<SysTenantProduct>()
                    .eq("tenant_id", sysUser.getTenantId())
                    //当前时间未超过授权结束时间
                    .ge("auth_etime", new Date())
                    .le("auth_stime", new Date());
            //如果角色中包含roleType为10的超级管理员角色，则查询租户下所有已授权产品
            if(sysRoles != null && sysRoles.size() > 0){
                boolean isAdmin = false;
                for (SysRole sysRole : sysRoles) {
                    if(sysRole.getRoleType() == 10){
                        isAdmin = true;
                        break;
                    }
                }
                if(isAdmin){
                    //租户管理员不用查机构相关
                    wrapper.andNew("dept_id is null OR dept_id = ''");
                }else {
                    //其他需关联机构查询
                    wrapper.eq("dept_id", sysUser.getDeptId());
                }
            }else {
                return false;
            }
            List<SysTenantProduct> sysTenantProducts = sysTenantProductMapper.selectList(wrapper);
            if(sysTenantProducts != null && sysTenantProducts.size() > 0){
                return true;
            }
        }
        return false;
    }

    @Override
    public R getIsCompanyProductByUserId(Long userId, String productId, String sourceId) {
        if (userId == null) {
            userId = this.getUserId();
        }
        R r = sysUserService.getPermissionCheck(userId,productId,sourceId);
        return r;
    }


    public Long getUserId() {
        return UserUtils.getUserId(request);
    }

    public UserVO findUserByUsername(String username) {
        try {
            username = URLDecoder.decode(username, "UTF-8");
            log.info("对应用户名："+username);
        }catch (UnsupportedEncodingException e){
            log.error("用户名解码异常");
            return null;
        }

        String userType = username.substring(username.length()-2,username.length()-1);
        String grantType = username.substring(username.length()-1,username.length());
        String newUserName = username.substring(0,username.length()-2);
        UserVO user = null;
        if(!userType.equals("P") && !userType.equals("C")){
            user = sysUserService.findUserByUsername(username);
        }else{
            user = sysUserService.findUserByUsername(newUserName);
        }

        if(user == null){
            return null;
        }

        if(grantType.equals(LoginGrantTypeEnum.CODE.getCode())){
            String code = (String)redisTemplate.opsForValue().get(SystemConstants.DEFAULT_SMS_CODE_KEY + newUserName);
            if(StringUtils.isNotBlank(code)){
                user.setPassword(ENCODER.encode(code));
            }
        }else if(grantType.equals(LoginGrantTypeEnum.SCAN.getCode())){
            user.setPassword(ENCODER.encode(user.getPhone()));
        }

        return user;
    }

    @Override
    public UserVO findUserByMobile(String mobile) {
        return sysUserService.findUserByMobile(mobile);
    }

    @Override
    public R<Map<String, String>> getSourceInfoByUri(String uri) {
        try {
            uri = URLDecoder.decode(uri, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return sysUserService.getSourceInfoByUri(uri);
    }

    @Override
    public R<String> getProductInfoById(String productId) {
        return sysUserService.getProductInfoById(productId);
    }

    @Override
    public R sensorsLogin(HashMap<String, String> map) {
        String deviceId = String.valueOf(map.get("deviceId"));
        String userType = String.valueOf(map.get("userType"));
        EntUserDTO entUserDTO = new EntUserDTO();
        entUserDTO.setProductId(map.get("productId"));
        entUserDTO.setProductName(map.get("productName"));
        entUserDTO.setUsername(map.get("username"));
        entUserDTO.setPlatformType(map.get("platformType"));
        if(StringUtils.isNotBlank(map.get("platform"))){
            entUserDTO.setPlatform(map.get("platform"));
        }
        R r = sysUserService.sensorsLogin(entUserDTO,deviceId,userType);
        return r;
    }

    public R autoRegister(EntUserDTO entUserDTO) throws Exception {
        log.info("注册传入用户信息为："+ JSON.toJSONString(entUserDTO));
        return sysUserService.register(entUserDTO);
    }


}
