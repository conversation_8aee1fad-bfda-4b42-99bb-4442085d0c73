package com.dxhy.core.service;



import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.vo.SysDeptResqVo;

import java.util.List;

public interface BSystemLogicService {
    //数据库触发器 当sysdept更新时 数据同步 集成大象 统一注册接口

    //获取DX token
    String getDxToken();

    CommonRspVo synchronizationDXData(String deptId,Long userId, int type) throws Exception;

    CommonRspVo synchronUserAuth(Long userId);

    CommonRspVo syncXiaoxiang(SysDept sysDept, int type);

    CommonRspVo synchronDXjxUserData(Long userId);

    CommonRspVo synchronDXjxTaxData(List<String> deptIdList, Long userId);

    Result tbThirdsysInfo(Result result, Long userId, SysDeptResqVo sysDept, int updateType, Result res);

    CommonRspVo tbThirdsysInfoBatch(String deptId, Long userId, String productId,String tenantId) throws Exception;

    Result accountCheck(JSONObject json, String url);

    CommonRspVo einvoiceAddDept(JSONObject json);

    String dualRedirectUrl(String redirectUrl,String taxNo,String redirectURI,String username);

	CommonRspVo jxInvoiceAddDept(JSONObject json);
}
