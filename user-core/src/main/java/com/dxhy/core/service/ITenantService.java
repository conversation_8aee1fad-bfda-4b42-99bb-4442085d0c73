/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.service;

import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.common.response.Result;
import com.dxhy.core.pojo.entity.SysTenant;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 10:42
 */
public interface ITenantService extends IService<SysTenant> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param params
	 * @return
	 */
	Result listByPage(Page<SysTenant> page, Map<String, Object> params);


	/**
	 * 新增
	 *
	 *
	 * @param username
	 * @param tenant
	 * @return
	 */
	Result saveTenant(String username, SysTenant tenant);

	/**
	 * 删除
	 * @param ids
	 */
	Result remove(String ids);

	List<String> getTenantIdList(String tenantId);

	/**
	 * 查询租户信息
	 * @param tenantId
	 * @return
	 */
	SysTenant getTenantInfo(String tenantId);

	/**
	 * 查询公钥
	 * @param secretId
	 * @return
	 */
	SysTenant getSecret(String secretId);

	/**
	 * 校验租户是否有该产品的权限
	 * @param secretId
	 * @return
	 */
	boolean validTenantProduct(String secretId,String productId);
}
