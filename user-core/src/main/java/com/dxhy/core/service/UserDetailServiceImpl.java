/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.service;

import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.impl.UserDetailsImpl;
import org.apache.catalina.util.URLEncoder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2019/05/24
 * <p>
 */
@Service("userDetailService")
public class UserDetailServiceImpl implements UserDetailsService {
    @Autowired
    private UserService userService;

    URLEncoder urlEncoder = new URLEncoder();

    @Override
    public UserDetailsImpl loadUserByUsername(String username) throws UsernameNotFoundException {
        String encoderUsername  = urlEncoder.encode(username, StandardCharsets.UTF_8);
        UserVO userVo = userService.findUserByUsername(encoderUsername);
        System.out.println("进入了加载账户防范............"+userVo.getUsername());
//        ResponseEntity<UserVO> resp = restTemplate.getForEntity("http://localhost:8083/user-service/user/findUserByUsername/"+username,UserVO.class);
//        System.out.println("service/UserDetailServiceImpl类中的调用findUserByUsername请求结果："+resp.getBody());
//        UserVO userVo = resp.getBody();
        if(userVo == null) {
            throw new UsernameNotFoundException("用户名不存在");
        }
        userVo.setUsername(username);
        return new UserDetailsImpl(userVo,userVo.getUserType());
    }
}
