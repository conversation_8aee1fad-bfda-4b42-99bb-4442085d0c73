package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.ComboDistributorMapper;
import com.dxhy.core.pojo.entity.ComboDistributor;
import com.dxhy.core.pojo.entity.ComboInfo;
import com.dxhy.core.service.IComboAccountService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Service
public class ComboDistributorServiceImpl extends ServiceImpl<ComboDistributorMapper, ComboDistributor> implements IComboAccountService.IComboDistributorService {

    /**
     * 获取产品套餐
     * @param productId
     * @return
     */
    @Override
    public List<ComboInfo> selectCombos(String productId) {
        return this.baseMapper.selectCombos(productId);
    }
    /**
     * 获取产品套餐
     * @param params
     * @return
     */
    @Override
    public List<ComboDistributor> getComboDistributorByproductId(Map<String,Object> params) {
        return this.baseMapper.getComboDistributorByproductId(params);
    }

    @Override
    public ComboDistributor getComboDistByProductId(String productId, String distributorId,String sellLabelId){
        return this.baseMapper.getComboDistByProductId(productId,distributorId,sellLabelId);
    }

    @Override
    public List<String> getChargeItmesByComboTemplateId(String templateId) {
        return this.baseMapper.getChargeItmesByComboTemplateId(templateId);
    }

    @Override
    public String getSellLabelIdByComboDistributorId(String comboDistributorId) {
        return this.baseMapper.getSellLabelIdByComboDistributorId(comboDistributorId);
    }

    @Override
    public List<Map> getProductIdAndSellLabelIdByDistributorId(String distributorId) {
        return this.baseMapper.getProductIdAndSellLabelIdByDistributorId(distributorId);
    }
}
