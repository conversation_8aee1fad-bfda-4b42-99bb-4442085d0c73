package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.SystemVersionMapper;
import com.dxhy.core.pojo.entity.SysVersionTree;
import com.dxhy.core.pojo.entity.SystemVersion;
import com.dxhy.core.pojo.vo.SysVersionDataVo;
import com.dxhy.core.pojo.vo.SysVersionVo;
import com.dxhy.core.service.SystemVersionService;
import com.dxhy.core.utils.TreeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 版本日志
 * @return
 * <AUTHOR>
 * @date 2022-09-08
 */
@Service
public class SystemVersionServiceImpl extends ServiceImpl<SystemVersionMapper, SystemVersion> implements SystemVersionService {
    @Autowired
    private SystemVersionMapper systemVersionMapper;

    @Override
    public List<SysVersionDataVo> selectSysVersionList() {
        //1.查询所有版本日志
        List<SystemVersion> list = systemVersionMapper.queryHistoryVersionList(0);
        //2.转换集合
        List<SysVersionTree> svTreeList  = new ArrayList<>();
        list.stream().forEach(systemVersion -> svTreeList.add(new SysVersionTree(systemVersion)));
        //3.构建树级递归
        List<SysVersionTree> treelist = TreeUtil.buildTreeByRecursive(svTreeList,-1);
        //4.查询版本更新集合
        List<SysVersionVo> versionVoList = systemVersionMapper.selectRootVersionList();
        //5.按版本号和日期帅选 赋值SysVersionVo 中 tail集合
        Set<String> set = new HashSet<>();
        versionVoList.stream().forEach(sysVersionVo -> {
            List<SysVersionTree> svtlist = treelist.parallelStream().filter(
                    sysVersionTree -> sysVersionVo.getVersion().equals(sysVersionTree.getVersionNum()) &&  sysVersionVo.getDate().equals(sysVersionTree.getReleaseDate()))
                    .collect(Collectors.toList());
            sysVersionVo.setDetail(svtlist);

            /**
             * 每个实体中 截取date前4位（年） 放入map 集合中
             */
            set.add(sysVersionVo.getDate().substring(0,4));
        });

        //6.按年筛选
        List<SysVersionDataVo> showData = new ArrayList<>();
        set.stream().forEach(s -> {
            List<SysVersionVo> dateList = versionVoList.parallelStream().filter(sysVersionVo -> s.equals(sysVersionVo.getDate().substring(0,4))).collect(Collectors.toList());
            SysVersionDataVo sysVersionDataVo = new SysVersionDataVo();
            sysVersionDataVo.setYear(Integer.parseInt(s));
            sysVersionDataVo.setDateList(dateList);
            showData.add(sysVersionDataVo);
        });

        return showData;
    }
}
