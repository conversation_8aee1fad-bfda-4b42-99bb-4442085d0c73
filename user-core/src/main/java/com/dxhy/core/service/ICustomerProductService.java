package com.dxhy.core.service;

import com.baomidou.mybatisplus.service.IService;
import com.dxhy.core.pojo.entity.CustomerProduct;

import java.util.List;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-06
 */
public interface ICustomerProductService extends IService<CustomerProduct> {


    String getProductId(String id);

    /**
     *修改开通产品的计费周期
     * @param accountInfoId
     * @param customerId
     * @param productId
     * @return
     */
    boolean updateCustomerProduct(String accountInfoId,String customerId,String productId,String total);

    List<CustomerProduct> getCustomerProductByDis(String distributorId, String productId);


    Boolean updateProductStatus(String accountInfoId, String productId, String customerId, int status);

    int updateCustomerProductTotal(String accountInfoId, String total, String customerId, String productId);

    String getProductIdByComboId(String comboDistributorId);

    List<CustomerProduct> getCustomerProductByUserId(Long userId);
}
