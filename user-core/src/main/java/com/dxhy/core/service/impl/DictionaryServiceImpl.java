package com.dxhy.core.service.impl;

import com.baomidou.mybatisplus.service.impl.ServiceImpl;
import com.dxhy.core.mapper.DictionaryMapper;
import com.dxhy.core.pojo.entity.Dictionary;
import com.dxhy.core.service.IDictionaryService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
@Service
public class DictionaryServiceImpl extends ServiceImpl<DictionaryMapper, Dictionary> implements IDictionaryService {
    @Resource
    private DictionaryMapper dictionaryMapper;

    @Override
    public List<Dictionary> getTree(String parent) {
        return this.baseMapper.getTree(parent);
    }

    public Dictionary getByFlag(String flag) {
        return this.baseMapper.getByFlag(flag);
    }

    @Override
    public List<Dictionary> selectDictionary(String id, String code, String name, String desc, String parent, String flag) {
        List<Dictionary> list = dictionaryMapper.selectDictionary(id,code,name,desc,parent,flag);
        return list;
    }

    @Override
    public Long selectMaxId() {
        Long maxId = dictionaryMapper.selectMaxId();
        if(maxId!=null){
            return maxId;
        }
        return 0L;
    }
}
