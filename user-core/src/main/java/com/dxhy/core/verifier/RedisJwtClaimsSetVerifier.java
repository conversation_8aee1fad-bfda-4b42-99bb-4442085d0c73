package com.dxhy.core.verifier;

import com.dxhy.core.constants.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.security.oauth2.provider.token.store.JwtClaimsSetVerifier;

import java.util.Map;

@Slf4j
public class RedisJwtClaimsSetVerifier implements JwtClaimsSetVerifier {
    @Autowired
    private RedisTemplate redisTemplate;
    /**
     * 检查jti是否在Redis中存在（即是否过期），如过期则抛异常
     */
    @Override
    public void verify(Map<String, Object> claims) throws InvalidTokenException {
        log.info("dddddddddddddddddddddddddddddddddd:"+claims.size());
        if (claims.containsKey("jti")) {
            String jti = claims.get("jti").toString();
            log.info("####jti的值是否为key:::::"+jti);
            Object value = redisTemplate.opsForValue().get(SecurityConstants.PIG_PREFIX+"access:"+jti);
            log.info("令牌的值："+value);
            if (value == null) {
                throw new InvalidTokenException("无效的令牌");
            }
        }
    }
}
