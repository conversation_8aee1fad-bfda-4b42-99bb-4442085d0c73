/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.config;

import com.dxhy.core.constants.SecurityConstants;
import com.google.code.kaptcha.impl.DefaultKaptcha;
import com.google.code.kaptcha.util.Config;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2017-12-21 21:12:18
 */
@Configuration
public class KaptchaConfig {

    private static final String KAPTCHA_BORDER = "kaptcha.border";
    private static final String KAPTCHA_TEXTPRODUCER_FONT_COLOR = "kaptcha.textproducer.font.color";
    private static final String KAPTCHA_TEXTPRODUCER_CHAR_SPACE = "kaptcha.textproducer.char.space";
    private static final String KAPTCHA_IMAGE_WIDTH = "kaptcha.image.width";
    private static final String KAPTCHA_IMAGE_HEIGHT = "kaptcha.image.height";
    private static final String KAPTCHA_TEXTPRODUCER_CHAR_LENGTH = "kaptcha.textproducer.char.length";
    private static final Object KAPTCHA_IMAGE_FONT_SIZE = "kaptcha.textproducer.font.size";
    private static final String KAPTCHA_NOISE_COLOR = "kaptcha.noise.color";
    @Bean
    public DefaultKaptcha producer() {
        Properties properties = new Properties();
        properties.put(KAPTCHA_BORDER, SecurityConstants.DEFAULT_IMAGE_BORDER);
//        properties.put(KAPTCHA_TEXTPRODUCER_FONT_COLOR, SecurityConstants.DEFAULT_COLOR_FONT);
        properties.put(KAPTCHA_TEXTPRODUCER_FONT_COLOR, "42,100,246");
        //干扰颜色
        properties.put(KAPTCHA_NOISE_COLOR, "42,100,246");
        properties.put(KAPTCHA_TEXTPRODUCER_CHAR_SPACE, SecurityConstants.DEFAULT_CHAR_SPACE);
        properties.put(KAPTCHA_IMAGE_WIDTH, SecurityConstants.DEFAULT_IMAGE_WIDTH);
        properties.put(KAPTCHA_IMAGE_HEIGHT, SecurityConstants.DEFAULT_IMAGE_HEIGHT);
//        properties.put(KAPTCHA_IMAGE_WIDTH, "200");
//        properties.put(KAPTCHA_IMAGE_HEIGHT, "80");
        properties.put(KAPTCHA_IMAGE_FONT_SIZE, SecurityConstants.DEFAULT_IMAGE_FONT_SIZE);
//        properties.put(KAPTCHA_IMAGE_FONT_SIZE, "60");;
        properties.put(KAPTCHA_TEXTPRODUCER_CHAR_LENGTH, SecurityConstants.DEFAULT_IMAGE_LENGTH);
        properties.setProperty("kaptcha.background.clear.from", "233,244,254");
        // 背景颜色渐变， 结束颜色，默认值white
        properties.setProperty("kaptcha.background.clear.to", "233,244,254");
        // 水纹com.google.code.kaptcha.impl.WaterRipple
        // 鱼眼com.google.code.kaptcha.impl.FishEyeGimpy
        // 阴影com.google.code.kaptcha.impl.ShadowGimpy
        properties.setProperty("kaptcha.obscurificator.impl", "com.google.code.kaptcha.impl.ShadowGimpy");
        properties.setProperty("kaptcha.noise.impl","com.google.code.kaptcha.impl.NoNoise");
        properties.setProperty("kaptcha.textproducer.font.names","宋体");
        Config config = new Config(properties);
        DefaultKaptcha defaultKaptcha = new DefaultKaptcha();
        defaultKaptcha.setConfig(config);
        return defaultKaptcha;
    }
}


//    property	说明	取值范围	默认值
//    kaptcha.border	图片边框	yes , no	yes
//        kaptcha.border.color	边框颜色	合法值：r,g,b (and optional alpha) 或者 white,black,blue	black
//        kaptcha.border.thickness	边框厚度	>0	1
//        kaptcha.image.width	图片宽	>0	200
//        kaptcha.image.height	图片高	>0	50
//        kaptcha.producer.impl	图片实现类		com.google.code.kaptcha.impl.DefaultKaptcha
//        kaptcha.textproducer.impl	文本实现类		com.google.code.kaptcha.text.impl.DefaultTextCreator
//        kaptcha.textproducer.char.string	文本集合，验证码值从此集合中获取		abcde2345678gfynmnpwx
//        kaptcha.textproducer.char.length	验证码长度	>0	5
//        kaptcha.textproducer.font.names	字体	Arial, Courier
//        kaptcha.textproducer.font.size	字体大小 40px
//        kaptcha.textproducer.font.color	字体颜色，合法值： r,g,b 或者 white,black,blue. black
//        kaptcha.textproducer.char.space	文字间隔 2
//        kaptcha.noise.impl	干扰实现类 com.google.code.kaptcha.impl.DefaultNoise
//        kaptcha.noise.color	干扰颜色，合法值： r,g,b 或者 white,black,blue. black
//        kaptcha.obscurificator.impl	图片样式：水纹com.google.code.kaptcha.impl.WaterRipple鱼眼com.google.code.kaptcha.impl.FishEyeGimpy阴影com.google.code.kaptcha.impl.ShadowGimpy com.google.code.kaptcha.impl.WaterRipple
//        kaptcha.background.impl	背景实现类 com.google.code.kaptcha.impl.DefaultBackground
//        kaptcha.background.clear.from	背景颜色渐变，开始颜色 light grey
//        kaptcha.background.clear.to	背景颜色渐变，结束颜色 white
//        kaptcha.word.impl	文字渲染器 com.google.code.kaptcha.text.impl.DefaultWordRenderer
//        kaptcha.session.key	session键值		KAPTCHA_SESSION_KEY
//        kaptcha.session.date	session过期时间		KAPTCHA_SESSION_
