/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2017/10/28
 * social 登录基础配置
 */
@Component
@Data
@ConfigurationProperties(prefix = "sso.login")
public class SocialPropertiesConfig {
    /**
     * 提供商
     */
    private String providerId;
    /**
     * 应用ID
     */
    private String clientId;
    /**
     * 应用密钥
     */
    private String clientSecret;

    /**
     * 回调地址
     */
    private String redirectUri;

    /**
     * 产品权限校验开关 0 打开 1 关闭
     */
    private String permissions;

    /**
     * 渠道Id
     */
    private String sourceId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品版本
     */
    private String version;

    /**
     * 平台类型
     */
    private String platformType;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 产品logo
     */
    private String logo;

}
