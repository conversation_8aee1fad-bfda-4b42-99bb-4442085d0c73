/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.config;

import com.dxhy.core.handler.HeavenLoginFailureHandler;
import com.dxhy.core.handler.HeavenLoginSuccessHandler;
import com.dxhy.core.handler.HeavenLogoutHandler;
import com.dxhy.core.interceptor.TokenVerifyFilter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.config.BeanIds;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.builders.WebSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configuration.WebSecurityConfigurerAdapter;
import org.springframework.security.config.annotation.web.configurers.ExpressionUrlAuthorizationConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;

/**
 * <AUTHOR>
 * @date 2018/3/10
 */
//@Order(ManagementServerProperties.ACCESS_OVERRIDE_ORDER)
@Configuration
@EnableWebSecurity
public class HeavenSecurityConfigurerAdapter extends WebSecurityConfigurerAdapter {
    @Autowired
    private FilterIgnorePropertiesConfig filterIgnorePropertiesConfig;
//    @Resource
//    private MobileSecurityConfigurer mobileSecurityConfigurer;
    /**
     *  服务域名，默认空，http域名时可以不配置：解决https域名时默认跳转http问题
     */
    @Value("${server.domain:}")
    private String serverDomain;


    @Override
    public void configure(HttpSecurity http) throws Exception {

        ExpressionUrlAuthorizationConfigurer<HttpSecurity>.ExpressionInterceptUrlRegistry registry =
                http.formLogin().successHandler(new HeavenLoginSuccessHandler())
                        .failureHandler(new HeavenLoginFailureHandler())
                        .loginPage(serverDomain + "/uauthentication/require")
                        .loginPage("/uauthentication/portal/login")
//                        .loginProcessingUrl("/uauthentication/form")
                        .and()
                        .authorizeRequests()
                        // 放行这个路径
                        .antMatchers(
                                "/uauthentication/**",
                                "/syncapi/**",
                                "/user/**",
                                "/websocket",
                                "/code/**",
                                "/v2/api-docs",
                                "/swagger-resources/configuration/ui",
                                "/swagger-resources",
                                "/swagger-resources/configuration/security",
                                "/swagger-ui.html",
                                "/css/**",
                                "/js/**",
                                "/images/**",
                                "/csrf",
                                "/webjars/**",
                                "/import/test",
                                "**/favicon.ico",
                                "/index"
                        ).permitAll();
//                        // 直接放行
//                        .antMatchers("/uauthentication/portal/login",
//                                "/user/queryUserInfo",
//                                "/uauthentication/logout",
//                                "/error/**",
//                                "/dev/**").permitAll();
//                        // 权限认证
//                        .anyRequest().authenticated();
                http.logout().logoutUrl("/uauthentication/form/logout").addLogoutHandler(new HeavenLogoutHandler());
        filterIgnorePropertiesConfig.getUrls().forEach(url -> registry.antMatchers(url).permitAll());
        registry.anyRequest().authenticated()
                .and()
                .csrf().disable();
//        http.apply(mobileSecurityConfigurer);

        http.headers().frameOptions().disable(); // 允许前端VUE被frame加载
        http
            .addFilterAfter(new TokenVerifyFilter(super.authenticationManager()), UsernamePasswordAuthenticationFilter.class)
            .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS);

//        http.addFilter(new TokenVerifyFilter(super.authenticationManager()));
//        http.addFilter(new TokenVerifyFilter(super.authenticationManager()), UsernamePasswordAuthenticationFilter.class);
    }

    @Bean(name = BeanIds.AUTHENTICATION_MANAGER)
    @Override
    public AuthenticationManager authenticationManagerBean() throws Exception {
        return super.authenticationManagerBean();
    }

    //忽略拦截
    @Override
    public void configure(WebSecurity webSecurity){
        webSecurity.ignoring().antMatchers(
                "/websocket/**"
        );
        webSecurity.ignoring().antMatchers("/uauthentication/**");
        webSecurity.ignoring().antMatchers(
                "/code/**",
                "/user/encryptParam/**",
                "/user/syncSaasData/**",
                "/user/password/forget/**",
                "/user/vaildCode/**",
                "/user/getAuthNameByTaxno/**",
                "/itax/**",
                "/test/getSgin/**",
                "/common/getRegisterUrl/**",
                "/dept/queryDeptByNameAndCode/**",
                "/produceAPI/**",
                "/swagger-resources/**",
                "/swagger-ui.html",
                "/webjars/**",
                "/v2/api-docs",
                "/swagger/api-docs",
                "/tenant/**",
                "/log/**",
                "/syncapi/**",
                "/uauthentication/portal/hwLogin",
                "/channelManagement/getChannelName"
        );

    }
}
