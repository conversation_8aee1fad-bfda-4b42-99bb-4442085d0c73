package com.dxhy.core.thread;

import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.handler.WebSocketHandler;
import com.dxhy.core.service.OhtherSsoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.socket.WebSocketSession;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class GetTokenTask {
    public static  GetTokenTask getTokenTask;
    @Value("${refresh.token.time}")
    private String tokenTime;
    @Resource
    private RedisTemplate redisTemplate;
    @Resource
    private OhtherSsoService ohtherSsoService;
    public WebSocketHandler webSocketHandler() {
        return new WebSocketHandler();
    }

    @PostConstruct
    public void init(){
        getTokenTask = this;
        log.info("## 获取到的cron配置："+tokenTime);
    }

    @Scheduled(cron= "${refresh.token.time}")
    public void handerGetToken() {
        log.info("###  GetTokenTask Start ...");
        try {
            //获取WebSocketServer对象的映射。
            Map<String, WebSocketSession> map = WebSocketHandler.getWebSocketMap();
            log.info("WebSocket当前连接数："+map.size());
            if (map.size() != 0){
                for (Map.Entry<String, WebSocketSession> entry : map.entrySet()) {
                    String userconnect = entry.getKey();
                    String userId=userconnect.split("\\|")[0].split("-")[0];
                    String sso_session_id = "";
                    List<String> cookies = null;
                    boolean flag = false;
                    /**标普etax演示环境 销项、进项、纳税申报系统 （新全电系统使用票税token）*/
//                    Object result = redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY + userId);
                    Object result = redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY + "#etaxpre");
//                    JSONObject json = JSONObject.parseObject(result);
                    JSONObject json = (JSONObject) result;
                    if (json == null) {
                        //redis中存储的数据已失效 重新获取并存储到redis中
                        sso_session_id = ohtherSsoService.getJxSsoId(userId);
                        if (sso_session_id == null) {
                            continue;
                        }
                        log.info("当前账号连接:"+userconnect+",redis中cookie已失效，主动向前端发送新sso_session_id：{}",sso_session_id);
                        flag = true;
                    }else {
                        cookies = (List<String>) json.get("Set-Cookie");
                        log.info("当前账号连接:"+userconnect+",目前的cookie是：{}",cookies);
                        sso_session_id  = json.get("body").toString();
                        //验证cookie是否有效 无效获取新sessionId
                        flag = ohtherSsoService.verifyJxSession(cookies);
                        if (flag) {
                            //cookie过期 重新获取并存储到redis中
                            //先清除redis
                            redisTemplate.delete(SystemConstants.SSO_COOKIE_CODE_KEY + "#etaxpre");
                            sso_session_id = ohtherSsoService.getJxSsoId(userId);
                            log.info("当前账号连接:"+userconnect+",cookie已过期，主动向前端发送新sso_session_id:{}",sso_session_id);
                        }
                    }

                    /**大象demo演示环境 报销、风控系统*/
                    Boolean ssoidflag = false;
                    String ssoid = "";
//                    String ssoid = (String) redisTemplate.opsForValue().get(SystemConstants.SSO_COOKIE_CODE_KEY + "#demoele");
//                    if (StringUtils.isBlank(ssoid)) {
//                        //redis 中cookie已不存在 那么重新获取
//                        ssoid = ohtherSsoService.getDbSsoId(userId);
//                        ssoidflag = true;
//                    }
                    if (flag || ssoidflag) {
                        WebSocketSession session = entry.getValue();
                        String token = "";
                        webSocketHandler().sendMessage(session,token,sso_session_id,ssoid);
                    }
                }

//            }else {
//                log.info("WebSocket未连接");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    @Scheduled(cron= "0 */1 * * * *")
//    @Scheduled(cron= "5/5 * * ? * ?")
    //1分钟后 每隔60分钟开始执行
//    @Scheduled(cron= "0 0/119 * * * ?")
    //从第1分钟后开始 每2小时执行一次 1(降级分)/2（时）
//    @Scheduled(cron= "0 0 1/2 * * ?")
//    public void timingGetDemoSsoid() {
//        log.info("###  timingGetDemoSsoid Start ...");
//        try {
//            //获取WebSocketServer对象的映射。
//            Map<String, WebSocketSession> map = WebSocketHandler.getWebSocketMap();
//            if (map.size() != 0){
//                for (Map.Entry<String, WebSocketSession> entry : map.entrySet()) {
//                    String userconnect = entry.getKey();
//                    String userId=userconnect.split("\\|")[0].split("-")[0];
//                    String ssoid = ohtherSsoService.getDbSsoId(userId);
//                    if (StringUtils.isNotBlank(ssoid)) {
//                        WebSocketSession session = entry.getValue();
//                        webSocketHandler().sendMessage(session,"","",ssoid);
//                    }
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }

    /**
     * 暂时不用 20220728
     * @param userconnect
     * @param userId
     * @param sso_session_id
     * @param cookies
     * @return
     */
    public String etaxyanshi(String userconnect,String userId,String sso_session_id,List<String> cookies){
      return null;
    }

    public static void main(String[] args) {
//        OhtherSsoService ohtherSsoService = new OtherSsoServiceImpl();
//        ohtherSsoService.getDbSsoId("100001");

        RestTemplate restTemplate = new RestTemplate();
        String getSessionUrl = "https://demo.ele-cloud.com/itax/doLogin";
        JSONObject param = new JSONObject();
        param.put("dxhyu","VUW64oCMWB2bGKYHkuMEPg==");
        param.put("dxhyp","rzYhPucdyNKWbD8aFTpypw==");
        param.put("verifyCode","");

        ResponseEntity<JSONObject> entity = restTemplate.postForEntity(getSessionUrl,param,JSONObject.class);
        List<String> cookies = entity.getHeaders().get("Set-Cookie");
        log.info("demo环境的cookie存储格式是：{}",cookies);
        String sso_session_id  = cookies.get(1);
        log.info(sso_session_id);
        sso_session_id=cookies.get(1).replace("dxhy_sso_sessionid=","").replace("; Path=/; HttpOnly","");
        log.info("进项获取到风控系统最新ssoid：{},cookie:{}",sso_session_id,cookies);
    }
}

