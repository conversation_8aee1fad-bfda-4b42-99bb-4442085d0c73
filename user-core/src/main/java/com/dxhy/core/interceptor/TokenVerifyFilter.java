package com.dxhy.core.interceptor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.handler.TokenHandleUtil;
import com.dxhy.core.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;

import javax.annotation.PostConstruct;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

/**
 * 校验Token是否合法的Filter
 */
@Slf4j
public class TokenVerifyFilter  extends BasicAuthenticationFilter {
    @Autowired
    private TokenStore tokenStore;
    @Autowired
    private SysUserMapper sysUserMapper;
    private static TokenVerifyFilter tokenVerifyFilter;

    public TokenVerifyFilter(AuthenticationManager authenticationManager) {
        super(authenticationManager);
    }
    @PostConstruct
    private void init () {
        tokenVerifyFilter = this;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain chain) throws IOException, ServletException {
        String header = request.getHeader("Authorization");
        Map resultMap = new HashMap();
        if (header == null || !header.startsWith("Bearer")) {

//            response.setContentType("application/json;charset=utf-8");
//            response.setStatus(HttpServletResponse.SC_FORBIDDEN);
            printErrMsg(new CommonRspVo(ResponseCodeEnum.TOKEN_NULL),response);
        } else {
            //如果携带了正确格式的token要先得到token
//            String token = header.replace("Bearer", "");
            String str [] =header.split(" ");
            String token = str[1];
            log.info("token过滤器校验：send {} to {} token {}",request.getMethod(),request.getRequestURL().toString(),token);

            if(null == token){
                log.error("token is null ...");
                printErrMsg(new CommonRspVo(ResponseCodeEnum.TOKEN_NULL),response);
            }else{
                //校验是否存在redis中
                TokenHandleUtil tokenHandleUtil = new TokenHandleUtil();
                //验证key  token
                String[] strarry = token.split("\\.");
                if (strarry.length<3) {
                    printErrMsg(new CommonRspVo(ResponseCodeEnum.TOKEN_FORMAT_ERROR),response);
                }

//                Boolean flag  = tokenHandleUtil.verifyToken(token,userId);
                CommonRspVo commonRspVo = tokenHandleUtil.verifyToken(token);
                if (!commonRspVo.getCode().equals("0000")) {
                    printErrMsg(commonRspVo,response);
                }else{
                    JSONObject jsonObject =JSONObject.parseObject(com.xiaoleilu.hutool.codec.Base64.decodeStr(token.split("\\.")[1]));
                    Long userId = jsonObject.getLong("userId");
                    //校验redis中token是否有效
                    log.info("access token is ok!!!");
                    UsernamePasswordAuthenticationToken authResult =
                            new UsernamePasswordAuthenticationToken(userId, null, null);
                    SecurityContextHolder.getContext().setAuthentication(authResult);
                    chain.doFilter(request, response);
                }
            }
        }

    }

    public void printErrMsg(CommonRspVo resultMap,HttpServletResponse response) throws IOException {
        response.setCharacterEncoding("utf-8");
        response.setContentType("application/json;charset=utf-8");
        response.setHeader("Cache-Control", "no-cache");
        PrintWriter out = response.getWriter();
        out.write(JSON.toJSONString(resultMap));
        out.flush();
        out.close();
    }

}
