package com.dxhy.core.interceptor;

import com.dxhy.core.constants.SecurityConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.common.exceptions.InvalidTokenException;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
/**
 * 添加拦截器,拦截请求,校验token
 */
@Slf4j
public class JWTInterceptor implements HandlerInterceptor {
    @Autowired
    private RedisTemplate redisTemplate;
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        HashMap<String, Object> map = new HashMap<>();
        String token = request.getHeader("token");  //从request中获取到请求头中的token,进行解析校验
        log.info("####token:::::"+token);
        if (token!=null) {
            Object value = redisTemplate.opsForValue().get(SecurityConstants.PIG_PREFIX+"access:"+token);
            log.info("令牌的值："+value);
            if (value == null) {
                throw new InvalidTokenException("无效的令牌");
            }
        }
        return false;  //异常不放行
    }
}
