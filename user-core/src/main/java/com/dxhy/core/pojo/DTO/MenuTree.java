/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysMenu;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2017年11月9日23:33:27
 */
@Data
public class MenuTree extends TreeNode {
    private String menuId;
    private String parentId;
    private String icon;
    private String name;
    private String url;
    private boolean spread = false;
    private String path;
    private String component;
    private String authority;
    private String redirect;
    private String code;
    private String type;
    private String label;
    private Integer sort;
    private String method;


    public MenuTree() {
    }

    public MenuTree(String menuId, String name, String parentId) {
        this.menuId = menuId;
        this.parentId = parentId;
        this.name = name;
        this.label = name;
    }

    public MenuTree(String menuId, String name, MenuTree parent) {
        this.menuId = menuId;
        this.parentId = parent.getMenuId();
        this.name = name;
        this.label = name;
    }

    public MenuTree(SysMenu sysMenu) {
        this.menuId = sysMenu.getMenuId();
        this.parentId = sysMenu.getParentId();
        this.icon = sysMenu.getIcon();
        this.name = sysMenu.getName();
        this.url = sysMenu.getUrl();
        this.path = sysMenu.getPath();
        this.type = sysMenu.getType();
        this.label = sysMenu.getName();
        this.sort = sysMenu.getSort();
        this.method = sysMenu.getMethod();
    }
}
