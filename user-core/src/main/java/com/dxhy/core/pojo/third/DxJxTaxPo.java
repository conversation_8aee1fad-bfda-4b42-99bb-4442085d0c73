package com.dxhy.core.pojo.third;

import com.dxhy.core.pojo.entity.SysDept;
import lombok.Data;

@Data
public class DxJxTaxPo {
    /*
        *企业税号
    */
    private String taxno;
    /*
    *企业名称
    */
    private String taxName;
    /*
    *注册地址
    */
    private String address;
    /*
    *注册电话
    */
    private String phone;
    /*
    *开户银行
    */
    private String bank;
    /*
    *银行账号
    */
    private String account;
    /*
    *集团编码
    */
    private String company;
    /*
    *集团名称
    */
    private String companyName;
    /*
    *采集鉴权ID
    */
    private String aceId;
    /*
    *采集鉴权KEY
    */
    private String aceKey;

    public DxJxTaxPo(SysDept sysDept,String aceId,String aceKey){
        this.taxno=sysDept.getTaxpayerCode();
        this.taxName=sysDept.getName();
        this.address=sysDept.getTaxpayerAddress().equals("无")?"":sysDept.getTaxpayerAddress();
        this.phone=sysDept.getTaxpayerPhone();
        this.bank=sysDept.getTaxpayerBank().equals("无")?"":sysDept.getTaxpayerBank();
        this.account=sysDept.getTaxpayerAccount().equals("无")?"":sysDept.getTaxpayerAccount();
        this.company=sysDept.getSourceId();
        this.companyName=sysDept.getDeptSname();
        this.aceId=aceId;
        this.aceKey=aceKey;
    }


}
