package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品续费表（目前没用）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 10:58:16
 */
@TableName("hw_renew_product_info")
public class HwRenewProductInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	private String instanceId;
	/**
	 * 客户手机号
	 */
	private String orderId;
	/**
	 * 产品规格标识
	 */
	private String productId;
	/**
	 * 过期时间。格式：yyyyMMddHHmmss
	 */
	private String expireTime;
	/**
	 * 是否是试用转正请求。 1：是（试用转正请求） 不传参数代表不是 
	 */
	private String trialToFormal;
	/**
	 * 周期类型
	 */
	private String periodType;
	/**
	 * 周期数量
	 */
	private String periodNumber;
	/**
	 * 订单金额。
	 */
	private String orderAmount;
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 设置：
	 */
	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 获取：
	 */
	public Long getId() {
		return id;
	}
	/**
	 * 设置：实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	public void setInstanceId(String instanceId) {
		this.instanceId = instanceId;
	}
	/**
	 * 获取：实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	public String getInstanceId() {
		return instanceId;
	}
	/**
	 * 设置：客户手机号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	/**
	 * 获取：客户手机号
	 */
	public String getOrderId() {
		return orderId;
	}
	/**
	 * 设置：产品规格标识
	 */
	public void setProductId(String productId) {
		this.productId = productId;
	}
	/**
	 * 获取：产品规格标识
	 */
	public String getProductId() {
		return productId;
	}
	/**
	 * 设置：过期时间。格式：yyyyMMddHHmmss
	 */
	public void setExpireTime(String expireTime) {
		this.expireTime = expireTime;
	}
	/**
	 * 获取：过期时间。格式：yyyyMMddHHmmss
	 */
	public String getExpireTime() {
		return expireTime;
	}
	/**
	 * 设置：是否是试用转正请求。 1：是（试用转正请求） 不传参数代表不是 
	 */
	public void setTrialToFormal(String trialToFormal) {
		this.trialToFormal = trialToFormal;
	}
	/**
	 * 获取：是否是试用转正请求。 1：是（试用转正请求） 不传参数代表不是 
	 */
	public String getTrialToFormal() {
		return trialToFormal;
	}
	/**
	 * 设置：周期类型
	 */
	public void setPeriodType(String periodType) {
		this.periodType = periodType;
	}
	/**
	 * 获取：周期类型
	 */
	public String getPeriodType() {
		return periodType;
	}
	/**
	 * 设置：周期数量
	 */
	public void setPeriodNumber(String periodNumber) {
		this.periodNumber = periodNumber;
	}
	/**
	 * 获取：周期数量
	 */
	public String getPeriodNumber() {
		return periodNumber;
	}
	/**
	 * 设置：订单金额。
	 */
	public void setOrderAmount(String orderAmount) {
		this.orderAmount = orderAmount;
	}
	/**
	 * 获取：订单金额。
	 */
	public String getOrderAmount() {
		return orderAmount;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
}
