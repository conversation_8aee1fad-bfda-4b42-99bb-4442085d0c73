package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_menu_sell")
public class ProductMenuSell extends Model<ProductMenuSell> {

    private static final long serialVersionUID = 1L;

    @TableField("sell_label_id")
    private String sellLabelId;
    /**
     * 产品菜单id
     */
    @TableField("product_menu_id")
    private String productMenuId;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
