package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 分销商数据表（目前没用，使用sys_dept机构表）
 * @author: zhangjinjing
 * @Date: 2022/4/25 16:48
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = false)
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class Distributor extends Model<Distributor> implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.INPUT)
    private String id;
    @TableField("company_name")
    private String companyName;
    private String location;
    private String region;
    @TableField("is_distributor")
    private Integer isDistributor;
    @TableField("contact_phone")
    private String contactPhone;
    @TableField("tax_no")
    private String taxNo;
    @TableField("bank_name")
    private String bankName;
    @TableField("bank_no")
    private String bankNo;
    @TableField("contact_email")
    private String contactEmail;
    private String superior;
    @TableField(
            exist = false
    )
    private String superiorName;
    private String level;
    @TableField("del_flag")
    private Integer delFlag;
    @TableField("create_time")
    private Date createTime;
    @TableField("modify_time")
    private Date modifyTime;
    @TableField(
            exist = false
    )
    private List<Distributor> sub;
    @TableField(
            exist = false
    )
    private Integer count;
    @TableField(
            exist = false
    )
    private List sysRoles;
    @TableField(
            exist = false
    )
    private String password;
    @TableField("simple_code")
    private String simpleCode;
    @TableField("type")
    private String type;
    @TableField("taxpayer_type")
    private String taxpayerType;
    @TableField("accountant_standard")
    private String accountantStandard;
    @TableField("control_mode")
    private String controlMode;
    @TableField("trade")
    private String trade;
    @TableField("trial_days")
    private String trialDays;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
