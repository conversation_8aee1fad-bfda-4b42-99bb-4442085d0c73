/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/2 10:44
 */
@Getter
@Setter
@Accessors(chain = true)
public class StatisticsRzVo extends BaseVo {


	/**
	 * 租户ID
	 */
	private String tenantId;

	/**
	 * 租户IDS
	 */
	private List<String> tenantIdList;

	/**
	 * 产品类型（1、归集2、认证、3、查验4、开票）
	 */
	private String serviceType;

	/**
	 * 开始时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM")
	@JsonFormat(pattern = "yyyy-MM")
	private Date startTime;

	/**
	 * 结束时间
	 */
	@DateTimeFormat(pattern = "yyyy-MM")
	@JsonFormat(pattern = "yyyy-MM")
	private Date endTime;

}