package com.dxhy.core.pojo.third;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class DxRegisterPara {
    @JSONField(name="YHID")
    private String YHID;
    @J<PERSON><PERSON><PERSON>(name="QYID")
    private String QYID;
    @J<PERSON><PERSON><PERSON>(name="QDID")
    private String QDID;
    @J<PERSON><PERSON><PERSON>(name="XHFMC")
    private String XHFMC;
    @J<PERSON><PERSON>ield(name="XHFSBH")
    private String XHFSBH;
    @JSONField(name="XHFZH")
    private String XHFZH;
    @J<PERSON>NField(name="XHFYHZH")
    private String XHFYHZH;
    @J<PERSON><PERSON>ield(name="XHFDZ")
    private String XHFDZ;
    @J<PERSON><PERSON>ield(name="XHFDH")
    private String XHFDH;
    @J<PERSON><PERSON>ield(name="LXRXM")
    private String LXRXM;
    @JSONField(name="LXRDH")
    private String LXRDH;
    @J<PERSON><PERSON>ield(name="LXRYX")
    private String LXRYX;
    @J<PERSON><PERSON><PERSON>(name="SKSBDM")
    private String SKSBDM;
    @<PERSON><PERSON><PERSON><PERSON>(name="GXBS")
    private String GXBS;
}
