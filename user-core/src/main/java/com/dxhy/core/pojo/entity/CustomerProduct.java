package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("customer_product")
public class CustomerProduct extends Model<CustomerProduct> {

    private static final long serialVersionUID = 1L;

    private String id;

    /**
     * 账号id
     */
    private String accountId;
    /**
     * 客户id
     */
    @TableField("customer_id")
    private String customerId;
    /**
     * 产品id
     */
    @TableField("product_id")
    private String productId;
    /**
     * 客户类型：1：企业客户，2：个人客户
     */
    @TableField("customer_type")
    private Integer customerType;
    /**
     * 开通时间
     */
    @TableField("begin_time")
    private Date beginTime;
    /**
     * 使用量
     */
    private Integer used;
    /**
     * 开通量
     */
    private String total;
    /**
     * 产品状态，1：正式，2：试用
     */
    @TableField("product_type")
    private Integer productType;
    /**
     * 开通状态，1：正常，2：预警，3：已停用
     */
    private Integer status;
    /**
     * 开通维度，1：时间，2：数量
     */
    private Integer unit;
    /**
     * 删除标志，1：已删除，0：未删除
     */
    @TableField("del_flag")
    private Integer delFlag;
    @TableField("create_time")
    private Date createTime;
    @TableField("modify_time")
    private Date modifyTime;

    @TableField("account_info_id")
    private String accountInfoId;

    @TableField(exist =  false)
    private String yearNum;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
