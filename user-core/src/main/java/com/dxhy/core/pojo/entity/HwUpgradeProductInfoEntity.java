package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品升级表（目前没用）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 10:58:16
 */
@TableName("hw_upgrade_product_info")
public class HwUpgradeProductInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	private String instanceId;
	/**
	 * 客户手机号
	 */
	private String orderId;
	/**
	 * 产品规格标识
	 */
	private String skuCode;
	/**
	 * 产品规格标识
	 */
	private String productId;
	/**
	 * 过期时间。格式：yyyyMMddHHmmss
	 */
	private String amount;
	/**
	 * 数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	 */
	private String diskSize;
	/**
	 * 数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps
	 */
	private String bandWidth;
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 设置：
	 */
	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 获取：
	 */
	public Long getId() {
		return id;
	}
	/**
	 * 设置：实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	public void setInstanceId(String instanceId) {
		this.instanceId = instanceId;
	}
	/**
	 * 获取：实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	public String getInstanceId() {
		return instanceId;
	}
	/**
	 * 设置：客户手机号
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	/**
	 * 获取：客户手机号
	 */
	public String getOrderId() {
		return orderId;
	}
	/**
	 * 设置：产品规格标识
	 */
	public void setSkuCode(String skuCode) {
		this.skuCode = skuCode;
	}
	/**
	 * 获取：产品规格标识
	 */
	public String getSkuCode() {
		return skuCode;
	}
	/**
	 * 设置：产品规格标识
	 */
	public void setProductId(String productId) {
		this.productId = productId;
	}
	/**
	 * 获取：产品规格标识
	 */
	public String getProductId() {
		return productId;
	}
	/**
	 * 设置：过期时间。格式：yyyyMMddHHmmss
	 */
	public void setAmount(String amount) {
		this.amount = amount;
	}
	/**
	 * 获取：过期时间。格式：yyyyMMddHHmmss
	 */
	public String getAmount() {
		return amount;
	}
	/**
	 * 设置：数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	 */
	public void setDiskSize(String diskSize) {
		this.diskSize = diskSize;
	}
	/**
	 * 获取：数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	 */
	public String getDiskSize() {
		return diskSize;
	}
	/**
	 * 设置：数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps
	 */
	public void setBandWidth(String bandWidth) {
		this.bandWidth = bandWidth;
	}
	/**
	 * 获取：数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps
	 */
	public String getBandWidth() {
		return bandWidth;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
}
