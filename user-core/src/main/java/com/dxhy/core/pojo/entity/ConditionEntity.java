package com.dxhy.core.pojo.entity;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 14:162019/3/6 0006
 * @description 查询参数实体，封装 eq，lt，gt，bw，like，等查询参数
 */
@Data
@ToString
public class ConditionEntity<T> {
    private QueryTypeEnum type;
    private T value;
    private List<T> values;
    private String column;

    /**
     * <AUTHOR>
     * @date 14:472019/3/6 0006
     * @description 查询条件匹配类型枚举
     */
    public enum QueryTypeEnum {
        /**
         * 相等
         */
        eq,
        /**
         * 小于
         */
        lt,
        /**
         * 大于
         */
        gt,
        /**
         * <=
         */
        le,
        /**
         * >=
         */
        ge,
        /**
         * between
         */
        between,
        /**
         * not between
         */
        notbetween,
        /**
         * 模糊匹配 (%,value)
         */
        llike,
        /**
         * 模糊匹配 (value,%)
         */
        rlike,
        /**
         * 模糊匹配 (%,value,%)
         */
        dlike,
        /**
         * in
         */
        in,
        notin,
        notlike,
        exist,
        notexist,
        notnull,
        /**
         * 不等于
         */
        ne;
    }

}
