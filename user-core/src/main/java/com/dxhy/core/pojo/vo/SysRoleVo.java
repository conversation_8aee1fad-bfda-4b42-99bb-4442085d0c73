package com.dxhy.core.pojo.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 角色列表
 * @ClassName:SysRoleVo.java
 * <AUTHOR>
 * @date 2019年3月12日下午2:22:09
 */
@Data
public class SysRoleVo extends SysRole implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = -6922980768533875510L;
    /**
	 * 创建时间
	 */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     *所属部门id
     */
    private String deptId;

	/**
	 *所属部门名称
	 */
	private String deptName;

	/**
	 *创建人id
	 */
	private Long createBy;

	/**
	 *更新人id
	 */
	private Long updateBy;

	/**
	 *角色类型 0：私有,1:共享
	 */
	private Integer type;

   
}
