package com.dxhy.core.pojo.DTO;

import lombok.Data;

@Data
public class TopOrg {
    /**
     * 顶级机构名称，唯一
     */
    private String name;
    /**
     * 顶级机构税号
     */
    private String taxpayerCode;
    /**
     * 顶级机构地址
     */
    private String taxpayerAddress;
    /**
     * 顶级机构注册电话
     */
    private String taxpayerPhone;
    /**
     * 顶级机构开户行
     */
    private String taxpayerBank;
    /**
     * 顶级机构银行账号
     */
    private String taxpayerAccount;
    /**
     * 省
     */
    private String taxpayerProvince;
    /**
     * 市
     */
    private String taxpayerCity;
    /**
     * 国家
     */
    private String taxpayerCounty;
    /**
     * 纳税人类型，0:小规模纳税人 1:一般纳税人
     */
    private String taxpayerType;
    /**
     * 所属行业
     */
    private String taxpayerIndustry;
    /**
     * 会计准则，1：企业会计准则-一般企业 2：小企业会计准则
     */
    private String accountingPrincipleCode;
    /**
     * 联系人邮箱
     */
    private String contactEmail;
    /**
     * 联系人姓名
     */
    private String contactName;
    /**
     * 联系人电话
     */
    private String contactPhone;
    /**
     * 税控类型，只有销项需要传
     */
    private String sksbbm;

    /**
     * 采集鉴权Id，只有进项需要传
     */
    private String aceId;
    /**
     * 采集鉴权Key，只有进项需要传
     */
    private String aceKey;
    /**
     * 全电-电子税务局账号
     */
    private String taxBureaName;
    /**
     * 全电-电子税务局密码
     */
    private String taxBureaPass;

}
