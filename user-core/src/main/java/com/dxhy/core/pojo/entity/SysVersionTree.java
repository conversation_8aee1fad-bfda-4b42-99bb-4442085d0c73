package com.dxhy.core.pojo.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class SysVersionTree  implements Serializable  {
    /**
     * id
     */
    private Integer id;

    /**
     * 父级Id，根节点为-1
     */
    private Integer parentId;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 文本数字前缀
     */
    private String releaseDate;

    /**
     * 文本说明
     */
    private String prefix;

    /**
     * 文本说明
     */
    private String text;

    /**
     * 数字前缀与文本说明分隔符 默认"."
     */
    private String connector;

    /**
     * create_time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * update_time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    private List<SysVersionTree> children = new ArrayList();

    public SysVersionTree(SystemVersion systemVersion) {
        this.id = systemVersion.getId();
        this.parentId = systemVersion.getParentId();
        this.versionNum = systemVersion.getVersionNum();
        this.releaseDate = systemVersion.getReleaseDate();
        this.prefix = systemVersion.getPrefix();
        this.text = systemVersion.getText();
        this.connector = systemVersion.getConnector();
        this.createTime = systemVersion.getCreateTime();
        this.updateTime = systemVersion.getUpdateTime();
    }

    public void add(SysVersionTree sysVersionTree) {
        children.add(sysVersionTree);
    }

}
