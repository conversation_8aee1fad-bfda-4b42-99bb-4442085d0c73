package com.dxhy.core.pojo.entity;


import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class SyncFailLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private int id;

    /**
     * dept_id
     */
    private String deptId;

    /**
     * tax_no
     */
    private String taxNo;

    /**
     * 错误描述
     */
    private String content;

    private Date createTime;
    private Date updateTime;

    private Integer flag;

    private String description;
}
