package com.dxhy.core.pojo.DTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 通道管理列表查询DTO
 * <AUTHOR>
 * @Date 2025/01/10
 * @Version 1.0
 **/
@Data
@ApiModel("通道管理列表查询DTO")
public class ChannelManagementListDTO {

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty(value = "所属纳税人识别号")
    private String baseNsrsbh;

    /**
     * 通道类别
     */
    @ApiModelProperty("通道类别")
    private String channelType;

    /**
     * 通道名称
     */
    @ApiModelProperty("通道名称")
    private String channelName;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 业务类别
     */
    @ApiModelProperty("业务类别")
    private String businessType;

    /**
     * 状态 0-正常 1-禁用
     */
    @ApiModelProperty("状态 0-正常 1-禁用")
    private String status;

    /**
     * 当前页码
     */
    @ApiModelProperty("当前页码")
    private Integer pageNo = 1;

    /**
     * 每页条数
     */
    @ApiModelProperty("每页条数")
    private Integer size = 10;
} 