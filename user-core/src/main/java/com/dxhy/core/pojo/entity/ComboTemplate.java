package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("combo_template")
public class ComboTemplate extends Model<ComboTemplate> {

    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 套餐模板名称
     */
    private String name;
    /**
     * 有效时间 0  年 1 不限制
     */
    @TableField("valid_time")
    private String validTime;
    /**
     * 描述
     */
    private String describe;
    /**
     * 状态 0 正常 1 删除  2 禁用
     */
    private String status;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    //计费项明细
    @TableField(exist = false)
    private List<ChargeItem> chargeItems;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productName;

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productClass;

    /**
     * 模板编码
     */
    @TableField("combo_num")
    private String comboNum;
    /**
     * 分销商ID
     */
    @TableField("distributor_id")
    private String distributorId;
    /**
     * 产品ID
     */
    @TableField("product_id")
    private String productId;
    /**
     * 产品售卖ID
     */
    @TableField("sell_label_id")
    private String sellLabelId;

    /**
     * 售卖类型
     */
    @TableField(exist = false)
    private String sellLabelName;


    @Override
    protected Serializable pkVal() {
        return null;
    }

}
