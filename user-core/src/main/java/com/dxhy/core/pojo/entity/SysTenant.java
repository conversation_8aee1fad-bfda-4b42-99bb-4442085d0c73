package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableLogic;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 租户信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29 10:42
 */
@Setter
@Getter
@Accessors(chain = true)
@TableName("sys_tenant")
public class SysTenant extends Model<SysTenant> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ID_WORKER)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @TableField("tenant_id")
    private String tenantId;

    /**
     * 上级id
     */
    @TableField("parent_id")
    private String parentId;

    /**
     * 层级
     */
    @TableField("level")
    private String level;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 域名地址
     */
    @TableField("domain")
    private String domain;

    /**
     * 联系人
     */
    @TableField("linkman")
    private String linkman;

    /**
     * 联系电话
     */
    @TableField("contact_number")
    private String contactNumber;

    /**
     * 联系地址
     */
    @TableField("address")
    private String address;

    /**
     * 秘钥
     */
    @TableField("secret_id")
    private String secretId;

    /**
     * 秘钥
     */
    @TableField("secret_key")
    private String secretKey;

    /**
     * 创建人
     */
    @TableField("create_by")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 修改人
     */
    @TableField("update_by")
    private String updateBy;


    /**
     * 修改时间
     */
    @TableField("update_time")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 是否删除
     */
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @TableField(exist = false)
    private List<SysTenantProduct> tenantProducts;
    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    /**
     * 纳税人识别号
     */
    private String nsrsbh;
}
