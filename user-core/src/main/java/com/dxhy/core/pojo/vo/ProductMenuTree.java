package com.dxhy.core.pojo.vo;

import com.dxhy.core.pojo.entity.ProductMenu;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * sys_permission
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Data
public class ProductMenuTree implements Serializable{

    protected String id;
    protected String parentId;
    private String name;
    private String permission;
    private String path;
    private String url;
    private String icon;
    private String method;
    private String systemSign;
    private String productId;
    private String productName;
    private Integer sort;
    private String type;
    private String menuId;
    private List<ProductMenuTree> children = new ArrayList<ProductMenuTree>();
    public ProductMenuTree() {
    }

    public ProductMenuTree(String id, String name, String parentId) {
        this.id = id;
        this.parentId = parentId;
        this.name = name;

    }

    public ProductMenuTree(String id, String name, ProductMenuTree parent) {
        this.id = id;
        this.parentId = parent.getId();
        this.name = name;
    }

    public ProductMenuTree(ProductMenu productMenu) {
        this.id = productMenu.getId();
        this.parentId = productMenu.getParentId();
        this.icon = productMenu.getIcon();
        this.name = productMenu.getName();
        this.url = productMenu.getUrl();
        this.path = productMenu.getPath();
        this.type = productMenu.getType();
        this.sort = productMenu.getSort();
        this.permission = productMenu.getPermission();
        this.method = productMenu.getMethod();
        this.systemSign = productMenu.getSystemSign();
        this.productId = productMenu.getProductId();
        this.productName = productMenu.getProductName();
        this.menuId = productMenu.getId();
    }

}
