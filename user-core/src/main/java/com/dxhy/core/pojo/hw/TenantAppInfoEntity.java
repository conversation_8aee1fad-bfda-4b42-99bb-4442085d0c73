package com.dxhy.core.pojo.hw;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 租户应用信息同步接口（目前没用）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-22 15:24:24
 */
@Data
@TableName("tenant_app_info")
public class TenantAppInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 实例id
	 */
	private String instanceId;
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 应用的ID
	 */
	private String appId;
	/**
	 * clientId
	 */
	private String clientId;
	/**
	 * clientSecret
	 */
	private String clientSecret;
	/**
	 * 0-删除 1-新增
	 */
	private Integer flag;

	/**
	 * 0-生产正式数据 1-调测数据
	 */
	private Integer testFlag;
	/**
	 * 默认时区东8区，时间戳,时间格式：20220420114117642
	 */
	private String timeStamp;

	/**
	 * 创建时间
	 */
	private Date createTime;
}
