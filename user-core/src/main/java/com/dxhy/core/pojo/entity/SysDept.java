/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.FieldStrategy;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 部门管理
 * </p>
 *
 * <AUTHOR>
 * @since 2018-01-22
 */
@Data
@TableName("sys_dept")
public class SysDept extends Model<SysDept> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableId(value = "dept_id", type = IdType.INPUT)
    private String deptId;
    /**
     * 上级部门ID，一级部门为0
     */
    @TableField(value = "parent_id", strategy= FieldStrategy.IGNORED)
    private String parentId;
    /**
     * 组织名称
     */
    private String name;
    /**
     * 组织简称 （默认和组织名称一样）
     */
    @TableField("dept_sname")
    private String deptSname;
    /**
     * 树形编码
     */
    private String code;
    /**
     * 层级
     */
    private Integer level;
    /**
     * 税号类型
     */
    @TableField("ein_type")
    private String einType;
    /**
     * 纳税人税号
     */
    @TableField("taxpayer_code")
    private String taxpayerCode;
    /**
     * 纳税人注册省份
     */
    @TableField("taxpayer_province")
    private String taxpayerProvince;
    /**
     * 纳税人注册市区
     */
    @TableField("taxpayer_city")
    private String taxpayerCity;
    /**
     * 纳税人注册区/县
     */
    @TableField("taxpayer_county")
    private String taxpayerCounty;
    /**
     * 纳税人注册省份代码
     */
    @TableField("taxpayer_province_code")
    private String taxpayerProvinceCode;
    /**
     * 纳税人注册市区代码
     */
    @TableField("taxpayer_city_code")
    private String taxpayerCityCode;
    /**
     * 地区code存最末尾的编码code
     */
    @TableField("location_code")
    private String locationCode;

    /**
     * 纳税人注册区/县代码
     */
    @TableField("taxpayer_county_code")
    private String taxpayerCountyCode;
    /**
     * 纳税人地址
     */
    @TableField("taxpayer_address")
    private String taxpayerAddress;
    /**
     * 纳税人电话
     */
    @TableField("taxpayer_phone")
    private String taxpayerPhone;
    /**
     * 纳税人银行
     */
    @TableField("taxpayer_bank")
    private String taxpayerBank;
    /**
     * 纳税人账号
     */
    @TableField("taxpayer_account")
    private String taxpayerAccount;
    /**
     * 纳税人类型
     */
    @TableField("taxpayer_type")
    private String taxpayerType;
    /**
     * 所属行业
     */
    @TableField("taxpayer_industry")
    private String taxpayerIndustry;
    /**
     * 会计准则
     */
    @TableField("accounting_principle")
    private String accountingPrinciple;
    /**
     * 所属行业代码
     */
    @TableField("taxpayer_industry_code")
    private String taxpayerIndustryCode;
    /**
     * 会计准则代码
     */
    @TableField("accounting_principle_code")
    private String accountingPrincipleCode;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 部门类型，1.总公司 2.子公司 3.分支机构 4.部门 5.虚拟机构 6.独立公司
     */
    @TableField("dept_type")
    private Integer deptType;
    /**
     * 创建人
     */
    @TableField("create_user")
    private Long createUser;
    /**
     * 修改人
     */
    @TableField("update_user")
    private Long updateUser;
    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;
    /**
     * 联系人电话
     */
    @TableField("contact_phone")
    private String contactPhone;
    /**
     * 联系人邮箱
     */
    @TableField("contact_email")
    private String contactEmail;
    /**
     * 联系人地址
     */
    private String contactAddress;
    /**
     * 企业编号
     */
    @TableField("enterprise_numbers")
    private String enterpriseNumbers;

    /**
     * 企业授权码
     */
    @TableField("authorization_code")
    private String authorizationCode;

    /**
     * 渠道来源和用户信息渠道来源一致
     */
    @TableField("source_id")
    private String sourceId;
    /**
     * 排序
     */
    @TableField("order_num")
    private Integer orderNum;
    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 是否删除  -1：已删除  0：正常
     */
    @TableField("del_flag")
    private String delFlag;
    /**
     * 数据来源
     */
    @TableField("data_source")
    private String dataSource;

    /**
     * 负责人邮箱
     */
    private String regionEmail;

    /**
     * 是否有即将到期的产品 1：是，2：否---新增
     */
    @TableField("product_status")
    private String productStatus;

    /**
     * 状态  0：禁用   1：正常
     */
    private Integer status;

    /**
     * 多租户ID
     */
    @TableField("tenant_id")
    private String tenantId;

    /**
     * spid,税航票帮手返回的唯一终端id
     */
    private String spId;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "SysDept{" +
                ", id=" + id +
                ", deptId=" + deptId +
                ", parentId=" + parentId +
                ", name=" + name +
                ", deptSname=" + deptSname +
                ", code=" + code +
                ", level=" + level +
                ", taxpayerCode=" + taxpayerCode +
                ", taxpayerProvince=" + taxpayerProvince +
                ", taxpayerCity=" + taxpayerCity +
                ", taxpayerCounty=" + taxpayerCounty +
                ", taxpayerProvinceCode=" + taxpayerProvinceCode +
                ", taxpayerCityCode=" + taxpayerCityCode +
                ", taxpayerCountyCode=" + taxpayerCountyCode +
                ", taxpayerAddress=" + taxpayerAddress +
                ", taxpayerPhone=" + taxpayerPhone +
                ", taxpayerBank=" + taxpayerBank +
                ", taxpayerAccount=" + taxpayerAccount +
                ", taxpayerType=" + taxpayerType +
                ", taxpayerIndustry=" + taxpayerIndustry +
                ", accountingPrinciple=" + accountingPrinciple +
                ", taxpayerIndustryCode=" + taxpayerIndustryCode +
                ", accountingPrincipleCode=" + accountingPrincipleCode +
                ", createTime=" + createTime +
                ", deptType=" + deptType +
                ", createUser=" + createUser +
                ", updateUser=" + updateUser +
                ", contactName=" + contactName +
                ", contactPhone=" + contactPhone +
                ", contactEmail=" + contactEmail +
                ", enterpriseNumbers=" + enterpriseNumbers +
                ", authorizationCode=" + authorizationCode +
                ", sourceId=" + sourceId +
                ", orderNum=" + orderNum +
                ", updateTime=" + updateTime +
                ", delFlag=" + delFlag +
                ", dataSource=" + dataSource +
                ", regionEmail=" + regionEmail +
                ", productStatus=" + productStatus +
                ", status=" + status +
                "}";
    }
}
