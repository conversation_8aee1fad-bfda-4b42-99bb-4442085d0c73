package com.dxhy.core.pojo.DTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 *
 * @author: zhangjinjing
 * @Date: 2022/4/27 19:22
 * @Version 1.0
 */
@Data
@ToString
@EqualsAndHashCode(callSuper = false)
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("id入参DTO")
public class IdDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(name = "ID", required = true)
    private String id;

    @ApiModelProperty(name = "纳税人识别号")
    private String baseNsrsbh;
}
