/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 菜单权限表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-11-08
 */
@Data
@TableName("sys_menu")
public class SysMenu extends Model<SysMenu> {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单ID
     */
    @TableId(value = "menu_id",type = IdType.INPUT)
	private String menuId;
    /**
     * 菜单名称
     */
	private String name;
    /**
     * 菜单权限标识
     */
	private String permission;
    /**
     * 请求链接
     */
	private String url;
    /**
     * 请求方法
     */
	private String method;
    /**
     * 父菜单ID
     */
	@TableField("parent_id")
	private String parentId;
    /**
     * 图标
     */
	private String icon;
    /**
     * VUE页面
     */
	private String component;

	/**
	 * 产品id
	 */
	private String productId;
    /**
     * 排序值
     */
	private Integer sort;
    /**
     * 菜单类型 类型   0：目录   1：菜单   2：按钮
     */
	private String type;
	/**
	 * 系统标识
	 */
	private String systemSign;
	/**
	 * 创建人
	 */
	private Long puserId;
    /**
     * 创建时间
     */
	@TableField("create_time")
	private Date createTime;
    /**
     * 更新时间
     */
	@TableField("update_time")
	private Date updateTime;
    /**
     * 0--正常 1--删除
     */
	@TableField("del_flag")
	private String delFlag;
	/**
	 * 前端URL
	 */
	private String path;
	/**
	 * 菜单所属的产品名称
	 */
	@TableField(exist = false)
	private String productName;

	@TableField(exist = false)
	private List<SysMenu> children = new ArrayList<SysMenu>();

    @TableField(exist = false)
    private Integer selected = 0; // 1-已勾选，0-未勾选


	@Override
	protected Serializable pkVal() {
		return this.menuId;
	}

	@Override
	public String toString() {
		return "SysMenu{" +
				"menuId='" + menuId + '\'' +
				", name='" + name + '\'' +
				", permission='" + permission + '\'' +
				", url='" + url + '\'' +
				", method='" + method + '\'' +
				", parentId='" + parentId + '\'' +
				", icon='" + icon + '\'' +
				", component='" + component + '\'' +
				", productId='" + productId + '\'' +
				", sort=" + sort +
				", type='" + type + '\'' +
				", createTime=" + createTime +
				", updateTime=" + updateTime +
				", delFlag='" + delFlag + '\'' +
				", path='" + path + '\'' +
				", productName='" + productName + '\'' +
				'}';
	}
}
