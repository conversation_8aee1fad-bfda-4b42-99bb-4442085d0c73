package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysMenu;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.vo.SysDeptVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2018/8/13
 */
@Data
public class UserInfoResponseDto implements Serializable {

    private Long userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 租户id
     */
    private String TenantId;
    /**
     * 所属组织id
     */
    private String deptId;
    /**
     * 密码
     */
    private String password;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 状态  0：禁用   1：正常
     */
    private Integer status;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 员工编号
     */
    private String number;
    /**
     * salt
     */
    private String salt;
    /**
     * 头像
     */
    private String avatar;
    /**
     * 姓名
     */
    private String name;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 0-正常，1-删除
     */
    private String delFlag;

    /**
     *  创建人id
     */
    private Long createBy;
    /**
     *  用户类型
     */
    private String userType;

    /**
     *  用户来源
     */
    private String userSource;

    /**
     *  更新人
     */
    private Long updateBy;

    /**
     * 上次登录时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;
    /**
     * dept
     */
    private SysDept dept;

    /**
     * 菜单集合
     */
    private List<SysMenu> menus;

    /**
     * 用户在总公司下的角色
     */
    private List<SysRole> roles;

    /**
     * 数据权限带税号集合
     */
    private List<SysDeptVo> taxplayercodeDeptList;


    /**
     * 渠道ID
     */
    private String distributorId;

    /**
     * 是否通行
     */
    private boolean tipPass;

    /**
     * 试用期剩余天数
     */
    private int remainDay;

}
