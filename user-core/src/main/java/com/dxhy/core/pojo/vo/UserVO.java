/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/10/29
 */
@Data
public class UserVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long userId;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 随机盐
     */
    private String salt;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 0-正常，1-删除
     */
    private String delFlag;
    /**
     * 简介
     */
    private String phone;
    /**
     * 头像
     */
    private String avatar;

    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 角色列表
     */
    private List<SysRole> roleList;

    /**
     *  邮箱
     */
    private String email;

    /**
     *  昵称
     */
    private String nickname;

    /**
     *  用户类型 1.主账户  2子账户 3个人用户
     */
    private String userType;

    /**
     * 渠道ID
     */
    private String distributorId;
    /**
     * 描述
     */
    private String remark;
    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 顶级机构企业ID
     */
    private String topLevel;

    /**
     * 状态  0：禁用   1：正常
     */
    private Integer status;

    /**
     * 最后登陆时间
     */
    private Date lastLoginTime;


    /**
     *  创建人id
     */
    private Long createBy;

    /**
     *  用户来源
     */
    private String userSource;

    /**
     *  更新人id
     */
    private Long updateBy;
}
