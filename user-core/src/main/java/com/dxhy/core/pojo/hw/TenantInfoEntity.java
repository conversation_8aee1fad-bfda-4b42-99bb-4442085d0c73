package com.dxhy.core.pojo.hw;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门管理（目前没用，使用sys_tenant租户信息表）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2019-03-22 15:24:24
 */
@Data
@TableName("tenant_info")
public class TenantInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 实例id
	 */
	private String instanceId;
	/**
	 * 订单id
	 */
	private String orderId;
	/**
	 * 租户id
	 */
	private String tenantId;
	/**
	 * 租户编码
	 */
	private String tenantCode;
	/**
	 * 租户名称
	 */
	private String name;
	/**
	 * 域名
	 */
	private String domainName;
	/**
	 * 时间戳
	 */
	private String timeStamp;
	/**
	 * 0-删除 1-新增
	 */
	private Integer flag;
	/**
	 * 0-生产正式数据 1-调测数据
	 */
	private Integer testFlag;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新时间
	 */
	private Date updateTime;

	private String appKey;
	private String appSecret;
}
