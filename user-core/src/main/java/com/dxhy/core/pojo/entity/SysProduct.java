package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @author: zhangjinjing
 * @Date: 2022/4/25 17:55
 * @Version 1.0
 */
@Getter
@Setter
@Accessors(chain = true)
@TableName("sys_product")
public class SysProduct extends Model<SysProduct> implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.ID_WORKER)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 产品名称
     */
    @TableField("name")
    private String name;

    /**
     * 产品icon的图片id
      */
    @TableField("icon")
    private String icon;

    /**
     * 产品介绍
     */
    @TableField("detail")
    private String detail;

    /**
     * 是否已删除
     */
    @TableField("is_deleted")
    private String isDeleted;


    protected Serializable pkVal() {
        return this.id;
    }
}
