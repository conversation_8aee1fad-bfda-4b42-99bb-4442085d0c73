package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: SysUserDept
 * @projectName user_base
 * @date 2020-03-3117:26
 */
@Data
@TableName("sys_user_dept")
public class SysUserDept extends Model<SysUserDept> {


    private static final long serialVersionUID = 5004941714361751255L;

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户ID
     */
    @TableId(type = IdType.INPUT)
    private Long userId;
    /**
     * 组织ID
     */
    @TableId(type = IdType.INPUT)
    private String deptId;

    @Override
    protected Serializable pkVal() {
        return id;
    }

    @Override
    public String toString() {
        return "SysUserDept{" +
                ", userId=" + userId +
                ", deptId=" + deptId +
                "}";
    }
}
