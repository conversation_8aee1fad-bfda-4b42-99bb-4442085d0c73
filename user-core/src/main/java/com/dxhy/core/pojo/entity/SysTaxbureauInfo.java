package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
/**
 * @description sys_taxbureau_info
 * <AUTHOR>
 * @date 2022-11-03
 */
@Data
public class SysTaxbureauInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private Integer id;

    /**
     * 机构id
     */
    private String deptId;

    /**
     * 税号
     */
    private String taxNo;

    /**
     * 电子税务局账号
     */
    private String userName;

    /**
     * 电子税务局密码
     */
    private String userPass;

    public SysTaxbureauInfo() {}
}