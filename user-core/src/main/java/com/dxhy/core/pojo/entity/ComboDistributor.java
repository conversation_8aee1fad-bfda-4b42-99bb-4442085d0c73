package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("combo_distributor")
public class ComboDistributor extends Model<ComboDistributor> {

    private static final long serialVersionUID = 1L;
    private String id;
    /**
     * 套餐模板id
     */
    @TableField("template_id")
    private String templateId;
    /**
     * 分销商id
     */
    @TableField("distributor_id")
    private String distributorId;
    /**
     * 状态 0 正常 1 删除  2 禁用
     */
    private String status;
    /**
     * 套餐名称
     */
    @TableField("combo_name")
    private String comboName;
    /**
     * 最后优惠价
     */
    @TableField("preferential_price")
    private String preferentialPrice;
    /**
     * 套餐价格
     */
    @TableField("combo_price")
    private String comboPrice;
    /**
     * 底价
     */
    @TableField("base_price")
    private String basePrice;
    /**
     * 1 底价模式 2 分成模式
     */
    @TableField("combo_type")
    private String comboType;
    /**
     * 分成比例
     */
    private String proportion;
    /**
     * 划线价
     */
    @TableField("line_price")
    private String linePrice;
    /**
     * 售卖价
     */
    @TableField("sale_price")
    private String salePrice;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    @TableField(exist = false)
    private ComboTemplate comboTemplate;

    @TableField(exist = false)
    private String sellLabelName;

    @TableField(exist = false)
    private String validTime;
    @TableField(exist = false)
    private String sellLablelId;
    /**
     * 版本级次
     */
    @TableField(exist = false)
    private Integer sort;
    /**
     * 模板编码
     */
    @TableField(exist = false)
    private String comboNum;
    /**
     * 描述
     */
    @TableField(exist = false)
    private String describe;

    /**
     * 剩余天数
     */
    @TableField(exist = false)
    private String days;

    @TableField(exist = false)
    private List chargeItems;

    @TableField(exist = false)
    private int chargeItemSelectedCount;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
