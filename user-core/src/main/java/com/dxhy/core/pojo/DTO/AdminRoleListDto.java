package com.dxhy.core.pojo.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by lenovo on 2019/4/1.
 */
@Data
public class AdminRoleListDto {
    private Long roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 是否为共享角色0：否,1:是
     */
    private Integer type;
    /**
     * 描述
     */
    private String describe;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 角色编码
     */
    private String roleCode;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date updateTime;
    /**
     * 创建人
     */
    private String pUsername;
    /**
     * 用户个数
     */
    private Integer userCount;

    /**
     * 1为中台、2为企业
     */
    private String roleProperty;

    /**
     * 角色类型 0后台超级管理员10：超级管理员11：管理员12：其他
     */
    private Integer roleType;

}
