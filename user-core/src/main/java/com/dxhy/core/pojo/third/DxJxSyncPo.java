package com.dxhy.core.pojo.third;
import cn.hutool.crypto.SecureUtil;
import lombok.Data;

@Data
public class DxJxSyncPo {
    /*
     *接入编码
     */
    private String core;
    /*
     *鉴权key
     */
    private String signAceKey;
    /*
     *当前请求时间
     */
    private String signTime;
    /*
     *鉴权加密信息
     */
    private String sign;
    /*
     *业务数据信息 base 64加密
     */
    private String data;

    public DxJxSyncPo(String core, String psw, String signAceKey, String data){
        this.core=core;
        this.signAceKey=signAceKey;
        this.signTime=System.currentTimeMillis()/1000+"";
        this.sign= SecureUtil.md5(SecureUtil.md5(core+signAceKey+signTime+psw));
        this.data=data;
    }
}
