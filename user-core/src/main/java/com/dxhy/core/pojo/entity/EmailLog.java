package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description email_log
 * <AUTHOR>
 * @date 2022-10-27
 */
@Data
public class EmailLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private Integer id;

    /**
     * content
     */
    private String content;

    /**
     * create_time
     */
    private Date createTime;

    public EmailLog(String content){
        this.content=content;
        this.createTime=new Date();
    }

}