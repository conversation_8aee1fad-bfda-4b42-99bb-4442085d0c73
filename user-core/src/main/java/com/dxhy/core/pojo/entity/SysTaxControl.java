package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigInteger;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_tax_control")
public class SysTaxControl extends Model<SysTaxControl> {

    @TableId(value = "id",type = IdType.INPUT)
    private BigInteger id;
    /**
     * 税控设备编码
     */
    @TableField(value ="dept_id")
    private String deptId;

    /**
     * 税控设备编码
     */
    @TableField("sksbbm")
    private String sksbbm;

    /** 税控设备名称 */
    @TableField("sksbmc")
    private String sksbmc;

    /** 类型 */
    @TableField("type")
    private int type;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }
}
