package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.vo.SysDeptVo;
import com.dxhy.core.pojo.vo.SysRoleVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by lenovo on 2019/3/29.
 */
@Data
public class AdminUserListDto {
    private Long userId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 状态
     */
    private Integer status;
    /**
     * 状态  0：正常   1：删除(禁用)
     */
    private String delFlag;
    /**
     * 部门id
     */
    private String deptId;
    /**
     * 部门名称
     */
    private String deptName;
    /**
     *企业名称
     */
    private String epName;
    /**
     * 角色名称
     */
    private List<String> roleName;
    /**
     * 部门数据权限
     */
    private List<SysDeptVo> deptRoleList;

    /**
     * 所有角色
     */
    private List<SysRoleVo> roleList;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 最后登陆时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;
    /**
     * 最后更新时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastUpdateTime;
    /**
     * 创建人
     */
    private String pUsername;
    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 账号类型 1.主账户  2子账户 3个人用户
     */
    private String userType;
    /**
     * 用户所属组织
     */
    private SysDept sysDept;

}
