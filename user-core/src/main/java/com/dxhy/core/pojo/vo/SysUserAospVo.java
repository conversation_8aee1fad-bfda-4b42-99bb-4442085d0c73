package com.dxhy.core.pojo.vo;

import com.baomidou.mybatisplus.annotations.TableField;
import lombok.Data;

import java.io.Serializable;

@Data
public class SysUserAospVo implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -3947124298491373901L;
	/**
	 * id
	 */
	@TableField("user_id")
	private Long userId;
	/**
	 * 手机号
	 */
	private String phone;
    /**
     * 姓名
     */
    private String nickname;
    /**
     * 邮箱
     */
    private String email;
	/**
	 * 状态值 0-激活，1-冻结
	 */
	@TableField("del_flag")
	private String delFlag;
    
}
