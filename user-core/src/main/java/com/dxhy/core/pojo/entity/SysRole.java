/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
@TableName("sys_role")
public class SysRole extends Model<SysRole> {

    private static final long serialVersionUID = 1L;

    @TableId(value = "role_id", type = IdType.AUTO)
    private Long roleId;
    @TableField("role_name")
    private String roleName;
    private Integer type;
    /**
     * 角色属性 ---新增   1为中台、2为门户
     */
    @TableField("role_property")
    private String roleProperty;
    @TableField("role_code")
    private String roleCode;
    @TableField("role_desc")
    private String roleDesc;
    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;
    /**
     *所属部门id
     */
    @TableField("dept_id")
    private String deptId;

    /**
     *所属部门名称查询性能优化
     */
    @TableField("dept_name")
    private String deptName;

    /**
     *创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     *更新人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 状态  0-正常，1-删除
     */
    @TableField("del_flag")
    private String delFlag;

    /**
     * 角色类型 0后台超级管理员10：超级管理员11：管理员12：其他
     */
    @TableField("role_type")
    private Integer roleType;

    /**
     * 渠道ID
     */
    @TableField("distributor_id")
    private String distributorId;
    /**
     * 租户ID
     */
    private String tenantId;

    @Override
    protected Serializable pkVal() {
        return this.roleId;
    }

    @Override
    public String toString() {
        return "SysRole{" +
                ", roleId=" + roleId +
                ", roleName=" + roleName +
                ", roleCode=" + roleCode +
                ", roleProperty=" + roleProperty +
                ", type=" + type +
                ", deptId=" + deptId +
                ", roleDesc=" + roleDesc +
                ", createTime=" + createTime +
                ", createBy=" + createBy +
                ", updateTime=" + updateTime +
                ", updateBy=" + updateBy +
                ", delFlag=" + delFlag +
                ", distributorId=" + distributorId +
                "}";
    }
}
