/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户表
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
@TableName("sys_user")
public class SysUser extends Model<SysUser> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "user_id", type = IdType.AUTO)
    private Long userId;
    /**
     * 用户名
     */
    private String username;

    private String password;
    /**
     * 随机盐
     */
    @JsonIgnore
    private String salt;
    /**
     *  邮箱
     */
    private String email;

    /**
     *  用户类型
     */
    @TableField("user_type")
    private String userType;
    /**
     * 0-正常，1-删除
     */
    @TableField("del_flag")
    private String delFlag;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 状态  0：禁用   1：正常
     */
    private Integer status;
    /**
     * 部门ID
     */
    @TableField("dept_id")
    private String deptId;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("update_time")
    private Date updateTime;
    /**
     *  姓名
     */
    private String nickname;
    /**
     * 最后登陆时间
     */
    @TableField("last_login_time")
    private Date lastLoginTime;
    /**
     *  创建人id
     */
    private Long createBy;
    /**
     * 头像
     */
    private String avatar;
    /**
     *  用户来源
     */
    private String userSource;
    /**
     *  更新人id
     */
    private Long updateBy;
    /**
     * 渠道ID
     */
    private String distributorId;
    /**
     * 描述
     */
    private String remark;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 顶级机构企业ID
     */
    private String topLevel;

    /**
     * 数据list
     */
    @TableField(exist = false)
    private List<String> deptList;

    @Override
    protected Serializable pkVal() {
        return this.userId;
    }

    @Override
    public String toString() {
        return "SysUser{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", password='" + password + '\'' +
                ", salt='" + salt + '\'' +
                ", email='" + email + '\'' +
                ", userType='" + userType + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", phone='" + phone + '\'' +
                ", status='" + status + '\'' +
                ", deptId=" + deptId +
                ", createTime=" + createTime +
                ", nickname=" + nickname +
                ", lastLoginTime=" + lastLoginTime +
                ", createBy=" + createBy +
                ", avatar='" + avatar + '\'' +
                ", userSource='" + userSource + '\'' +
                ", updateTime=" + updateTime +
                ", updateBy=" + updateBy +
                ", distributorId=" + distributorId +
                ", remark=" + remark +
                ", tenantId=" + tenantId +
                '}';
    }
}
