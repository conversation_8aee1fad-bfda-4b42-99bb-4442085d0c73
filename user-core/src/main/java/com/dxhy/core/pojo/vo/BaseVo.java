/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.pojo.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/2 10:49
 */
@Getter
@Setter
@Accessors(chain = true)
public class BaseVo implements Serializable {

	private static final long serialVersionUID = 8607881934700233211L;

	private Integer pageNo;

	private Integer	pageSize;

}