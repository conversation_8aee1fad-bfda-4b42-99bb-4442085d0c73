package com.dxhy.core.pojo.PO;

import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/5 18:08
 * @Version 1.0
 */
@Data
@ToString
@TableName("temp_table")
public class TempTablePO implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Integer id;

    @TableField("name")
    private String name;

}
