package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import com.dxhy.core.pojo.vo.SellLabel;
import lombok.*;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 产品信息表（目前没用，使用sys_product产品表）
 * @author: zhangjinjing
 * @Date: 2022/4/25 17:55
 * @Version 1.0
 */
@EqualsAndHashCode(callSuper = false)
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
@TableName("product_info")
public class ProductInfo extends Model<ProductInfo> implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private String uid;
    @TableField("distributor_id")
    private String distributorId;
    private String name;
    private String type;
    private String icon;
    private String introduction;
    @TableField("open_dimension")
    private String openDimension;
    @TableField(
            exist = false
    )
    private String status;
    @TableField("del_flag")
    private String delFlag;
    @TableField("create_time")
    private Date createTime;
    @TableField("modify_time")
    private Date modifyTime;
    @TableField("product_status")
    private String productStatus;
    @TableField("product_class")
    private String productClass;
    @TableField("product_source")
    private String productSource;
    @TableField(
            exist = false
    )
    private String distributorName;
    @TableField(
            exist = false
    )
    private String superior;
    @TableField(
            exist = false
    )
    private String version;
    @TableField(
            exist = false
    )
    private String productLink;
    @TableField(
            exist = false
    )
    private String checkLink;
    @TableField(
            exist = false
    )
    private List<SellLabel> sellLabels;

    protected Serializable pkVal() {
        return this.id;
    }
}
