package com.dxhy.core.pojo.vo;

import com.dxhy.core.enums.ResponseCodeEnum;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 17:222019/2/27 0027
 * @description 接口返回通用对象
 */
@Data
@ToString
public class CommonRspVo<T> implements Serializable {
    /**
     * 错误码
     */
    private String code;
    /**
     * 错误描述
     */
    private String message;
    /**
     * 返回数据
     */
    private T data;

    public CommonRspVo(){ }

    public CommonRspVo(ResponseCodeEnum code){
        this.code=code.getCode();
        this.message=code.getMessage();
    }
    public CommonRspVo(String code, String message){
        this.code=code;
        this.message=message;
    }
    public CommonRspVo(T data, ResponseCodeEnum code){
        this.code=code.getCode();
        this.message=code.getMessage();
        this.data=data;
    }
    public CommonRspVo(T data){
        this(ResponseCodeEnum.SUCCESS);
        this.data=data;
    }

    /**
     * 判断是否成功
     * @return
     */
    public boolean successfull(){
        return ResponseCodeEnum.SUCCESS.getCode().equals(this.code);
    }

    /**
     * 获取成功对象
     * @param data
     * @param <C>
     * @return
     */
    public  static <C> CommonRspVo  success(C data){
        CommonRspVo success=new CommonRspVo(ResponseCodeEnum.SUCCESS);
        success.data=data;
        return success;
    }

    public static CommonRspVo faild(ResponseCodeEnum code) {
        CommonRspVo faild=new CommonRspVo(code);
        return faild;
    }

    /**
     * 获取失败对象
     * @return
     */
    public  static  CommonRspVo  faild(){
        CommonRspVo faild=new CommonRspVo(ResponseCodeEnum.SYSTEM_ERROR);
        return faild;
    }
}