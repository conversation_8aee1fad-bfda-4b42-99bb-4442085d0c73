package com.dxhy.core.pojo.DTO;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * Created by lenovo on 2019/4/2.
 */
@Data
public class AdminMenuListDto {

    private String menuId;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单URL
     */
    private String url;
    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 类型 0系统  1：目录   2：菜单   3：按钮
     */
    private String type;

    /**
     * 创建人
     */
    private String pUsername;

}
