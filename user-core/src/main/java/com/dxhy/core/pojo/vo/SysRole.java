/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
@Data
public class SysRole implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long roleId;
    private String roleName;
    private Integer type;
    private String roleProperty;
    private String roleCode;
    private String roleDesc;
    private Date createTime;
    private Date updateTime;
    private String deptId;
    private Long createBy;
    private Long updateBy;
    private String delFlag;
    private Integer roleType;
    private String distributorId;



    @Override
    public String toString() {
        return "SysRole{" +
                ", roleId=" + roleId +
                ", roleName=" + roleName +
                ", type=" + type +
                ", roleProperty=" + roleProperty +
                ", roleCode=" + roleCode +
                ", roleDesc=" + roleDesc +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", deptId=" + deptId +
                ", createBy=" + createBy +
                ", updateBy=" + updateBy +
                ", deptId=" + deptId +
                ", delFlag=" + delFlag +
                ", distributorId=" + distributorId +
                "}";
    }
}
