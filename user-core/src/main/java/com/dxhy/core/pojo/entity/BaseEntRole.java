package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("base_ent_role")
public class BaseEntRole extends Model<BaseEntRole> {

    @TableId(value = "role_id", type = IdType.INPUT)
    private String roleId;

    @TableId(value = "ent_id", type = IdType.INPUT)
    private String entId;

    @TableField("create_by")
    private String createBy;

    @TableField("create_time")
    private Date createTime;

    @Override
    protected Serializable pkVal() {
        return roleId + entId;
    }
}
