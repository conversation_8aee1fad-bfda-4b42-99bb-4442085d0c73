package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 数据权限返回
 * <AUTHOR>
 * @date 2022-06-22
 */
@Data
public class DeptInfoResponseDto implements Serializable {

    /**
     * userInfo
     */
    private UserInfoDTO userInfo;

    /**
     * deptInfo
     */
    private SysDept deptInfo;

    /**
     * 数据权限带税号集合
     */
    private List<SysDept> taxplayercodeDeptList;


    /**
     * 产品集合
     */
    private List<SysTenantProduct> tenantProductList;
}
