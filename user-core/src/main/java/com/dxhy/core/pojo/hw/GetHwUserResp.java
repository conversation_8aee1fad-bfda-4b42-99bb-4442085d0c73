package com.dxhy.core.pojo.hw;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 查询华为用户信息接口响应报文实体
 * <AUTHOR>
 * @date 2023-04-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetHwUserResp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 用户ID
     **/
    private String id;
    /**
     * 用户名
     **/
    private String userName;
    /**
     * 姓名
     **/
    private String name;
    /**
     * 邮箱地址
     **/
    private String email;
    /**
     * 手机号
     **/
    private String mobile;
    /**
     * 错误类型
     * 从 GeneralParam 中取值
     **/
    private String error;
    /**
     * 错误描述
     * 从 GeneralParam 中取值
     **/
    private String error_description;
    /**
     * 角色
     **/
    private String role;
    /**
     * 组织名称
     **/
    private String organizationName;
    /**
     * 登录名称
     **/
    //private String accName;
    /**
     * 用户id
     **/
    private String userId;
    /**
     * 组织编码
     **/
    private String organizationCode;
    /**
     * 登录名称
     **/
    //private String loginName;
    /**
     * 租户名称
     **/
    private String projectName;
    /**
     * 租户id
     **/
    private String tenant;
}
