package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysMenu;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by lenovo on 2019/4/9.
 */
@Data
public class AdminMenuInfoDto {

    private String menuId;
    /**
     * 父菜单ID，一级菜单为0
     */
    private String parentId;
    /**
     * 菜单名称
     */
    private String name;
    /**
     * 菜单URL
     */
    private String url;
    /**
     * 类型   0：目录   1：菜单   2：按钮
     */
    private String type;
    /**
     * 系统标识
     */
    private String systemSign;
    /**
     * 排序
     */
    private Integer orderNum;
    /**
     * 菜单图标
     */
    private String icon;
    /**
     * 创建时间
     */
    private Date createTime;


    private List<SysMenu> SysMenuList;
}
