package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @title: SysDeptMenu
 * @projectName user_base
 * @date 2020-03-3012:23
 */
@Data
@TableName("sys_dept_menu")
public class SysDeptMenu extends Model<SysDeptMenu> {

    private static final long serialVersionUID = 3875664585389609762L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;


    @TableId(value = "dept_id", type = IdType.AUTO)
    private String deptId;

    @TableId(value = "menu_id", type = IdType.AUTO)
    private String menuId;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

    @Override
    public String toString() {
        return "SyxsDeptMenu{" +
                ", deptId=" + deptId +
                ", menuId=" + menuId +
                "}";
    }

}
