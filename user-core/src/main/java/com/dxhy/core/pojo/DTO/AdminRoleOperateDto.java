package com.dxhy.core.pojo.DTO;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by lenovo on 2019/4/1.
 */
@Data
public class AdminRoleOperateDto {
    /**
     * 部门ID
     */
    private String deptId;
    /**
     * 角色id
     */
    private Long roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 是否为共享角色0：否,1:是
     */
    private Integer type;
    /**
     * 角色说明
     */
    private String describe;
    /**
     * 功能权限
     */
    private List<String> menusList;
    /**
     * 当前登陆人id
     */
    private Long userId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 角色类型（中台、门户）1为中台、2为企业
     */
    private String roleProperty;

    /**
     * 所属组织名称
     */
    private String deptName;

    /**
     * 渠道ID
     */
    private String distributorId;

    /**
     * 角色类型 0后台超级管理员10：超级管理员11：管理员12：其他
     */
    private Integer roleType;
}
