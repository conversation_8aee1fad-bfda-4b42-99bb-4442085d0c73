package com.dxhy.core.pojo.hw;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 获取华为token响应报文实体
 * <AUTHOR>
 * @date 2023-04-19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAccessTokenResp implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 授权服务器返回给第三方应用的访问令牌
     **/
    private String access_token;
    /**
     * 访问令牌类型。固定值：Bearer
     **/
    private String token_type;
    /**
     * 访问令牌的有效期，以秒为单位。
     **/
    private String expires_in;
    /**
     * 授权范围
     **/
    private String scope;
    /**
     * 刷新令牌。默认不生成refresh_token，如果需要，在应用的“认证配置”中设置“Refresh Token有效期”后生成返回
     **/
    private String refresh_token;
    /**
     * 错误类型
     * 从 GeneralParam 中取值
     **/
    private String error;
    /**
     * 错误描述
     * 从 GeneralParam 中取值
     **/
    private String error_description;
}
