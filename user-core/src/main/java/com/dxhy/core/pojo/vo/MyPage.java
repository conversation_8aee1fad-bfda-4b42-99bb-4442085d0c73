package com.dxhy.core.pojo.vo;

import com.baomidou.mybatisplus.enums.SqlLike;
import com.baomidou.mybatisplus.mapper.EntityWrapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.baomidou.mybatisplus.toolkit.StringUtils;
import com.dxhy.core.pojo.entity.ConditionEntity;
import com.dxhy.core.pojo.entity.PageQueryEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 13:272019/2/28 0028
 * @description 分页实体类
 */
public class MyPage<T> extends Page<T> {

    private List<ConditionEntity> conditions;
    public MyPage(PageQueryEntity pageQueryEntity) {
        super(pageQueryEntity.getPage()<=0?1:pageQueryEntity.getPage(), pageQueryEntity.getLimit()<=0?10:pageQueryEntity.getLimit());
        if (StringUtils.isNotEmpty(pageQueryEntity.getOrderByField())) {
            this.setOrderByField(pageQueryEntity.getOrderByField());
        }
        this.setAscs(pageQueryEntity.getAscs());
        this.setDescs(pageQueryEntity.getDescs());
        this.setAsc(pageQueryEntity.getIsAsc()==null?true:pageQueryEntity.getIsAsc());
        this.setCondition(pageQueryEntity.getParams());
        this.conditions=pageQueryEntity.getConditions();
    }
    public MyPage(){

    }
    public EntityWrapper<T> getWrapper(){
        EntityWrapper<T> wrapper=new EntityWrapper<>();
        if(conditions==null||conditions.isEmpty()){
            return wrapper;
        }
        for(ConditionEntity condition:conditions){
            switch(condition.getType()){
                case eq:
                    wrapper.eq(condition.getColumn(),condition.getValue());
                    break;
                case gt:
                    wrapper.gt(condition.getColumn(),condition.getValue());
                    break;
                case lt:
                    wrapper.lt(condition.getColumn(),condition.getValue());
                    break;
                case ge:
                    wrapper.ge(condition.getColumn(),condition.getValue());
                    break;
                case le:
                    wrapper.le(condition.getColumn(),condition.getValue());
                    break;
                case in:
                    wrapper.in(condition.getColumn(),condition.getValues());
                    break;
                case ne:
                    wrapper.ne(condition.getColumn(),condition.getValue());
                    break;
                case rlike:
                    wrapper.like(condition.getColumn(),condition.getValue().toString(),SqlLike.RIGHT);
                    break;
                case llike:
                    wrapper.like(condition.getColumn(),condition.getValue().toString(),SqlLike.LEFT);
                    break;
                case dlike:
                    wrapper.like(condition.getColumn(),condition.getValue().toString());
                    break;
                case notlike:
                    wrapper.notLike(condition.getColumn(),condition.getValue().toString());
                    break;
                case between:
                    wrapper.between(condition.getColumn(),condition.getValues().get(0),condition.getValues().get(1));
                    break;
                case notbetween:
                    wrapper.notBetween(condition.getColumn(),condition.getValues().get(0),condition.getValues().get(1));
                    break;
                case notin:
                    wrapper.notIn(condition.getColumn(),condition.getValues());
                    break;
                case exist:
                    wrapper.exists(condition.getValue().toString());
                    break;
                case notexist:
                    wrapper.notExists(condition.getValue().toString());
                    break;
                case notnull:
                    wrapper.isNotNull(condition.getColumn());
                    break;
                default:
                    break;
            }
        }
        return wrapper;
    }
}
