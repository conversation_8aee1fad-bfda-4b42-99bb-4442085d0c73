package com.dxhy.core.pojo.third;

import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.pojo.vo.UserVO;
import lombok.Data;

import java.util.List;

@Data
public class DxJxUserData {
    /*
     *登录名
     */
    private String loginName;
    /*
     *用户名
     */
    private String userName;
    /*
     *邮箱
     */
    private String email;
    /*
     *手机号
     */
    private String mobile;
    /*
     *状态 0--禁用 1--启用
     */
    private String status;
    /*
     *集团编码
     */
    private String company;
    /*
     *集团名称
     */
    private String companyName;
    /*
     *用户税号权限
     */
    private List<DxJxUserOrg> userOrg;

    public  DxJxUserData(UserVO userVO){
        this.loginName= CommonConstant.UID_PREFIX+userVO.getUsername();
        this.userName=CommonConstant.UID_PREFIX+userVO.getUsername();
        this.email=userVO.getEmail();
        this.mobile=userVO.getPhone();
        this.status=userVO.getStatus()+"";
        this.company=userVO.getDistributorId();
        this.companyName=userVO.getDeptName();
    }
}
