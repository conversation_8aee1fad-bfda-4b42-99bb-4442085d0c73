/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 产品菜单表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-10
 */
@Data
@TableName("sys_product_menu")
public class SysProductMenu extends Model<SysProductMenu> {

    private static final long serialVersionUID = 1L;

	/**
	 * 产品ID
	 */
	@TableId(value = "product_id",type = IdType.INPUT)
	private String productId;

	/**
	 * 菜单ID
	 */
	@TableField("menu_id")
	private String menuId;
	/**
	 * 产品名称
	 */
	@TableField("product_name")
	private String productName;
	/**
	 * 描述
	 */
	@TableField("desc")
	private String desc;
	/**
	 * 0--正常 1--删除
	 */
	@TableField("del_flag")
	private String delFlag;

	@Override
	protected Serializable pkVal() {
		return this.productId;
	}

	@Override
	public String toString() {
		return "SysRoleMenu{" +
			", productId=" + productId +
			", menuId=" + menuId +
				", productName=" + productName +
				", desc=" + desc +
				", delFlag=" + delFlag +
			"}";
	}
}
