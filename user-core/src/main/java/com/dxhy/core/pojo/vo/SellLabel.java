package com.dxhy.core.pojo.vo;

import com.baomidou.mybatisplus.activerecord.Model;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-06
 */
@Data
public class SellLabel extends Model<SellLabel> {

    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 产品ID
     */
    private String productId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 售卖类型名称
     */
    private String name;

    /**
     * 排序
     */
    private String sort;


    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
