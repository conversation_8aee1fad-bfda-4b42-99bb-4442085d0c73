/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysUser;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/5
 */
@Data
public class UserDTO extends SysUser {
    /**
	 * 
	 */
	private static final long serialVersionUID = -6428395461346036603L;

	/**
     * 角色ID
     */
    private List<Long> role;

    private String deptId;

    /**
     * 新密码
     */
    private String newPassword;
    /**
     * 当前登录账户ID
     */
    private Long createBy;
    /**
     * 企业ID
     */
    private String entId;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 备注
     */
    private String remark;
    /**
     * 产品id
     */
    private String productId;

    /**
     * 企业ID
     */
    private List<String> entIds;
}
