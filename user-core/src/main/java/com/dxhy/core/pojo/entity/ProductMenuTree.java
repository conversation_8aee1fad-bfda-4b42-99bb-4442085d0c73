package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableField;
import lombok.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: zhangjinjing
 * @Date: 2022/4/25 17:55
 * @Version 1.0
 */
@EqualsAndHashCode
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductMenuTree implements Serializable {
    private static final long serialVersionUID = 1L;
    protected String id;
    @TableField("parent_id")
    protected String parentId;
    private String name;
    private String permission;
    private String path;
    private String url;
    private String icon;
    private String method;
    @TableField("system_sign")
    private String systemSign;
    @TableField("product_id")
    private String productId;
    @TableField("product_name")
    private String productName;
    private Integer sort;
    private String type;
    private String status;
    private List<ProductMenuTree> children = new ArrayList();

}
