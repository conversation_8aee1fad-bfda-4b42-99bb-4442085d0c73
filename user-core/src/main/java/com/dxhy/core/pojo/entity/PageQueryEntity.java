package com.dxhy.core.pojo.entity;
import lombok.Data;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 14:552019/3/6 0006
 * @description 分页请求实体类
 */
@Data
@ToString
public class PageQueryEntity {

    public PageQueryEntity() {
    }

    public PageQueryEntity(int limit, int page) {
        this.limit = limit;
        this.page = page;
    }

    /**
     * 查询条件
     */
    private List<ConditionEntity> conditions;
    /**
     * equals查询条件
     */
    private Map<String,Object> params;
    /**
     * 是否正序排序
     */
    private Boolean isAsc;
    /**
     * 单个排序字段
     */
    private String orderByField;
    /**
     * 正序排序字段
     */
    private List<String> ascs;
    /**
     * 倒序排序字段
     */
    private List<String> descs;
    /**
     * 每页大小
     */
    private int limit;
    /**
     * 当前页
     */
    private int page;
}
