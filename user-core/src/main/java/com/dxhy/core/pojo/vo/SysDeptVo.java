package com.dxhy.core.pojo.vo;

import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysMenu;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title: SysDeptVo
 * @projectName user_base
 * @description: dept返回VO
 * @date 2020-03-2622:20
 */
@Data
public class SysDeptVo extends SysDept implements Serializable {

    private static final long serialVersionUID = -2390738163011431963L;
    /**
     * 组织下总人数
     */
    private Integer userCount;

    /**
     * 所属上级组织名称
     */
    private String epName;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 创建人当前组织下的所有角色
     */
    private List<SysRole> roleList;

    /**
     * 用户在当前组织下拥有的菜单
     */
    private List<SysMenu> menus;

    /**
     * 创建时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 销项传入的税控设备类型新增---需传给门户
     */
    private List<String> taxControlList;

    /**
     * 渠道名称
      */
    private String sourceName;

    /**
     * 是否存在子级校验 0表示不存在，1表示存在
     */
    private String isNextCheck;

    private String sksbbm;
    private String sksbmc;

    private String aceId;
    private String aceKey;

    private String taxBureaName;
    private String taxBureaPass;
    private String taxpayerTypeName;
    private List<SysTenantProduct> sysTenantProductList;
}
