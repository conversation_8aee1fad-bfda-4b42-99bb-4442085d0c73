package com.dxhy.core.pojo.vo;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @title: LoginLog
 * @projectName user_base
 * @date 2020-10-2921:23
 */
@Data
@TableName("login_log")
public class LoginLog extends Model<LoginLog> {

    private static final long serialVersionUID = -2586348471847136217L;
    private Long id;
    private Long uid;
    @TableField("login_account")
    private String loginAccount;
    @TableField("login_time")
    private Date loginTime;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
