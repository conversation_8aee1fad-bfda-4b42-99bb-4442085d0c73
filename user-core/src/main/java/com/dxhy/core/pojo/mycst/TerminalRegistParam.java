package com.dxhy.core.pojo.mycst;

import lombok.Data;

/**
 * 终端注册参数
 */
@Data
public class TerminalRegistParam {
    //企业名称
    private String qymc;
    //企业税号
    private String qysh;
    //所属地区（省份）
    private String ssdq;
    //终端编号
    private String zdbh;
    //终端的类型: 2:税控盘 4:金税盘 6:税务 UKey 8： 数电 10：乐企，01托管百旺，02托管ukey
    private String zdlx;
    //复核人
    private String fhr;
    //收款人
    private String skr;
    //开票人
    private String kpr;
    //地址电话
    private String dzdh;
    //银行账号
    private String yhzh;
    //注册类型 0 试用 1正式
    private String zclx;
    //企业类型 0 企业自己开票 1 供应商开票
    private String qylx;
    //供应商登陆代码
    private String gysdm;
    //供应商登陆名称
    private String gysmc;
    //供应商开票时，下游客户的客户税号
    private String khsh;
    //配置id，由服务商进行提供
    private String configid;
    //手机号，实现电局的自动登陆
    private String sjh;
    //电子税务局账号
    private String dzswjyh;
    //电子税务局密码
    private String dzswjmm;
    //电子税务局登陆方式 0 rpa端手工输入短信 1手机卡托管模式 2，调用方式输入短信，4、二维码扫脸登陆
    private String dlfs;
    //分配的rpa的id，专属rpa时，如果需指定rpa，可以传这个内容
    private String sbid;
    //token（不属于该参数体中的数据内容）
    private String token;
    //终端id（票帮手返回信息）
    private String spid;
    //纳税人名称
    private String nsrmc;



}
