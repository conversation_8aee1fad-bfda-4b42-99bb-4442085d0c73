package com.dxhy.core.pojo.third;

import lombok.Data;

import java.io.Serializable;

@Data
public class DxPubMsg implements Serializable {
    /**
     * 压缩标识
     * 0：不压缩 1：压缩（用 Gzip 压缩）
     * 默认 0
     */
    private String zipCode="0";
    /**
     * 加密标识 0:base64 加密 1：3DES 加密 默认
     * 0
     */
    private String encryptCode="0";
    /**
     * 数据交换流
     * 水号（唯一）
     * 时间(yyyyMMddHHmmssSSS)+15 位
     * 的随机数
     */
    private String dataExchangeId;
    /**
     * 企业标识 税号
     */
    private String entCode;
    /**
     * 内层报文
     */
    private String content;
}
