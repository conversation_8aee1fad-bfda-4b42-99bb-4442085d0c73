package com.dxhy.core.pojo.vo;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Description ：渠道实体类
 */

@Getter
@Setter
@Accessors(chain = true)
public class SysProductVo {

    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品icon的图片id
     */
    private String icon;

    /**
     * 产品介绍
     */
    private String detail;

    /**
     * 授权结束时间
     */
    private Date authEtime;


}
