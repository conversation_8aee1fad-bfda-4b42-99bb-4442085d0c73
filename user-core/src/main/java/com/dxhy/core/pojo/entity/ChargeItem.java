package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 计费项
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("charge_item")
public class ChargeItem extends Model<ChargeItem> {

    private static final long serialVersionUID = 1L;

    private String id;
    /**
     * 计费项名称
     */
    private String name;
    /**
     * 计费项标准价
     */
    @TableField("normal_price")
    private String normalPrice;
    /**
     * 单位
     */
    private String unit;
    /**
     * 描述
     */
    private String describe;
    /**
     * 状态 0 正常 1 删除  2 禁用
     */
    private String status;
    /**
     * 分销商id
     */
    @TableField("distributor_id")
    private String distributorId;
    /**
     * 产品 id
     */
    @TableField("product_id")
    private String productId;
    /**
     * 产品名称
     */
    @TableField("product_name")
    private String productName;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 修改时间
     */
    @TableField("modify_time")
    private Date modifyTime;

    /**
     * 数量
     */
    @TableField(exist = false)
    private String chargeNumber;
    /**
     * 预警数量
     */
    @TableField(exist = false)
    private String warnNumber;
    /**
     * 计费项名称
     */
    @TableField(exist = false)
    private String chargeName;
    /**
     * 计费项ID
     */
    @TableField(exist = false)
    private String chargeId;
    /**
     * 套餐与计费项关联的唯一标识
     */
    @TableField(exist = false)
    private String comboChageId;

    @TableField("api_id")
    private String apiId;

    /**
     *   是否为当前套餐中的计费项，selected时是
     */
    @TableField(exist = false)
    private String selected;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
