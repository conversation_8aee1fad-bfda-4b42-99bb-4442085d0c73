package com.dxhy.core.pojo.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class SmsSessionContextVo implements Serializable {
    /**
    实体编号 (固定1)
    */
    private int entityCode=1;
    /**
    渠道编号(固定1)
    */
    private String channel="1";
    /**
    服务编号(固定FLEX0001)
    */
    private String serviceCode="FLEX0001";
    /**
    系统处理日（取当前时间） 20220726200902
     */
    private String postingDateText;
    /**
    实际入账日期（取当前时间 20220726200902
    */
    private String valueDateText;
    /**
    本地时间（取当前时间 20220726200902
    */
    private String localDateTimeText;
    /**
    交易机构编号(固定0100001)
    */
    private String transactionBranch="0100001";
    /**
    柜员编号(固定sysadmin)
    */
    private String userId="sysadmin";
    /**
    柜员密码(固定1)
    */
    private String password="1";
    /**
    授权柜员(固定sysadmin)
     */
    private String superUserId="sysadmin";
    /**
    授权柜员密码(固定1)
    */
    private String superPassword="1";
    /**
    认证通过原因(固定1)
    */
    private String authorizationReason="1";
    /**
    内部流水号(32位随机数)
    */
    private String externalReferenceNo;
    /**
    外部流水号(32位随机数)
    */
    private String userReferenceNumber;
    /**
    原始流水号(32位随机数)
    */
    private String originalReferenceNo;
    /**
    渠道推广的标志(固定1)
    */
    private String marketCode="1";
    /**
    步骤编号(固定1)
    */
    private String stepCode="1";
    /**
    访问源(固定1)
    */
    private String accessSource="1";
    /**
    访问源类型(固定1)
    */
    private String accessSourceType="1";
    /**
    风控数据(固定1)
    */
    private String riskMessage="1";

}
