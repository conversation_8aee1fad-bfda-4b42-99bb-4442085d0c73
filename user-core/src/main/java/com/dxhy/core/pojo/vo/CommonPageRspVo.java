package com.dxhy.core.pojo.vo;

import com.dxhy.core.enums.ResponseCodeEnum;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 17:222019/2/27 0027
 * @description 接口返回通用对象
 */
@Data
@ToString
public class CommonPageRspVo<T> extends CommonRspVo implements Serializable {

    /**
     * 页码
     */
    private Integer page;
    /**
     * 每页条数
     */
    private Integer rows;
    /**
     * 总条数
     */
    private long total;

    public CommonPageRspVo() {}

    /**
     *
     * @param data 数据对象
     * @param code 状态
     * @param page 页码
     * @param rows 行数
     * @param total 总数
     */
    public CommonPageRspVo(T data, ResponseCodeEnum code, Integer page, Integer rows, long total) {
        super(data, code);
        this.page = page;
        this.rows = rows;
        this.total = total;
    }

    /**
     *
     * @param data 数据对象
     * @param page 页码
     * @param rows 行数
     * @param total 总数
     * @return
     */
    public static <T> CommonPageRspVo success(T data, Integer page, Integer rows, long total) {
        CommonPageRspVo success = new CommonPageRspVo(data,ResponseCodeEnum.SUCCESS, page, rows, total);
        return success;
    }

}