package com.dxhy.core.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 机构导入Excel DTO
 *
 * <AUTHOR>
 */
@Data
public class SysDeptImportDto {

    /**
     * 组织名称
     */
    @ExcelProperty("组织名称")
    private String name;

    /**
     * 组织简称
     */
    @ExcelProperty("组织简称")
    private String deptSname;

    /**
     * 上级组织ID
     */
    @ExcelProperty("上级组织ID")
    private String parentId;

    /**
     * 纳税人税号
     */
    @ExcelProperty("纳税人税号")
    private String taxpayerCode;

    /**
     * 纳税人类型
     */
    @ExcelProperty("纳税人类型 0:小规模纳税人 1:一般纳税人")
    private String taxpayerType;

    /**
     * 纳税人地址
     */
    @ExcelProperty("纳税人地址")
    private String taxpayerAddress;

    /**
     * 纳税人电话
     */
    @ExcelProperty("纳税人电话")
    private String taxpayerPhone;

    /**
     * 纳税人银行
     */
    @ExcelProperty("纳税人银行")
    private String taxpayerBank;

    /**
     * 纳税人账号
     */
    @ExcelProperty("纳税人账号")
    private String taxpayerAccount;

    /**
     * 税号类型
     */
    @ExcelProperty("税号类型 1.独立税号 2.使用总部税号  3.无税号")
    private String eintype;

    /**
     * 部门类型，0.企业1.部门2.集团
     */
    @ExcelProperty("部门类型 1.总公司 2.子公司 3.分支机构 4.部门 5.虚拟机构 6.独立公司")
    private Integer deptType;

    /**
     * 联系人姓名
     */
    @ExcelProperty("联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @ExcelProperty("联系人电话")
    private String contactPhone;

    /**
     * 联系人邮箱
     */
    @ExcelProperty("联系人邮箱")
    private String contactEmail;
    /**
     * 联系人邮箱
     */
    @ExcelProperty("联系人地址")
    private String contactAddress;
} 