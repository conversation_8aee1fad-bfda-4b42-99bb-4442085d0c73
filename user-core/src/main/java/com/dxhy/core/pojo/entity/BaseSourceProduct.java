package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("base_source_product")
public class BaseSourceProduct extends Model<BaseSourceProduct> {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @TableId(value = "domain")
    private String domain;

    @TableField("source_id")
    private String sourceId;

    @TableField("product_id")
    private String productId;

    @TableField("product_name")
    private String productName;

    @TableField("platform_type")
    private String platformType;

    @TableField("version")
    private String version;

    @TableField("create_time")
    private Date createTime;

    @TableField("del_flag")
    private String delFlag;

    @Override
    protected Serializable pkVal() {
        return id;
    }
}
