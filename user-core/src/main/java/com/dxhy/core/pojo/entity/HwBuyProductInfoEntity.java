package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * 商品新购  过期  资源释放 资源状态变更 （目前没用）
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-04-21 10:58:16
 */
@TableName("hw_buy_product_info")
public class HwBuyProductInfoEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private Long id;
	/**
	 * 实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	private String instanceId;
	/**
	 * 客户在华为云注册账号的唯一标识
	 */
	private String customerId;
	/**
	 * 客户在华为云注册的账户名
	 */
	private String customerName;
	/**
	 * 
	 */
	private String userId;
	/**
	 * 客户名
	 */
	private String userName;
	/**
	 * 客户手机号
	 */
	private String mobilePhone;
	/**
	 * 客户邮箱
	 */
	private String email;
	/**
	 * 云商店业务ID
	 */
	private String businessId;
	/**
	 * 订单ID
	 */
	private String orderId;
	/**
	 * 产品规格标识
	 */
	private String skuCode;
	/**
	 * 产品标识
	 */
	private String productId;
	/**
	 * 是否是开通试用实例。	1：试用实例 0：非试用实
	 */
	private String trialFlag;
	/**
	 * 是否调测数据。 1：接口调测数据 不传参数：真实买家同步数据
	 */
	private String testFlag;
	/**
	 * 过期时间。格式：yyyyMMddHHmmss
	 */
	private String expireTime;
	/**
	 * 计费模式 0：表示按需购买； 1：表示包周期购买； 3：表示按次购买； 5：表示按需套餐包购买
	 */
	private String chargingMode;
	/**
	 * 扩展参数。非必填。
	 */
	private String saasExtendParams;
	/**
	 * 数量类型的商品定价属性。非必填。属性名称：数量（支持服务商自定义名称）单位：个（次）
	 */
	private String amount;
	/**
	 * 数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	 */
	private String diskSize;
	/**
	 * 数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps
	 */
	private String bandWidth;
	/**
	 * 周期类型
	 */
	private String periodType;
	/**
	 * 周期数量
	 */
	private String periodNumber;
	/**
	 * 商品实例开通方式。
	 */
	private String provisionType;
	/**
	 * 是否过期 1未过期 0过期
	 */
	private String status;
	/**
	 * 冻结状态
	 */
	private String freezeStatus;
	/**
	 * 是否商品资源释放 1未释放 0释放
	 */
	private String resourceRelease;
	/**
	 * 用户验收时间。 yyyyMMddHHmmssSSS
	 */
	private String acceptanceTime;
	/**
	 * 订单金额。
	 */
	private String orderAmount;
	/**
	 * 创建时间
	 */
	private Date createTime;

	/**
	 * 设置：
	 */
	public void setId(Long id) {
		this.id = id;
	}
	/**
	 * 获取：
	 */
	public Long getId() {
		return id;
	}
	/**
	 * 设置：实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	public void setInstanceId(String instanceId) {
		this.instanceId = instanceId;
	}
	/**
	 * 获取：实例ID，服务商提供的唯一标识，建议此ID直接使用该订单首次请求时云商店传入的businessId，以确保instanceId的唯一性
	 */
	public String getInstanceId() {
		return instanceId;
	}
	/**
	 * 设置：客户在华为云注册账号的唯一标识
	 */
	public void setCustomerId(String customerId) {
		this.customerId = customerId;
	}
	/**
	 * 获取：客户在华为云注册账号的唯一标识
	 */
	public String getCustomerId() {
		return customerId;
	}
	/**
	 * 设置：客户在华为云注册的账户名
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	/**
	 * 获取：客户在华为云注册的账户名
	 */
	public String getCustomerName() {
		return customerName;
	}
	/**
	 * 设置：
	 */
	public void setUserId(String userId) {
		this.userId = userId;
	}
	/**
	 * 获取：
	 */
	public String getUserId() {
		return userId;
	}
	/**
	 * 设置：客户名
	 */
	public void setUserName(String userName) {
		this.userName = userName;
	}
	/**
	 * 获取：客户名
	 */
	public String getUserName() {
		return userName;
	}
	/**
	 * 设置：客户手机号
	 */
	public void setMobilePhone(String mobilePhone) {
		this.mobilePhone = mobilePhone;
	}
	/**
	 * 获取：客户手机号
	 */
	public String getMobilePhone() {
		return mobilePhone;
	}
	/**
	 * 设置：客户邮箱
	 */
	public void setEmail(String email) {
		this.email = email;
	}
	/**
	 * 获取：客户邮箱
	 */
	public String getEmail() {
		return email;
	}
	/**
	 * 设置：云商店业务ID
	 */
	public void setBusinessId(String businessId) {
		this.businessId = businessId;
	}
	/**
	 * 获取：云商店业务ID
	 */
	public String getBusinessId() {
		return businessId;
	}
	/**
	 * 设置：订单ID
	 */
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	/**
	 * 获取：订单ID
	 */
	public String getOrderId() {
		return orderId;
	}
	/**
	 * 设置：产品规格标识
	 */
	public void setSkuCode(String skuCode) {
		this.skuCode = skuCode;
	}
	/**
	 * 获取：产品规格标识
	 */
	public String getSkuCode() {
		return skuCode;
	}
	/**
	 * 设置：产品标识
	 */
	public void setProductId(String productId) {
		this.productId = productId;
	}
	/**
	 * 获取：产品标识
	 */
	public String getProductId() {
		return productId;
	}
	/**
	 * 设置：是否是开通试用实例。	1：试用实例 0：非试用实
	 */
	public void setTrialFlag(String trialFlag) {
		this.trialFlag = trialFlag;
	}
	/**
	 * 获取：是否是开通试用实例。	1：试用实例 0：非试用实
	 */
	public String getTrialFlag() {
		return trialFlag;
	}
	/**
	 * 设置：是否调测数据。 1：接口调测数据 不传参数：真实买家同步数据
	 */
	public void setTestFlag(String testFlag) {
		this.testFlag = testFlag;
	}
	/**
	 * 获取：是否调测数据。 1：接口调测数据 不传参数：真实买家同步数据
	 */
	public String getTestFlag() {
		return testFlag;
	}
	/**
	 * 设置：过期时间。格式：yyyyMMddHHmmss
	 */
	public void setExpireTime(String expireTime) {
		this.expireTime = expireTime;
	}
	/**
	 * 获取：过期时间。格式：yyyyMMddHHmmss
	 */
	public String getExpireTime() {
		return expireTime;
	}
	/**
	 * 设置：计费模式 0：表示按需购买； 1：表示包周期购买； 3：表示按次购买； 5：表示按需套餐包购买
	 */
	public void setChargingMode(String chargingMode) {
		this.chargingMode = chargingMode;
	}
	/**
	 * 获取：计费模式 0：表示按需购买； 1：表示包周期购买； 3：表示按次购买； 5：表示按需套餐包购买
	 */
	public String getChargingMode() {
		return chargingMode;
	}
	/**
	 * 设置：扩展参数。非必填。
	 */
	public void setSaasExtendParams(String saasExtendParams) {
		this.saasExtendParams = saasExtendParams;
	}
	/**
	 * 获取：扩展参数。非必填。
	 */
	public String getSaasExtendParams() {
		return saasExtendParams;
	}
	/**
	 * 设置：数量类型的商品定价属性。非必填。属性名称：数量（支持服务商自定义名称）单位：个（次）
	 */
	public void setAmount(String amount) {
		this.amount = amount;
	}
	/**
	 * 获取：数量类型的商品定价属性。非必填。属性名称：数量（支持服务商自定义名称）单位：个（次）
	 */
	public String getAmount() {
		return amount;
	}
	/**
	 * 设置：数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	 */
	public void setDiskSize(String diskSize) {
		this.diskSize = diskSize;
	}
	/**
	 * 获取：数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	 */
	public String getDiskSize() {
		return diskSize;
	}
	/**
	 * 设置：数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps
	 */
	public void setBandWidth(String bandWidth) {
		this.bandWidth = bandWidth;
	}
	/**
	 * 获取：数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps
	 */
	public String getBandWidth() {
		return bandWidth;
	}
	/**
	 * 设置：周期类型
	 */
	public void setPeriodType(String periodType) {
		this.periodType = periodType;
	}
	/**
	 * 获取：周期类型
	 */
	public String getPeriodType() {
		return periodType;
	}
	/**
	 * 设置：周期数量
	 */
	public void setPeriodNumber(String periodNumber) {
		this.periodNumber = periodNumber;
	}
	/**
	 * 获取：周期数量
	 */
	public String getPeriodNumber() {
		return periodNumber;
	}
	/**
	 * 设置：商品实例开通方式。
	 */
	public void setProvisionType(String provisionType) {
		this.provisionType = provisionType;
	}
	/**
	 * 获取：商品实例开通方式。
	 */
	public String getProvisionType() {
		return provisionType;
	}
	/**
	 * 设置：是否过期 1未过期 0过期
	 */
	public void setStatus(String status) {
		this.status = status;
	}
	/**
	 * 获取：是否过期 1未过期 0过期
	 */
	public String getStatus() {
		return status;
	}
	/**
	 * 设置：冻结状态
	 */
	public void setFreezeStatus(String freezeStatus) {
		this.freezeStatus = freezeStatus;
	}
	/**
	 * 获取：冻结状态
	 */
	public String getFreezeStatus() {
		return freezeStatus;
	}
	/**
	 * 设置：是否商品资源释放 1未释放 0释放
	 */
	public void setResourceRelease(String resourceRelease) {
		this.resourceRelease = resourceRelease;
	}
	/**
	 * 获取：是否商品资源释放 1未释放 0释放
	 */
	public String getResourceRelease() {
		return resourceRelease;
	}
	/**
	 * 设置：用户验收时间。 yyyyMMddHHmmssSSS
	 */
	public void setAcceptanceTime(String acceptanceTime) {
		this.acceptanceTime = acceptanceTime;
	}
	/**
	 * 获取：用户验收时间。 yyyyMMddHHmmssSSS
	 */
	public String getAcceptanceTime() {
		return acceptanceTime;
	}
	/**
	 * 设置：订单金额。
	 */
	public void setOrderAmount(String orderAmount) {
		this.orderAmount = orderAmount;
	}
	/**
	 * 获取：订单金额。
	 */
	public String getOrderAmount() {
		return orderAmount;
	}
	/**
	 * 设置：创建时间
	 */
	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	/**
	 * 获取：创建时间
	 */
	public Date getCreateTime() {
		return createTime;
	}
}
