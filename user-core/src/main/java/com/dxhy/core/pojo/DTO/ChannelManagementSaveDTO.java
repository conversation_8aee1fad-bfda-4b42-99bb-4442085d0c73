package com.dxhy.core.pojo.DTO;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 通道管理保存DTO
 * <AUTHOR>
 * @Date 2025/01/10
 * @Version 1.0
 **/
@Data
@ApiModel("通道管理保存DTO")
public class ChannelManagementSaveDTO {

    /**
     * 主键ID（新增时为通道ID，修改时需要）
     */
    @ApiModelProperty(value = "主键ID（新增时为通道ID，修改时需要）")
    private String id;

    /**
     * 产品名称
     */
    @ApiModelProperty(value = "产品名称", required = true)
    private String productName;

    /**
     * 通道类别
     */
    @ApiModelProperty(value = "通道类别", required = true)
    private String channelType;

    /**
     * 通道名称
     */
    @ApiModelProperty(value = "通道名称", required = true)
    private String channelName;

    /**
     * 业务类别列表
     */
    @ApiModelProperty("业务类别列表")
    private List<String> businessTypeList;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty(value = "所属纳税人识别号", required = true)
    private String baseNsrsbh;

    /**
     * 状态 0-正常 1-禁用
     */
    @ApiModelProperty("状态 0-正常 1-禁用")
    private String status = "0";
} 