package com.dxhy.core.pojo.DTO;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by lenovo on 2019/3/29.
 */
@Data
public class AdminUserOperateDto {

    /**
     *  用户id
     */
    private Long userId;
    /**
     * 登陆人id
     */
    private Long puserId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 密码
     */
    private String password;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 邮箱
     */
    private String email;
    /**
     * 1.主账户  2子账户 3个人用户 4平台 5渠道 6企业
     */
    private String type;

    /**
     * 新的主账号id
     */
    private Long newUserId;
    /**
     * 所属组织ID
     */
    private String deptId;
    /**
     * 角色id
     */
    private List<Long> roleIdList;
    /**
     * 数据权限id
     */
    private List<String> deptList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 产品id
     */
    private String productId;
    /**
     * 用户来源
     */
    private String userSource;

    /**
     * 接口类型
     */
    private String interFaceType;

    /**
     * 渠道ID
     */
    private String sourceId;

    /**
     * 租户Id
     */
    private String tenantId;


    @Override
    public String toString() {
        return "AdminUserOperateDto{" +
                "userId=" + userId +
                ", puserId=" + puserId +
                ", userName='" + userName + '\'' +
                ", name='" + name + '\'' +
                ", password='" + password + '\'' +
                ", mobile='" + mobile + '\'' +
                ", email='" + email + '\'' +
                ", type='" + type + '\'' +
                ", newUserId=" + newUserId +
                ", deptId='" + deptId + '\'' +
                ", roleIdList=" + roleIdList +
                ", deptList=" + deptList +
                ", createTime=" + createTime +
                ", productId='" + productId + '\'' +
                ", userSource='" + userSource + '\'' +
                ", interFaceType='" + interFaceType + '\'' +
                ", sourceId='" + sourceId + '\'' +
                ", tenantId='" + tenantId + '\'' +
                '}';
    }
}
