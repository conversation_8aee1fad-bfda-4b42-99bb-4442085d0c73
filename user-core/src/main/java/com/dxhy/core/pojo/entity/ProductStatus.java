package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品状态表（目前没用，使用sys_tenant_product租户产品关联表）
 * @description product_status
 * <AUTHOR>
 * @date 2022-10-13
 */
@Data
public class ProductStatus implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键id
     */
    private Integer id;

    /**
     * 产品id
     */
    private String productId;

    /**
     * 分销商id
     */
    private String distributorId;

    /**
     * 产品授权开始时间
     */
    private String authStime;

    /**
     * 产品授权结束时间
     */
    private String authEtime;

    /**
     * 用户uid
     */
    private String uid;

    /**
     * 0 默认 1 下架 2 已上架 3 删除
     */
    private String status;

    /**
     * create_time
     */
    private Date createTime;

    /**
     * modify_time
     */
    private Date modifyTime;

    public ProductStatus() {}
}