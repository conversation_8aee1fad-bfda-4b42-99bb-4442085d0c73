package com.dxhy.core.pojo.DTO;

import lombok.Data;

@Data
public class AssociatedMarketDto {


	private String businessId; //流水id
	private String instanceId; //实例ID
	private String activity; //新购场景
	private String orderId; //订单ID
	private String skuCode; //产品规格标识（同一客户规格标识不变，计费方式变，冗余字段）
	private String productId; //产品标识
	private String trialToFormal; //不传参数：不是 1：是 默认不是试用转正。默认不是试用转按需。
	private String expireTime; //过期时间 格式：yyyyMMddHHmmss
	private String chargingMode; //计费模式。1：表示包周期购买 3：表示按次购买。
	private String orderAmount; //订单金额
	private String periodType; //周期类型
	private String periodNumber; //周期数量
	private String status; //是否过期 1未过期 0过期
	private String freezeStatus; //冻结状态
	private String instanceStatus;//冻结状态
	private String acceptanceTime; //验收时间
	private String resourceRelease; //是否商品资源释放 1未释放 0释放
	private java.util.Date createTime; //创建时间
	private String timeStamp; //请求发起时的时间戳，取UTC时间。格式：yyyyMMddHHmmssSSS
	private String userId;
	private String userName;
	private String testFlag;
	private String saasExtendParams;
	private String provisionType;


	private String customerId; //客户在华为云注册账号的唯一标识
	private String customerName; //客户在华为云注册的账户名
	private String mobilePhone; //客户手机号
	private String email; //客户邮箱
	private String trialFlag; //是否是开通试用实例。	1：试用实例 0：非试用实
	private String amount; //数量类型的商品定价属性。非必填。属性名称：数量（支持服务商自定义名称）单位：个（次）
	private String diskSize; //数量类型的商品定价属性。非必填。属性名称：硬盘大小（支持服务商自定义名称）单位：GB
	private String bandWidth; //数量类型的商品定价属性。非必填。属性名称：带宽（支持服务商自定义名称）单位：Mbps

}

