package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.elephant.dbcache.annotation.CacheBean;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * sys_permission
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("product_menu")
@CacheBean
public class ProductMenu extends Model<ProductMenu> {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(value = "id", type = IdType.ID_WORKER_STR)
    private String id;
    /**
     * 权限名称
     */
    private String name;
    /**
     * 权限描述
     */
    private String permission;
    private String path;
    /**
     * 授权链接
     */
    private String url;
    private String icon;
    /**
     * 请求方式
     */
    private String method;
    /**
     * 系统类型
     */
    private String systemSign;
    /**
     * 父菜单ID
     */
    @TableField("parent_id")
    private String parentId;
    /**
     * 排序值
     */
    private Integer sort;
    /**
     * 菜单类型
     */
    private String type;
    /**
     * 删除状态
     */
    private String status = "0";
    /**
     * 产品id
     */
    @TableField("product_id")
    private String productId;
    /**
     * 创建时间
     */
    @TableField("create_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime = new Date();
    /**
     * 修改时间
     */
    @TableField("modify_time")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    private Date modifyTime = new Date();

    /**
     * 产品名称
     */
    @TableField(exist = false)
    private String productName;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 更新人
     */
    private String updateBy;

    @Override
    protected Serializable pkVal() {
        return this.id;
    }

}
