package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

/**
 * 通道管理表
 * @author: system
 * @Date: 2025/01/10
 * @Version 1.0
 */
@TableName("channel_management")
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(callSuper = false)
@ApiModel("通道管理表")
public class ChannelManagement implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID（同时作为通道ID）
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty("主键ID（同时作为通道ID）")
    private String id;

    /**
     * 产品名称
     */
    @ApiModelProperty("产品名称")
    private String productName;

    /**
     * 通道类别
     */
    @ApiModelProperty("通道类别")
    private String channelType;

    /**
     * 通道名称
     */
    @ApiModelProperty("通道名称")
    private String channelName;

    /**
     * 业务类别（JSON格式存储多个业务类别）
     */
    @ApiModelProperty("业务类别")
    private String businessType;

    /**
     * 所属纳税人识别号
     */
    @ApiModelProperty("所属纳税人识别号")
    private String baseNsrsbh;

    /**
     * 状态 0-正常 1-禁用
     */
    @ApiModelProperty("状态 0-正常 1-禁用")
    private String status;

    /**
     * 逻辑删除 0-正常 1-已删除
     */
    @ApiModelProperty("逻辑删除 0-正常 1-已删除")
    private String isDelete;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String createBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

    /**
     * 更新人
     */
    @ApiModelProperty("更新人")
    private String updateBy;
} 