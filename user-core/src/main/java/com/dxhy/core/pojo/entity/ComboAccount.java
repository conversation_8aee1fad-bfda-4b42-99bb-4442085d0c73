package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("combo_account")
public class ComboAccount extends Model<ComboAccount> {

    private static final long serialVersionUID = 1L;

    private String id;
    @TableField("account_info_id")
    private String accountInfoId;
    @TableField("combo_distributor_id")
    private String comboDistributorId;
    @TableField("create_time")
    private Date createTime;
    private Integer number;
    @TableField("product_id")
    private String productId;
    @TableField("customer_id")
    private String customerId;
    /*
        单位
    */
    private String unit;
    /**
     *  状态 0 正常 1删除
     */
    private String status;

    @TableField(exist = false)
    private String productName;

    @Override
    protected Serializable pkVal() {
        return null;
    }

}
