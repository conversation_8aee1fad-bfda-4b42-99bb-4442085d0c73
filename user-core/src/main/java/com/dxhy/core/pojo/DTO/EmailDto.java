package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.PO.EmailFilePO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EmailDto implements Serializable {
    /**
     邮件主题
     */
    private String title;
    /**
     调用端ip地址
     */
    private String ipAddress;
    /**
     邮件内容
     */
    private String content;

    /**
     * 收件人邮件地址列表
     */
    private List<String> receiverList;

    /**
     * 抄送人邮件地址列表
     */
    private List<String> ccList;

    /**
     * 密送人邮件地址列表
     */
    private List<String> bccList;

    /**
     * 附件列表
     */
    private List<EmailFilePO> attachmentList;
}
