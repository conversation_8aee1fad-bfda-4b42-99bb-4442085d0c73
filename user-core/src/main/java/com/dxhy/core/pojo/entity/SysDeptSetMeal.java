/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 组织对应套餐信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-28
 */
@Data
@TableName("sys_dept_set_meal")
public class SysDeptSetMeal extends Model<SysDeptSetMeal> {

    private static final long serialVersionUID = 1L;

	@TableId(value = "id", type = IdType.AUTO)
	private Long id;


	@TableId(value = "dept_id", type = IdType.AUTO)
	private String deptId;

	/**
	 * 税号数量
	 */
	@TableId(value = "ein_number", type = IdType.AUTO)
	private Integer einNumber;

	/**
	 * 套餐id
	 */
	@TableId(value = "set_meal_id", type = IdType.AUTO)
	private Long setMealId;

	@Override
	protected Serializable pkVal() {
		return this.id;
	}

	@Override
	public String toString() {
		return "SysDeptSetMeal{" +
				", deptId=" + deptId +
				", einNumber=" + einNumber +
				", setMealId=" + setMealId +
				"}";
	}


}
