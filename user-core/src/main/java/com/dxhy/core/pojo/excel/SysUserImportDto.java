package com.dxhy.core.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 用户导入Excel DTO
 *
 * <AUTHOR>
 */
@Data
public class SysUserImportDto {

    /**
     * 用户名
     */
    @ExcelProperty("用户名")
    private String userName;

    /**
     * 姓名
     */
    @ExcelProperty("姓名")
    private String name;

    /**
     * 手机号
     */
    @ExcelProperty("手机号")
    private String mobile;

    /**
     * 邮箱
     */
    @ExcelProperty("邮箱")
    private String email;

    /**
     * 所属部门ID
     */
    @ExcelProperty("所属部门ID")
    private String deptId;

    /**
     * 所属部门名称
     */
    /*@ExcelProperty("所属部门名称")
    private String deptName;*/

    /**
     * 所属部门ID
     */
    @ExcelProperty("数据权限集合(部门ID英文逗号分隔)")
    private String deptIds;

    /**
     * 角色ID（多个用逗号分隔）
     */
    @ExcelProperty("角色集合(角色ID英文逗号分隔)")
    private String roleIds;

    /**
     * 角色名称（多个用逗号分隔）
     */
    /*@ExcelProperty("角色名称")
    private String roleNames;*/

    /**
     * 备注
     */
    @ExcelProperty("备注")
    private String remark;
}