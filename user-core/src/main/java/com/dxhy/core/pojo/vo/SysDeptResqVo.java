package com.dxhy.core.pojo.vo;

import com.dxhy.core.pojo.entity.SysDept;
import com.dxhy.core.pojo.entity.SysTenantProduct;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @title: SysDeptResqVo
 * @projectName user_base
 * @description: 添加或修改组织VO
 * @date 2020-03-3011:20
 */
@Data
public class SysDeptResqVo implements Serializable {


    private static final long serialVersionUID = -1989300447227570964L;
    /**
     * 添加组织信息
     */
    private SysDept apiDeptEntity;

    /**
     * 授权产品信息
     */
    private List<SysTenantProduct> sysTenantProductList;

    private String sksbbm;
    private String sksbmc;

    private String aceId;
    private String aceKey;
}
