package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.activerecord.Model;
import com.baomidou.mybatisplus.annotations.TableField;
import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.annotations.TableName;
import com.baomidou.mybatisplus.enums.IdType;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("base_source_user")
public class BaseSourceUser extends Model<BaseSourceUser> {

    @TableId(value = "user_id", type = IdType.INPUT)
    private String userId;

    @TableId(value = "source_id", type = IdType.INPUT)
    private String sourceId;

    /**
     *  类型：1-注册;2-登录
     */
    @TableId(type = IdType.INPUT)
    private int type;

    @TableField("create_time")
    private Date createTime;

    @TableField("activity_id")
    private String activityId;


    @Override
    protected Serializable pkVal() {
        return userId + sourceId + type;
    }
}
