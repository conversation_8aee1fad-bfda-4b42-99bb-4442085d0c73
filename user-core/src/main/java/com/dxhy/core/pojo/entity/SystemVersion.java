package com.dxhy.core.pojo.entity;

import com.baomidou.mybatisplus.annotations.TableId;
import com.baomidou.mybatisplus.enums.IdType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description system_version
 * <AUTHOR>
 * @date 2022-08-22
 */
@Data
public class SystemVersion implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * id
     */
    private Integer id;

    /**
     * 父级Id，根节点为-1
     */
    private Integer parentId;

    /**
     * 版本号
     */
    private String versionNum;

    /**
     * 文本数字前缀
     */
    private String releaseDate;

    /**
     * 文本说明
     */
    private String prefix;

    /**
     * 文本说明
     */
    private String text;

    /**
     * 数字前缀与文本说明分隔符 默认"."
     */
    private String connector;

    /**
     * create_time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTime;

    /**
     * update_time
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;

}