package com.dxhy.core.pojo.DTO;

import com.dxhy.core.pojo.entity.SysMenu;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by lenovo on 2019/4/1.
 */
@Data
public class AdminRoleInfoDto {
    private Long roleId;
    /**
     * 角色名称
     */
    private String roleName;
    /**
     * 备注
     */
    private String describe;
    /**
     * 是否为共享角色0：否,1:是
     */
    private Integer type;
    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改人
     */
    private String deptId;
    /**
     * 修改时间
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date updateTime;

    /**
     * 菜单权限
     */
    private List<SysMenu> menusList;


    /**
     * 1为中台、2为企业
     */
    private String roleProperty;

    /**
     * 角色类型 0后台超级管理员10：超级管理员11：管理员12：其他
     */
    private Integer roleType;
}
