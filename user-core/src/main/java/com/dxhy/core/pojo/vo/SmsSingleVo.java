package com.dxhy.core.pojo.vo;

import lombok.Data;

import java.io.Serializable;

@Data
public class SmsSingleVo implements Serializable  {
    /**
    请求上下 (参考上方通用报文格式)
     */
    private SmsSessionContextVo sessionContext;
    /**
    平台编号
     */
    private String platformNo;
    /**
    平台密钥
    */
    private String platformKey;
    /**
    短信接收方
    */
    private String phone;
    /**
    调用端ip地址
    */
    private String ipAddress;
    /**
    短信内容
    */
    private String content;
    /**
    短信类型(1：验证码 2：通知  5:语音验证码)
    */
    private int msgType;
    /**
    渠道
    */
    private String channel;


}
