/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.common.util.Query;
import com.dxhy.core.pojo.DTO.AdminRoleOperateDto;
import com.dxhy.core.pojo.entity.SysRole;
import com.dxhy.core.pojo.entity.SysRoleMenuEntity;
import com.dxhy.core.pojo.vo.SysRoleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
  *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysRoleMapper extends BaseMapper<SysRole> {

    /**
     * 查询角色列表含有部门信息
     * @param query 查询对象
     * @param condition 条件
     * @return List
     */
    List<Object> selectRolePage(Query<Object> query, Map<String, Object> condition);

    /**
     * 通过部门ID查询角色列表
     *
     * @param deptId 部门ID
     * @return 角色列表
     */
    List<SysRole> selectListByDeptId(String deptId);

	/**
	 * 通过部门ID查询角色列表
	 *
	 * @param deptId 部门ID
	 * @return 角色列表
	 */
	List<SysRole> selectRolesByDeptId(@Param("deptId") String deptId);
    /**
     * 通过企业ID和角色名称查询列表
     * @Methods:getEntRoleList
     * <AUTHOR>
     * @date 2019年3月13日下午2:03:40
     * @param entId
     * @param roleName
     * @return
     */
	List<SysRoleVo> getEntRoleList(@Param("entId") String entId, @Param("roleName") String roleName);
	/**
	 * 查询名称是否存在
	 * @Methods:getRoleName
	 * <AUTHOR>
	 * @date 2019年3月19日下午5:05:15
	 * @param entId
	 * @param roleName
	 * @return
	 */
	List<SysRoleVo> getRoleName(@Param("entId") String entId,@Param("roleName") String roleName);

	/**
	 * 通过企业ID和用户id查询角色列表
	 * @Methods:getEntRoleList
	 * <AUTHOR>
	 * @date 2019年3月13日下午2:03:40
	 * @param entId
	 * @param userId
	 * @return
	 */
	List<SysRoleVo> getUserAndEntRoleList(@Param("entId") String entId,@Param("userId") String userId);

	/**
	 * 查询部门下角色名称的总记录数
	 * @param deptId
	 * @return
	 */
	int queryRoleNameCountByDeptId(@Param("deptId")String deptId , @Param("roleName")String roleName);


	/**
	*@author:cdl
	*@description:分页查询组织下的角色信息
	*@date:2020/3/30
	*/
    List<SysRoleVo> queryRoleListByDeptId(Page page,@Param("deptId") String deptId,@Param("roleName") String roleName,@Param("loginUserId") Long loginUserId);

    /**
     * 角色id查询用户的个数
     * @param roleId
     * @return int
     */
	int queryRoleOfUserCountByRoleId(Long roleId);

	/**
	 * 角色id查询详情
	 * @param roleId
	 * @return com.dxhy.heaven.admin.api.vo.SysRoleVo
	 */
	SysRoleVo selectByRoleId(Long roleId);

	/**
	 * 角色id查询详情
	 * @param roleId
	 * @return
	 */
	SysRole selectByRoleId2(Long roleId);

	/**
	 * 组织ID查询对应角色信息
	 * @param deptId
	 * @return
	 */
	List<SysRoleVo> selectByDeptId(@Param("deptId") String deptId);

	/**
	 * 角色id查询菜单list
	 * @param roleId
	 * @return java.util.List<java.lang.Integer>
	 */
	List<String> queryMenuIdListByRoleId(Long roleId);


	/**
	 * 根据id更新角色信息
	 * @param adminRoleOperateDto
	 * @return void
	 */
	void updateColumnById(AdminRoleOperateDto adminRoleOperateDto);

	/**
	 * 角色id删除关联的菜单
	 * @param roleId
	 * @return void
	 */
	void deleteRoleMenuByRoleId(Long roleId);

	/**
	 *
	 * @param sysRoleMenuEntity
	 * @return void
	 */
	void addRoleMenuRelation(SysRoleMenuEntity sysRoleMenuEntity);

	/**
	 * 添加角色
	 * @param adminRoleOperateDto
	 * @return void
	 */
	void addRole(AdminRoleOperateDto adminRoleOperateDto);

	/**
	 * 根据角色id 查询关联的用户
	 * @param roleId
	 * @return java.util.List<java.lang.Integer>
	 */
	List<Long> queryUserIdListByRoleId(Long roleId);




    /**
     * 添加角色与部门之间的关系
     * @param roleId
     * @param deptId
     * @return void
     */
	void addRoleDeptRelation(@Param("roleId")Long roleId, @Param("deptId")String deptId);

	/**
	 * 用户id查询角色列表
	 * @param userId
	 * @return java.util.List<com.dxhy.heaven.admin.api.vo.SysRoleVo>
	 */
	List<SysRole> selectRolesByUserId(Long userId);

	/**
	 * 用户id 组织id 查询角色id
	 * @param userId
	 * @param deptId
	 * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysRole>
	 */
    List<SysRole> selectRolesByUserIdAndDeptId(@Param("userId")Long userId, @Param("deptId")String deptId);

    /**
     * 根据角色id删除角色与组织之间的关联关系
     * @param roleId
     * @return void
     */
    void deleteRoleDeptRelation(Long roleId);

    /**
     * 根据组织id 角色名称查询角色信息
     * @param deptId
     * @param roleName
     * @return com.dxhy.heaven.admin.api.entity.SysRole
     */
    SysRole selectRoleByDeptIdAndRoleName(@Param("deptId")String deptId, @Param("roleName")String roleName);
    /**
     * 查询最后一个企业编码
     * @param
     * @return java.lang.String
     */
    String selectLastRoleCode();

    /**
     * 根据用户id  查询管理员信息
     * @param puserId
     * @return com.dxhy.heaven.admin.api.entity.SysRole
     */
    SysRole selectRoleByUserId(Long puserId);

	/**
	 * 查找平台侧所有角色
	 * @return
	 */
	List<SysRoleVo>  queryListAll();

	SysRole selectSmRolesByUserId(Long smUserId);

    List<SysRole> getSuperRoleListByCompanyName(String name);

	List<SysRole> queryRoleList(@Param("roleType")String roleType, @Param("roleName")String roleName, @Param("tenantId")String tenantId);
}