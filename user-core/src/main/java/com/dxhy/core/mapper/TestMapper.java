package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.PO.TempTablePO;
import org.apache.ibatis.annotations.Mapper;

/**
 * @author: zhangjinjing
 * @Date: 2022/5/5 17:39
 * @Version 1.0
 */
@Mapper
public interface TestMapper extends BaseMapper<TempTablePO> {

    /**
     * 测试
     * @param id
     * @return java.lang.String
     * <AUTHOR>
     **/
    TempTablePO getById(Integer id);

//    /**
//     * 测试分页
//     * @param page 分页参数
//     * @return java.util.List<com.dxhy.core.pojo.PO.TempTablePO>
//     * <AUTHOR>
//     **/
//    List<TempTablePO> selectAll(Page<TempTablePO> page);


}
