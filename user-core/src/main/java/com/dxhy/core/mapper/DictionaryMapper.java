package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.Dictionary;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-05-27
 */
public interface DictionaryMapper extends BaseMapper<Dictionary> {

    /**
     * 获取树状结构
     * @param parent
     * @return
     */
    List<Dictionary> getTree(String parent);

    Dictionary getByFlag(String flag);

    List<Dictionary> selectDictionary(@Param("id") String id, @Param("code") String code,
                                @Param("name") String name, @Param("desc") String desc,
                                @Param("parent") String parent, @Param("flag") String flag);
    Long selectMaxId();
}
