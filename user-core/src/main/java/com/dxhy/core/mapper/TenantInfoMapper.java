package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.hw.TenantInfoEntity;
import com.dxhy.core.pojo.hw.TenantTokenVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-07
 */
@Mapper
public interface TenantInfoMapper extends BaseMapper<TenantInfoEntity> {

    TenantTokenVo getAccessTokenParam(@Param("tenantId") String tenantId);

    TenantInfoEntity getAppKeyAndSecret(@Param("taxNo") String taxNo);

    TenantInfoEntity getSecretKey(@Param("secretId") String secretId);
}
