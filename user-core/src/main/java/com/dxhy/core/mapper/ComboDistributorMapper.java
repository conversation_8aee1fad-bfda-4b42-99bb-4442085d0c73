package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.ComboDistributor;
import com.dxhy.core.pojo.entity.ComboInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-04-24
 */
public interface ComboDistributorMapper extends BaseMapper<ComboDistributor> {

    /**
     * 获取产品套餐
     * @param productId
     * @return
     */
    List<ComboInfo> selectCombos(String productId);

    /**
     *  查询分销商配置的结算模板信息
     * @param
     * @return
     */
    List<ComboDistributor> getComboDistributorByproductId(Map<String,Object> params);

    ComboDistributor getComboDistByProductId(@Param("productId") String productId,@Param("distributorId") String distributorId,@Param("sellLabelId") String sellLabelId);

    List<String> getChargeItmesByComboTemplateId(@Param("templateId") String templateId);

    String getSellLabelIdByComboDistributorId(@Param("comboDistributorId") String comboDistributorId);

    List<Map> getProductIdAndSellLabelIdByDistributorId(@Param("distributorId") String distributorId);
}
