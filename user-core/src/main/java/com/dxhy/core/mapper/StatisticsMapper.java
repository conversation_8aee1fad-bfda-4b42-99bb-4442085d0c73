/**
 * Copyright 2021-2030 qixiangyun.com All right reserved. This software is the confidential and proprietary information
 * of qixiangyun.com ("Confidential Information"). You shall not disclose such Confidential Information and shall use it
 * only in accordance with the terms of the license agreement you entered into with qixiangyun.com.
 */
package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.pojo.DTO.StatisticsDto;
import com.dxhy.core.pojo.vo.StatisticsRzVo;
import com.dxhy.core.pojo.vo.StatisticsVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/9 16:23
 */
@Repository
public interface StatisticsMapper {


	List<StatisticsDto> cylist(Page<StatisticsDto> page, @Param("vo") StatisticsVo vo);

	List<StatisticsDto> rzlist(Page<StatisticsDto> page, @Param("vo") StatisticsRzVo vo);

	List<StatisticsDto> kplist(Page<StatisticsDto> page, @Param("vo") StatisticsVo vo);

	List<String> selectDept(@Param("tenantIdList") List<String> tenantIdList);

}
