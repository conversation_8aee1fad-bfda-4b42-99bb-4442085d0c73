package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.SystemVersion;
import com.dxhy.core.pojo.vo.SysVersionVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2022-08-22
 */
public interface SystemVersionMapper extends BaseMapper<SystemVersion> {


    List<SystemVersion> queryHistoryVersionList(@Param("curFlag") int curFlag);

    List<SysVersionVo> selectRootVersionList();
}
