/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.pojo.entity.ProductMenuTree;
import com.dxhy.core.pojo.entity.SysMenu;
import com.dxhy.core.pojo.entity.SysProduct;
import com.dxhy.core.pojo.vo.MenuTreeVO;
import com.dxhy.core.pojo.vo.MenuVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <p>
 * 菜单权限表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysMenuMapper extends BaseMapper<SysMenu> {

    /**
     * 通过角色名查询菜单
     *
     * @param role 角色名称
     * @return 菜单列表
     */
    List<SysMenu> findMenuByRoleName(@Param("role") String role);

    /**
     *  通过用户ID+企业ID获取 权限
     * @param userId
     * @param entId
     * @return 菜单列表
     */
    Set<MenuVO> findMenuByEntUser(@Param("userId") Long userId, @Param("entId") String entId);
    /**
     * 查询角色已勾线菜单树
     * @Methods:getMenuList
     * <AUTHOR>
     * @date 2019年3月14日下午2:38:23
     * @param entId
     * @param roleId
     * @return
     */
	List<MenuTreeVO> getMenuList(@Param("entId")String entId, @Param("roleId") String roleId);

    /**
     * 通过用户ID+企业ID获取 开通的产品
     * @param userId
     * @param entId
     * @return
     */
    Set<MenuVO> findProductByEntUser(@Param("userId") Long userId, @Param("entId") String entId);


    /**
     * 通过角色id获取菜单列表
     * @param roleId
     * @return
     */
    List<MenuTreeVO> getMenuListByRoleId(@Param("roleId") Long roleId);


    /**
     * 通过角色id获取产品菜单列表
     * @param roleId
     * @return
     */
    List<MenuTreeVO> getProductByRoleId(@Param("roleId") Long roleId);

    /**
     *  通过用户ID+渠道id 获取菜单列表
     * @param roleId
     * @param sourceId
     * @return 菜单列表
     */
    Set<MenuVO> findMenuByUserIdAndSourceId(@Param("roleId") Long roleId, @Param("sourceId") String sourceId);


    /**
     * 渠道id 获取菜单列表
     * @param sourceId
     * @return 菜单列表
     */
    Set<MenuVO> findMenuBySourceId(@Param("sourceId") String sourceId);

    /**
     * 获取菜单列表中最大的主键id
     * @return
     */
    int findMaxMenuId();

    /**
     * 通过菜单id获取菜单详情
     * @param menuId
     * @return 菜单详情
     */
    MenuVO getMenuVoById(@Param("menuId") Integer menuId);


    /**
     * 通过角色名称以及版本号，渠道查询URL 权限
     *
     * @param role 角色名称
     * @return 菜单列表
     */
    List<SysMenu> findMenuByRoleNameAndVersion(@Param("role") String role,@Param("version") String version,@Param("sourceId") String sourceId);

    /**
     * 通过角色名称以及版本号，渠道查询URL 权限
     *
     * @param
     * @return 菜单列表
     */
    Set<SysMenu> findMenuByEntUserAndVersion(@Param("userId") Long userId,@Param("entId") String entId,@Param("version") String version,@Param("sourceId") String sourceId);

    /**
     * 根据角色id查询所有的菜单
     * @param roleId
     * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysMenu>
     */
    List<SysMenu> selectMenusByRoleId(Long roleId);

    /**
     * 组织id查询所有关联菜单
     * @param deptId
     * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysMenu>
     */
    List<SysMenu> queryMenusByDeptId(@Param("deptId")  String deptId);

    /**
     * 用户id查询菜单
     * @param userId
     * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysMenu>
     */
    List<String> selectMenusByUserId(Long userId);

    /**
     * 查询所有的一级菜单
     * @param
     * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysMenu>
     */
    List<SysMenu> querySysMenus();

    /**
     * 查询所有菜单列表
     * @return
     */
    List<SysMenu> selectMenusAll();

    /**
     * 查询所有系统级菜单
     * @return
     */
    List<SysMenu> queryMenusSystem();


    /**
     * 根据渠道ID查询菜单权限
     * @param id
     * @return
     */
    List<SysMenu> queryMenuByDistributorId(@Param("id") String id);



    /**
     * 根据用户id 组织id 查询菜单
     * @param userId
     * @param deptId
     * @return java.util.List<java.lang.String>
     */
    List<String> selectMenusByUserIdAndDeptId(@Param("userId") Long userId,@Param("deptId") String deptId);

    /**
     * 菜单id查询菜单信息
     * @param MenuId
     * @return com.dxhy.heaven.admin.api.entity.SysMenu
     */
    SysMenu selectMenuByMenuId(String MenuId);

    /**
     * 新增根据菜单ID查询对应产品菜单信息
     * @param id
     * @return
     */
    ProductMenuTree selectProductMenuByMenuId(@Param("id") String id);

    /**
     * 产品id查询产品信息
     * @param id
     * @return
     */
    SysProduct selectSysProductByMenuId(@Param("id") String id);

    List<SysMenu> listMenusByParentMenuId(Page page, String menuId);

    int queryMenuNameCount(String name);

    int querySonMenuCountByMenuId(String menuId);

    void deleteRoleMenuByMenuId(String menuId);

    void insertRoleMenu(@Param("roleId") Long roleId, @Param("menuId")String menuId);

    /**
     * 根据系统标识查询对应系统级菜单
     * @param systemSign
     * @return
     */
    List<SysMenu> queryMenusBySystemSign(@Param("systemSign") String systemSign);


    /**
     * 根据渠道ID查询对应产品list
     * @param deptId
     * @return
     */
    List<String> queryProductsByDistributorId(@Param("deptId") String deptId);
}