package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.Distributor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-07
 */
@Mapper
public interface DistributorMapper extends BaseMapper<Distributor> {

	/**
	 * 根据ID查询渠道信息
	 * @param deptId
	 * @return
	 */
	Distributor selectDistributorById(@Param("deptId") String deptId);


	/**
	 * 根据渠道简码查询对应渠道信息
	 * @param simpleCode
	 * @return
	 */
	Distributor selectDistributorBySimpleCode(@Param("simpleCode") String simpleCode);

	/**
	 * 查询所有子级渠道信息
	 * @return
	 */
	List<Distributor>  selectAllDistributor();

	/**
	 * 查询下一级渠道信息
	 * @param deptId
	 * @return
	 */
	List<Distributor> selectNextDistributor(@Param("superior") String deptId);

	/**
	 * 按照名称模糊搜索渠道信息
	 * @param deptName
	 * @return
	 */
	List<Map<String,Object>>  queryAllDistributorByName(@Param("deptName") String deptName);

    String getNextSimpleCode();

	Distributor selectDistributorByCompanyName(@Param("companyName") String companyName);
}
