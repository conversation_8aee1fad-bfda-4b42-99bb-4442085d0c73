package com.dxhy.core.mapper;

import com.dxhy.core.pojo.vo.RegionParent;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 地区维护数据访问层
 * @date 2019-02-25 11:53
 */
public interface SysRegionMapper {


    /**
     * 查询所有启用的所属地区信息
     *
     * @param isdel 删除状态
     * @return 所属地区键值对
     */
    List<RegionParent> selectAllArea(@Param("isdel") Integer isdel);


}
