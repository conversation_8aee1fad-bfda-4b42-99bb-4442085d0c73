package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.ProductMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * sys_permission Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-04-01
 */
public interface ProductMenuMapper extends BaseMapper<ProductMenu> {

    List<ProductMenu> getProductMenuByProductIdAndSellLabelId(@Param("productId") String productId, @Param("sellLabelId")String sellLabelId);

    List<ProductMenu> getProMenuListByDeptId(@Param("deptId")String deptId,@Param("roleType")Integer roleType);

    ProductMenu selectMenuId(@Param("name")String name, @Param("systemSign")String systemSign);

    List<String> getMenuIdListByProId(@Param("list") List<Long> productList);
}
