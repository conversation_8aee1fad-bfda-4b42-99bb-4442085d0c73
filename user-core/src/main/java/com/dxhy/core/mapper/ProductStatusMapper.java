package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.ProductStatus;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description product_status
 * <AUTHOR>
 * @date 2022-10-13
 */
public interface ProductStatusMapper extends BaseMapper<ProductStatus> {

    int addUserProduct(ProductStatus productStatus);

    List<ProductStatus> getProductListByDistrAndProId(@Param("distributorId") String distributorId,@Param("list") List<String> proList);
}