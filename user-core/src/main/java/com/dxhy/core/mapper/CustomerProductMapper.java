package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.CustomerProduct;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-06
 */
public interface CustomerProductMapper extends BaseMapper<CustomerProduct> {

    /**
     * 获取产品开通状态
     * @param accountId
     * @param companyId
     * @param productIds
     * @return
     */
    List<CustomerProduct> selectOpenstatus(@Param("accountId") String accountId, @Param("companyId") String companyId, @Param("productIds") String[] productIds);

    String getProductId(@Param("id") String id);

    String getSellLabelId(@Param("comboDistributorId") String comboDistributorId);

    boolean updateCustomerProduct(@Param("accountInfoId") String accountInfoId, @Param("customerId")String customerId,@Param("productId") String productId,@Param("total") String total);

    List<CustomerProduct> getCustomerProductByDis(@Param("distributorId") String distributorId,@Param("productId") String productId);


    Boolean updateProductStatus(@Param("accountInfoId")String accountInfoId, @Param("productId")String productId, @Param("customerId")String customerId, @Param("status")int status);

    int updateCustomerProductTotal(@Param("accountInfoId") String accountInfoId, @Param("customerId")String customerId,@Param("productId") String productId,@Param("total") String total);

    String getProductIdByComboId(@Param("comboDistributorId") String comboDistributorId);

    List<CustomerProduct> getCustomerProductByUserId(@Param("userId")  Long userId);

    boolean insertRoleMenuRelation(@Param("deptId") String deptId);
}
