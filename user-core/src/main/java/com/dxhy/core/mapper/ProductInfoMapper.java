package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.ProductInfo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-03-08
 */
public interface ProductInfoMapper extends BaseMapper<ProductInfo> {
    /**
     * 分页查询产品列表信息
     * @return
     */
    List<ProductInfo> selectProductList(Map map);
    /**
     * 分页总条数
     * @param retSub
     * @param retParent
     * @param distributorId
     * @param productName
     * @param status
     * @param sign
     * @return
     */
    Integer selectProductCount(@Param("retSub") List<String> retSub,
                               @Param("retParent")List<String> retParent,
                               @Param("distributorId") String distributorId,
                               @Param("productName") String productName,
                               @Param("status") String status,
                               @Param("sign") String sign);

    /**
     * 根据分销商id列表查询产品列表
     * @param distributorId
     * @param status
     * @return
     */
    List<ProductInfo> selectListByDistributorId(@Param("distributorId") String distributorId, @Param("status") String status,@Param("currentDistributorId") String currentDistributorId,@Param("productName") String productName,@Param("productClass") String productClass);
    /**
     * 根据产品主键id列表查询产品列表
     * @param productIdList
     * @return
     */
    List<ProductInfo> selectListByProductId(@Param("productIdList")List<String> productIdList, @Param("status")String status);
    /**
     * 分支机构查询平台产品
     * @param params
     * @return
     */
    List<ProductInfo> selectProductInfoList(Map<String, Object> params);
    /**
     * 根据产品信息获取产品的id
     * @param id
     * @return
     */
    ProductInfo selectProductInfoById(@Param("id") String id);

    /**
     *  通过用户查询产品状态，1：正式，2：试用
     * @param accountInfoId
     * @param productId
     * @return
     */
    int selectCustomerProductTypeByUser(@Param("accountInfoId") String accountInfoId, @Param("productId") String productId);

    /**
     *  通过企业ID查询企业信息
     * @param deptId
     * @return
     */
    Map selectSysDeptByDeptId(@Param("deptId") String deptId);

    String selectTaxControlByDeptId(@Param("deptId") String deptId,@Param("type") int type);
}
