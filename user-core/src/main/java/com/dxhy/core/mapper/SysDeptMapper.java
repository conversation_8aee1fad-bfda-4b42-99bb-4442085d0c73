/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.pagination.Pagination;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 部门管理 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-01-20
 */
public interface SysDeptMapper extends BaseMapper<SysDept> {

    /**
     * 查询自己及以下组织
     * @param code
     * @param isContainBm
     * @return
     */
    List<SysDept> listMyselfAll(@Param("code") String code, @Param("isContainBm") boolean isContainBm,@Param("isDxAdmin") boolean isDxAdmin,@Param("isAdmin") boolean isAdmin);

    /**
     * 查询自己级别以下组织
     * @param code
     * @param isContainBm
     * @param level
     * @return
     */
    List<SysDept> listUnlessMyselfAll(@Param("code") String code, @Param("isContainBm") boolean isContainBm,@Param("level") Integer level);

    /**
     * 计算每个组织用户数
     * @param code
     * @return
     */
    Integer selectAllUserCount(@Param("code") String code);

    /**
     * 查询自己下一级组织
     * @param page
     * @param deptId
     * @param isContainBm
     * @return
     */
    List<SysDeptVo> listMyselfOneLevel(Pagination page, @Param("deptId") String deptId, @Param("isContainBm") boolean isContainBm,@Param("entName") String entName,@Param("nsrsbh") String nsrsbh);

    /**
     * 查询自己下一级组织及人员组成tree结构
     * @param deptId
     * @return
     */
    List<CommonTreeVo> listOneLevelBmAndUserDept(@Param("deptId") String deptId);

    /**
     * 查询自己下一级组织及人员组成tree结构
     * @param deptId
     * @return
     */
    List<CommonTreeVo> listOneLevelBmAndUserParent(@Param("deptId") String deptId);

    /**
     * 查询自己下所有组织及人员组成tree结构
     * @param code
     * @return
     */
    List<CommonTreeVo> listMyselfAllBmAndUserDept(@Param("code") String code);

    /**
     * 查询自己下所有组织及人员组成tree结构
     * @param code
     * @return
     */
    List<CommonTreeVo> listMyselfAllBmAndUserParent(@Param("code") String code);

    /**
     * 查询自己下所有组织及角色组成tree结构
     * @param code
     * @return
     */
    List<CommonTreeVo> listMyselfAllBmAndRoleDept(@Param("code") String code);

    /**
     * 查询自己下所有组织及角色组成tree结构
     * @param code
     * @return
     */
    List<CommonTreeVo> listMyselfAllBmAndRoleParent(@Param("code") String code);

    /**
     * 根据code查询对应组织信息
     * @param code
     * @return
     */
    SysDept selectByCode(@Param("code") String code);

    /**
     * 根据组织名称和税号查询组织信息
     * @param name
     * @param taxpayerCode
     * @return
     */
    SysDept queryDeptByTaxpayerNameAndCode(@Param("name") String name,@Param("taxpayerCode") String taxpayerCode);

    /**
     * 根据税号查询对应的企业信息
     * @param taxpayerCode
     * @return
     */
    List<SysDept> queryDeptByTaxpayerCode(@Param("taxpayerCode") String taxpayerCode);


    /**
     * 根据纳税人名称查询对应的企业信息
     * @param name
     * @return
     */
    List<SysDept> queryDeptLikeByName(@Param("name") String name);

    /**
     * 根据纳税人名称查询对应的企业信息
     * @param name
     * @return
     */
    List<SysDept> queryDeptByName(@Param("name") String name);
    /**
     * 根据地区code查询对应地区信息
     * @return
     */
    AddressVo  getAddressByCode(@Param("addressCode") String addressCode);

    /**
     * 注册地址下拉列表项
     * @param addressCode
     * @param type
     * @return
     */
    List<AddressVo> getAddressCodeBySf(@Param("addressCode") String addressCode, @Param("type") String type);
    /**
     * 注册地址下拉列表项
     * @param addressCode
     * @param type
     * @return
     */
    List<AddressVo> getAddressCodeBySq(@Param("addressCode") String addressCode,@Param("type") String type);

    /**
     * 注册地址获取直辖市
     * @param addressCode
     * @param type
     * @return
     */
    List<AddressVo> getAddressCodeByZxs(@Param("addressCode") String addressCode,@Param("type") String type);

    /**
     * getAddressCodeByZxsSsq
     * @param addressCode
     * @param type
     * @return
     */
    List<AddressVo> getAddressCodeByZxsSsq(@Param("addressCode") String addressCode,@Param("type") String type);



    /**
     * 注册地址下拉列表项
     * @param addressCode
     * @param type
     * @return
     */
    List<AddressVo> getAddressCodeByXq(@Param("addressCode") String addressCode,@Param("type") String type);

    /**
     * 获取行业编码
     * @return
     */
    List<IndustryVo> getIndustry();

    /**
     * 根据行业名称查询对应行业code
     * @param name
     * @return
     */
    IndustryVo selectIndustryByName(@Param("name") String name);

    /**
     * 根据行业Code查询对应行业名称
     * @param code
     * @return
     */
    IndustryVo selectIndustryByCode(@Param("code") String code);

    /**
     * 企业准则编码
     * @return
     */
    List<AccountantVo> getAccountant();

    /**
     * 根据企业名称查询对应行业code
     * @return
     */
    AccountantVo selectAccountantByName(@Param("name") String name);

    /**
     * 按照组织ID查询对应的套餐信息
     * @param deptId
     * @return
     */
    SetMealVo querySetMeal(@Param("deptId") String deptId);

    /**
     * 根据组织id查询对应的套餐信息
     * @param deptId
     * @return
     */
    SysDeptSetMeal getSetMeal(@Param("deptId") String deptId);

    /**
     * 根据组织id和税号数量更新对应企业下套餐信息
     * @param einNumber
     * @param deptId
     * @return
     */
    Integer updateDeptSetMeal(Integer einNumber ,String deptId);

    /**
     * 更新分销商删除标记
     * @param delFlag
     * @param id
     * @return
     */
    Integer updateDistributor(@Param("delFlag") Integer delFlag,@Param("id") String id);


    /**
     * 更新企业禁用启用状态
     * @param status
     * @param deptId
     * @return
     */
    Integer updateDeptStatus(@Param("status") Integer status,@Param("deptId") String deptId);

    /**
     * 根据组织ID删除对应权限ID
     * @param deptId
     * @return
     */
    Integer deleteMenuByDeptId(@Param("deptId") String deptId);

    /**
     * 菜单menu插入
     * @param sysDeptMenu
     * @return
     */
    Integer insertSysDeptMenu(SysDeptMenu sysDeptMenu);

    /**
     * 组织和角色关联插入
     * @param sysRoleDept
     * @return
     */
    Integer insertSysdeptRole(SysRoleDept sysRoleDept);

    /**
     * 关联dept——relation
     *
     * @param delFlag 删除标记
     * @return 数据列表
     */
    List<SysDept> selectDeptDtoList(String delFlag);

    /**
     * 删除部门关系表数据
     * @param id 部门ID
     */
    void deleteDeptRealtion(Integer id);

    /**
     * 根据企业授权码查询对应企业信息
     * @param authorizationCode
     * @return
     */
    SysDept queryDeptBySqm(@Param("authorizationCode") String authorizationCode);

    /**
     * 根据企业编码查询对应企业信息
     * @param enterpriseNumbers
     * @return
     */
    SysDept queryDeptByQybm(@Param("enterpriseNumbers") String enterpriseNumbers);

    /**
     * 重写查询条数方法
     * @param deptId
     * @param name
     * @param code
     * @param deptSname
     * @param parentId
     * @param taxpayerCode
     * @return
     */
    int queryDeptCount(@Param("deptId")  String deptId,@Param("name") String name,@Param("code") String code,@Param("deptSname")  String deptSname,@Param("parentId") Long parentId,@Param("taxpayerCode") String taxpayerCode);



    /**
     * 根据组织ID查询对应的用户信息
     * @return
     */
    List<SysUserDept> queryUserListByDeptId(@Param("deptId")  String deptId);

    /**
     * 根据组织ID查询对应的角色信息
     * @return
     */
    List<SysRoleDept> queryDeptAndRoleById(@Param("deptId")  String deptId);

    /**
     * 查询当前组织及以下所有组织
     * @param deptId
     * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysDept>
     */
    List<SysDeptVo> queryDeptsByDeptId(String deptId);

    /**
     * 根据deptId查询组织信息
     * @param deptId
     * @return com.dxhy.heaven.admin.api.entity.SysDept
     */
    SysDept selectByDeptId(String deptId);

    /**
     * 根据机构ID查询对应的产品id集合
     * @param deptId
     * @return
     */
    List<Long> selectProductIdByDeptId(String deptId);

    /**
     * 新增删除部门对应的数据权限关系
     * @return
     */
    int deleteUserDeptByDeptId(@Param("deptId") String deptId);

    /**
     * 新增删除部门对应的菜单权限关系
     * @param deptId
     * @return
     */
    int deleteDeptMenuByDeptId(@Param("deptId") String deptId);


    int deleteDeptRoleByDeptId(@Param("deptId") String deptId);

    /**
     * 查询全部组织信息
     * @param
     * @return java.util.List<com.dxhy.heaven.admin.api.entity.SysDept>
     */
    List<SysDept> selectList();

    /**
     * 查询角色和组织的关系
     * @param roleId
     * @return
     */
    List<SysRoleDept> selectSysdeptRole(@Param("roleId") String roleId);

    /**
     * 根据渠道ID查询所有组织
     * @param sourceId
     * @return
     */
    List<SysDept>  selectDeptBySourceId(@Param("sourceId") String sourceId);


    /**
     * 根据渠道ID查询所有1级组织
     * @param sourceId
     * @return
     */
    List<SysDept>  selectOneLevelDeptBySourceId(@Param("sourceId") String sourceId);


    /**
     * 查询所有组织
     * @return
     */
    List<SysDeptVo> queryAllDepts();

    /**
     * 查询所有子级企业大于1条的渠道id
     * @return
     */
    List<String> selectDeptCountCondion();

    /**
     * 查询所有子级企业大于1条的顶级企业id
     * @return
     */
    List<String> selectNextDeptCountCondion();

    /**
     * 根据组织名称模糊查询对应渠道下的组织信息
     * @param deptName
     * @param distributorId
     * @return
     */
    List<Map<String,Object>>  queryDeptByNameAndDisId(@Param("deptName") String deptName,@Param("distributorId") String distributorId);

    /**
     *
     * @param deptName
     * @return
     */
    List<Map<String,Object>>  queryAllDeptByName(@Param("deptName") String deptName);


    List<SysDept> queryDeptList(@Param("params") Map<String, Object> params);

    SysDept selectByDeptId2(String deptId);

    List<SysDept> queryUserDeptList(@Param("userId")Long userId);

    SysDept selectTopLevelByDeptId(@Param("deptId") String deptId);

    List<SysDept> selectUnlessByDeptId(@Param("deptId") String deptId);

    SysDept queryDeptByTaxpayerNameAndCodeAndTenat(@Param("name")String name, @Param("taxpayerCode")String taxpayerCode, @Param("tenantId")String tenantId);

    SysDept queryDeptByTaxpayerNameOrCode(@Param("name")String name, @Param("taxpayerCode")String taxpayerCode);

    List<SysDept> selectUnlessByTenantId(@Param("tenantId") String tenantId);

    SysDept selectTopOrgByTaxNo(@Param("taxNo") String taxNo,@Param("tenantId") String tenantId);

    SysDept selectByDeptIdAndTenantId(@Param("deptId") String deptId,@Param("tenantId")  String tenantId,@Param("parentId")  String parentId);

    SysDept selectTopOrgByTenantId(@Param("tenantId") String tenantId);

    int updateCodeByTenantIdAndDeptId(@Param("deptId") String orgCode,@Param("tenantId")  String tenantId);
}