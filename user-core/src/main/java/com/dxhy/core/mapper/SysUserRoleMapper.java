/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.mapper;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.SysUserRole;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
  * 用户角色表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysUserRoleMapper extends BaseMapper<SysUserRole> {
    /**
     * 根据用户Id删除该用户的角色关系
     *
     * @param userId 用户ID
     * @return boolean
     * <AUTHOR>
     * @date 2017年12月7日 16:31:38
     */
    Boolean deleteByUserId(@Param("userId") Long userId);

    /**
     * 根据用户ID查询所有关联角色
     */
    List<SysUserRole>  getUserRoleByUserId(@Param("userId")Long userId);

    List<SysUserRole> getUserRoleByRoleId(@Param("roleId") Long roleId);
}