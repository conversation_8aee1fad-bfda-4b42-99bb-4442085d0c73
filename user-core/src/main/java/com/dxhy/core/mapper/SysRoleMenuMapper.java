/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.mapper;


import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.dxhy.core.pojo.entity.SysRoleMenu;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
  * 角色菜单表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysRoleMenuMapper extends BaseMapper<SysRoleMenu> {


    List<SysRoleMenu> getRoleMenuByRoleId(@Param("roleId")Long  roleId);

    boolean  delRoleMenu(SysRoleMenu roleMenu);

    Integer addRoleMenuRelation(SysRoleMenu sysRoleMenu);

    List<SysRoleMenu> getJxeMenuByRoleId(@Param("list") List<Long> roleIdList,@Param("menuId")String  menuId);
}