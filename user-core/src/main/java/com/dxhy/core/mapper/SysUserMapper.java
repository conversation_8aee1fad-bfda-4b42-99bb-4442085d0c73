/*
 *    Copyright (c) 2018-2025, lengleng All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 * Neither the name of the pig4cloud.com developer nor the names of its
 * contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * Author: lengleng (<EMAIL>)
 */

package com.dxhy.core.mapper;

import com.baomidou.mybatisplus.mapper.BaseMapper;
import com.baomidou.mybatisplus.plugins.Page;
import com.dxhy.core.pojo.DTO.AdminUserOperateDto;
import com.dxhy.core.pojo.entity.*;
import com.dxhy.core.pojo.third.DxJxUserOrg;
import com.dxhy.core.pojo.vo.SysUserRoleVo;
import com.dxhy.core.pojo.vo.UserVO;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2017-10-29
 */
public interface SysUserMapper extends BaseMapper<SysUser> {
	/**
	 * 根据用户id 获取到当前所有数据权限
	 * @param userId
	 * @return
	 */
	List<SysUserDept> selectDeptIdsByUserId(@Param("userId")Long userId);

	/**
	 * 根据用户id 获取到当前所有角色信息
	 * @param userId
	 * @return
	 */
	List<SysUserRole> selectRoleIdsByUserId(@Param("userId")Long userId);

	/**
	 * 根据角色IDList查询角色信息
	 * @param roleIdList
	 * @return
	 */
	List<String>  queryMenuIdListByRoles(@Param("roleIdList") List<Long> roleIdList);

	/**
	 * 通过用户名查询用户信息（含有角色信息）
	 *
	 * @param username 用户名
	 * @return userVo
	 */
	UserVO selectUserVoByUsername(String username);

	/**
	 *
	 * @param productId
	 * @param distributorId
	 * @return
	 */
	List<String> selectUriByDistributorIdProId(@Param("productId") String productId,@Param("distributorId") String distributorId);

	/**
	 * 根据用户ID和产品ID查看对应开通状态
	 * @param accountInfoId
	 * @param productId
	 * @return
	 */
	Integer selectByUserAndProductId(@Param("accountInfoId") Long accountInfoId,@Param("productId") String productId);

	/**
	 * 查询字典获取默认开通的产品id
	 * @param code
	 * @return
	 */
	List<String> getDictionaryByParent(@Param("code") Integer code);

	/**
	 * 企业客户开通产品
	 * @param map
	 */
	void insertCustomerProduct(Map<String,Object> map);

	/**
	 * 企业客户更新产品
	 * @param map
	 */
	void updateCustomerProduct(Map<String,Object> map);

	/**
	 * 根据ID查询产品名称
	 * @param id
	 * @return
	 */
	String selectProductInfoById(@Param("id") String id);

	/**
	 * 通过手机号查询用户信息（含有角色信息）
	 *
	 * @param mobile 用户名
	 * @return userVo
	 */
	UserVO selectUserVoByMobile(String mobile);

	/**
	 * 根据uri查询对应distributor_productlink
	 * @return
	 */
	HashMap<String, String> selectSourceIdProIdByUri(@Param("uri") String uri);

	UserVO selectUserVoById(Long userId);

    String getSimpeCodeByUnameAndTenid(@Param("username")String username, @Param("tenantId")String tenantId);

    UserVO selectUserVoByContactPhone(@Param("contactPhone") String contactPhone);

	/**
	 * 根据组织id 邮箱 手机 账号 查询用户列表
	 * @param page
	 * @param deptIdList
	 * @param email
	 * @param phone
	 * @param username
	 * @return java.util.List<com.dxyun.heaven.admin.api.entity.SysUser>
	 */
	List<SysUser> listUsersByDeptIdList(Page page,@Param("deptIdList") List<String> deptIdList, @Param("email")String email, @Param("phone")String phone, @Param("username")String username);
	/**
	 * 查询用户角色
	 * @param userId
	 * @return java.util.List<com.dxyun.heaven.admin.api.entity.SysRoleEntity>
	 */
	List<SysRole> queryRoleIdByUserId(Long userId);

	/**
	 * 根据user_id分页查询用户信息
	 * @param page
	 * @param userIdList
	 * @return java.util.List<com.dxyun.heaven.admin.api.entity.SysUserEntity>
	 */
	List<SysUser> queryPageUserByUserIdList(Page page, @Param("userIdList") List<Long> userIdList);

	/**
	 * 根据userId查询渠道id
	 * @param puserId
	 * @return java.lang.String
	 */
	String getSourceIdByUserId(Long puserId);

	/**
	 * 用户名邮箱查询账号是否存在
	 * @param userName
	 * @param email
	 * @return int
	 */
	SysUser selectByUserNameAndEmail(@Param("userName")String userName, @Param("email")String email);
	/**
	 * 新增用户
	 * @param sysUser
	 * @return void
	 */
	void addUser(SysUser sysUser);

	/**
	 * 更新用户信息
	 * @param adminUserOperateDto
	 * @return int
	 */
	int updateUserByUserId(AdminUserOperateDto adminUserOperateDto);

	/**
	 * 查询用户渠道关联是否重复
	 * @param userId
	 * @param sourceId
	 * @return com.dxyun.heaven.admin.api.entity.BaseSourceUser
	 */
	BaseSourceUser selectBaseSourceUserByUserIdAnd(@Param("userId")Long userId, @Param("sourceId")String sourceId);

	/**
	 * 添加用户渠道之间的关系
	 * @param baseSourceUser
	 * @return void
	 */
	void addBaseSourceUser(BaseSourceUser baseSourceUser);
	/**
	 * 添加用户组织之间的关联
	 * @param userId
	 * @return void
	 */
	void addUserDeptRelation(@Param("userId")Long userId, @Param("deptId")String deptId);

	/**
	 * 添加用户角色关联
	 * @param sysUserRoleVo
	 * @return void
	 */
	void addUserRoleRelation(SysUserRoleVo sysUserRoleVo);

	/**
	 * 根据用户id删除用户-组织关系
	 * @param userId
	 */
	void deleteUserDeptRelation(Long userId);

	/**
	 * 根据用户id删除用户-角色关系
	 * @param userId
	 * @return void
	 */
	void deleteUserRoleRelation(Long userId);

	/**
	 * 查询部门下禁用用户(重写selectList)
	 * @param deptId
	 * @param status
	 * @return
	 */
	List<SysUser> querySysUserStatus(@Param("deptId") String deptId,@Param("status") int status);

	/**
	 * 根据deptId和状态删除对应的用户信息
	 * @param deptId
	 * @param status
	 * @return
	 */
	int deleteByDeptIdAndStatus(@Param("deptId") String deptId,@Param("status") int status);


	/**
	 * 主账号切换
	 * @param userId
	 * @param type
	 * @return void
	 */
	void updateUserType(@Param("userId")Long userId, @Param("type")int type);

	SysUser selectByUserNameAndMobile(@Param("userName")String userName, @Param("phone")String phone);

    SysUser selectByDeptId(@Param("deptId")String deptId);

	String selectSksbbmByDeptId(@Param("deptId")String deptId,@Param("type")int type);

    Integer selectDxProduct(@Param("userId") Long userId,@Param("systemSign") String systemSign);

    SysUser selectUserByDeptId(@Param("deptId")String deptId);

    int selectVersionStatus(@Param("userId") Long userId);

	int updateVersionStatus(@Param("userId") Long userId,@Param("versionStatus") Integer versionStatus);

	SysUser selectSmrByDeptId(@Param("deptId") String deptId);

	List<String> selectDeptIds(@Param("userId")Long userId,@Param("deptName")String deptName,@Param("topLevel")String topLevel);

    List<String> getAuthNameByDeptId(@Param("deptId") String deptId,@Param("topLevel") String topLevel);

    List<DxJxUserOrg> selectUserOrgByUserId(@Param("userId")  Long userId);

	Long getMinUserId();

    SysUser selectSmrBySubLevleDeptId(@Param("deptId") String deptId);

    int getAccountTotalByName(@Param("username") String username);

    List<Long> getUserIdList(@Param("tenantId") String tenantId);

    List<Long> queryUserIdByNameList(@Param("namelist") List<String> namelist, @Param("tenantId") String tenantId);

	List<String> selectDeptIdsHw(@Param("deptName") String deptName,@Param("topLevel") String topLevel);

	List<String> checkDeptList(@Param("deptList")List<String> deptList);
}