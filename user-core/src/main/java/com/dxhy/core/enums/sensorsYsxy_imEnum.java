package com.dxhy.core.enums;/**
 * @Auther: 李永强
 * @Date: 2019/10/8 09:21
 * @Description:
 */

/**
 *@program: 神策咨询事件参数
 *@description: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *@author: liu yan
 *@create: 2019-10-08 09:21
 */
public enum sensorsYsxy_imEnum {


    IMID("imId"),
    USERSOURCE("userSource"),
    BEGINCONSULTATIONTIME("beginConsultationTime"),
    EXPERTNAME("expertName"),
    EXPERTSYPE("expertSype"),
    RESPONSEDURATION("responseDuration"),
    EXPERTREPLYSLIP("expertReplySlip"),
    CONSULTATIONDURATION("consultationDuration"),
    ISCHANGEORDER("isChangeOrder"),
    EVALUATE("evaluate"),
    ISCONSULTATIONCATEGORIZE("isConsultationCategorize"),
    WHOOVER("whoOver");

    private String parameterName;

    sensorsYsxy_imEnum(String parameterName) {
        this.parameterName = parameterName;
    }

    public String getParameterName(){
        return parameterName;
    }

}