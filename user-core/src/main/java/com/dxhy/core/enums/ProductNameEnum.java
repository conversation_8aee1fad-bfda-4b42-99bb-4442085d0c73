package com.dxhy.core.enums;

/**
 *  产品名称
 */
public enum ProductNameEnum {

    CEPING( "优税测评","e9a6bcffc95d421e95a75f54c718a0ce",11000),
    XUEYUAN("优税学院","ac5463eec58f4fca80d1a00802581ddc",14000),
    ZHUANJIA("优税专家","a2cc30f6552542ad8d22155f96adba30",15000),
    BAODIAN( "优税宝典","036a01086f1342d48f9af733a4d521d6",16000),
    GUANJIA( "优税管家","27d8f87724924b3089cc6fbd7d3684c7",17000)
    ;


    private String name;
    private String id;
    private int menuId;

    ProductNameEnum(String name, String id, int menuId) {
        this.name = name;
        this.id =id;
        this.menuId = menuId;
    }

    public String getName(){
        return name;
    }

    public String getId(){
        return id;
    }

    public int getMenuId() {return menuId;}

}
