package com.dxhy.core.enums;/**
 * @Auther: 李永强
 * @Date: 2019/10/8 09:21
 * @Description:
 */

/**
 *@program: 神策注册事件参数
 *@description: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *@author: l<PERSON> yan
 *@create: 2019-10-08 09:21
 */
public enum sensorsRegisterSuccessEnum {


    REGISTMETHOD("registration"),
    ACCOUNT("account"),
    ISSUCCESS("succeed"),
    //ISEXPERT("isExpert");
    USERTYPE("userType");

    private String parameterName;

    sensorsRegisterSuccessEnum(String parameterName) {
        this.parameterName = parameterName;
    }

    public String getParameterName(){
        return parameterName;
    }

}