package com.dxhy.core.enums;

/**
 *  验证码类型定义
 */
public enum CodeTypeEnum {

    LOGIN("1", "登录"),
    REGISTER("2", "注册"),
    FORGOT_PASSWORD("3", "忘记密码"),
    BIND("4", "绑定"),
    QUICKLOGIN("5", "手机号快捷登录"),
    BINDWECHAT("6", "绑定微信"),
    UNTIEDWECHAT("7", "解绑微信"),
    XIAOXIANG("8", "登录验证码"),
    XIAOXIANG_REGISTER("9", "注册验证码"),
    TEXT_MESSAGE("10", "其他通知短信")
    ;

    private String code;

    private String msg;

    CodeTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg(){
        return msg;
    }

}
