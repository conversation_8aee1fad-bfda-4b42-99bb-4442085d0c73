package com.dxhy.core.enums;

/**
 *  用户状态类型定义
 */
public enum StateTypeEnum {

    ACTIVATION("0", "激活"),
    FROZEN("1", "冻结")
    ;

    private String code;

    private String msg;

    StateTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return code;
    }

    public String getMsg(){
        return msg;
    }

}
