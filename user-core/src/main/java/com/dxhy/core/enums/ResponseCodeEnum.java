package com.dxhy.core.enums;

/**
 * @author: z<PERSON><PERSON><PERSON>g
 * @Date: 2022/4/25 15:37
 * @Version 1.0
 */
public enum ResponseCodeEnum {
    SUCCESS("0000", "SUCCESSFULL!"),
    SYSTEM_ERROR("9999", "SYSTEM ERROR!"),
    MONGODB_ERROR("7000", "MONGO ERROR!"),
    PERMISSION_DENIED("10001","PERMISSION_DENIED"),
    TOKEN_NULL("10002","token为空"),
    TOKEN_FORMAT_ERROR("10004", "token格式有误"),
    TOKEN_SIGN_ERROR("10005","token签名被篡改"),
    TOKEN_RENEWAL_ERROR("10006","token续期失败"),
    TOKEN_CHECK_ERROR("10007","token校验失败，用户信息不存在"),

    /*DX加密字符串校验代码*/
    ACCOUT_NULL("10008","账号不能为空"),
    TAXNO_NULL("10009","税号不能为空"),
    ENT_NAME_NULL("10010","企业名称不能为空"),
    MENU_URI_NULL("10011","菜单地址不能为空"),
    DX_SIMPLE_CODE_NULL("10011","DX简码不能为空"),


    /**用户校验代码*/
    PHONE_IS_EXITES("10012","手机号已存在,请修改手机号再试"),
    EAMIL_IS_EXITES("10015","邮箱已存在,请修改邮箱再试"),

    EAMIL_NULL("10013","邮箱不能为空"),

    DEPT_NOT_EXITES("10014","企业信息不存在"),

    TENANTID_NULL("10016","租户ID不能为空"),
    ADDRESS_NULL("10017","地址不能为空"),
    BNAK_NULL("10018","开户行不能为空"),
    YHZH_NULL("10019","银行账号不能为空"),
    NSRLX_NULL("10020","纳税人类型不能为空"),
    KJZZ_NULL("10021","会计准则不能为空"),
    PHONE_NULL("10022","注册电话不能为空"),
    NSRLX_NUMBER("10023","纳税人类型只能为0和1"),
    KJZZ_NUMBER("10024","会计准则只能为纯数字"),
    PRODUCT_NULL("10025","开通的产品不能为空"),
    SUSER_IS_EXITES("10026","账号已存在"),
    SIGN_ERROR("10027","签名校验不通过"),
    MAXORG_LIMT("10028","该租户下组织数量超过最大限制"),
    SKSBBM_NO_EXITS("10029","税控类型不存在"),
    SKSBBM_IS_NULL("10030","开通了销项产品，但是税控类型为空"),
    ACE_IS_NULL("10030","开通了进项产品，但是采集鉴权ID或KEY为空"),
    UORG_IS_EXITS("10031","超管账号绑定的机构已存在"),
    TAXNO_RULES("10032","购方纳税人识别号长度只能为15/17/18/20 由大写英文和数字组成"),
    MOBILE_NULL("10033","手机号不能为空"),
    REG_CHANNEL_NULL("10034","租户来源不能为空"),
    REG_CHANNEL_NUMBER("10035","租户来源只能为纯数字"),
    PLG_EXPIRE_REMIND("10036","试用期到期"),
    PLG_ERROR("10037","官网注册，但全电产品不标准"),
    ;
    private final String code;
    private final String message;

    private ResponseCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}
