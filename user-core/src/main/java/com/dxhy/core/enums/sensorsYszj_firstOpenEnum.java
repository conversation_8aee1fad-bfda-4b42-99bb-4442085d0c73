package com.dxhy.core.enums;/**
 * @Auther: 李永强
 * @Date: 2019/10/8 09:21
 * @Description:
 */

/**
 *@program: 神策首次开通产品参数
 *@description: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *@author: liu yan
 *@create: 2019-10-08 09:21
 */
public enum sensorsYszj_firstOpenEnum {


    OPENTIME("openTime"),
    ACCOUNT("account"),
    ACCOUNTSYPE("AccountSype"),
    ACCOUNTKIND("AccountKind");

    private String parameterName;

    sensorsYszj_firstOpenEnum(String parameterName) {
        this.parameterName = parameterName;
    }

    public String getParameterName(){
        return parameterName;
    }

}