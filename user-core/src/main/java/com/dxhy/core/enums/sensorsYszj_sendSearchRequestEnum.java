package com.dxhy.core.enums;/**
 * @Auther: 李永强
 * @Date: 2019/10/8 09:21
 * @Description:
 */

/**
 *@program: 神策搜索结果参数
 *@description: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 *@author: liu yan
 *@create: 2019-10-08 09:21
 */
public enum sensorsYszj_sendSearchRequestEnum {


    REGISTMETHOD("keyWord"),
    ACCOUNT("isResult");

    private String parameterName;

    sensorsYszj_sendSearchRequestEnum(String parameterName) {
        this.parameterName = parameterName;
    }

    public String getParameterName(){
        return parameterName;
    }

}