package com.dxhy.core.enums;

/**
 * <AUTHOR>
 * @date 17:252019/2/27 0027
 * @description 错误码枚举类
 */
public enum DictionaryCodeEnum {
    /**
     * 操作成功
     */
    SUCCESS("0000", "SUCCESSFULL!"),
    /**
     * 系统异常，可在9000---9998之间添加详细系统异常码
     */
    SYSTEM_ERROR("9999", "SYSTEM ERROR!"),
    /**
     * 返回结果为空
     */
    RESULT_NULL("9998", "返回结果为空"),
    /**
     * mysql 相关错误，可在8000---9000添加详细错误码
     */
    MYSQL_ERROR("8000", "MYSQL ERROR!"),
    /**
     * 保存失败
     */
    MYSQL_INSERT_ERROR("8001", "MYSQL INSERT ERROR!"),
    /**
     * mongodb 错误，可在7000-8000之间添加详细错误码
     */
    MONGODB_ERROR("7000", "MONGO ERROR!"),
    /**
     * redis相关错误，可在6000----7000之间添加详细错误码
     */
    REDIS_ERROR("6000", "REDIS ERROR"),
    /**
     * 参数，可在5000----6000之间添加详细错误码
     */
    PARAMETER_ERROR("5000", "参数错误"),
    PARAMETER_NULL("5100", "参数为空"),
    PARAMETER_USER_NULL("5101", "用户id为空"),
    PARAMETER_PRODUCT_NULL("5102", "产品id为空"),
    PARAMETER_COMPANY_NULL("5103", "企业id为空"),

    TAX_NO_ALREADY_EXISTS("5001", "纳税人识别号已存在"),
    PHONE_ALREADY_EXISTS("5002", "手机号已存在");
    /**
     * 错误码
     */
    private final String code;
    /**
     * 错误描述
     */
    private final String message;

    DictionaryCodeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
