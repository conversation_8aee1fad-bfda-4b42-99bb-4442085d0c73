package com.dxhy.core.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.logout.LogoutHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@Component
public class Heaven<PERSON>ogoutHandler implements LogoutHandler {
    @Override
    public void logout(HttpServletRequest request, HttpServletResponse response, Authentication authentication) {
        try {
//            Cookie[] cookies = request.getCookies();
//            Cookie cookie = new Cookie("JSESSIONID", null);
//            String cookiePath = request.getContextPath() + "/";
//            cookie.setPath(cookiePath);
//            cookie.setMaxAge(0);
//            response.addCookie(cookie);
            String redirect_uri = request.getParameter("redirect_uri");
            log.info("用户登出，登出回调地址：{}", redirect_uri);
            response.sendRedirect(redirect_uri);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
