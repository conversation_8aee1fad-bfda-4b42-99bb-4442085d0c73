package com.dxhy.core.handler;

import com.alibaba.fastjson.JSON;
import com.dxhy.core.common.response.R;
import com.dxhy.core.pojo.vo.UserVO;
import com.dxhy.core.service.BaseSourceProductService;
import com.dxhy.core.service.SensorsService;
import com.dxhy.core.service.UserService;
import com.dxhy.core.service.impl.UserDetailsImpl;
import com.dxhy.core.utils.CommonUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.web.authentication.AuthenticationSuccessHandler;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class HeavenLoginSuccessHandler implements AuthenticationSuccessHandler {


    public static final String REDIRECT_URI = "redirect_uri";
    public static final String UTF_8 = "UTF-8";
    @Autowired
    private BaseSourceProductService baseSourceProductService;

    @Autowired
    private SensorsService sensorsService;

    @Autowired
    private UserService userService;

    private static HeavenLoginSuccessHandler  instance ;

    @PostConstruct
    public void init() {
        instance = this;
        instance.baseSourceProductService = this.baseSourceProductService;
        instance.sensorsService = this.sensorsService;
        instance.userService = this.userService;
    }


    @Override
    public void onAuthenticationSuccess(HttpServletRequest request, HttpServletResponse response, Authentication authentication) throws IOException {
        System.out.println("是否成功登录。。。。。。。。");
        R r = new R();
        Map<String, String> paramMap = CommonUtils.getRequestParamsFromCache(request, response);
        //获取用户信息
        UserDetailsImpl userDetails = (UserDetailsImpl)authentication.getPrincipal();
        // 认证通过，需要校验，用户产品权限
        String redirectURI = paramMap.get(REDIRECT_URI);
        String domain = CommonUtils.getDomain(redirectURI);

        // REDIRECT_URI要url编码
        paramMap.put(REDIRECT_URI, URLEncoder.encode(redirectURI,UTF_8));
        r.setData(paramMap);
        r.setMsg(authentication.getName());

        String encoderUsername  = new org.apache.catalina.util.URLEncoder().encode(userDetails.getUsername(), StandardCharsets.UTF_8);
        UserVO userByUsername = instance.userService.findUserByUsername(encoderUsername);
//        String encoderUsername = userDetails.getUsername();
//        ResponseEntity<UserVO> resp = restTemplate.getForEntity("http://localhost:8083/user-service/user/findUserByUsername/"+encoderUsername,UserVO.class);
//        System.out.println("handler/HeavenLoginSuccesHandler类中的调用findUserByUsername请求结果："+resp.getBody());
//        UserVO userByUsername = resp.getBody();

        long loginTime = System.currentTimeMillis();
        long createTime = userByUsername.getCreateTime().getTime();
        int difference = (int)((loginTime - createTime) / 1000);
        //注册完10秒内登陆不校验是否绑定手机号
        if(userByUsername != null && StringUtils.isBlank(userByUsername.getPhone()) && StringUtils.isBlank(userByUsername.getEmail())&& difference > 10){
            r.setCode(10);
            r.setMsg("手机号未绑定");
        }

        /*//神策登录事件
        HashMap<String, String> map = new HashMap<>();
        redirectURI = URLEncoder.encode(redirectURI, "UTF-8");
        R<Map<String,String>> sourceInfoByUri = instance.userService.getSourceInfoByUri(redirectURI);
        if(sourceInfoByUri==null|| sourceInfoByUri.getData() ==null){
            log.error("未获取到产品来源关系，请联系后台工作人员进行基础配置,产品来源:{}", redirectURI);
        }else{
            map.put("productName",sourceInfoByUri.getData().get("productName"));
            map.put("productId",sourceInfoByUri.getData().get("productId"));
            map.put("platformType","web");
        }
        map.put("username",userDetails.getUsername());
        map.put("userType",userDetails.getUserType());
        map.put("deviceId",paramMap.get("device_id"));
        map.put("platformType","web");
        instance.sensorsService.sensorsLogin(map);*/

        if(r.getCode() != 0) {
        } else {
            // 清除认证状态
//            authentication.setAuthenticated(false);
            // 登录认证成功是清除请求缓存
            CommonUtils.removeRequestFromCache(request, response);
        }
        response.setHeader("Content-Type", "application/json;charset=UTF-8");
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JSON.toJSONString(r));

    }

}
