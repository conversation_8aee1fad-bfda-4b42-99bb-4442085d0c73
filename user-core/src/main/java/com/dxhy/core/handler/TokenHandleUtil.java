package com.dxhy.core.handler;


import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.AlgorithmMismatchException;
import com.auth0.jwt.exceptions.InvalidClaimException;
import com.auth0.jwt.exceptions.SignatureVerificationException;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.dxhy.core.common.response.CommonRspVo;
import com.dxhy.core.constants.CommonConstant;
import com.dxhy.core.constants.SystemConstants;
import com.dxhy.core.enums.ResponseCodeEnum;
import com.dxhy.core.mapper.SysUserMapper;
import com.dxhy.core.pojo.entity.SysUser;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.oauth2.provider.token.TokenStore;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.concurrent.TimeUnit;

/**
 *
 * 类名称：TokenHandleUtil
 * 类描述：token处理工具类
 * 创建人：shenlifang
 * 创建时间：2022年6月24日 下午16:55:00
 *
 */
@Slf4j
@Component
public class TokenHandleUtil {

    @Autowired
    private TokenStore tokenStore;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private RedisTemplate redisTemplate;
    private static final String ACCESS = "access:";

    private static TokenHandleUtil tokenHandleUtil;


    @PostConstruct
    private void init () {
        tokenHandleUtil = this;
    }

    /**
     * 延长token有效期
     */
    public Boolean extendTokenValidity(String authorization,Long extendTime) throws UnsupportedEncodingException {
        log.debug("token续签开始");
//        String str [] =authorization.split(" ");
//        String token = str[1];
        String token = StringUtils.substringAfter(authorization, CommonConstant.TOKEN_SPLIT);
        log.info("目前token是{}",token);
        if (token !=null && StringUtils.isNotEmpty(token)) {
            //重新签发token
            JSONObject jsonObject =JSONObject.parseObject(com.xiaoleilu.hutool.codec.Base64.decodeStr(token.split("\\.")[1]));
            System.out.println(jsonObject);
            String newtoken = SignToken(jsonObject,extendTime);
            tokenHandleUtil.redisTemplate.opsForValue().set(SystemConstants.USER_TOKEN_CODE_KEY + token,newtoken, extendTime*2, TimeUnit.SECONDS);
            log.info("token过期已续期");
            return true;
        }
        return false;
    }


    public CommonRspVo verifyToken(String token) throws UnsupportedEncodingException {
        log.info("[TokenHandleUtile verifyToken]接收参数：{},{}",token);
        //校验签名是否被篡改
        String [] splitStr = token.split("\\.");
        String headerAndClaimsStr = splitStr[0] + "." +splitStr[1];
        String veryStr = signHmac256(headerAndClaimsStr, CommonConstant.SIGN_KEY);
        if (!veryStr.equals(splitStr[2])) {
            log.info("签名被篡改，无权限 验证签名：{},携带签名：{}",veryStr,splitStr[2]);
            return new CommonRspVo(ResponseCodeEnum.TOKEN_SIGN_ERROR);
        }

        //校验有效期的真正token  redis中key
        String access_token = (String) tokenHandleUtil.redisTemplate.opsForValue().get(SystemConstants.USER_TOKEN_CODE_KEY + token);
        if (access_token == null) {
            //log.error("token is not exits ...");
            //return false;
            log.info("redis中token已不存在，进行自动续期");
            Boolean b = tokenHandleUtil.extendTokenValidity(CommonConstant.TOKEN_SPLIT+token,60*60*2L);
            if (!b) {
                log.info("token续期失败");
                return new CommonRspVo(ResponseCodeEnum.TOKEN_SIGN_ERROR);
            }else{
                return new CommonRspVo(ResponseCodeEnum.SUCCESS);
            }
        }

        log.info("redis缓存中的token是："+access_token);
        JSONObject jsonObject =JSONObject.parseObject(com.xiaoleilu.hutool.codec.Base64.decodeStr(access_token.split("\\.")[1]));
        Long userId = jsonObject.getLong("userId");
        SysUser sysUser =  tokenHandleUtil.sysUserMapper.selectById(userId);
        if (sysUser == null || "".equals(sysUser)) {
            log.error("token校验失败，用户信息不存在");
            return new CommonRspVo(ResponseCodeEnum.TOKEN_CHECK_ERROR);
        }


        Long exp = jsonObject.getLong("exp");
        //获取当前系统时间戳
        long nowTime = System.currentTimeMillis()/1000;
        if (nowTime >= exp) {
            log.info("【token过滤器】token已到期,nowTime:{},exp:{} 进行自动续期",nowTime,exp);

            Boolean b = tokenHandleUtil.extendTokenValidity(CommonConstant.TOKEN_SPLIT+token,60*60*2L);
            if (!b) {
                log.info("【token过滤器】token续期失败");
                return new CommonRspVo(ResponseCodeEnum.TOKEN_SIGN_ERROR);
            }else{
                return new CommonRspVo(ResponseCodeEnum.SUCCESS);
            }
        }else{
            DecodedJWT djwt = verify(access_token, CommonConstant.SIGN_KEY);
            if (djwt!=null) {
                return new CommonRspVo(ResponseCodeEnum.SUCCESS);
            }
        }
        return CommonRspVo.faild();
    }

    /**
     * 验证token
     *  不知道秘钥放弃
     * @param token
     */
    public static DecodedJWT verify(String token,String secret) {
        try {
            //设置加密算法
            JWTVerifier verifier = JWT.require(Algorithm.HMAC256(secret)).build();
            //校验token
            DecodedJWT jwt = verifier.verify(token);
            System.out.println(jwt.getClaim("exp").asInt());
            return jwt;
        }catch (SignatureVerificationException e) {
//            e.printStackTrace();
            log.error("签名不一致异常:",e);
        } catch (AlgorithmMismatchException e) {
//            e.printStackTrace();
            log.error("算法不匹配异常:",e);
        } catch (InvalidClaimException e) {
//            e.printStackTrace();
            log.error("失效的payload异常:",e);
        }catch (Exception e) {
//            e.printStackTrace();
            log.error("token校验异常:",e);
        }
        return null;
    }

    public static void main(String[] args) {
        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************************************************.FK_OUu57UqmDsVsYGXmIZ4kG-RD9jCgpYy1uN0N1NsE";
//        log.info(verify(token, CommonConstant.SIGN_KEY)+"");
        String [] strtoken = token.split("\\.");
        JSONObject jsonObject =JSONObject.parseObject(com.xiaoleilu.hutool.codec.Base64.decodeStr(strtoken[1]));
        System.out.println(jsonObject);

        String newtoken = null;
        try {
            newtoken =  SignToken(jsonObject,1000L);
            System.out.println(newtoken);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        //获取当前系统时间戳
//        long nowTime = System.currentTimeMillis()/1000+1000;
//        jsonObject.put("exp",nowTime);
//        System.out.println(jsonObject);
//        String token1 = com.xiaoleilu.hutool.codec.Base64.encode(jsonObject.toJSONString());
//        System.out.println(token1);
//
//        String newtoken = strtoken[0]+"."+token1+"."+strtoken[2];
//        System.out.println(newtoken);
        log.info(verify(newtoken, CommonConstant.SIGN_KEY)+"");
    }

    public static String SignToken(JSONObject jsonObject,Long qxtime) throws UnsupportedEncodingException {
        String license = jsonObject.getString("license");
        String username = jsonObject.getString("user_name");
        String userType = jsonObject.getString("userType");
        Long userId = jsonObject.getLong("userId");
        String jti = jsonObject.getString("jti");
        String client_id = jsonObject.getString("client_id");
        //续期可能不对
        Date date = new Date(System.currentTimeMillis()+qxtime*1000);

        String scopestr = (String) jsonObject.getJSONArray("scope").get(0);
        String [] scope = new String[1];
        scope[0]=scopestr;

        String authstr = (String) jsonObject.getJSONArray("authorities").get(0);
        String [] authorities = new String[1];
        authorities[0]=authstr;

        //使用HS256生成token
        Algorithm algorithm = Algorithm.HMAC256(CommonConstant.SIGN_KEY);
        String token = JWT.create().withClaim("license",license)
                .withClaim("user_name",username)
                .withArrayClaim("scope", scope)
                .withClaim("userType",userType)
                .withExpiresAt(date)
                .withClaim("jti",jti)
                .withClaim("userId", Math.toIntExact(userId))
                .withArrayClaim("authorities",authorities)
                .withJWTId(jti)
                .withClaim("client_id",client_id).sign(algorithm);

        return token;
    }
    /**
     * 将headerAndClaimsStr用SHA1加密获取sign
     *
     * @param headerAndClaimsStr
     * @param appSecretKey
     * @return
     */
    private String signHmac256(String headerAndClaimsStr, String appSecretKey) {
        SecretKey key = new SecretKeySpec(appSecretKey.getBytes(), "HmacSHA256");
        String result = null;
        try {
            Mac mac;
            mac = Mac.getInstance(key.getAlgorithm());
            mac.init(key);
            result = Base64.encodeBase64URLSafeString(mac.doFinal(headerAndClaimsStr.getBytes()));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error(e.getMessage(), e);
        }
        return result;
    }

}

