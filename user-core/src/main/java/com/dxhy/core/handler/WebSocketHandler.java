package com.dxhy.core.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dxhy.core.common.response.R;
import com.dxhy.core.service.OhtherSsoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.*;
import org.springframework.web.socket.handler.AbstractWebSocketHandler;
import org.springframework.web.socket.sockjs.transport.session.WebSocketServerSockJsSession;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class WebSocketHandler extends AbstractWebSocketHandler {

    /**
     * 存储sessionId和webSocketSession
     * 需要注意的是，webSocketSession没有提供无参构造，不能进行序列化，也就不能通过redis存储
     * 在分布式系统中，要想别的办法实现webSocketSession共享
     */
    private static Map<String, WebSocketSession> sessionMap = new ConcurrentHashMap<>();
    private static Map<String, String> userMap = new ConcurrentHashMap<>();
    @Autowired
    private OhtherSsoService ohtherSsoService;
    private static WebSocketHandler webSocket;
    @PostConstruct
    public void init(){
        webSocket = this;
    }

    public static Map<String, String> getUserMap(String user) {
        Map<String, String> map = new ConcurrentHashMap<>();
        Set set = userMap.keySet();
        for (Object o : set) {
            System.out.println(o+""+map.get(o));
            if (user.equals(o.toString()) ) {
                map.put((String)o,map.get(o));
            }
        }
        return map;
    }


    /**
     * 获取sessionId
     */
    private String getSessionId(WebSocketSession session) {
        if (session instanceof WebSocketServerSockJsSession) {
            // sock js 连接
            try {
                return  ((WebSocketSession) FieldUtils.readField(session, "webSocketSession", true)).getId();
            } catch (IllegalAccessException e) {
                throw new RuntimeException("get sessionId error");
            }
        }
        return session.getId();
    }

    /**
     * webSocket连接创建后调用
     */
    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        log.debug("connect to the websocket success......");
        //获取参数userId
        String user = String.valueOf(session.getAttributes().get("user"));
        String sessionId = getSessionId(session);
        log.info("建立连接后获取当前账号:{}，连接:{}",user,sessionId);
        sessionMap.put(user+"|"+sessionId, session);
        String token="";
        String userId = user.split("-")[0];
        //调用大B 子系统 报销管理 获取sessionid  数据库字典中配置
        //String ssoid = webSocket.ohtherSsoService.getDbSsoId(userId);
        String ssoid = "";
        //调用单点登录接口获取大B sso_session_id
        String sso_session_id = webSocket.ohtherSsoService.getJxSsoId(userId);
        if (StringUtils.isBlank(sso_session_id) && StringUtils.isBlank(ssoid)) {
            //session 为空 关闭连接
            log.info("获取的sessionid 并且 ssoid 为空，删除连接："+user);
            sessionMap.remove(user+"|"+sessionId);
        }else{
            log.info("建立连接后单点登录大B，获取最新sso_session_id:{} ssoid:{}",sso_session_id,ssoid);
            sendMessage(session,token,sso_session_id,ssoid);
        }
    }

    /**
     * 接收到消息会调用
     */
    @Override
    public void handleMessage(WebSocketSession session, WebSocketMessage<?> message) throws Exception {
        log.info("从前端接收的消息是："+message.toString());
        if (message instanceof TextMessage) {
            session.sendMessage(new TextMessage("pong"));
        } else if (message instanceof BinaryMessage) {

        } else if (message instanceof PongMessage) {

        } else {
            System.out.println("Unexpected WebSocket message type: " + message);
        }
    }

    /**
     * 连接出错会调用
     */
    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        String user = String.valueOf(session.getAttributes().get("user"));
        log.info("连接出错，删除连接："+user);
        sessionMap.remove(user+"|"+getSessionId(session));
    }

    /**
     * 连接关闭会调用
     */
    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String user = String.valueOf(session.getAttributes().get("user"));
        log.info("连接关闭，删除连接："+user);
        sessionMap.remove(user+"|"+getSessionId(session));
    }

    @Override
    public boolean supportsPartialMessages() {
        return false;
    }

    /**
     * 后端发送消息
     */
    public void sendMessage(WebSocketSession ses,String token,String sso_session_id,String ssoid) {
//    public void sendMessage(String user, String message,WebSocketSession ses) {
//        String sessionId = userMap.get(user);
//        log.info("发送消息的信息："+sessionId+","+sessionMap.size());
//        if (sessionId != null) {
//            WebSocketSession session = sessionMap.get(sessionId);
           WebSocketSession session = ses;
            try {
                //推送消息封装为json
                HashMap<String, Object> map = new HashMap<>();
                map.put("newtoken", token);
                map.put("newsession", sso_session_id);
                map.put("ssoid", ssoid);
                R result = new R(map);
                JSONObject jsonObject = (JSONObject) JSON.toJSON(result);
                System.out.println(jsonObject);
                session.sendMessage(new TextMessage(jsonObject.toJSONString()));
            } catch (IOException e) {
                e.printStackTrace();
            }
//        }
    }

    public static Map<String, WebSocketSession> getWebSocketMap() {
        return sessionMap;
    }
}

